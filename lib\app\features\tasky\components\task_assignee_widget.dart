
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import 'package:logger/logger.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:tvumobile/app/features/tasky/components/assignee_tile_widget.dart';
import 'package:tvumobile/app/features/tasky/controllers/taskycreate_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class TaskAssigneeWidget extends StatefulWidget {
  final TaskyCreateController taskCreateController;
  final List<dynamic> users;
  const TaskAssigneeWidget({
    super.key,
    required this.taskCreateController, required this.users,
  });

  @override
  State<TaskAssigneeWidget> createState() => _TaskAssigneeWidgetState();
}

class _TaskAssigneeWidgetState extends State<TaskAssigneeWidget> {
  bool isChecked = false;
  final Logger _logger = Logger();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        margin: const EdgeInsets.symmetric(vertical: 24),
        child: Column(
          children: [
            const SizedBox(
              height: 14,
            ),
            Container(
              height: 8,
              width: 50,
              decoration: BoxDecoration(color: Colors.grey, borderRadius: BorderRadius.circular(45)),
            ),
            const SizedBox(
              height: 14,
            ),
            MyText.titleMedium("Vui long chon nguoi thuc hoen nhiem vu"),

            ListView(
              shrinkWrap: true,
              padding: const EdgeInsets.all(24),
              controller: ModalScrollController.of(context),
              children: List.generate(widget.taskCreateController.teamMembers.length, (index) {
                return AssigneeTileWidget(
                  isChecked: widget.users.isEmpty ? false : (widget.users
                      .singleWhereOrNull((it) => it["vienChucId"] == widget.taskCreateController.teamMembers[index]["vienChucId"])) !=
                      null,
                  selectedUser: widget.taskCreateController.teamMembers[index],
                  onTap: (Map user) {

                    setState(() {
                      if ((widget.users.singleWhereOrNull((it) => it["vienChucId"] == user["vienChucId"])) != null) {
                        widget.users.remove(user);
                      } else {
                        widget.users.add(user);
                      }
                    });
                    //widget.taskCreateController.setAssignees(widget.users);
                    _logger.d(widget.users);
                  },
                );
              }),
            ),
            const SizedBox(
              height: 25,
            ),
            TextButton(
              style: TextButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  backgroundColor: Colors.black),
              onPressed: () {
                setState(() {
                  if (widget.users.isNotEmpty) {
                    widget.taskCreateController.setAssignees(widget.users);
                  }
                });
                //if (widget.taskCreateController.assignees.isNotEmpty) {
                widget.taskCreateController.update();
                  Navigator.pop(context);
                //}
              },
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Text('Done',
                    style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.white)),
              ),
            ),
            const SizedBox(height: 15),
          ],
        ),
      ),
    );
  }
}