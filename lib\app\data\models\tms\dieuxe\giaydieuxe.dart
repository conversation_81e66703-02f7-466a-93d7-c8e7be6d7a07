import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxetaixe.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/hopdongthuexe.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepinfo.dart';

class GiayDieuXe {
  GiayDieuXe({
    this.id,
    this.donVi,
    this.noiDung,
    this.noiCongTac,
    this.tuNgay,
    this.denNgay,
    this.thoiGianKhoiHanh,
    this.thoiGianVe,
    this.diaDiemKhoiHanh,
    this.diaDiemVe,
    this.ketQua,
    this.trangThai,
    this.nguoiTao,
    this.hcDuyet,
    this.donViDuyet,
    this.bghDuyet,
    this.ngayTao,
    this.ngayHCDuyet,
    this.ngayDonViDuyet,
    this.ngayBGHDuyet,
    this.dsDiCongTac,
    this.hopDongThueXe,
    this.ttDieuXe,
    this.vienChucNgoaiTruong,
    this.donViVienChucNgoaiTruong,
    this.parentId,
    this.laPhieuGhep,
    this.fileDinhKem,
    this.ghiChu,
  });

  final int? id;
  final DonVi? donVi;
  final String? noiDung;
  final String? noiCongTac;
  final DateTime? tuNgay;
  final DateTime? denNgay;
  final String? thoiGianKhoiHanh;
  final String? thoiGianVe;
  final String? diaDiemKhoiHanh;
  final String? diaDiemVe;
  final String? ketQua;
  final String? trangThai;
  final NguoiTao? nguoiTao;
  final NguoiTao? hcDuyet;
  final NguoiTao? donViDuyet;
  final NguoiTao? bghDuyet;
  final DateTime? ngayTao;
  final DateTime? ngayHCDuyet;
  final DateTime? ngayDonViDuyet;
  final DateTime? ngayBGHDuyet;
  final List<NguoiTao>? dsDiCongTac;
  final HopDongThueXe? hopDongThueXe;
  final DieuXeTaiXe? ttDieuXe;
  final String? vienChucNgoaiTruong;
  final String? donViVienChucNgoaiTruong;
  final int? parentId;
  final bool? laPhieuGhep;
  final String? fileDinhKem;
  final String? ghiChu;

  GiayDieuXe copyWith({
    int? id,
    DonVi? donVi,
    String? noiDung,
    String? noiCongTac,
    DateTime? tuNgay,
    DateTime? denNgay,
    String? thoiGianKhoiHanh,
    String? thoiGianVe,
    String? diaDiemKhoiHanh,
    String? diaDiemVe,
    String? ketQua,
    String? trangThai,
    NguoiTao? nguoiTao,
    NguoiTao? hcDuyet,
    NguoiTao? donViDuyet,
    NguoiTao? bghDuyet,
    DateTime? ngayTao,
    DateTime? ngayHCDuyet,
    DateTime? ngayDonViDuyet,
    DateTime? ngayBGHDuyet,
    List<NguoiTao>? dsDiCongTac,
    HopDongThueXe? hopDongThueXe,
    DieuXeTaiXe? ttDieuXe,
    String? vienChucNgoaiTruong,
    String? donViVienChucNgoaiTruong,
    int? parentId,
    bool? laPhieuGhep,
    String? fileDinhKem,
    String? ghiChu,
  }) {
    return GiayDieuXe(
      id: id ?? this.id,
      donVi: donVi ?? this.donVi,
      noiDung: noiDung ?? this.noiDung,
      noiCongTac: noiCongTac ?? this.noiCongTac,
      tuNgay: tuNgay ?? this.tuNgay,
      denNgay: denNgay ?? this.denNgay,
      thoiGianKhoiHanh: thoiGianKhoiHanh ?? this.thoiGianKhoiHanh,
      thoiGianVe: thoiGianVe ?? this.thoiGianVe,
      diaDiemKhoiHanh: diaDiemKhoiHanh ?? this.diaDiemKhoiHanh,
      diaDiemVe: diaDiemVe ?? this.diaDiemVe,
      ketQua: ketQua ?? this.ketQua,
      trangThai: trangThai ?? this.trangThai,
      nguoiTao: nguoiTao ?? this.nguoiTao,
      hcDuyet: hcDuyet ?? this.hcDuyet,
      donViDuyet: donViDuyet ?? this.donViDuyet,
      bghDuyet: bghDuyet ?? this.bghDuyet,
      ngayTao: ngayTao ?? this.ngayTao,
      ngayHCDuyet: ngayHCDuyet ?? this.ngayHCDuyet,
      ngayDonViDuyet: ngayDonViDuyet ?? this.ngayDonViDuyet,
      ngayBGHDuyet: ngayBGHDuyet ?? this.ngayBGHDuyet,
      dsDiCongTac: dsDiCongTac ?? this.dsDiCongTac,
      hopDongThueXe: hopDongThueXe ?? this.hopDongThueXe,
      ttDieuXe: ttDieuXe ?? this.ttDieuXe,
      vienChucNgoaiTruong: vienChucNgoaiTruong ?? this.vienChucNgoaiTruong,
      donViVienChucNgoaiTruong:
          donViVienChucNgoaiTruong ?? this.donViVienChucNgoaiTruong,
      parentId: parentId ?? this.parentId,
      laPhieuGhep: laPhieuGhep ?? this.laPhieuGhep,
      fileDinhKem: fileDinhKem ?? this.fileDinhKem,
      ghiChu: ghiChu ?? this.ghiChu,
    );
  }

  factory GiayDieuXe.fromJson(Map<String, dynamic> json) {
    return GiayDieuXe(
      id: json["id"],
      donVi: json["donVi"] == null ? null : DonVi.fromJson(json["donVi"]),
      noiDung: json["noiDung"],
      noiCongTac: json["noiCongTac"],
      tuNgay: DateTime.tryParse(json["tuNgay"] ?? ""),
      denNgay: DateTime.tryParse(json["denNgay"] ?? ""),
      thoiGianKhoiHanh: json["thoiGianKhoiHanh"],
      thoiGianVe: json["thoiGianVe"],
      diaDiemKhoiHanh: json["diaDiemKhoiHanh"],
      diaDiemVe: json["diaDiemVe"],
      ketQua: json["ketQua"],
      trangThai: json["trangThai"],
      nguoiTao:
          json["nguoiTao"] == null ? null : NguoiTao.fromJson(json["nguoiTao"]),
      hcDuyet:
          json["hcDuyet"] == null ? null : NguoiTao.fromJson(json["hcDuyet"]),
      donViDuyet: json["donViDuyet"] == null
          ? null
          : NguoiTao.fromJson(json["donViDuyet"]),
      bghDuyet:
          json["bghDuyet"] == null ? null : NguoiTao.fromJson(json["bghDuyet"]),
      ngayTao: DateTime.tryParse(json["ngayTao"] ?? ""),
      ngayHCDuyet: DateTime.tryParse(json["ngayHCDuyet"] ?? ""),
      ngayDonViDuyet: DateTime.tryParse(json["ngayDonViDuyet"] ?? ""),
      ngayBGHDuyet: DateTime.tryParse(json["ngayBGHDuyet"] ?? ""),
      dsDiCongTac: json["dsDiCongTac"] == null
          ? null
          : List<NguoiTao>.from(
              json["dsDiCongTac"]!.map((x) => NguoiTao.fromJson(x))),
      hopDongThueXe: json["hopDongThueXe"] == null
          ? null
          : HopDongThueXe.fromJson(json["hopDongThueXe"]),
      ttDieuXe: json["ttDieuXe"] == null
          ? null
          : DieuXeTaiXe.fromJson(json["ttDieuXe"]),
      vienChucNgoaiTruong: json["vienChucNgoaiTruong"],
      donViVienChucNgoaiTruong: json["donViVienChucNgoaiTruong"],
      parentId: json["parentId"],
      laPhieuGhep: json["laPhieuGhep"],
      fileDinhKem: json["fileDinhKem"],
      ghiChu: json["ghiChu"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "donVi": donVi?.toJson(),
        "noiDung": noiDung,
        "noiCongTac": noiCongTac,
        "tuNgay": tuNgay?.toIso8601String(),
        "denNgay": denNgay?.toIso8601String(),
        "thoiGianKhoiHanh": thoiGianKhoiHanh,
        "thoiGianVe": thoiGianVe,
        "diaDiemKhoiHanh": diaDiemKhoiHanh,
        "diaDiemVe": diaDiemVe,
        "ketQua": ketQua,
        "trangThai": trangThai,
        "nguoiTao": nguoiTao?.toJson(),
        "hcDuyet": hcDuyet?.toJson(),
        "donViDuyet": donViDuyet?.toJson(),
        "bghDuyet": bghDuyet?.toJson(),
        "ngayTao": ngayTao?.toIso8601String(),
        "ngayHCDuyet": ngayHCDuyet?.toIso8601String(),
        "ngayDonViDuyet": ngayDonViDuyet?.toIso8601String(),
        "ngayBGHDuyet": ngayBGHDuyet?.toIso8601String(),
        "dsDiCongTac": dsDiCongTac?.map((x) => x.toJson()).toList(),
        "hopDongThueXe": hopDongThueXe?.toJson(),
        "ttDieuXe": ttDieuXe?.toJson(),
        "vienChucNgoaiTruong": vienChucNgoaiTruong,
        "donViVienChucNgoaiTruong": donViVienChucNgoaiTruong,
        "parentId": parentId,
        "laPhieuGhep": laPhieuGhep,
        "fileDinhKem": fileDinhKem,
        "ghiChu": ghiChu,
      };

  static List<GiayDieuXe> parse(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed.map<GiayDieuXe>((json) => GiayDieuXe.fromJson(json)).toList();
  }

  @override
  String toString() {
    return "$id, $donVi, $noiDung, $noiCongTac, $tuNgay, $denNgay, $thoiGianKhoiHanh, $thoiGianVe, $diaDiemKhoiHanh, $diaDiemVe, $ketQua, $trangThai, $nguoiTao, $hcDuyet, $donViDuyet, $bghDuyet, $ngayTao, $ngayHCDuyet, $ngayDonViDuyet, $ngayBGHDuyet, $dsDiCongTac, $hopDongThueXe, $ttDieuXe, $vienChucNgoaiTruong, $donViVienChucNgoaiTruong, $parentId, $laPhieuGhep, $fileDinhKem, $ghiChu, ";
  }
}

// class DonVi {
//   DonVi({
//     required this.id,
//     required this.tenDonVi,
//   });

//   final int? id;
//   final String? tenDonVi;
//   // Add this factory constructor in the DonVi class
//   factory DonVi.empty() {
//     return DonVi(
//       id: 0,
//       tenDonVi: '',
//     );
//   }

//   DonVi copyWith({
//     int? id,
//     String? tenDonVi,
//   }) {
//     return DonVi(
//       id: id ?? this.id,
//       tenDonVi: tenDonVi ?? this.tenDonVi,
//     );
//   }

//   factory DonVi.fromJson(Map<String, dynamic> json) {
//     return DonVi(
//       id: json["id"],
//       tenDonVi: json["tenDonVi"],
//     );
//   }

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "tenDonVi": tenDonVi,
//       };

//   @override
//   String toString() {
//     return "$id, $tenDonVi, ";
//   }
// }

class NguoiTao {
  NguoiTao({
    required this.id,
    required this.ho,
    required this.tenDem,
    required this.ten,
    required this.maVienChuc,
    required this.chucVu,
    required this.donVi,
    required this.hinhAnh,
  });

  final int? id;
  final String? ho;
  final String? tenDem;
  final String? ten;
  final String? maVienChuc;
  final String? chucVu;
  final String? donVi;
  final String? hinhAnh;

  NguoiTao copyWith({
    int? id,
    String? ho,
    String? tenDem,
    String? ten,
    String? maVienChuc,
    String? chucVu,
    String? donVi,
    String? hinhAnh,
  }) {
    return NguoiTao(
      id: id ?? this.id,
      ho: ho ?? this.ho,
      tenDem: tenDem ?? this.tenDem,
      ten: ten ?? this.ten,
      maVienChuc: maVienChuc ?? this.maVienChuc,
      chucVu: chucVu ?? this.chucVu,
      donVi: donVi ?? this.donVi,
      hinhAnh: hinhAnh ?? this.hinhAnh,
    );
  }

  factory NguoiTao.fromJson(Map<String, dynamic> json) {
    return NguoiTao(
      id: json["id"],
      ho: json["ho"],
      tenDem: json["tenDem"],
      ten: json["ten"],
      maVienChuc: json["maVienChuc"],
      chucVu: json["chucVu"],
      donVi: json["donVi"],
      hinhAnh: json["hinhAnh"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "ho": ho,
        "tenDem": tenDem,
        "ten": ten,
        "maVienChuc": maVienChuc,
        "chucVu": chucVu,
        "donVi": donVi,
        "hinhAnh": hinhAnh,
      };

  @override
  String toString() {
    return "$id, $ho, $tenDem, $ten, $maVienChuc, $chucVu, $donVi, $hinhAnh, ";
  }
}
