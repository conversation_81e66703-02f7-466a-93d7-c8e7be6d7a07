import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/tasky/Task.dart';
import 'package:tvumobile/app/data/models/tms/danhmuc.dart';
import 'package:tvumobile/app/data/models/tms/danhmuc_constants.dart';
import 'package:tvumobile/app/features/tasky/components/file_upload_widget.dart';
import 'package:tvumobile/app/utils/extensions/string_extentions.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:http/http.dart' as http;

class TaskyCreateController extends GetxController {
  ApiProvider apiProvider = Get.find();
  UserInfo? userInfo = LocalStorageServices.getUserInfo();
  final formKey = GlobalKey<FormBuilderState>();
  final DateFormat dateFormat = DateFormat('dd-MM-yyyy HH:mm');
  RxList<dynamic> teamMembers = [].obs;
  RxList<dynamic> assignees = [].obs;
  String findVCs = "";
  final dmloaiCongviec = <DanhMuc>[].obs;
  final dmMucdo = <DanhMuc>[].obs;
  final dmuutien = <DanhMuc>[].obs;
  final uploadWidgetKey = GlobalKey<FileUploadWidgetState>();
  final Logger _logger = Logger();
  bool isCheckLeader = false;
  bool isCheckMember = false;
  bool isCheckAll = false;
  List<PlatformFile> selectedFiles = [];

  bool isEditMode = false; // Biến xác định chế độ (tạo mới hay sửa)
  int? taskId; // ID của task khi sửa

  RxBool isLoading = true.obs;

// Thêm các thuộc tính cho RelatedDocumentsPicker
  final RxList<dynamic> availableDocuments = <dynamic>[].obs;
  final RxList<dynamic> selectedDocuments = <dynamic>[].obs;

  // Biến trạng thái để theo dõi việc chọn
  final hasSelectedFiles = false.obs; // Theo dõi FileUploadWidget
  final hasSelectedDocuments = false.obs; // Theo dõi RelatedDocumentsPicker

  Map<String, dynamic> formData = {
    'startEndDay': DateTimeRange(start: DateTime.now(), end: DateTime.now()),
    // 'ReminderTime': "5",
    // 'ReminderType': "phut",
    'TaskId': 0,
    // 'reminder': false,
    'MoTa': '',
    'TaskName': '',
    'NguoiGiaoViec': null,
    'NguoiTao': null,
    'NguoiGiamSat': null,
    'NguoiThucHien': [],
    'LoaiCongViec': null,
    'DoUuTien': null,
    'Mucdo': null,
    'TrangThai': null,
    'DocumentId': null
  };

  @override
  void onInit() {
    super.onInit();

    // getTeams();
    // getDanhMucs();
    _initializeData();
    // Tải danh sách văn bản ban đầu
    fetchInitialDocuments();
  }

  Future<void> _initializeData() async {
    try {
      isLoading.value = true;

      // await getTeams();
      // await getDanhMucs();

      // Chờ cả getTeams và getDanhMucs hoàn thành
      await Future.wait<void>([
        getTeams(),
        getDanhMucs(),
      ]);

      if (Get.arguments != null && Get.arguments['task'] != null) {
        isEditMode = true;
        Task task = Get.arguments['task'];
        taskId = task.id;
        loadTaskData(task);
      }
    } catch (e) {
      Get.snackbar('Lỗi', 'Không thể tải dữ liệu: $e');
      _logger.e('Error in _initializeData: $e');
    } finally {
      isLoading.value = false;
    }
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  // ------------------------ LẤY DANH SÁCH VIÊN CHỨC TRONG ĐƠN VỊ (KÈM THEO CHỨC VỤ) ------------------------
  Future<void> getTeams() async {
    var teamMembersTmp = await apiProvider.getTeamMembers(waiting: false);
    var tmp1 = teamMembersTmp
        .where((w) => w["tenChucVu"].toString().toLowerCase() != "viên chức")
        .toList();
    var tmp2 = teamMembersTmp
        .where((w) => w["tenChucVu"].toString().toLowerCase() == "viên chức")
        .toList();
    tmp1.sort((ele1, ele2) =>
        ele2["tenChucVu"].toString().compareTo(ele1["tenChucVu"].toString()));
    teamMembers.clear();
    teamMembers.addAll(tmp1);
    teamMembers.addAll(tmp2);
    formData['NguoiGiaoViec'] = teamMembers
        .where((ele) => ele["vienChucId"].toString() == userInfo!.vienchucid)
        .firstOrNull;
    Logger().i('getTeammembers: $teamMembers');
    //Logger().i('UserInfo: $userInfo');

    update();
  }

  // --------------------------------------- LẤY DANH SÁCH DANH MỤC ------------------------------------------
  Future<void> getDanhMucs() async {
    var teamMembersTmp =
        await apiProvider.getDanhMucs(MaDanhMuc.LoaiCongViec, waiting: false);
    if (teamMembersTmp.isEmpty) return;
    dmloaiCongviec.clear();
    dmloaiCongviec.addAll(teamMembersTmp);
    formData['LoaiCongViec'] =
        dmloaiCongviec.where((ele) => ele.id == 25).firstOrNull?.id;
    teamMembersTmp =
        await apiProvider.getDanhMucs(MaDanhMuc.MucDo, waiting: false);
    dmMucdo.clear();
    dmMucdo.addAll(teamMembersTmp);
    formData['Mucdo'] = dmMucdo.where((ele) => ele.id == 37).firstOrNull?.id;
    teamMembersTmp =
        await apiProvider.getDanhMucs(MaDanhMuc.DoUuTien, waiting: false);
    dmuutien.clear();
    dmuutien.addAll(teamMembersTmp);
    formData['DoUuTien'] =
        dmuutien.where((ele) => ele.id == 39).firstOrNull?.id;
    update();
  }

  // ------------------------------------- TÌM KIẾM NGƯỜI DÙNG -----------------------------------------------
  Future<Map<String, dynamic>> searchUsers(
      String query, int pageNumber, int pageSize) async {
    // Thay thế bằng logic gọi API thực tế
    //Logger().i(query);
    final congVanNhanData = await apiProvider.loadVienChucs(
        search: query, page: pageNumber, pageSize: pageSize);
    //Logger().i(congVanNhanData);
    return congVanNhanData;
  }

  // -------------------------------------- TÌM KIẾM TÀI LIỆU LIÊN QUAN --------------------------------------
  // Future<List<dynamic>> fetchDocuments(
  //     String search, int page, int pageSize) async {
  //   final documents = await apiProvider.fetchDocuments(
  //       searchQuery: search, page: page, pageSize: pageSize);
  //   //_logger.d(documents);
  //   print('documents: $documents');
  //   return documents;
  // }
  // Future<Map<String, dynamic>> fetchDocuments(
  //     String search, int page, int pageSize) async {
  //   try {
  //     final documents = await apiProvider.fetchDocuments(
  //         searchQuery: search, page: page, pageSize: pageSize);
  //     print('Documents fetched from API: $documents');
  //     // Kiểm tra nếu documents không phải là List
  //     if (documents is! List) {
  //       print('Error: API did not return a list. Received: $documents');
  //       return {
  //         'documents': [],
  //         'total': 0,
  //         'page': page,
  //         'pageSize': pageSize
  //       };
  //     }
  //     // Kiểm tra nếu documents rỗng
  //     if (documents.isEmpty) {
  //       print(
  //           'Warning: No documents found for search: $search, page: $page, pageSize: $pageSize');
  //     }
  //     // Bọc dữ liệu thành Map để phù hợp với RelatedDocumentsPicker
  //     final response = {
  //       'documents': documents,
  //       'total': documents.length,
  //       'page': page,
  //       'pageSize': pageSize,
  //     };
  //     return response;
  //   } catch (e) {
  //     print('Error in fetchDocuments: $e');
  //     return {'documents': [], 'total': 0, 'page': page, 'pageSize': pageSize};
  //   }
  // }

  Future<Map<String, dynamic>> fetchDocuments(
      String search, int page, int pageSize) async {
    try {
      final response = await apiProvider.fetchDocuments(
          searchQuery: search, page: page, pageSize: pageSize);
      print('Documents fetched from API: $response');

      // Kiểm tra nếu response không phải là Map hoặc không có 'results'
      if (response['results'] == null) {
        print(
            'Error: API did not return a valid response. Received: $response');
        return {
          'results': [],
          'totalPages': 0,
          'totalRecords': 0,
          'page': page,
          'pageSize': pageSize,
        };
      }

      // Kiểm tra nếu không có dữ liệu
      if (response['results'].isEmpty) {
        print(
            'Warning: No documents found for search: $search, page: $page, pageSize: $pageSize');
      }

      // Trả về dữ liệu với cấu trúc phù hợp
      return {
        'results': response['results'],
        'totalPages': response['totalPages'] ?? 0,
        'totalRecords': response['totalRecords'] ?? 0,
        'page': response['page'] ?? page,
        'pageSize': response['pageSize'] ?? pageSize,
      };
    } catch (e) {
      print('Error in fetchDocuments: $e');
      return {
        'results': [],
        'totalPages': 0,
        'totalRecords': 0,
        'page': page,
        'pageSize': pageSize,
      };
    }
  }

  // Tải danh sách văn bản ban đầu
  Future<void> fetchInitialDocuments() async {
    try {
      final result = await fetchDocuments('', 1, 10);
      print('Initial documents result: $result');
      if (result['results'] != null && result['results'].isNotEmpty) {
        availableDocuments.assignAll(result['results']);
        print("Initial documents loaded: ${availableDocuments.length}");
      } else {
        print("No initial documents loaded.");
      }
    } catch (e) {
      print("Error loading initial documents: $e");
    }
  }

  // ---------------------------- CẬP NHẬT DANH SÁCH NGƯỜI THỰC HIỆN CÔNG VIỆC -------------------------------
  void onAssigneesChanged(List<dynamic> selectedUsers) {
    assignees.clear();
    assignees.addAll(selectedUsers);
    update();
  }

  // ---------------------------------------------------------------------------------------------------------
  void setAssignees(List<dynamic> users) {
    if (users.isEmpty) return;
    assignees.clear();
    assignees.addAll(users);
    _logger.d(assignees);
    update();
  }

  // -------------------------------------------- TẠO CÔNG VIỆC ----------------------------------------------
  Future<void> taoMoiCongViec() async {
    try {
      // Kiểm tra và lấy dữ liệu từ form
      if (!formKey.currentState!.validate()) {
        Get.snackbar("Lỗi", "Vui lòng điền đầy đủ thông tin!");
        return;
      }
      formKey.currentState!.save();

      var formValue = Map<String, dynamic>.from(formKey.currentState!.value);

      // Kiểm tra và chuẩn bị dữ liệu
      if (formValue['NguoiGiaoViec'] == null) {
        Get.snackbar("Lỗi", "Vui lòng chọn người giao việc!");
        return;
      }
      if (formValue["startEndDay"] == null) {
        Get.snackbar("Lỗi", "Vui lòng chọn khoảng thời gian thực hiện!");
        return;
      }

      // Chuẩn bị dữ liệu để gửi lên API
      formValue['NguoiTao'] = teamMembers
          .where((ele) => ele["vienChucId"].toString() == userInfo!.vienchucid)
          // .where((ele) => ele["vienChucId"].toString() == "18279")
          .firstOrNull;

      formValue['NguoiGiaoViec'] = {
        "vienChucId": formValue['NguoiGiaoViec']['vienChucId'],
      };

      formValue['NguoiThucHien'] =
          assignees.map((e) => {"vienChucId": e["vienChucId"]}).toList();

      formValue['StartDate'] = formValue["startEndDay"].start.toString();
      formValue['EndDate'] = formValue["startEndDay"].end.toString();

      formValue['startEndDay'] = null;

      formValue['TrangThai'] = {"id": 30, "loaiDanhMucId": 5};

      // Thêm RelatedDocuments từ formData (giờ là Map hoặc null)
      formValue['DocumentId'] = formData['DocumentId'] ?? 0;

      // Lấy danh sách file từ upload widget
      final uploadState = uploadWidgetKey.currentState;
      List<PlatformFile>? selectedFiles = uploadState?.selectedFiles;

      // Gọi API thông qua ApiProvider
      await apiProvider.createTask(
        formValue: formValue,
        selectedFiles: selectedFiles,
      );

      formKey.currentState?.reset();
      uploadState?.clearFiles();

      // Xử lý sau khi tạo công việc thành công
      Get.back();
      Get.snackbar(
        "Thông báo",
        "Tạo công việc thành công!",
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar("Lỗi", "Tạo công việc thất bại: $e");
    }
  }

  // -------------------------------------------- SỬA CÔNG VIỆC ----------------------------------------------
  Future<void> updateTask() async {
    try {
      // Kiểm tra và lấy dữ liệu từ form
      if (!formKey.currentState!.validate()) {
        Get.snackbar("Lỗi", "Vui lòng điền đầy đủ thông tin!");
        return;
      }
      formKey.currentState!.save();

      var formValue = Map<String, dynamic>.from(formKey.currentState!.value);

      // Chuẩn bị dữ liệu để gửi lên API
      formValue['TaskId'] = taskId; // Gửi taskId để xác định công việc cần sửa

      // Gán NguoiGiaoViec từ form hoặc formData
      if (formValue['NguoiGiaoViec'] != null) {
        formValue['NguoiGiaoViec'] = {
          "vienChucId": formValue['NguoiGiaoViec']['vienChucId'],
        };
      } else if (formData['NguoiGiaoViec'] != null) {
        formValue['NguoiGiaoViec'] = {
          "vienChucId": formData['NguoiGiaoViec']['vienChucId'],
        };
      }

      formValue['NguoiTao'] = teamMembers
          .where((ele) => ele["vienChucId"].toString() == userInfo!.vienchucid)
          // .where((ele) => ele["vienChucId"].toString() == "18279")
          .firstOrNull;

      formValue['NguoiThucHien'] =
          assignees.map((e) => {"vienChucId": e["vienChucId"]}).toList();

      formValue['StartDate'] = formValue["startEndDay"].start.toString();
      formValue['EndDate'] = formValue["startEndDay"].end.toString();
      formValue['startEndDay'] = null;

      // Giữ nguyên trạng thái từ formData (được tải từ loadTaskData)
      formValue['TrangThai'] = formData['TrangThai'] ?? {"id": 30};

      // Thêm DocumentId từ formData (giờ là Map hoặc null)
      // formValue['DocumentId'] = formData['DocumentId'];

      // Gọi API thông qua ApiProvider để cập nhật task
      await apiProvider.updateTask(
        // taskId: taskId!,
        formValue: formValue,
      );

      // Xử lý sau khi cập nhật công việc thành công
      Get.back();
      Get.snackbar(
        "Thông báo",
        "Cập nhật công việc thành công!",
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      formKey.currentState!.reset();
    } catch (e) {
      Get.snackbar("Lỗi", "Cập nhật công việc thất bại: $e");
      _logger.e('Error in updateTask: $e');
    }
  }

  // --------------------------------- TẢI DỮ LIỆU TASK KHI Ở CHẾ ĐỘ SỬA -------------------------------------
  void loadTaskData(Task task) {
    try {
      _logger.i('Loading task data: ${task.toJson()}');
      formData['TaskName'] = task.taskName ?? '';
      formData['MoTa'] = task.moTa ?? '';
      formData['LoaiCongViec'] =
          task.taskType != null && task.taskType!.maDanhMuc != null
              ? dmloaiCongviec
                  .firstWhere(
                    (dm) => dm.maDanhMuc == task.taskType!.maDanhMuc,
                    orElse: () => DanhMuc(
                        id: null,
                        tenDanhMuc: '',
                        maDanhMuc: '',
                        loaiDanhMucId: null),
                  )
                  .id
              : null;
      formData['startEndDay'] =
          task.startDay != null && task.endDateDuKien != null
              ? DateTimeRange(
                  start: task.startDay!,
                  end: task.endDateDuKien!,
                )
              : DateTimeRange(start: DateTime.now(), end: DateTime.now());
      formData['NguoiGiaoViec'] = task.nguoiGiaoViec != null
          ? teamMembers.firstWhere(
              (member) => member['vienChucId'] == task.nguoiGiaoViec!.id,
              orElse: () => null,
            )
          : null;
      formData['NguoiGiamSat'] = task.nguoiGiamSat != null
          ? teamMembers.firstWhere(
              (member) => member['vienChucId'] == task.nguoiGiamSat!.id,
              orElse: () => null,
            )
          : null;
      formData['DoUuTien'] =
          task.priority != null && task.priority!.maDanhMuc != null
              ? dmuutien
                  .firstWhere(
                    (dm) => dm.maDanhMuc == task.priority!.maDanhMuc,
                    orElse: () => DanhMuc(
                        id: null,
                        tenDanhMuc: '',
                        maDanhMuc: '',
                        loaiDanhMucId: null),
                  )
                  .id
              : null;
      formData['Mucdo'] = task.tag != null && task.tag!.maDanhMuc != null
          ? dmMucdo
              .firstWhere(
                (dm) => dm.maDanhMuc == task.tag!.maDanhMuc,
                orElse: () => DanhMuc(
                    id: null,
                    tenDanhMuc: '',
                    maDanhMuc: '',
                    loaiDanhMucId: null),
              )
              .id
          : null;
      formData['TrangThai'] = task.trangThai;

      // Tải văn bản liên quan
      // if (task.taskResource != null && task.taskResource!.contains('id')) {
      //   selectedDocuments.value = task.taskResource!;
      //   formData['DocumentId'] = task.taskResource!['id'.toInt()] ??
      //       0; // Gán trực tiếp id dưới dạng số nguyên
      // } else {
      //   selectedDocuments.value = [];
      //   formData['DocumentId'] = 0;
      // }

      if (task.danhSachNguoiThucHien != null) {
        assignees.clear();
        assignees.addAll(task.danhSachNguoiThucHien!.map((user) {
          return teamMembers.firstWhere(
            (member) => member['vienChucId'] == user.id,
            orElse: () => {
              'vienChucId': user.id,
              'ho': user.ho,
              'tenDem': user.tenDem,
              'ten': user.ten,
              'hinhAnh': user.hinhAnh,
            },
          );
        }).toList());
      }

      update();
    } catch (e) {
      _logger.e('Error in loadTaskData: $e');
      Get.snackbar('Lỗi', 'Không thể tải dữ liệu task: $e');
    }
  }
}
