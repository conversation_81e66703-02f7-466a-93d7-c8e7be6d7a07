import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/config/themes/colors.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/giaydieuxe.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_home_controller.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

// ------------- HIỂN THỊ DANH SÁCH PHIẾU ĐIỀU XE ----------------
DieuXeList(
    BuildContext context, GiayDieuXe dieuxe, DieuXeHomeController controller) {
  DateFormat format = DateFormat("dd/MM/yyyy");
  return MyContainer.none(
    padding: const EdgeInsets.fromLTRB(12, 0, 12, 0),
    margin: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
    border: Border.all(color: Colors.grey[300]!),
    borderRadius: BorderRadius.circular(4),
    onTap: () {
      Get.toNamed(
        Routes.DIEUXE_DETAIL,
        parameters: {
          'pdxId': dieuxe.id.toString(),
          'phieughep': dieuxe.laPhieuGhep.toString(),
          'trangthai': dieuxe.trangThai.toString(),
          'currentview': controller.currentView.value.toString(),
        },
      );
    },
    child: GestureDetector(
      onTap: controller.selectMode.value
          ? () {
              // Trong chế độ chọn, tap để chọn/hủy chọn
              if (dieuxe.laPhieuGhep != true) {
                controller.toggleSelection(dieuxe.id.toString());
              }
            }
          : () {
              // Chuyển đến màn hình chi tiết khi không ở chế độ chọn
              Get.toNamed(
                Routes.DIEUXE_DETAIL,
                parameters: {
                  'pdxId': dieuxe.id.toString(),
                  'phieughep': dieuxe.laPhieuGhep.toString(),
                  'trangthai': dieuxe.trangThai.toString(),
                  'currentview': controller.currentView.value.toString(),
                },
              );
            },
      onLongPress: () {
        // Xử lý long press với thời gian giữ 5 giây
        if (controller.currentView.value == "hcthduyet")
          controller.toggleSelection(dieuxe.id.toString());
      },
      child: Obx(
        () => Container(
          padding: EdgeInsets.fromLTRB(8, 8, 8, 8),
          decoration: BoxDecoration(
            color: controller.selectedDieuXe[dieuxe.id.toString()] ?? false
                ? Colors.blue.withOpacity(0.2) // Màu nền khi được chọn
                : Colors.white,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: dieuxe.trangThai != null
                  ? getStatusColors(dieuxe.trangThai!).withOpacity(0.2)
                  : Colors.grey.withOpacity(0.2),
              width: 2,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Checkbox để hiển thị trạng thái chọn
              if (controller.selectMode.value &&
                  dieuxe.laPhieuGhep != true) ...[
                Checkbox(
                  value:
                      controller.selectedDieuXe[dieuxe.id.toString()] ?? false,
                  onChanged: dieuxe.laPhieuGhep == true
                      ? null // Vô hiệu hóa checkbox cho phiếu ghép
                      : (value) {
                          controller.toggleSelection(dieuxe.id.toString());
                        },
                ),
                SizedBox(width: 8),
              ],
              Column(
                children: [
                  if (!controller.selectMode.value)
                    Container(
                      padding: EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: getStatusColors(dieuxe.trangThai!),
                      ),
                      child: Icon(
                        LucideIcons.car,
                        size: 40,
                        color: Colors.white,
                        semanticLabel: "Status",
                      ),
                    ),
                  // SizedBox(height: 4),
                  // Container(
                  //   constraints: BoxConstraints(maxWidth: 70),
                  //   padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  //   decoration: BoxDecoration(
                  //     // color: getStatusColors(dieuxe.trangThai!).withOpacity(0.1),
                  //     borderRadius: BorderRadius.circular(4),
                  //   ),
                  //   child: MyText.labelMedium(
                  //     dieuxe.trangThai!,
                  //     // color:
                  //     //     getStatusColors(dieuxe.trangThai!).computeLuminance() > 0.2
                  //     //         ? Colors.black
                  //     //         : dieuxe.trangThai!.maDanhMuc == "Assigned"
                  //     //             ? Colors.black
                  //     //             : Colors.white,
                  //     maxLines: 3,
                  //     textAlign: TextAlign.center,
                  //     softWrap: true,
                  //   ),
                  // ),
                ],
              ),
              SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: MyText.titleMedium(
                            dieuxe.donVi == null
                                ? "Phiếu ghép"
                                : dieuxe.donVi!.tenDonVi!,
                            overflow: TextOverflow.ellipsis,
                            fontWeight: 700,
                          ),
                        ),
                        // Container(
                        //   padding: EdgeInsets.all(4),
                        //   decoration: BoxDecoration(
                        //     // color: _getDueDateColor(
                        //     //     dieuxe.endDateDuKien!, dieuxe.trangThai!.maDanhMuc),
                        //     borderRadius: BorderRadius.circular(4),
                        //   ),
                        //   child: MyText.bodyMedium(
                        //     format.format(dieuxe.ngayTao!),
                        //     color: Colors.white,
                        //   ),
                        // ),
                      ],
                    ),
                    // SizedBox(height: 4),
                    // MyText.bodyMedium(
                    //   "Nơi công tác: ${dieuxe.noiCongTac.toString()}",
                    //   maxLines: 1,
                    //   overflow: TextOverflow.ellipsis,
                    //   color: Colors.grey[700],
                    // ),
                    MyText.bodyMedium(
                      "Nội dung: ${dieuxe.noiDung.toString()}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      color: Colors.grey[700],
                    ),
                    MyText.bodyMedium(
                      "Thời gian: ${format.format(dieuxe.tuNgay!)} - ${format.format(dieuxe.denNgay!)}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      color: Colors.grey[700],
                    ),
                    // SizedBox(height: 8),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

getStatusColors(String status) {
  switch (status) {
    case "Đơn vị đã duyệt":
      return getColorFromHex("#64D9FF"); // Xanh nhạt
    case "Phòng HC-TC đã duyệt":
      return getColorFromHex("#00E200"); // Xanh lá
    case "Đơn vị yêu cầu điều chỉnh":
    case "PHCTH yêu cầu điều chỉnh":
      return getColorFromHex("#FFAF3D"); // Cam
    case "Chờ duyệt":
      return getColorFromHex("#FAD800").withOpacity(1.0); // Vàng
    case "Đã hủy":
    case "Không duyệt":
      return getColorFromHex("#FF2A04"); // Đỏ
    default:
      return getColorFromHex("#7B8089"); // Xám
  }
}
