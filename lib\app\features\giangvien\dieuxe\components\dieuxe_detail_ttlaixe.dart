import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_detail_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/date_time_extension.dart';

Widget ThongTinLaiXeTab(
    BuildContext context, DieuXeDetailController controller) {
  final theme = Theme.of(context);
  return ListView(
    shrinkWrap: true,
    children: [
      Padding(
        padding: EdgeInsets.only(top: 5),
        child: Column(
          children: [
            // -------------------------- SỐ LỆNH ĐIỀU XE --------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.hash,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Số lệnh điều xe:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.ttDieuXe?.soLenhDieuXe ?? "",
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            // ---------------------------- NGÀY DUYỆT -----------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.calendarCheck,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Ngày duyệt:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.ttDieuXe?.ngayThang!
                              .toFormat(format: "HH:mm dd/MM/yyyy") ??
                          '-',
                      color: Colors.black87,
                      maxLines: 3,
                      softWrap: true,
                    ),
                  ),
                ],
              ),
            ),

            // ------------------------ NHÂN VIÊN LÁI XE ---------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.userRound,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " NV lái xe:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      "${controller.pdx.ttDieuXe?.taiXe?.ho} ${controller.pdx.ttDieuXe?.taiXe?.tenDem} ${controller.pdx.ttDieuXe?.taiXe?.ten}",
                      color: Colors.black87,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            // -------------------------- BIỂN KIỂM SOÁT ---------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.squareAsterisk,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Biển kiểm soát:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.ttDieuXe?.xe?.bienKiemSoat ?? "",
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            // ----------------------------- NGÀY ĐI -------------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.calendarArrowUp,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Ngày đi:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.ttDieuXe?.ngayDi!
                              .toFormat(format: "HH:mm dd/MM/yyyy") ??
                          '-',
                      color: Colors.black87,
                      maxLines: 3,
                      softWrap: true,
                    ),
                  ),
                ],
              ),
            ),

            // ----------------------------- NGÀY VỀ -------------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.calendarArrowDown,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Ngày về:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.ttDieuXe?.ngayVe!
                              .toFormat(format: "HH:mm dd/MM/yyyy") ??
                          '-',
                      color: Colors.black87,
                      maxLines: 3,
                      softWrap: true,
                    ),
                  ),
                ],
              ),
            ),

            // -------------------------- CÁN BỘ ĐI CÙNG ---------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.usersRound,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Cán bộ đi cùng:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.ttDieuXe?.canBoDiCung ?? "",
                      color: Colors.black87,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            // -------------------------- SỐ KM DỰ KIẾN ----------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.binary,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Số Km dự kiến:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.ttDieuXe?.soKmDuKien ?? "",
                      color: Colors.black87,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      )
    ],
  );
}
