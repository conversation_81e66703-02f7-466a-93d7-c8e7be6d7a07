import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';
import 'package:tvumobile/app/features/giangvien/chamcong/chamcong_controller.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/date_time_extension.dart';

class ChamCongView extends GetView<ChamCongController> {
  const ChamCongView({super.key});

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        // ignore: deprecated_member_use
        backgroundColor: theme.primaryColor.withOpacity(0.95),
        title: MyText.titleMedium(
          "CHẤM CÔNG".tr.toUpperCase(),
          color: theme.colorScheme.onPrimary,
        ),
        //leading: MyButton.small(onPressed: (){Get.back();}, backgroundColor: Colors.transparent, child: Icon(LucideIcons.arrowLeft, color: theme.colorScheme.onPrimary,)),
        centerTitle: true,
        foregroundColor: theme.colorScheme.onPrimary,
        iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(8, 6, 8, 24),
        child: Column(
          children: [
            MyContainer(
              height: 250,
              padding: const EdgeInsets.all(4),
              child: Card(
                elevation: 2,
                child: Column(
                  children: [
                    MyText.titleLarge("Hôm nay bạn thế nào?"),
                    MySpacing.height(16),
                    MyButton.block(
                        onPressed: () {},
                        child: MyText.titleMedium(
                          "OK",
                          color: theme.colorScheme.onPrimary,
                        )),
                  ],
                ),
              ),
            ),
            MySpacing.height(16),
            Expanded(
              child: SfCalendar(
                controller: controller.calendarController,
                view: CalendarView.month,
                showNavigationArrow: true,
                showDatePickerButton: true,
                monthViewSettings: const MonthViewSettings(
                    appointmentDisplayMode:
                        MonthAppointmentDisplayMode.appointment,
                    numberOfWeeksInView: 4,
                    agendaViewHeight: -1,
                    showAgenda: true),
                monthCellBuilder: (context, details) {
                  return Center(
                    child: Column(
                      children: [
                        Text(
                          details.date.day.toString(),
                          style: details.date.isToday
                              ? theme.textTheme.labelLarge
                                  ?.copyWith(fontWeight: FontWeight.bold)
                              : theme.textTheme.labelLarge,
                        ),
                        const Chip(
                          label: Text("+"),
                          labelPadding: EdgeInsets.zero,
                          padding: EdgeInsets.all(1),
                        )
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
