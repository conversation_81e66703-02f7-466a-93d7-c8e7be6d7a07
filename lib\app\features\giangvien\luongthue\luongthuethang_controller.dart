import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tvumobile/app/data/models/tms/luongthue/luongthanginfo.dart';
import 'package:tvumobile/app/data/models/tms/luongthue/thuenaminfo.dart';
import 'package:tvumobile/app/data/models/tms/luongthue/thuethanginfo.dart';
import 'package:tvumobile/app/services/api_provider.dart';

class LuongThueController extends GetxController {
  bool uiLoading = true;
  ApiProvider apiProvider = Get.find();
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final appbarTitle = "Lương tháng".obs;
  final currentYear = DateTime.now().year.obs;
  final maxYear = DateTime.now().year.obs;
  final maxMonth = DateTime.now().month;
  final currentMonth = DateTime.now().month.obs;
  final luongThangInfo = <LuongThangInfo>[].obs;
  final thueThangInfo = <ThueThangInfo>[].obs;
  final thueNamInfo = <ThueNamInfo>[].obs;
  ScrollController monthScroll = ScrollController();
  final viFormat = NumberFormat.currency(locale: "vi");
  final currentView = "".obs;
  final currentDataAvaliable = true.obs;
  final thueNam = (DateTime.now().year - 1).obs;

  changeView(data) {
    currentDataAvaliable.value = true;
    luongThangInfo.clear();
    thueThangInfo.clear();
    thueNamInfo.clear();
    switch (currentView.value) {
      case "TO":
        maxYear.value = DateTime.now().year;
        appbarTitle.value = "Thông tin Lương";
        scrolltoFocus();
        getluongThang();
        break;
      case "TT":
        maxYear.value = DateTime.now().year;
        appbarTitle.value = "Thông tin Thuế (tháng)";
        scrolltoFocus();
        getthueThang();
        break;
      case "TN":
        maxYear.value = DateTime.now().year - 1;
        if (thueNam > maxYear.value) thueNam.value--;
        appbarTitle.value = "Thông tin Thuế (năm)";
        //scrolltoFocus();
        getThueNam();
        break;
    }
    update();
  }

  @override
  Future<void> onReady() async {
    currentView.listen(changeView);
    currentView.value = "TO";
  }

  @override
  void onClose() {}

  void openDrawer() {
    scaffoldKey.currentState?.openDrawer();
  }

  void closeDrawer() {
    if (scaffoldKey.currentState!.isEndDrawerOpen) {
      scaffoldKey.currentState!.closeEndDrawer();
    } else {
      scaffoldKey.currentState!.closeDrawer();
    }
  }

  void drawerClick(String view) {
    currentView.value = view;
    closeDrawer();
    //changeTitle();
    update();
  }

  Future<void> getluongThang() async {
    var tmp = await apiProvider.getLuongthang(
        month: currentMonth.value, year: currentYear.value);
    if (tmp != null) {
      currentDataAvaliable.value = true;
      luongThangInfo.add(tmp);
      update();
    } else {
      if (kDebugMode) {
        print("getluongThang");
      }
      currentDataAvaliable.value = false;
      update();
    }
  }

  Future<void> getThueNam() async {
    var tmp = await apiProvider.getThuenam(year: thueNam.value);
    if (tmp != null) {
      currentDataAvaliable.value = true;
      thueNamInfo.add(tmp);
      update();
    } else {
      if (kDebugMode) {
        print("getThueNam");
      }
      currentDataAvaliable.value = false;
      update();
    }
  }

  Future<void> getthueThang() async {
    var tmp = await apiProvider.getThuethang(
        month: currentMonth.value, year: currentYear.value);
    if (tmp != null) {
      currentDataAvaliable.value = true;
      thueThangInfo.add(tmp);
      update();
    } else {
      if (kDebugMode) {
        print("getthueThang");
      }
      currentDataAvaliable.value = false;
      update();
    }
  }

  scrolltoFocus() {
    //if(currentMonth.value <= 5) return;
    double ti = 1.0;
    ti = currentMonth.value == 5
        ? ((4) * currentMonth.value) * 1.0
        : ((16 + 16 + 6) * currentMonth.value) * 1.0;
    if (currentMonth.value <= 4) ti = 1.0;
    //print(ti);
    monthScroll.animateTo(ti,
        duration: Duration(milliseconds: 200), curve: Curves.linear);
  }

  void currentYearPlus() {
    if (currentView.value == "TN") {
      if (thueNam.value >= maxYear.value) return;
      thueNam.value += 1;
      changeView(null);
      return;
    }
    if (currentYear.value >= maxYear.value) return;
    currentYear.value += 1;
    changeView(null);
  }

  void currentYearMinus() {
    if (currentView.value == "TN") {
      if (thueNam.value <= 2006) return;
      thueNam.value -= 1;
      changeView(null);
      return;
    }
    if (currentYear.value <= 2006) return;
    currentYear.value -= 1;
    changeView(null);
  }

  void onMonthTap(int m) {
    if (currentYear.value == maxYear.value) {
      if (m > maxMonth) {
        return;
      }
      currentMonth.value = m;
    } else {
      currentMonth.value = m;
    }
    changeView(null);
    //scrolltoFocus();
    //getluongThang();
  }
}
