import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/features/kyso/kyso_controller.dart';
import 'package:tvumobile/app/features/kyso/home_screen.dart';
import 'package:tvumobile/app/features/kyso/waiting_list_screen.dart';
import 'package:tvumobile/app/features/kyso/signed_list_screen.dart';
import 'package:tvumobile/app/features/kyso/profile_screen.dart';

class KysoScreen extends StatelessWidget {
  KysoScreen({super.key});
  final kysoController = Get.put(KysoController());

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Scaffold(
      body: Obx(() => IndexedStack(
            index: kysoController.currentIndex.value,
            children: [
              HomeScreen(),
              WaitingListScreen(),
              SignedListScreen(),
              ProfileScreen(),
            ],
          )),
      bottomNavigationBar: Obx(() => BottomNavigationBar(
            backgroundColor: theme.colorScheme.background,
            selectedItemColor: theme.colorScheme.primary,
            unselectedItemColor: theme.colorScheme.onSurface.withOpacity(0.5),
            currentIndex: kysoController.currentIndex.value,
            onTap: kysoController.changeTabIndex,
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home),
                label: 'Trang chủ',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.list_alt),
                label: 'Chờ ký',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.check_circle),
                label: 'Đã ký',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person),
                label: 'Cá nhân',
              ),
            ],
          )),
    );
  }
}
