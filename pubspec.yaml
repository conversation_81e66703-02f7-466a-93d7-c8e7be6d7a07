name: tvumobile
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.10.0-96.0.dev

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  app_links: ^6.4.1
  get: ^4.7.2
  logger: ^2.6.1
  firebase_core: ^4.0.0
  syncfusion_localizations: ^30.2.6
  firebase_crashlytics: ^5.0.0
  flutter_localizations:
    sdk: flutter
  get_storage: ^2.1.1
  shorebird_code_push: ^2.0.4
  shimmer: ^3.0.0
  lucide_icons: ^0.257.0
  device_info_plus: ^11.5.0
  lottie: ^3.3.1
  firebase_messaging: ^16.0.0
  flutter_local_notifications: ^19.4.0
  provider: ^6.1.5+1
  shared_preferences: ^2.5.3
  image_picker: ^1.2.0
  image_cropper: ^9.1.0
  flutter_svg: ^2.0.13
  flutter_custom_dialog: ^1.3.0
  bot_toast: ^4.1.3
  lucide_icons_flutter: ^3.0.9
  syncfusion_flutter_pdfviewer: ^30.2.6
  syncfusion_flutter_pdf: ^30.2.6
  skeletons: ^0.0.3
  lazy_load_scrollview: ^1.3.0
  url_launcher: ^6.3.2
  intl: ^0.20.2
  image: ^4.5.4
  dropdown_search: ^6.0.2
  flutter_form_builder: ^10.1.0
  file_picker: ^10.3.1
  dotted_border: ^3.1.0
  google_fonts: ^6.3.0
  avatar_glow: ^3.0.1
  flutter_html: ^3.0.0
  cached_network_image: ^3.4.1
  font_awesome_flutter: ^10.10.0
  flutter_carousel_widget: ^3.1.0
  flutter_widget_from_html: ^0.17.0
  share_plus: ^11.1.0
  http: ^1.5.0
  connectivity_plus: ^6.1.5
  corsac_jwt: ^2.0.2
  path_provider: ^2.1.5
  modal_bottom_sheet: ^3.0.0
  collection: ^1.19.1
  form_builder_validators: ^11.2.0
  flutter_toggle_tab: ^1.5.1
  circular_profile_avatar: ^2.0.5
  percent_indicator: ^4.2.5
  sticky_headers: ^0.3.0+2
  syncfusion_flutter_calendar: ^30.2.6
  flutter_timetable: ^1.1.6
  eva_icons_flutter: ^3.1.0
  stylish_bottom_bar: ^1.1.1
  flutter_web_auth_2: ^4.1.0
  crypto: ^3.0.6
  pdf_render: ^1.4.12
  signature: ^5.5.0
  permission_handler: ^12.0.1
  tiengviet: ^1.0.0
  material_design_icons_flutter: ^7.0.7296
  table_calendar: ^3.2.0
  equatable: ^2.0.7
  qr_flutter: ^4.1.0
  mobile_scanner: ^7.0.1
  qr_scanner_overlay: ^0.0.2
  flutter_rating_bar: ^4.0.1
  flutter_screenutil: ^5.9.3
  hive_flutter: ^1.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
