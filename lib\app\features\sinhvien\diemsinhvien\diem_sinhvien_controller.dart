import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/data/models/edusoft/diem_sinhvien.dart';
import 'package:tvumobile/app/services/api_provider.dart';

class DiemSinhVienController extends GetxController {
  bool uiLoading = true;
  final diemSvLists = <DsDiemHocky>[].obs;
  ApiProvider apiProvider = Get.find();

  late DateTime minDate, maxDate;

  @override
  Future<void> onInit() async {
    super.onInit();
  }

  @override
  Future<void> onReady() async {
    await getTKBFunctions();
  }

  getTKBFunctions() async {
    // ignore: no_leading_underscores_for_local_identifiers
    var _diemSvLists = await apiProvider.loadDiemSinhVien();
    if (kDebugMode) {
      print(_diemSvLists!);
    }
    if (_diemSvLists != null && _diemSvLists.isNotEmpty) {
      diemSvLists.addAll(_diemSvLists);
      uiLoading = false;
      update();
    } else {
      diemSvLists.value = [];
      update();
    }
  }

  void goBack() {
    Get.back();
    // Navigator.pop(context);
  }
}
