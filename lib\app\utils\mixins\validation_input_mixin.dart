part of 'app_mixins.dart';

/// use this mixin for all form field
mixin ValidatorMixin {
  // example :
  // String? validateTextFieldIsRequired(String? value) {
  //   if (value == null || value.trim().isEmpty) return "this field is required";
  //   return null;
  // }

  // String? validateDropdownIsRequired(String? value) {
  //   if (value == null || value.trim().isEmpty) return "please select item";
  //   return null;
  // }
}
