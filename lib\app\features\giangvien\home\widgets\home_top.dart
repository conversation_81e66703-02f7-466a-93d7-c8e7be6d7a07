import 'package:flutter/material.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/data/models/sch/tkb/tkb_tuan_giangvien.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/tkb_helper.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import '../gv_home_controller.dart';
import 'header.dart';

class HeaderTopComponent extends StatelessWidget {
  const HeaderTopComponent(this.controller, {super.key});
  final GvHomeController controller;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    var width = Get.width;
    width = (width - 50);
    return Stack(
      children: [
        Header(controller),
        Positioned(
          top: 120,
          left: 25,
          child: Container(
            width: width,
            alignment: Alignment.center,
            //decoration: boxDecorationRoundedWithShadow(12, backgroundColor: theme.cardColor, blurRadius: 0.0, spreadRadius: 0.5, shadowColor: Colors.grey[350]),
            child: Card(
              child:  Obx(() {
                //if(controller.tkbs.isEmpty) {
                //  return schedulingCardEmpty(width);
                //}
                return SizedBox(width: width, child: buildCarousel(theme));

              }),
            )
          ),
        ),
      ],
    );
  }

  Widget schedulingCard(double width, ThemeData theme, TkbTuanGiangVien tkb) {
    return SizedBox(
      width: width+24,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Image.asset(
            'assets/vectors/icons8-task-100.png',
            width: 70,
            height: 70,
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MySpacing.height(4),
              MyText.bodyMedium(
                "(${tkb.soTiet.toString()} tiết)${tkb.tenMon}",
                fontWeight: 900,
                overflow: TextOverflow.ellipsis,
              ),
              MyText.bodyMedium(
                "Lớp: ${tkb.maLop} | Nhóm: ${tkb.nhomHoc}${tkb.nhomTH.toString().trim().isEmpty ? "" : " | Tổ: ${tkb.nhomTH}"}",
                maxLines: 2,
              ),
              MyText.bodyMedium("Thời gian: ${dateToWeekday(tkb.batDau!)} - ${DateFormat("dd/MM/yyyy").format(tkb.batDau!)}"),
              MyText.bodyMedium("Bắt đầu từ: ${DateFormat("HH:mm").format(tkb.batDau!)} - ${DateFormat("HH:mm").format(tkb.ketThuc!)}",
                  color: mlPrimaryColor),

            ],
          ),
          MySpacing.width(8),
          GestureDetector(
            onTap: () {
              controller.onTapFunctions("tkb");
            },
            child: Icon(
              LucideIcons.chevronRight,
              size: 32,
              color: theme.primaryColor,
            ),
          )
        ],
      ),
    );
  }

  Widget schedulingCardEmpty(double width) {
    var theme = LocalStorageServices.getThemeIsLight() ? AppTheme.light : AppTheme.dark;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Image.asset(
          'assets/vectors/icons8-task-100.png',
          width: 100,
          height: 100,
        ),
        SizedBox(
          height: 100,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Spacer(),
              MyText.labelSmall(
                "Lịch trình hôm nay: ${DateFormat("dd/MM/yyyy").format(DateTime.now())}",
                muted: true,
              ),

              MyText.labelMedium(
                "Bạn chưa có lịch trình 😆",
              ),
              Spacer(),
            ],
          ),
        ),
        Spacer(),
        GestureDetector(
          onTap: () {
            controller.onTapFunctions("tkb");
          },
          child: Icon(
            LucideIcons.chevronRight,
            size: 32,
            color: theme.primaryColor,
          ),
        )
      ],
    );
  }

  // ignore: unused_element
  Widget _indicator(bool isActive) {
    var theme = Theme.of(Get.context!);
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInToLinear,
      margin: MySpacing.symmetric(horizontal: 4.0),
      height: 8.0,
      width: 8,
      decoration: BoxDecoration(
        color: isActive
            ? theme.colorScheme.primary
            : theme.colorScheme.primary.withAlpha(120),
        borderRadius: BorderRadius.all(Radius.circular(4)),
      ),
    );
  }

  Widget buildCarousel(ThemeData theme) {
    List<TkbTuanGiangVien> tkbs = controller.tkbs;

    List<Widget> pageList = [];

    /*pageList.add(AttendentCard(
      onPressedCheck: () {},
      height: 80, elevation: 0,
    ));*/
    var width = Get.width;
    if(tkbs.isEmpty) {
      pageList.add(schedulingCardEmpty(width));
    } else {
      for (int i = 0; i < tkbs.length; i++) {
        pageList.add(schedulingCard(width, theme, tkbs[i]));
      }
    }
    return FlutterCarousel(
      options: FlutterCarouselOptions(
        viewportFraction: 1.0,
        height: 95,
        autoPlay: true,
        autoPlayInterval: Duration(seconds: 5),
        slideIndicator: CircularSlideIndicator(
            slideIndicatorOptions: SlideIndicatorOptions(
                itemSpacing: 12,
                indicatorBackgroundColor: Colors.grey[100]!,
                indicatorBorderWidth: 1,
                indicatorRadius: 3,
                indicatorBorderColor: theme.colorScheme.primary.withOpacity(0.6)
            )
        ),
        indicatorMargin: 0,
      ),
      items: pageList.map((i) {
        return i;
      }).toList(),
    );
    // return FlutterCarousel(
    //   options: FlutterCarouselOptions(
    //     height: 95,
    //     autoPlay: false,
    //     //autoPlayInterval: const Duration(seconds: 5),
    //     showIndicator: true,
    //     viewportFraction: 1,
    //     slideIndicator: CircularSlideIndicator(
    //         slideIndicatorOptions: SlideIndicatorOptions(
    //             itemSpacing: 12,
    //             indicatorBackgroundColor: theme.colorScheme.primary,
    //             indicatorBorderWidth: 1,
    //             indicatorRadius: 5,
    //             indicatorBorderColor: theme.colorScheme.secondary
    //         )
    //       ),
    //     indicatorMargin: 0,
    //   ),
    //   items: pageList,
    // );
    // return Column(
    //   children: [
    //     Container(
    //       padding: MySpacing.bottom(8),
    //       child: Row(
    //         mainAxisAlignment: MainAxisAlignment.center,
    //         children: _buildPageIndicatorStatic(),
    //       ),
    //     ),
    //     SizedBox(
    //       width: 400,
    //       height: 85,
    //       child: PageView(
    //           pageSnapping: true,
    //           physics: ClampingScrollPhysics(),
    //           controller: _pageController,
    //           onPageChanged: (int page) {
    //             _currentPage = page;
    //           },
    //           children: pageList
    //       ),
    //     ),
    //   ],
    // );


  }

}
