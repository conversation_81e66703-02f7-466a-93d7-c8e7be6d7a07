import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxeinfo.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxetaixe.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/giaydieuxe.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_divider.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_detail_controller.dart';
import 'package:tvumobile/app/shared_components/input_decoration_theme.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class HctcDuyetWidget extends GetView<DieuXeDetailController> {
  const HctcDuyetWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final width = MediaQuery.of(context).size.width;
    final myDecoration =
        const InputDecoration().applyDefaults(GlobalInputDecoration(context));

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.primaryColor.withOpacity(0.95),
        title: MyText.titleMedium(
          "PHÒNG HC-TC DUYỆT".tr.toUpperCase(),
          color: theme.colorScheme.onPrimary,
          fontWeight: 700,
        ),
        iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
        centerTitle: true,
      ),
      body: Obx(
        () => Column(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 16.0, right: 16.0, bottom: 16.0, top: 4),
                child: FormBuilder(
                  key: controller.formKeyXeNVLai,
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: MyText.bodyMedium(
                            controller.isEditNVLai.value
                                ? "Chỉnh sửa hình thức duyệt".toUpperCase()
                                : "Chọn hình thức duyệt".toUpperCase(),
                            decoration: TextDecoration.underline,
                            color: theme.colorScheme.onSurface,
                            fontSize: 15,
                            fontWeight: 700,
                          ),
                        ),
                      ),

                      DieuXeDivider(context, "thuê xe ngoài"),
                      MySpacing.height(7),

                      // SWITCH THUÊ XE NGOÀI
                      FormBuilderSwitch(
                        key: ValueKey(controller.thuexengoai.value),
                        title: const MyText.bodyLarge('Thuê xe ngoài'),
                        name: 'thuexengoai',
                        initialValue: controller.thuexengoai.value,
                        onChanged: (value) {
                          controller.thuexengoai.value = value!;
                          // Cập nhật field hiện tại
                          if (value) {
                            controller.phonghctcduyet.value = false;
                            controller.formKeyXeNVLai.currentState
                                ?.fields['phonghctcduyet']
                                ?.didChange(false);
                          }
                          controller.formData["thuexengoai"] = value;
                          // controller.update();
                        },
                        onSaved: (newValue) {
                          controller.formData["thuexengoai"] = newValue;
                        },
                        decoration: myDecoration,
                      ),

                      // HIỂN THỊ THÀNH PHẦN TRONG MỤC CHỌN THUÊ XE NGOÀI
                      Visibility(
                          visible: controller.thuexengoai.value,
                          child: const SizedBox(height: 14)),
                      Visibility(
                        visible: controller.thuexengoai.value,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // GHI CHÚ
                            MyText.bodyMedium('Ghi chú:', fontWeight: 700),
                            FormBuilderTextField(
                              name: 'ghichuthuexe',
                              // validator: FormBuilderValidators.required(
                              //     errorText: 'Vui lòng nhập ghi chú'),
                              decoration: InputDecoration(
                                hintText: 'Nhập ghi chú',
                                hintStyle: TextStyle(
                                    color: theme.colorScheme.onSurface
                                        .withOpacity(0.3)),
                              ),
                              initialValue: controller.pdx.ghiChu ?? '',
                              maxLines: 2,
                              onChanged: (value) =>
                                  controller.formData['ghichuthuexe'] = value,
                              onSaved: (newValue) => controller
                                  .formData['ghichuthuexe'] = newValue ?? '',
                              style: theme.textTheme.labelLarge,
                            ),
                          ],
                        ),
                      ),

                      MySpacing.height(10),
                      DieuXeDivider(context, "phòng hc-tc đã duyệt"),
                      MySpacing.height(7),

                      // SWITCH PHÒNG HC-TC ĐÃ DUYỆT
                      FormBuilderSwitch(
                        key: ValueKey(controller.phonghctcduyet.value),
                        title: const MyText.bodyLarge('Phòng HC-TC đã duyệt'),
                        name: 'phonghctcduyet',
                        initialValue: controller.phonghctcduyet.value,
                        onChanged: (value) {
                          controller.phonghctcduyet.value = value!;
                          if (value) {
                            controller.thuexengoai.value = false;
                            controller.formKeyXeNVLai.currentState
                                ?.fields['thuexengoai']
                                ?.didChange(false);
                          }
                          controller.formData["phonghctcduyet"] = value;
                          // controller.update();
                        },
                        onSaved: (newValue) {
                          controller.formData["phonghctcduyet"] = newValue;
                        },
                        decoration: myDecoration,
                      ),

                      // HIỂN THỊ CÁC THÀNH PHẦN ĐỂ CHỌN XE VÀ NHÂN VIÊN LÁI
                      Visibility(
                          visible: controller.phonghctcduyet.value,
                          child: const SizedBox(height: 14)),
                      Visibility(
                        visible: controller.phonghctcduyet.value,
                        child: Obx(
                          () => Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // ---------- BIỂN KIỂM SOÁT ----------
                              MyText.bodyMedium(
                                'Biển kiểm soát:',
                                fontWeight: 700,
                              ),
                              MySpacing.height(5),

                              FormBuilderDropdown<Xe>(
                                key: ValueKey(controller.xeList.length),
                                name: 'xe',
                                decoration: myDecoration.copyWith(
                                  hintText: 'Chọn xe...',
                                ),
                                validator: FormBuilderValidators.compose([
                                  FormBuilderValidators.required(
                                      errorText: 'Vui lòng chọn xe')
                                ]),
                                // items: controller.xeList
                                //     .map((ele) => DropdownMenuItem(
                                //           alignment:
                                //               AlignmentDirectional.center,
                                //           value: ele,
                                //           child: Text(
                                //               "${ele.bienKiemSoat} (${controller.getHoTen(ele.nvLai)})"),
                                //         ))
                                //     .toList(),
                                items: controller.xeList.map((ele) {
                                  // Kiểm tra xem xe có bị vô hiệu hóa không
                                  bool isDisabled =
                                      controller.isXeDisabled(ele);
                                  return DropdownMenuItem(
                                    alignment: AlignmentDirectional.center,
                                    value: ele,
                                    enabled:
                                        !isDisabled, // Vô hiệu hóa nếu xe bị chiếm dụng
                                    child: Text(
                                      "${ele.bienKiemSoat} (${controller.getHoTen(ele.nvLai)})",
                                      style: TextStyle(
                                        color: isDisabled
                                            ? Colors.grey
                                            : null, // Màu xám nếu bị vô hiệu hóa
                                      ),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  controller.selectedXe.value = value;
                                  controller.formData['xe'] = value?.id;
                                  controller.updateNvLaiFromXe(value);
                                },
                                onSaved: (newValue) {
                                  controller.formData['xe'] = newValue?.id;
                                },
                                initialValue:
                                    controller.pdx.ttDieuXe?.xe?.id != null &&
                                            controller.xeList.any((xe) =>
                                                xe.id ==
                                                controller.pdx.ttDieuXe!.xe!.id)
                                        ? controller.xeList.firstWhere((xe) =>
                                            xe.id ==
                                            controller.pdx.ttDieuXe!.xe!.id)
                                        : null,
                              ),
                              MySpacing.height(14),

                              // ---------- NHÂN VIÊN LÁI XE ----------
                              MyText.bodyMedium(
                                'Nhân viên lái xe:',
                                fontWeight: 700,
                              ),
                              MySpacing.height(5),

                              FormBuilderDropdown<NguoiTao>(
                                key: ValueKey(
                                    controller.selectedNvLai.value?.id ??
                                        'null'),
                                name: 'nhanVienLaiXe',
                                decoration: myDecoration.copyWith(
                                  hintText: 'Chọn nhân viên lái xe',
                                ),
                                validator: FormBuilderValidators.compose([
                                  FormBuilderValidators.required(
                                      errorText:
                                          'Vui lòng chọn nhân viên lái xe')
                                ]),
                                // items: controller.nvLaiList
                                //     .map((ele) => DropdownMenuItem(
                                //           alignment:
                                //               AlignmentDirectional.center,
                                //           value: ele,
                                //           child: Text(
                                //               "${ele.ho} ${ele.tenDem ?? ''} ${ele.ten}"),
                                //         ))
                                //     .toList(),
                                items: controller.nvLaiList.map((ele) {
                                  // Kiểm tra xem tài xế có bị vô hiệu hóa không
                                  bool isDisabled =
                                      controller.isTaiXeDisabled(ele);
                                  return DropdownMenuItem(
                                    alignment: AlignmentDirectional.center,
                                    value: ele,
                                    enabled:
                                        !isDisabled, // Vô hiệu hóa nếu tài xế bị chiếm dụng
                                    child: Text(
                                      "${ele.ho} ${ele.tenDem ?? ''} ${ele.ten}",
                                      style: TextStyle(
                                        color: isDisabled
                                            ? Colors.grey
                                            : null, // Màu xám nếu bị vô hiệu hóa
                                      ),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  controller.selectedNvLai.value = value;
                                  controller.formData['nhanVienLaiXe'] =
                                      value?.id;
                                },
                                onSaved: (newValue) {
                                  controller.formData['nhanVienLaiXe'] =
                                      newValue?.id;
                                },
                                initialValue: controller
                                                .pdx.ttDieuXe?.taiXe!.id !=
                                            null &&
                                        controller.nvLaiList.any((taixe) =>
                                            taixe.id ==
                                            controller.pdx.ttDieuXe?.taiXe!.id)
                                    ? controller.nvLaiList.firstWhere((taixe) =>
                                        taixe.id ==
                                        controller.pdx.ttDieuXe?.taiXe!.id)
                                    : null,
                              ),
                              MySpacing.height(14),

                              // ---------- SỐ LỆNH ĐIỀU XE ----------
                              MyText.bodyMedium(
                                'Số lệnh điều xe:',
                                fontWeight: 700,
                              ),
                              FormBuilderTextField(
                                name: 'solenhdieuxe',
                                // validator: FormBuilderValidators.required(
                                //     errorText:
                                //         'Vui lòng nhập danh sách viên chức ngoài trường'),
                                decoration: InputDecoration(
                                  hintText:
                                      'Hệ thống tự cập nhật \nChỉ nhập khi điều chỉnh tài xế và đổi số lệnh điều xe',
                                  hintStyle: TextStyle(
                                    color: theme.colorScheme.onSurface
                                        .withOpacity(0.3),
                                  ),
                                ),
                                initialValue:
                                    controller.pdx.ttDieuXe?.soLenhDieuXe ?? '',
                                maxLines: 2,
                                onChanged: (value) =>
                                    controller.formData['solenhdieuxe'] = value,
                                onSaved: (newValue) => controller
                                    .formData['solenhdieuxe'] = newValue ?? '',
                                style: theme.textTheme.labelLarge,
                              ),
                              MySpacing.height(14),

                              // ---------- CÁN BỘ ĐI CÙNG ----------
                              MyText.bodyMedium(
                                'Cán bộ đi cùng:',
                                fontWeight: 700,
                              ),
                              FormBuilderTextField(
                                name: 'canbodicung',
                                // validator: FormBuilderValidators.required(
                                //     errorText:
                                //         'Vui lòng nhập danh sách viên chức ngoài trường'),
                                decoration: InputDecoration(
                                  hintText: 'Nhập cán bộ đi cùng (nếu có)',
                                  hintStyle: TextStyle(
                                    color: theme.colorScheme.onSurface
                                        .withOpacity(0.3),
                                  ),
                                ),
                                initialValue:
                                    controller.pdx.ttDieuXe?.canBoDiCung ?? '',
                                maxLines: 2,
                                onChanged: (value) =>
                                    controller.formData['canbodicung'] = value,
                                onSaved: (newValue) => controller
                                    .formData['canbodicung'] = newValue ?? '',
                                style: theme.textTheme.labelLarge,
                              ),
                              MySpacing.height(14),

                              // ---------- SỐ KM DỰ KIẾN ----------
                              MyText.bodyMedium(
                                'Số Km dự kiến:',
                                fontWeight: 700,
                              ),
                              FormBuilderTextField(
                                name: 'sokmdukien',
                                keyboardType: TextInputType.number,
                                decoration: InputDecoration(
                                  hintText: 'Nhập số Km dự kiến',
                                  hintStyle: TextStyle(
                                    color: theme.colorScheme.onSurface
                                        .withOpacity(0.3),
                                  ),
                                ),
                                initialValue:
                                    controller.pdx.ttDieuXe?.soKmDuKien ?? '',
                                maxLines: 2,
                                onChanged: (value) =>
                                    controller.formData['sokmdukien'] = value,
                                onSaved: (newValue) => controller
                                    .formData['sokmdukien'] = newValue ?? '',
                                style: theme.textTheme.labelLarge,
                              ),
                              MySpacing.height(14),

                              // ---------- THỜI GIAN ----------
                              MyText.bodyMedium(
                                'Thời gian:',
                                fontWeight: 700,
                              ),
                              MySpacing.height(14),

                              FormBuilderDateTimePicker(
                                name: "ngaydi",
                                decoration: myDecoration.copyWith(
                                  labelText: 'Thời gian đi',
                                  suffixIcon: IconButton(
                                    icon: Icon(Icons.clear,
                                        color: theme.primaryColor),
                                    onPressed: () {
                                      controller.formKeyXeNVLai.currentState
                                          ?.fields['ngaydi']
                                          ?.didChange(null);
                                      controller.formData['ngaydi'] = null;
                                    },
                                  ),
                                ),
                                validator: FormBuilderValidators.required(
                                    errorText: 'Vui lòng chọn thời gian đi'),
                                initialValue:
                                    controller.pdx.ttDieuXe?.ngayDi != null
                                        ? DateTime.parse(controller
                                            .pdx.ttDieuXe!.ngayDi!
                                            .toString())
                                        : DateTime.parse(
                                            controller.pdx.tuNgay.toString()),
                                inputType: InputType.both,
                                format: DateFormat("HH:mm dd/MM/yyyy"),
                                onChanged: (DateTime? value) {
                                  if (value != null) {
                                    controller.formData['ngaydi'] = value
                                        .toIso8601String(); // Lưu dưới dạng chuỗi ISO 8601
                                  }
                                },
                                onSaved: (DateTime? newValue) {
                                  if (newValue != null) {
                                    controller.formData['ngaydi'] =
                                        newValue.toIso8601String();
                                  }
                                },
                                style: theme.textTheme.labelLarge,
                                // firstDate: DateTime.now(), // Ngày bắt đầu
                                lastDate: DateTime(2100), // Ngày kết thúc
                              ),

                              MySpacing.height(14),

                              // Thời gian về
                              FormBuilderDateTimePicker(
                                name: "ngayve",
                                decoration: myDecoration.copyWith(
                                  labelText: 'Thời gian về',
                                  suffixIcon: IconButton(
                                    icon: Icon(Icons.clear,
                                        color: theme.primaryColor),
                                    onPressed: () {
                                      controller.formKeyXeNVLai.currentState
                                          ?.fields['ngayve']
                                          ?.didChange(null);
                                      controller.formData['ngayve'] = null;
                                    },
                                  ),
                                ),
                                validator: FormBuilderValidators.required(
                                    errorText: 'Vui lòng chọn thời gian về'),
                                initialValue:
                                    controller.pdx.ttDieuXe?.ngayVe != null
                                        ? DateTime.parse(controller
                                            .pdx.ttDieuXe!.ngayVe!
                                            .toString())
                                        : DateTime.parse(
                                            controller.pdx.denNgay.toString()),
                                inputType: InputType.both,
                                format: DateFormat("HH:mm dd/MM/yyyy"),
                                onChanged: (DateTime? value) {
                                  if (value != null) {
                                    controller.formData['ngayve'] = value
                                        .toIso8601String(); // Lưu dưới dạng chuỗi ISO 8601
                                  }
                                },
                                onSaved: (DateTime? newValue) {
                                  if (newValue != null) {
                                    controller.formData['ngayve'] =
                                        newValue.toIso8601String();
                                  }
                                },
                                style: theme.textTheme.labelLarge,
                                // firstDate: DateTime.now(), // Ngày bắt đầu
                                lastDate: DateTime(2100), // Ngày kết thúc
                              ),
                              MySpacing.height(14),

                              // GHI CHÚ
                              MyText.bodyMedium('Ghi chú:', fontWeight: 700),
                              FormBuilderTextField(
                                name: 'ghichu',
                                // validator: FormBuilderValidators.required(
                                //     errorText: 'Vui lòng nhập ghi chú'),
                                decoration: InputDecoration(
                                  hintText: 'Nhập ghi chú',
                                  hintStyle: TextStyle(
                                      color: theme.colorScheme.onSurface
                                          .withOpacity(0.3)),
                                ),
                                initialValue: controller.pdx.ghiChu ?? '',
                                maxLines: 2,
                                onChanged: (value) =>
                                    controller.formData['ghichu'] = value,
                                onSaved: (newValue) => controller
                                    .formData['ghichu'] = newValue ?? '',
                                style: theme.textTheme.labelLarge,
                              ),
                            ],
                          ),
                        ),
                      ),

                      MySpacing.height(14),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16.0),
              color: Colors.transparent,
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: MyButton.block(
                      onPressed: () async {
                        if (!(controller.thuexengoai.value ||
                            controller.phonghctcduyet.value)) {
                          Get.snackbar(
                            'Lỗi',
                            'Vui lòng chọn ít nhất một hình thức duyệt (Thuê xe ngoài hoặc Phòng HC-TC đã duyệt).',
                            snackPosition: SnackPosition.TOP,
                            backgroundColor: Colors.red.withOpacity(0.8),
                            colorText: Colors.white,
                          );
                          return;
                        }
                        bool isValid =
                            controller.formKeyXeNVLai.currentState!.validate();
                        if (isValid) {
                          controller.formKeyXeNVLai.currentState!.save();
                          // print("Form data: ${controller.formData}");
                          await controller.duyetDieuXeHCTC();
                          Navigator.pop(context);
                        }
                      },
                      child: MyText.labelLarge(
                        controller.isEditNVLai.value
                            ? 'Cập nhật'
                            : "Lưu kết quả duyệt",
                        fontWeight: 900,
                        color: theme.colorScheme.onPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
