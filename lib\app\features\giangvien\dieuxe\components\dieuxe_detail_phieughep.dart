import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/giaydieuxe.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_divider.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_detail_controller.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/date_time_extension.dart';

Widget PhieuGhepTab(BuildContext context, DieuXeDetailController controller) {
  final theme = Theme.of(context);
  return ListView(
    shrinkWrap: true,
    children: [
      if (controller.listpdxcon.isNotEmpty) ...[
        for (var i = 0; i < controller.listpdxcon.length; i++)
          MyContainer.none(
            padding: const EdgeInsets.fromLTRB(12, 5, 12, 0),
            margin: const EdgeInsets.symmetric(vertical: 0, horizontal: 8),
            border: Border.all(color: Colors.grey[300]!),
            // borderRadius: BorderRadius.circular(4),
            onTap: () {
              _showPhieuDetailDialog(context, controller.listpdxcon[i], theme);
            },
            child: Container(
              padding: EdgeInsets.fromLTRB(8, 8, 8, 8),
              decoration: BoxDecoration(
                  color: Colors.white,
                  // borderRadius: BorderRadius.circular(4), // Bo góc nhẹ cho nền
                  border: Border.all(
                    color: Colors.grey.withOpacity(0.2),
                    width: 2,
                  )),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: Colors.grey,
                        ),
                        child: Icon(
                          LucideIcons.car,
                          size: 40,
                          color: Colors.white,
                          semanticLabel: "Status",
                        ),
                      ),
                    ],
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: MyText.titleMedium(
                                controller.listpdxcon[i].donVi!.tenDonVi!,
                                overflow: TextOverflow.ellipsis,
                                fontWeight: 700,
                              ),
                            ),
                          ],
                        ),
                        MyText.bodyMedium(
                          "Thời gian: ${controller.listpdxcon[i].tuNgay!.toFormat(format: "dd.MM.yy")} - ${controller.listpdxcon[i].denNgay!.toFormat(format: "dd.MM.yy")}",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          color: Colors.grey[700],
                        ),
                        MyText.bodyMedium(
                          "Người tạo: ${controller.listpdxcon[i].nguoiTao?.ho} ${controller.listpdxcon[i].nguoiTao?.tenDem ?? ""} ${controller.listpdxcon[i].nguoiTao?.ten}",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          color: Colors.grey[700],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
      ]
    ],
  );
}

// Hàm hiển thị dialog chi tiết phiếu
void _showPhieuDetailDialog(
    BuildContext context, GiayDieuXe phieu, ThemeData theme) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: MyText.titleMedium(
          "CHI TIẾT PHIẾU ĐIỀU XE",
          color: theme.primaryColor,
          fontWeight: 700,
          textAlign: TextAlign.center,
        ),
        content: Column(
          children: [
            Divider(
              color: theme.dividerColor.withOpacity(0.5),
              thickness: 1,
              height: 0,
            ),
            SizedBox(height: 5),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // THÔNG TIN
                    DieuXeDivider(context, "thông tin"),
                    _buildDetailRow(LucideIcons.building2, "Đơn vị:",
                        phieu.donVi?.tenDonVi ?? "-", theme),
                    _buildDetailRow(
                        LucideIcons.userRoundPen,
                        "Người tạo:",
                        "${phieu.nguoiTao?.ho ?? ""} ${phieu.nguoiTao?.tenDem ?? ""} ${phieu.nguoiTao?.ten ?? ""}",
                        theme),
                    _buildDetailRow(LucideIcons.settings, "Trạng thái:",
                        phieu.trangThai ?? "-", theme),

                    // NỘI DUNG CÔNG TÁC
                    DieuXeDivider(context, "nội dung"),
                    _buildDetailRow(LucideIcons.filePen, "Nội dung công tác:",
                        phieu.noiDung ?? "-", theme),
                    _buildDetailRow(LucideIcons.mapPin, "Nơi công tác:",
                        phieu.noiCongTac ?? "-", theme),
                    _buildDetailRow(
                        LucideIcons.calendar,
                        "Thời gian:",
                        "${phieu.tuNgay?.toFormat(format: "dd.MM.yyy") ?? "-"} - ${phieu.denNgay?.toFormat(format: "dd.MM.yyyy") ?? "-"}",
                        theme),

                    // DI CHUYỂN
                    DieuXeDivider(context, "di chuyển"),
                    _buildDetailRow(LucideIcons.planeTakeoff, "Địa điểm đi:",
                        phieu.diaDiemKhoiHanh ?? "-", theme),
                    _buildDetailRow(LucideIcons.clockArrowUp, "Thời gian đi:",
                        phieu.thoiGianKhoiHanh ?? "-", theme),
                    _buildDetailRow(LucideIcons.planeLanding, "Địa điểm về:",
                        phieu.diaDiemVe ?? "", theme),
                    _buildDetailRow(LucideIcons.clockArrowDown, "Thời gian về:",
                        phieu.thoiGianVe ?? "-", theme),
                  ],
                ),
              ),
            ),
          ],
        ),
        actions: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Divider(
                color: theme.dividerColor.withOpacity(0.5),
                thickness: 1,
                height: 0,
              ),
              SizedBox(height: 5),
              MyButton.medium(
                child: MyText.bodyMedium(
                  "Đóng",
                  color: Colors.white,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        ],
      );
    },
  );
}

// Widget để xây dựng từng hàng trong dialog
Widget _buildDetailRow(
    IconData icon, String label, String value, ThemeData theme) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4.0),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 15, color: theme.primaryColor),
            SizedBox(width: 4),
            SizedBox(
              child: MyText.bodyMedium(
                label,
                fontWeight: 700,
                color: theme.primaryColor,
              ),
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.only(top: 4.0),
          child: MyText.bodyMedium(
            value,
            // maxLines: 3, // Giới hạn số dòng
            // overflow: TextOverflow.ellipsis, // Ẩn bớt nội dung dài
            softWrap: true, // Cho phép ngắt dòng
            color: Colors.black87,
            textAlign: TextAlign.start,
          ),
        ),
      ],
    ),
  );
}
