class PhieuDanhGiaXeModel {
  PhieuDanhGiaXeModel({
    required this.dsTaixe,
    required this.thongtinxe,
  });

  final List<DsTaixe> dsTaixe;
  final Thongtinxe? thongtinxe;

  PhieuDanhGiaXeModel copyWith({
    List<DsTaixe>? dsTaixe,
    Thongtinxe? thongtinxe,
  }) {
    return PhieuDanhGiaXeModel(
      dsTaixe: dsTaixe ?? this.dsTaixe,
      thongtinxe: thongtinxe ?? this.thongtinxe,
    );
  }

  factory PhieuDanhGiaXeModel.fromJson(Map<String, dynamic> json){
    return PhieuDanhGiaXeModel(
      dsTaixe: json["dsTaixe"] == null ? [] : List<DsTaixe>.from(json["dsTaixe"]!.map((x) => DsTaixe.fromJson(x))),
      thongtinxe: json["thongtinxe"] == null ? null : Thongtinxe.fromJson(json["thongtinxe"]),
    );
  }

  Map<String, dynamic> toJson() => {
    "dsTaixe": dsTaixe.map((x) => x.toJson()).toList(),
    "thongtinxe": thongtinxe?.toJson(),
  };

  @override
  String toString(){
    return "$dsTaixe, $thongtinxe, ";
  }
}

class DsTaixe {
  DsTaixe({
    required this.id,
    required this.ho,
    required this.tenDem,
    required this.ten,
    required this.tenDonVi,
  });

  final int? id;
  final String? ho;
  final String? tenDem;
  final String? ten;
  final String? tenDonVi;

  DsTaixe copyWith({
    int? id,
    String? ho,
    String? tenDem,
    String? ten,
    String? tenDonVi,
  }) {
    return DsTaixe(
      id: id ?? this.id,
      ho: ho ?? this.ho,
      tenDem: tenDem ?? this.tenDem,
      ten: ten ?? this.ten,
      tenDonVi: tenDonVi ?? this.tenDonVi,
    );
  }

  factory DsTaixe.fromJson(Map<String, dynamic> json){
    return DsTaixe(
      id: json["id"],
      ho: json["ho"],
      tenDem: json["tenDem"],
      ten: json["ten"],
      tenDonVi: json["tenDonVi"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "ho": ho,
    "tenDem": tenDem,
    "ten": ten,
    "tenDonVi": tenDonVi,
  };

  @override
  String toString(){
    return "$id, $ho, $tenDem, $ten, $tenDonVi, ";
  }
}

class Thongtinxe {
  Thongtinxe({
    required this.id,
    required this.tenXe,
    required this.hangXe,
    required this.namSanXuat,
    required this.bienKiemSoat,
    required this.soChoNgoi,
    required this.trangThaiId,
    required this.loaiXeId,
    required this.ghiChu,
    required this.hcDieuXeTaiXe,
    required this.loaiXe,
    required this.trangThai,
  });

  final int? id;
  final String? tenXe;
  final String? hangXe;
  final String? namSanXuat;
  final String? bienKiemSoat;
  final String? soChoNgoi;
  final dynamic trangThaiId;
  final num? loaiXeId;
  final String? ghiChu;
  final List<dynamic> hcDieuXeTaiXe;
  final dynamic loaiXe;
  final dynamic trangThai;

  Thongtinxe copyWith({
    int? id,
    String? tenXe,
    String? hangXe,
    String? namSanXuat,
    String? bienKiemSoat,
    String? soChoNgoi,
    dynamic trangThaiId,
    num? loaiXeId,
    String? ghiChu,
    List<dynamic>? hcDieuXeTaiXe,
    dynamic loaiXe,
    dynamic trangThai,
  }) {
    return Thongtinxe(
      id: id ?? this.id,
      tenXe: tenXe ?? this.tenXe,
      hangXe: hangXe ?? this.hangXe,
      namSanXuat: namSanXuat ?? this.namSanXuat,
      bienKiemSoat: bienKiemSoat ?? this.bienKiemSoat,
      soChoNgoi: soChoNgoi ?? this.soChoNgoi,
      trangThaiId: trangThaiId ?? this.trangThaiId,
      loaiXeId: loaiXeId ?? this.loaiXeId,
      ghiChu: ghiChu ?? this.ghiChu,
      hcDieuXeTaiXe: hcDieuXeTaiXe ?? this.hcDieuXeTaiXe,
      loaiXe: loaiXe ?? this.loaiXe,
      trangThai: trangThai ?? this.trangThai,
    );
  }

  factory Thongtinxe.fromJson(Map<String, dynamic> json){
    return Thongtinxe(
      id: json["id"],
      tenXe: json["tenXe"],
      hangXe: json["hangXe"],
      namSanXuat: json["namSanXuat"],
      bienKiemSoat: json["bienKiemSoat"],
      soChoNgoi: json["soChoNgoi"],
      trangThaiId: json["trangThaiId"],
      loaiXeId: json["loaiXeId"],
      ghiChu: json["ghiChu"],
      hcDieuXeTaiXe: json["hcDieuXeTaiXe"] == null ? [] : List<dynamic>.from(json["hcDieuXeTaiXe"]!.map((x) => x)),
      loaiXe: json["loaiXe"],
      trangThai: json["trangThai"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "tenXe": tenXe,
    "hangXe": hangXe,
    "namSanXuat": namSanXuat,
    "bienKiemSoat": bienKiemSoat,
    "soChoNgoi": soChoNgoi,
    "trangThaiId": trangThaiId,
    "loaiXeId": loaiXeId,
    "ghiChu": ghiChu,
    "hcDieuXeTaiXe": hcDieuXeTaiXe.map((x) => x).toList(),
    "loaiXe": loaiXe,
    "trangThai": trangThai,
  };

  @override
  String toString(){
    return "$id, $tenXe, $hangXe, $namSanXuat, $bienKiemSoat, $soChoNgoi, $trangThaiId, $loaiXeId, $ghiChu, $hcDieuXeTaiXe, $loaiXe, $trangThai, ";
  }
}
