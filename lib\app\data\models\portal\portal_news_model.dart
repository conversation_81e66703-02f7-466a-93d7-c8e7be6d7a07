import 'dart:convert';

class PortalNewsModel {
  PortalNewsModel({
    required this.title,
    required this.summary,
    required this.image,
    required this.imageSet,
    required this.publishDate,
    required this.content,
    required this.urlSlug,
  });

  final String? title;
  final String? summary;
  final String? image;
  final List<ImageSet> imageSet;
  final DateTime? publishDate;
  final String? content;
  final String? urlSlug;

  PortalNewsModel copyWith({
    String? title,
    String? summary,
    String? image,
    List<ImageSet>? imageSet,
    DateTime? publishDate,
    String? content,
    String? urlSlug,
  }) {
    return PortalNewsModel(
      title: title ?? this.title,
      summary: summary ?? this.summary,
      image: image ?? this.image,
      imageSet: imageSet ?? this.imageSet,
      publishDate: publishDate ?? this.publishDate,
      content: content ?? this.content,
      urlSlug: urlSlug ?? this.urlSlug,
    );
  }

  factory PortalNewsModel.fromJson(Map<String, dynamic> json) {
    return PortalNewsModel(
      title: json["title"],
      summary: json["summary"],
      image: json["image"],
      imageSet: json["imageSet"] == null
          ? []
          : List<ImageSet>.from(
              json["imageSet"]!.map((x) => ImageSet.fromJson(x))),
      publishDate: DateTime.tryParse(json["publishDate"] ?? ""),
      content: json["content"],
      urlSlug: json["urlSlug"],
    );
  }

  Map<String, dynamic> toJson() => {
        "title": title,
        "summary": summary,
        "image": image,
        "imageSet": imageSet.map((x) => x.toJson()).toList(),
        "publishDate": publishDate?.toIso8601String(),
        "content": content,
        "urlSlug": urlSlug,
      };

  @override
  String toString() {
    return "$title, $summary, $image, $imageSet, $publishDate, $content, $urlSlug, ";
  }

  String toJsonString() => json.encode(toJson());
  factory PortalNewsModel.fromJsonString(String source) =>
      PortalNewsModel.fromJson(json.decode(source));

  static List<PortalNewsModel> parseNews(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed
        .map<PortalNewsModel>((json) => PortalNewsModel.fromJson(json))
        .toList();
  }
}

class ImageSet {
  ImageSet({
    required this.url,
    required this.width,
  });

  final String? url;
  final int? width;

  ImageSet copyWith({
    String? url,
    int? width,
  }) {
    return ImageSet(
      url: url ?? this.url,
      width: width ?? this.width,
    );
  }

  factory ImageSet.fromJson(Map<String, dynamic> json) {
    return ImageSet(
      url: json["url"],
      width: json["width"],
    );
  }

  Map<String, dynamic> toJson() => {
        "url": url,
        "width": width,
      };

  @override
  String toString() {
    return "$url, $width, ";
  }

  String toJsonString() => json.encode(toJson());
  factory ImageSet.fromJsonString(String source) =>
      ImageSet.fromJson(json.decode(source));
}
