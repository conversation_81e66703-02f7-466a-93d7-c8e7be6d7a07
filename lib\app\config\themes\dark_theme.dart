// ignore_for_file: deprecated_member_use

part of 'app_theme.dart';

final ThemeData darkTheme = ThemeData(
  fontFamily: GoogleFonts.ibmPlexSans().fontFamily,
  scaffoldBackgroundColor: appBackgroundColorDark,
  highlightColor: appBackgroundColorDark,
  appBarTheme: const AppBarTheme(
    color: appBackgroundColorDark,
    iconTheme: IconThemeData(color: blackColor),
    systemOverlayStyle:
        SystemUiOverlayStyle(statusBarIconBrightness: Brightness.light),
  ),
  primaryColor: color_primary_black,
  dividerColor: const Color(0xFFDADADA).withOpacity(0.3),
  primaryColorDark: color_primary_black,
  textSelectionTheme: const TextSelectionThemeData(cursorColor: Colors.white),
  hoverColor: Colors.black12,
  //fontFamily: GoogleFonts.openSans().fontFamily,
  bottomSheetTheme:
      const BottomSheetThemeData(backgroundColor: appBackgroundColorDark),
  //primaryTextTheme: TextTheme(headline6: primaryTextStyle(color: Colors.white70), overline: primaryTextStyle(color: Colors.white)),
  cardTheme: const CardThemeData(color: cardBackgroundBlackDark),
  cardColor: appSecondaryBackgroundColor,
  iconTheme: const IconThemeData(color: whiteColor),
  textTheme: const TextTheme(
    labelLarge: TextStyle(color: color_primary_black),
    titleLarge: TextStyle(color: Colors.white70),
    titleSmall: TextStyle(color: Colors.white54),
  ),
  visualDensity: VisualDensity.adaptivePlatformDensity,
  dividerTheme:
      DividerThemeData(color: viewLineColor.withOpacity(0.5), thickness: 0.7),
  colorScheme: ColorScheme.dark(
          primary: mlColorDarkBlue,
          onPrimary: Colors.white70,
          error: const Color(0xFFCF6676),
          onSurface: Colors.white70)
      .copyWith(secondary: Colors.white70)
      .copyWith(error: const Color(0xFFCF6676)),
).copyWith(
  pageTransitionsTheme: const PageTransitionsTheme(
      builders: <TargetPlatform, PageTransitionsBuilder>{
        TargetPlatform.android: OpenUpwardsPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        TargetPlatform.linux: OpenUpwardsPageTransitionsBuilder(),
        TargetPlatform.macOS: OpenUpwardsPageTransitionsBuilder(),
      }),
);
