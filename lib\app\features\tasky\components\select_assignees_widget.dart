import 'package:flutter/material.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'dart:async';

import 'package:tvumobile/app/shared_components/mytext.dart';

class SelectAssignees extends StatefulWidget {
  final List<dynamic> initialUsers;
  final List<dynamic> availableUsers;
  final Future<Map<String, dynamic>> Function(String query, int pageNumber, int pageSize) onSearch;
  final void Function(List<dynamic>) onChanged;
  final int? maxSelections;

  const SelectAssignees({
    super.key,
    required this.initialUsers,
    required this.availableUsers,
    required this.onSearch,
    required this.onChanged,
    this.maxSelections,
  });

  @override
  // ignore: library_private_types_in_public_api
  _SelectAssigneesState createState() => _SelectAssigneesState();
}

class _SelectAssigneesState extends State<SelectAssignees> {
  List<dynamic> _selectedUsers = [];

  @override
  void initState() {
    super.initState();
    _selectedUsers = List.from(widget.initialUsers);
  }

  void _openUserSelectionDialog() async {
    List<dynamic>? selectedUsers = await showDialog<List<dynamic>>(
      context: context,
      builder: (context) {
        return Dialog(
          insetPadding: EdgeInsets.all(16),
          child: _UserSelectionDialog(
            selectedUsers: List.from(_selectedUsers),
            availableUsers: widget.availableUsers,
            onSearch: widget.onSearch,
            maxSelections: widget.maxSelections,
          ),
        );
      },
    );

    if (selectedUsers != null) {
      setState(() {
        _selectedUsers = selectedUsers;
      });
      widget.onChanged(_selectedUsers);
    }
  }

  void _removeSelectedUser(dynamic user) {
    setState(() {
      _selectedUsers.removeWhere((selected) => selected["vienChucId"] == user["vienChucId"]);
    });
    widget.onChanged(_selectedUsers);
  }

  Widget _buildSelectedUsers() {
    if (_selectedUsers.isEmpty) {
      return Text('Chưa có người thực hiện nào được chọn.');
    }

    return Wrap(
      spacing: 8,
      runSpacing: 0,
      children: _selectedUsers.map((user) {
        String name = '${user["ho"]} ${user["tenDem"]} ${user["ten"]}';
        String? avatarUrl = user["hinhAnh"];

        return Chip(
          avatar: avatarUrl != null
              ? CircleAvatar(
            backgroundImage: NetworkImage(avatarUrl),
            onBackgroundImageError: (_, __) {
              // Xử lý lỗi load ảnh
            },
          )
              : CircleAvatar(child: Text(name[0])),
          label: Text(name),
          deleteIcon: const Icon(Icons.close, size: 18),
          onDeleted: () => _removeSelectedUser(user),
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            MyText.bodyMedium(
              'Ai thực hiện?',
              fontWeight: 700,
            ),
            Spacer(),
            MyButton.small(
              onPressed: _openUserSelectionDialog,
              backgroundColor: theme.colorScheme.primaryContainer,
              elevation: 2,
              child: Row(
                children: [
                  Icon(
                    Icons.person_add,
                    size: 20,
                    color: theme.colorScheme.onPrimary,
                  ),
                  SizedBox(width: 6),
                  MyText.labelLarge(
                    'Chọn người thực hiện',
                    color: theme.colorScheme.onPrimary,
                  ),
                ],
              ),
            )
          ],
        ),
        _buildSelectedUsers(),
      ],
    );
  }
}

class _UserSelectionDialog extends StatefulWidget {
  final List<dynamic> selectedUsers;
  final List<dynamic> availableUsers;
  final Future<Map<String, dynamic>> Function(String query, int pageNumber, int pageSize) onSearch;
  final int? maxSelections;

  const _UserSelectionDialog({
    required this.selectedUsers,
    required this.availableUsers,
    required this.onSearch,
    this.maxSelections,
  });

  @override
  __UserSelectionDialogState createState() => __UserSelectionDialogState();
}

class __UserSelectionDialogState extends State<_UserSelectionDialog> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  Timer? _debounce;
  List<dynamic> _selectedUsers = [];
  List<dynamic> _displayedUsers = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreData = false;
  int _currentPage = 1;
  final int _pageSize = 20;

  @override
  void initState() {
    super.initState();
    _selectedUsers = List.from(widget.selectedUsers);
    _displayedUsers = List.from(widget.availableUsers);
    _searchController.addListener(_onSearchTextChanged);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchTextChanged);
    _searchController.dispose();
    _debounce?.cancel();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onSearchTextChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      _performSearch(_searchController.text);
    });
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore &&
        _hasMoreData) {
      _loadMoreData();
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.isEmpty) {
      setState(() {
        _displayedUsers = List.from(widget.availableUsers);
        _isLoading = false;
        _hasMoreData = false;
        _currentPage = 1;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _currentPage = 1;
      _hasMoreData = true;
    });

    try {
      final response = await widget.onSearch(query, _currentPage, _pageSize);
      List<dynamic> results = response['results'];
      int totalPages = response['totalPages'];

      setState(() {
        _displayedUsers = results;
        _hasMoreData = _currentPage < totalPages;
      });
    } catch (e) {
      // Xử lý lỗi
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Có lỗi xảy ra khi tìm kiếm: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreData() async {
    if (!_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    try {
      final response = await widget.onSearch(_searchController.text, _currentPage, _pageSize);
      List<dynamic> newResults = response['results'];
      int totalPages = response['totalPages'];

      setState(() {
        _displayedUsers.addAll(newResults);
        _hasMoreData = _currentPage < totalPages;
      });
    } catch (e) {
      // Xử lý lỗi
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Có lỗi xảy ra khi tải thêm dữ liệu: $e')),
      );
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  void _selectUser(dynamic user) {
    if (widget.maxSelections != null &&
        _selectedUsers.length >= widget.maxSelections! &&
        !_selectedUsers.any((u) => u["vienChucId"] == user["vienChucId"])) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Chỉ có thể chọn tối đa ${widget.maxSelections} người')),
      );
      return;
    }

    setState(() {
      if (_selectedUsers.any((selected) => selected["vienChucId"] == user["vienChucId"])) {
        _selectedUsers.removeWhere((selected) => selected["vienChucId"] == user["vienChucId"]);
      } else {
        _selectedUsers.add(user);
      }
    });
  }

  void _onSave() {
    Navigator.of(context).pop(_selectedUsers);
  }

  Widget _buildSearchField() {
    return TextField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: 'Tìm kiếm người dùng...',
        prefixIcon: const Icon(Icons.search),
        suffixIcon: _isLoading
            ? const Padding(
          padding: EdgeInsets.all(10.0),
          child: SizedBox(
            width: 15,
            height: 15,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        )
            : _searchController.text.isNotEmpty
            ? IconButton(
          icon: const Icon(Icons.clear),
          onPressed: () {
            _searchController.clear();
            setState(() {
              _displayedUsers = List.from(widget.availableUsers);
              _hasMoreData = false;
              _currentPage = 1;
            });
          },
        )
            : null,
        border: const OutlineInputBorder(),
      ),
    );
  }

  Widget _buildSelectedUsers() {
    if (_selectedUsers.isEmpty) {
      return Text('Chưa có người thực hiện nào được chọn.');
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _selectedUsers.map((user) {
        String name = '${user["ho"]} ${user["tenDem"]} ${user["ten"]}';
        String? avatarUrl = user["hinhAnh"];

        return Chip(
          avatar: avatarUrl != null
              ? CircleAvatar(
            backgroundImage: NetworkImage(avatarUrl),
            onBackgroundImageError: (_, __) {
              // Xử lý lỗi load ảnh
            },
          )
              : CircleAvatar(child: Text(name[0])),
          label: Text(name),
          deleteIcon: const Icon(Icons.close, size: 18),
          onDeleted: () => _selectUser(user),
        );
      }).toList(),
    );
  }

  Widget _buildUserList() {
    if (_displayedUsers.isEmpty && !_isLoading) {
      return Center(child: Text('Không có người dùng nào.'));
    }

    return ListView.builder(
      controller: _scrollController,
      itemCount: _displayedUsers.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _displayedUsers.length) {
          // Hiển thị chỉ báo tải thêm dữ liệu
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final user = _displayedUsers[index];
        String name = '${user["ho"]} ${user["tenDem"]} ${user["ten"]}';
        String? avatarUrl = user["hinhAnh"];
        final isSelected = _selectedUsers.any((u) => u["vienChucId"] == user["vienChucId"]);

        return ListTile(
          leading: avatarUrl != null
              ? CircleAvatar(
            backgroundImage: NetworkImage(avatarUrl),
            onBackgroundImageError: (_, __) {
              // Xử lý lỗi load ảnh
            },
          )
              : CircleAvatar(child: Text(name[0])),
          title: Text(name),
          subtitle: Text(user["tenChucVu"].toString().isEmpty ? 'Viên chức' : user["tenChucVu"].toString() == "Không chức vụ" ? 'Viên chức' : user["tenChucVu"].toString()),
          trailing: isSelected ? Icon(Icons.check, color: Colors.green) : null,
          onTap: () => _selectUser(user),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Material(
      // Đảm bảo các widget Material được hiển thị đúng
      type: MaterialType.transparency,
      child: Container(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.8, // Điều chỉnh chiều cao nếu cần
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.max, // Cho phép cột chiếm tối đa chiều cao
          children: [
            Text(
              'Chọn người thực hiện',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            _buildSearchField(),
            const SizedBox(height: 8),
            _buildSelectedUsers(),
            const SizedBox(height: 8),
            Expanded(
              child: _isLoading
                  ? Center(child: CircularProgressIndicator())
                  : _buildUserList(),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                MyButton.small(
                  onPressed: () => Navigator.of(context).pop(),
                  backgroundColor: theme.colorScheme.primaryContainer.withOpacity(0.2),
                  borderRadiusAll: 2,
                  borderColor: theme.colorScheme.secondary,
                  child: MyText.labelLarge('Hủy', color: theme.colorScheme.onSurface),
                ),
                const SizedBox(width: 8),
                MyButton.small(
                  onPressed: _onSave,
                  child: MyText.labelLarge('Lưu', color: theme.colorScheme.onPrimary, fontWeight: 700,),
                ),

              ],
            ),
          ],
        ),
      ),
    );
  }
}
