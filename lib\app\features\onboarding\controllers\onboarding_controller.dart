import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/features/onboarding/model/on_boarding_model.dart';
import 'package:tvumobile/app/utils/oauth2/src/credentials.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class OnboardingController extends GetxController {
  RxInt selectedPage = 0.obs;
  final pageController = PageController();
  final autoScrollDuration =
      const Duration(seconds: 4); // Adjust the duration as needed
  Timer? autoScrollTimer;
  Credentials? credential;
  /// Next
  forwardAction() {
    pageController.nextPage(duration: 300.milliseconds, curve: Curves.ease);
  }

  /// Previous
  backwardAction() {
    pageController.previousPage(duration: 300.milliseconds, curve: Curves.ease);
  }

  /// Auto scroll
  void startAutoScroll() {
    autoScrollTimer = Timer.periodic(autoScrollDuration, (timer) {
      if (selectedPage.value < onBoardingPages.length - 1) {
        selectedPage.value++;
        pageController.animateToPage(
          selectedPage.value,
          duration: 300.milliseconds,
          curve: Curves.ease,
        );
      } else {
        autoScrollTimer?.cancel();
      }
    });
  }

  /// List of Page
  List<OnBoardingModel> onBoardingPages = [
    OnBoardingModel(
        imageAsset: Onboarding.kBoard1,
        title:'TẬN TÂM',
        description:'Chúng tôi luôn đặt trải nghiệm và lợi ích của người dùng lên hàng đầu. Mỗi tính năng được thiết kế và mỗi quyết định được đưa ra đều với tâm huyết và mong muốn mang lại giá trị tốt nhất cho bạn.'),
    OnBoardingModel(
        imageAsset: Onboarding.kBoard2,
        title:'MINH BẠCH',
        description:'Trong mọi giao dịch và tương tác, chúng tôi cam kết duy trì sự rõ ràng và thẳng thắn. Bạn sẽ luôn được cập nhật đầy đủ thông tin, giúp bạn làm chủ quyết định của mình.'),
    OnBoardingModel(
        imageAsset: Onboarding.kBoard3,
        title:'THÂN THIỆN',
        description:'Giao diện dễ sử dụng, hỗ trợ nhanh chóng và tư vấn tận tình - chúng tôi nỗ lực tạo ra một môi trường ấm áp và chào đón, nơi mọi người dùng đều cảm thấy thoải mái và được trân trọng.'),
    OnBoardingModel(
        imageAsset: Onboarding.kBoard4,
        title:'SÁNG TẠO',
        description:'Chúng tôi không ngừng tìm tòi, phát triển những giải pháp sáng tạo để vượt qua giới hạn hiện tại, mang đến trải nghiệm mới mẻ và độc đáo cho người dùng.'),
  ];
  @override
  void onInit() {
    super.onInit();
    credential = LocalStorageServices.getCredential();
    //startAutoScroll();
  }

  @override
  void onClose() {
    autoScrollTimer?.cancel();
    super.onClose();
  }
}
