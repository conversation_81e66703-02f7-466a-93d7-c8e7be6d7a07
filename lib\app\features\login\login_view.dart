import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:tvumobile/app/features/login/login_controller.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'login_widgets.dart';

class LoginView extends GetView<LoginController> {
  const LoginView({super.key});


  @override
  Widget build(BuildContext context) {

    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const TopScreenImage2(),
              Obx(() {
                if(controller.logInWaiting.value == true) {
                  return const MyText.titleLarge(
                    "Vui lòng đợi...", textAlign: TextAlign.center,
                  );
                }
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(
                        right: 15.0, left: 15, bottom: 10, top: 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        const MyText.titleLarge(
                          "TRƯỜNG ĐẠI HỌC TRÀ VINH",
                          fontWeight: 900,
                        ),
                        const MyText.titleMedium(
                          "TẬN TÂM - MINH BẠCH - THÂN THIỆN - SÁNG TẠO",
                          fontSize: 14,
                          fontWeight: 700,
                          wordSpacing: -1,
                        ),
                        const MyText.bodyLarge(
                          'Vui đăng nhập nếu bạn là \nSinh viên, Giảng viên, Cán bộ viên chức đã có tài khoản email do nhà Trường cung cấp',
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Hero(
                          tag: 'login_btn',
                          child: MyButton.large(
                            onPressed: () async {
                              await controller.tvuOauthSignIn();
                              /*apiProvider.logInEd.listen((value) {
                              if (value) {
                                apiProvider.getUserInfo().then((value) =>
                                    Get.offNamed(Routes.SPLASH2,
                                        preventDuplicates: true));
                              }
                            });*/
                            },
                            borderRadiusAll: 12,
                            child: MyText.titleLarge('ĐĂNG NHẬP', color: theme.colorScheme.onPrimary,),
                          ),
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        const MyText.bodyLarge(
                          'hoặc bạn có thể sử dụng như là Khách',
                          textAlign: TextAlign.center,
                        ),
                        Hero(
                          tag: 'signup_btn',
                          child: MyButton.outlined(
                            onPressed: () async {

                            },
                            borderRadiusAll: 12,
                            borderColor: theme.colorScheme.onSurface,
                            child: MyText.titleLarge('TÌM HIỂU THÔNG TIN', color: theme.colorScheme.onSurface,),
                          ),
                        ),
                        const SizedBox(
                          height: 60,
                        ),
                      ],
                    ),
                  ),
                );
              })

            ],
          ),
        ),
      ),
    );
  }
}
