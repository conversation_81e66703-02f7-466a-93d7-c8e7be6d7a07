import 'package:intl/intl.dart';
import 'package:tvumobile/app/constans/app_constants.dart';

class Article {
  final int? id;
  final String? title;
  final String? content;
  final String? image;
  final String? video;
  final String? author;
  final String? avatar;
  final String? category;
  final String? date;
  final String? link;
  final int? catId;

  Article(
      {this.id,
        this.title,
        this.content,
        this.image,
        this.video,
        this.author,
        this.avatar,
        this.category,
        this.date,
        this.link,
        this.catId});

  factory Article.fromJson(Map<String, dynamic> json) {
    String content = json['content'] != null ? json['content']['rendered'] : "";

    String image = json['yoast_head_json']["og_image"] != ""
        ? json['yoast_head_json']["og_image"][0]["url"]
        : AppImages.defaultImgUrl;

    String video = json['custom'] != null ? json['custom']["td_video"] : "";

    String author = json['custom'] != null ? json['custom']["author"]["name"] : "";

    String avatar = json['custom'] != null ? json['custom']["author"]["avatar"] : "";

    String category = json["custom"] != null ? json["custom"]["categories"] != ""
        ? json["custom"]["categories"][0]["name"]
        : "" : "";

    int catId = json["categories"] != null ?
        json["categories"][0]
        : 0;

    String date = DateFormat('dd MMMM, yyyy', 'vi_VN')
        .format(DateTime.parse(json["date"]))
        .toString();

    return Article(
        id: json['id'],
        title: json['title']['rendered'],
        content: content,
        image: image,
        video: video,
        author: author,
        avatar: avatar,
        category: category,
        date: date,
        link: json["link"],
        catId: catId);
  }

  factory Article.fromDatabaseJson(Map<String, dynamic> data) => Article(
      id: data['id'],
      title: data['title'],
      content: data['content'],
      image: data['image'],
      video: data['video'],
      author: data['author'],
      avatar: data['avatar'],
      category: data['category'],
      date: data['date'],
      link: data['link'],
      catId: data["catId"]);

  Map<String, dynamic> toDatabaseJson() => {
    'id': id,
    'title': title,
    'content': content,
    'image': image,
    'video': video,
    'author': author,
    'avatar': avatar,
    'category': category,
    'date': date,
    'link': link,
    'catId': catId
  };
}