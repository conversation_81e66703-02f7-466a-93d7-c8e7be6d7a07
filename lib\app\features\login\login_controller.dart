import 'dart:convert';
import 'dart:math';

import 'package:corsac_jwt/corsac_jwt.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/utils/helpers/app_helpers.dart';
import 'package:tvumobile/app/utils/oauth2/src/handle_access_token_response.dart';
import 'package:tvumobile/app/utils/oauth2/src/parameters.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:flutter_web_auth_2/flutter_web_auth_2.dart';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;

class LoginController extends GetxController {
  final authorizationEndpoint =
  Uri.parse(AuthConfig.authorization_endpoint);
  final tokenEndpoint = Uri.parse(AuthConfig.token_endpoint);
  final identifier = AuthConfig.clientId;
  final secret = AuthConfig.clientSecrect;
  Uri redirectUrl = Uri.parse(AuthConfig.redirectUrl);
  final _scopes = AuthConfig.scopes;
  final tokens = LocalStorageServices.getAccessToken();
  late Uri _uri;
  RxBool logInEd = false.obs;
  RxBool logInWaiting = false.obs;
  // ignore: unused_field
  final _expirationGrace = const Duration(seconds: 10);
  final String _charset =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  late final String _codeVerifier;
  String _createCodeVerifier() => List.generate(
    128,
        (i) => _charset[Random.secure().nextInt(_charset.length)],
  ).join();
  ApiProvider apiProvider = Get.find();
  @override
  void onInit() {
    super.onInit();
    _codeVerifier = _createCodeVerifier();
    var codeChallenge = base64Url
        .encode(sha256.convert(ascii.encode(_codeVerifier)).bytes)
        .replaceAll('=', '');
    if(kIsWeb) {
      redirectUrl = Uri(scheme: Uri.base.scheme, host: Uri.base.host, port: Uri.base.port, path: "web/auth.html");
    }
    _uri = Uri.https(authorizationEndpoint.host, authorizationEndpoint.path, {
      'response_type': 'code',
      'client_id': AuthConfig.clientId,
      //'client_secret': AuthConfig.clientSecrect,
      'redirect_uri': redirectUrl.toString(),
      'scope': AuthConfig.scopes.join(" "),
      'code_challenge': codeChallenge,
      'code_challenge_method': 'S256'
    });
    Logger().i(_uri.toString());
  }
  Uri addQueryParameters(Uri url, Map<String, String> parameters) => url.replace(
      queryParameters: Map.from(url.queryParameters)..addAll(parameters));

  String basicAuthHeader(String identifier, String secret) {
    var userPass = '${Uri.encodeFull(identifier)}:${Uri.encodeFull(secret)}';
    return 'Basic ${base64Encode(ascii.encode(userPass))}';
  }
  Future<void> tvuOauthSignIn() async {
    var credentialsFile = LocalStorageServices.getCredentialString();
    if (credentialsFile != null && credentialsFile != "") {
      // ignore: unused_local_variable
      //var credentials = Credentials.fromJson(credentialsFile);
      //_client = Client(credentials, identifier: identifier, secret: secret, onCredentialsRefreshed: credentialsRefreshedCallback);
      logInEd.value = true;
      return;
    }
    logInWaiting.value = true;
    String result = "";
    try {
      result = await FlutterWebAuth2.authenticate(
        url: _uri.toString(),
        callbackUrlScheme: redirectUrl.scheme,
      );
    } catch (ex) {
      Logger().w(ex.toString());
      logInEd.value = false;
      logInWaiting.value = false;
      return;
    }
    final authorizationCode = Uri.parse(result).queryParameters['code'];
    var headers = <String, String>{};
    headers['Authorization'] = basicAuthHeader(identifier, secret);
    var body = {
      'grant_type': 'authorization_code',
      'code': authorizationCode,
      'redirect_uri': redirectUrl.toString(),
      'code_verifier': _codeVerifier,
      'client_id': AuthConfig.clientId,
      'client_secret': AuthConfig.clientSecrect
    };
    var startTime = DateTime.now();
    String delimiter = " ";
    final response = await http.post(AuthConfig.token_endpoint.toUri(), headers: headers, body: body);
    var getParameters = parseJsonParameters;
    var credentials = handleAccessTokenResponse(
        response, tokenEndpoint, startTime, _scopes, delimiter,
        getParameters: getParameters);
    apiProvider.setClient(credentials);
    await LocalStorageServices.setCredentialString(credentials.toJson());
    await LocalStorageServices.setAccessToken(credentials.accessToken);
    var decodedToken = JWT.parse(credentials.accessToken);
    //print(decodedToken.claims);
    UserInfo tmp = UserInfo.fromJson(decodedToken.claims);
    LocalStorageServices.setUserInfo(tmp);
    logInEd.value = true;
    logInWaiting.value = false;
    Get.offNamed(Routes.SPLASH2,
        preventDuplicates: true);
  }

  @override
  void onReady() {

  }

  @override
  void onClose() {

  }

}