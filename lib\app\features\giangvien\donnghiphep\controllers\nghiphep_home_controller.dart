import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepinfo.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepvienchuc.dart';
import 'package:tvumobile/app/services/api_provider.dart';

class NghiPhepHomeController extends GetxController {
  ApiProvider apiProvider = Get.find();
  late DonNghiPhepInfo donNghiPhepInfo;
  final danghi = RxDouble(-1);
  final conlai = RxDouble(-1);
  final donvi = ''.obs;
  final loadedds = false.obs;
  final isLanhDaoOrBGH = ''.obs;
  List<DonNghiPhepVienChuc> dsDNP = <DonNghiPhepVienChuc>[].obs;

  get scaffoldKey => null;

  @override
  void onInit() {
    super.onInit();
    if (kDebugMode) {
      print("NghiPhepHomeController onInit");
    }
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    if (kDebugMode) {
      print("NghiPhepHomeController onReady");
    }
    await checkLanhDaoOrBGH();
    await getDNPInfo();
    await getDsDNP();
  }

  @override
  void onClose() {}

  Future<void> checkLanhDaoOrBGH() async {
    try {
      final result = await apiProvider.checkLanhDaoOrBGH();
      isLanhDaoOrBGH.value = result;
      // isLanhDaoOrBGH.value = "ns";
    } catch (e) {
      if (kDebugMode) {
        print("Error checking LanhDao or BGH: $e");
      }
    }
    update();
  }

  Future<void> getDNPInfo() async {
    DonNghiPhepInfo? response = await apiProvider.getThongtinNghiPhep();
    donNghiPhepInfo = response;
    danghi.value = donNghiPhepInfo.soNgayDaNghi!;
    conlai.value = donNghiPhepInfo.soNgayConLai!;
    if (donNghiPhepInfo.donVi != null) {
      donvi.value = donNghiPhepInfo.donVi!.tenDonVi!;
    }
    update();
  }

  Future<void> getDsDNP() async {
    dsDNP.clear();
    List<DonNghiPhepVienChuc>? response = await apiProvider.getDsNghiPhep();
    if (response != null) {
      dsDNP.addAll(response);
      loadedds.value = true;
      update();
    } else {
      loadedds.value = true;
      update();
    }
  }

  void refreshData() async {
    dsDNP.clear();
    loadedds.value = false;
    danghi.value = -1;
    conlai.value = -1;
    await getDNPInfo();
    await getDsDNP();
    update();
  }
}
