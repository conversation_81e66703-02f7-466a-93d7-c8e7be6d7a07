// signature_dialogs.dart

// ignore_for_file: no_leading_underscores_for_local_identifiers, invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:signature/signature.dart';
import 'package:tvumobile/app/features/kyso/dynamic_document_sign_screen.dart';
import 'package:tvumobile/app/features/kyso/utils/helpers.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class SignatureDialogs {
  final DocumentSignScreenState screenState;
  SignatureDialogs(this.screenState);

  final _signatureController = SignatureController();
  bool _stampStyle = false;

  void showSignatureDialogue(BuildContext context) {
    var theme = Theme.of(context);
    bool _displayFullName = screenState.displayFullName;
    bool _displayDepartment = screenState.displayDepartment;
    bool _displayPosition = screenState.displayPosition;
    bool _displayDatetime = screenState.displayDatetime;
    _stampStyle = false; // Reset _stampStyle at the beginning of the dialog

    showDialog(
        context: context,
        builder: (c) {
          return StatefulBuilder(builder: (context, setStateDialog) {
            return Dialog(
              insetPadding:
                  const EdgeInsets.symmetric(horizontal: 15, vertical: 80),
              child: Container(
                width: 400,
                padding: const EdgeInsets.all(20),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          InkWell(
                            onTap: () {
                              _stampStyle =
                                  false; // Set _stampStyle to false for "Vẽ mới"
                              Navigator.pop(c);
                              _showDrawSignatureDialog(context);
                            },
                            child: Column(
                              children: [
                                Icon(LucideIcons.signature,
                                    size: 64, color: theme.primaryColor),
                                const SizedBox(height: 5),
                                MyText.bodyLarge(
                                  'Vẽ mới\nchữ ký',
                                  color: theme.colorScheme.onSurface,
                                  textAlign: TextAlign.center,
                                )
                              ],
                            ),
                          ),
                          const SizedBox(height: 40),
                          InkWell(
                            onTap: () {
                              _stampStyle =
                                  false; // Set _stampStyle to false for "Chọn ảnh"
                              Navigator.pop(c);
                              _showSelectSignatureUrlDialog(context);
                            },
                            child: Column(
                              children: [
                                Icon(LucideIcons.image,
                                    size: 64, color: theme.primaryColor),
                                const SizedBox(height: 5),
                                MyText.bodyLarge(
                                  'Chọn ảnh\nchữ ký',
                                  color: theme.colorScheme.onSurface,
                                  textAlign: TextAlign.center,
                                )
                              ],
                            ),
                          ),
                          const SizedBox(height: 40),
                          InkWell(
                            onTap: () {
                              _stampStyle =
                                  true; // Set _stampStyle to true for "Thông tin chữ ký"
                              // Automatically check all info checkboxes for stamp style
                              _displayFullName = true;
                              _displayDepartment = true;
                              _displayPosition = true;
                              _displayDatetime = true;
                              setStateDialog(
                                  () {}); // Update dialog UI immediately to reflect checkbox changes
                              Navigator.pop(c);
                            },
                            child: Column(
                              children: [
                                Icon(LucideIcons.imagePlus,
                                    size: 64, color: theme.primaryColor),
                                const SizedBox(height: 5),
                                MyText.bodyLarge(
                                  'Thông tin\nchữ ký',
                                  color: theme.colorScheme.onSurface,
                                  textAlign: TextAlign.center,
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      const Divider(),
                      const SizedBox(height: 10),
                      MyText.titleMedium('Thông tin hiển thị:',
                          fontWeight: 600),
                      CheckboxListTile(
                        title: MyText.bodyMedium('Họ và tên'),
                        value: _displayFullName,
                        onChanged: (bool? value) {
                          setStateDialog(() {
                            _displayFullName = value ?? false;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      CheckboxListTile(
                        title: MyText.bodyMedium('Phòng ban'),
                        value: _displayDepartment,
                        onChanged: (bool? value) {
                          setStateDialog(() {
                            _displayDepartment = value ?? false;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      CheckboxListTile(
                        title: MyText.bodyMedium('Chức vụ'),
                        value: _displayPosition,
                        onChanged: (bool? value) {
                          setStateDialog(() {
                            _displayPosition = value ?? false;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      CheckboxListTile(
                        title: MyText.bodyMedium('Thời gian ký'),
                        value: _displayDatetime,
                        onChanged: (bool? value) {
                          setStateDialog(() {
                            _displayDatetime = value ?? false;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          MyButton.small(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child:
                                MyText.bodyMedium('Huỷ', color: Colors.white),
                          ),
                          const SizedBox(width: 10),
                          MyButton.small(
                            onPressed: () {
                              screenState.displayFullName = _displayFullName;
                              screenState.displayDepartment =
                                  _displayDepartment;
                              screenState.displayPosition = _displayPosition;
                              screenState.displayDatetime = _displayDatetime;
                              screenState.setState(() {
                                screenState.stampStyle =
                                    _stampStyle; // Update main widget's _stampStyle
                              });
                              Navigator.pop(context);
                            },
                            child:
                                MyText.bodyMedium('Chọn', color: Colors.white),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          });
        });
  }

  void _showDrawSignatureDialog(BuildContext context) {
    showDialog(
        context: context,
        builder: (c) {
          return Dialog(
            insetPadding:
                const EdgeInsets.symmetric(horizontal: 10, vertical: 100),
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Signature(
                    controller: _signatureController,
                    width: 300,
                    height: 300,
                    backgroundColor: Colors.yellow.withOpacity(0.2),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      TextButton(
                        onPressed: () {
                          _signatureController.clear();
                        },
                        child: const Text('Xoá',
                            style: TextStyle(color: Colors.blue)),
                      ),
                      TextButton(
                        onPressed: () async {
                          final exportController = SignatureController(
                            penStrokeWidth: 2,
                            penColor: Colors.black,
                            exportBackgroundColor: Colors.white,
                            points: _signatureController.points,
                          );

                          screenState.signatureImageBytes =
                              await exportController.toPngBytes();

                          exportController.dispose();
                          screenState.setState(() {});
                          Navigator.pop(context);
                        },
                        child: const Text('Xác nhận',
                            style: TextStyle(color: Colors.blue)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        });
  }

  void _showSelectSignatureUrlDialog(BuildContext context) {
    showDialog(
        context: context,
        builder: (c) {
          return Dialog(
            insetPadding:
                const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            child: Container(
              height: 300,
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  MyText.titleMedium('Chọn chữ ký từ URL', fontWeight: 700),
                  const SizedBox(height: 10),
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: screenState.widget.availableSignatures.length,
                      itemBuilder: (context, index) {
                        final url =
                            screenState.widget.availableSignatures[index];
                        return ListTile(
                          leading: Image.network(
                            url,
                            width: 200,
                            errorBuilder: (context, error, stackTrace) =>
                                const Icon(
                              Icons.error_outline,
                              color: Colors.deepOrangeAccent,
                            ),
                            fit: BoxFit.contain,
                          ),
                          title: Text('Signature ${index + 1}'),
                          onTap: () async {
                            try {
                              screenState.signatureImageBytes =
                                  await networkImageToByte(url);
                              screenState.setState(() {});
                              Navigator.pop(context);
                            } catch (e) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                      content:
                                          Text('Không thể tải chữ ký từ URL')));
                              Navigator.pop(context);
                            }
                          },
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 10),
                  MyButton.small(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: MyText.bodyMedium('Huỷ', color: Colors.white),
                  ),
                ],
              ),
            ),
          );
        });
  }
}
