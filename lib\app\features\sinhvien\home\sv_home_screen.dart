import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/data/models/portal/portal_news_model.dart';
import 'package:tvumobile/app/data/models/wordpress/article.dart';
import 'package:tvumobile/app/features/sinhvien/home/<USER>';
import 'package:tvumobile/app/features/sinhvien/home/<USER>/sv_function_grid.dart';
import 'package:tvumobile/app/features/sinhvien/home/<USER>/sv_home_top.dart';
import 'package:tvumobile/app/shared/single_tvu_article.dart';
import 'package:tvumobile/app/shared_components/circular_cached_network_image.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_horizontal_list.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:url_launcher/url_launcher.dart';


class SvHome extends GetView<SvHomeController> {
  const SvHome({super.key});
  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: SizedBox(height: 220, child: SvHeaderTopComponent(controller)),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(
                            LucideIcons.bookCopy,
                            color: theme.colorScheme.onSurface,
                            size: theme.textTheme.labelLarge?.fontSize,
                          ),
                          MySpacing.width(6),
                          const MyText.labelLarge("Danh mục"),
                        ],
                      ),
                      Row(
                        children: [
                          Icon(
                            LucideIcons.slidersHorizontal,
                            color: theme.colorScheme.onSurface,
                            size: theme.textTheme.labelMedium?.fontSize,
                          ),
                          MySpacing.width(3),
                          const MyText.labelMedium("Tuỳ chỉnh")
                        ],
                      )
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.only(top: 20),
                    child: SvFunctionGrid(
                      homeController: controller,
                    ),
                  ),
                  MySpacing.height(16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(LucideIcons.stickyNote,
                              color: theme.colorScheme.onSurface,
                              size: theme.textTheme.labelMedium?.fontSize),
                          MySpacing.width(6),
                          const MyText.labelLarge(
                            'Tuyển sinh',
                            fontWeight: 700,
                          ),
                        ],
                      ),
                      MyButton.text(
                        onPressed: () async {
                          final Uri url = Uri.parse("https://daotao.tvu.edu.vn/");
                          if (!await launchUrl(url)) {
                            if (kDebugMode) {
                              print('Could not launch $url');
                            }
                          }
                        },
                        child: Row(
                          children: [
                            const MyText.labelMedium(
                              'Xem tất cả',
                              fontWeight: 500,
                              xMuted: true,
                            ),
                            Icon(
                              Icons.navigate_next,
                              color: theme.colorScheme.onSurface,
                              size: 20,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Obx(() {
                    if (controller.tsNews.isEmpty) {
                      return MyContainer(
                          width: 180, child: SkeletonListTile());
                    }
                    return _buildTsNewList(controller.tsNews);
                  }),
                  MySpacing.height(16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(
                            LucideIcons.appWindow,
                            color: theme.colorScheme.onSurface,
                            size: theme.textTheme.labelMedium?.fontSize,
                          ),
                          MySpacing.width(6),
                          const MyText.labelLarge(
                            'Tin mới nhất',
                            fontWeight: 700,
                          ),
                        ],
                      ),
                      MyButton.text(
                        onPressed: () async {
                          final Uri url = Uri.parse("https://www.tvu.edu.vn/");
                          if (!await launchUrl(url)) {
                            if (kDebugMode) {
                              print('Could not launch $url');
                            }
                          }
                        },
                        child: Row(
                          children: [
                            const MyText.labelMedium(
                              'Xem tất cả',
                              fontWeight: 500,
                              xMuted: true,
                            ),
                            Icon(
                              Icons.navigate_next,
                              color: theme.colorScheme.onSurface,
                              size: 20,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Obx(() {
                    if (controller.latestArticles.isEmpty) {
                      return Row(
                        children: [
                          MyContainer(
                              width: 160,
                              height: 160,
                              child: SkeletonParagraph()),
                          MySpacing.width(16),
                        ],
                      );
                    }
                    //return _buildPortalNewList(controller.portalNews);
                    return _buildLatestPosts(controller.latestArticles);
                  }),
                  MySpacing.height(54),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }



  Widget _buildSingleTsNew({int? index, PortalNewsModel? doc}) {
    var theme = Get.theme;

    return Padding(
      padding: MySpacing.right(16),
      child: MyContainer(
        width: 320,
        height: 96,
        margin: const EdgeInsets.only(top: 8, bottom: 8, left: 8),
        padding: const EdgeInsets.all(10),
        color: theme.cardColor,
        borderRadiusAll: 12,
        borderColor: Colors.grey.withOpacity(0.2),
        bordered: true ,
        onTap: () async {
          final Uri url = Uri.parse(doc.urlSlug.toString());
          if (!await launchUrl(url)) {
            if (kDebugMode) {
              print('Could not launch $url');
            }
          }
        },
        //decoration: boxDecorationRoundedWithShadow(12, backgroundColor: theme.cardColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MyText.bodyMedium(
              doc!.title.toString(),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            MySpacing.height(8),
            Row(
              children: [
                const Icon(Icons.schedule, size: 12),
                MyText.labelMedium(
                  doc.publishDate.toString().substring(0, 10),
                  fontWeight: 600,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  softWrap: true,
                  xMuted: true,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTsNewList(List<PortalNewsModel> cvn) {
    return MyHorizontalList(
      padding: const EdgeInsets.all(0.0),
      wrapAlignment: WrapAlignment.spaceEvenly,
      itemCount: cvn.length,
      itemBuilder: (BuildContext context, int i) {
        return _buildSingleTsNew(
          index: i,
          doc: cvn[i],
          //categoryIcon: cvn[i].categoryIcon
        );
      },
    );
  }

  Widget getProfileImage() {
    var userInfo = controller.userInfo!;
    if (userInfo.picture == null) {
      return Image.asset(
        'assets/images/app_icon_150.png',
        height: 100,
        width: 100,
        opacity: const AlwaysStoppedAnimation(.89),
      );
    }
    return CircularCachedNetworkImage(
      imageURL: userInfo.picture.toString(),
      size: 100,
      borderColor: Colors.transparent,
      fit: BoxFit.cover,
      alignment: Alignment.topCenter,
    );
  }

  Widget _buildLatestPosts(List<dynamic> cvn) {
    return MyHorizontalList(
      padding: const EdgeInsets.all(0.0),
      wrapAlignment: WrapAlignment.spaceEvenly,
      itemCount: cvn.length,
      itemBuilder: (BuildContext context, int i) {
        return  _buildSingleLatestPosts(index: i, doc: cvn[i]);
      },
    );
  }

  Widget _buildSingleLatestPosts({int? index, dynamic doc}) {
    var theme = LocalStorageServices.getThemeIsLight() ? AppTheme.light : AppTheme.dark;
    Article article = doc as Article;
    String heroKey ="$index-latest";
    return Padding(
      padding: MySpacing.right(16),
      child: MyContainer(
        width: 300,
        height: 256,
        margin: const EdgeInsets.only(top: 8, bottom: 8, left: 8),
        padding: const EdgeInsets.all(0),
        color: theme.cardColor,
        borderRadiusAll: 12,
        borderColor: Colors.grey.withOpacity(0.2),
        bordered: true ,
        onTap: () {
          //Get.to(() => SinglePortalNewsScreen(doc, heroKey, controller.portalNews), transition: Transition.fadeIn);
          Get.to(() =>  SingleTVUArticle(article, heroKey), transition: Transition.fadeIn);
        },
        //decoration: boxDecorationRoundedWithShadow(12, backgroundColor: theme.cardColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Hero(
                tag: heroKey,
                child: Container(
                  height: (260 / 16) * 9,
                  decoration: BoxDecoration(
                      color: Colors
                          .transparent, //bgColor[_random.nextInt(bgColor.length)],
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                          image: NetworkImage(article.image == ""
                              ? AppImages.defaultImgUrl
                              : article.image.toString()),
                          fit: BoxFit.cover)),
                )),

            Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                children: [
                  MyText.titleMedium(
                    article.title.toString(),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  MySpacing.height(8),
                  Row(
                    children: [
                      const Icon(Icons.schedule, size: 12),
                      MyText.labelMedium(
                        article.date.toString().substring(0, 10),
                        softWrap: true,
                        xMuted: true,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

