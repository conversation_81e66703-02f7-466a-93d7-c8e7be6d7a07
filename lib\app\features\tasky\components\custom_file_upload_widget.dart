import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';

// Tạo GlobalKey type để truy cập state từ bên ngo<PERSON>i
class FileUploadWidgetController {
  Future<void> Function()? startUpload;
  List<File> Function()? getSelectedFiles;
  void Function()? clearFiles;
}

class CustomFileUploadWidget extends StatefulWidget {
  final List<String> allowedExtensions;
  final Function(List<File>)? onFilesSelected;
  final Function(List<File>) onUploadPressed;
  final double maxFileSize; // In MB
  final bool allowMultiple;
  final FileUploadWidgetController? controller;
  final Function(double)? onUploadProgress;
  
  const CustomFileUploadWidget({
    super.key,
    this.allowedExtensions = const ['pdf', 'doc', 'docx', 'jpg', 'png'],
    this.onFilesSelected,
    required this.onUploadPressed,
    this.maxFileSize = 10,
    this.allowMultiple = true,
    this.controller,
    this.onUploadProgress,
  });

  @override
  // ignore: library_private_types_in_public_api
  _CustomFileUploadWidgetState createState() => _CustomFileUploadWidgetState();
}

class _CustomFileUploadWidgetState extends State<CustomFileUploadWidget> {
  List<File> selectedFiles = [];
  bool isUploading = false;
  double uploadProgress = 0.0;

  @override
  void initState() {
    super.initState();
    if (widget.controller != null) {
      widget.controller!.startUpload = startUpload;
      widget.controller!.getSelectedFiles = () => selectedFiles;
      widget.controller!.clearFiles = () {
        setState(() {
          selectedFiles.clear();
        });
      };
    }
  }

  Future<void> pickFiles() async {
    if (isUploading) return;
    
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: widget.allowedExtensions,
        allowMultiple: widget.allowMultiple,
      );

      if (result != null) {
        List<File> files = result.paths.map((path) => File(path!)).toList();
        
        // Kiểm tra kích thước file
        bool isValidSize = files.every((file) => 
          file.lengthSync() / (1024 * 1024) <= widget.maxFileSize);
        
        if (!isValidSize) {
          // ignore: use_build_context_synchronously
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('File size should not exceed ${widget.maxFileSize}MB'),
            ),
          );
          return;
        }

        setState(() {
          selectedFiles = files;
        });
        
        widget.onFilesSelected?.call(files);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error picking files: $e');
      }
    }
  }

  IconData getFileIcon(String fileName) {
    String extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'jpg':
      case 'jpeg':
      case 'png':
        return Icons.image;
      default:
        return Icons.insert_drive_file;
    }
  }

  Future<void> startUpload() async {
    if (selectedFiles.isEmpty || isUploading) return;

    setState(() {
      isUploading = true;
      uploadProgress = 0;
    });

    try {
      await widget.onUploadPressed(selectedFiles);
      
      // Giả lập tiến trình upload - bạn có thể thay thế bằng progress thực tế
      for (var i = 0; i <= 100; i++) {
        await Future.delayed(Duration(milliseconds: 50));
        setState(() {
          uploadProgress = i / 100;
        });
        widget.onUploadProgress?.call(uploadProgress);
      }
    } catch (e) {
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Upload failed: $e')),
      );
    } finally {
      setState(() {
        isUploading = false;
        uploadProgress = 0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Upload button area
            InkWell(
              onTap: pickFiles,
              child: Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  border: Border.all(color: theme.primaryColor),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Icon(Icons.cloud_upload, 
                      size: 48, 
                      color: theme.primaryColor
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Click to select files',
                      style: theme.textTheme.bodyLarge,
                    ),
                    Text(
                      'Supported formats: ${widget.allowedExtensions.join(", ")}',
                      style: theme.textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),
            
            // Selected files list
            if (selectedFiles.isNotEmpty) ...[
              SizedBox(height: 16),
              ListView.builder(
                shrinkWrap: true,
                itemCount: selectedFiles.length,
                itemBuilder: (context, index) {
                  final file = selectedFiles[index];
                  return ListTile(
                    leading: Icon(getFileIcon(file.path)),
                    title: Text(file.path.split('/').last),
                    subtitle: Text(
                      '${(file.lengthSync() / (1024 * 1024)).toStringAsFixed(2)} MB'
                    ),
                    trailing: IconButton(
                      icon: Icon(Icons.close),
                      onPressed: isUploading ? null : () {
                        setState(() {
                          selectedFiles.removeAt(index);
                        });
                      },
                    ),
                  );
                },
              ),
            ],
            
            // Progress bar
            if (isUploading) ...[
              SizedBox(height: 16),
              LinearProgressIndicator(
                value: uploadProgress,
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation<Color>(theme.primaryColor),
              ),
              SizedBox(height: 8),
              Text('Uploading: ${(uploadProgress * 100).toInt()}%'),
            ],
          ],
        ),
      ),
    );
  }
}
