import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/data/models/tms/congvan/cong_van_nhan.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';

// ignore: must_be_immutable
class CongVanNhanList extends StatelessWidget {
  CongVanNhanList({super.key, required this.portalNews});
  List<TmsCongVanModel> portalNews;

  // ignore: unused_field
  final _random = Random();
  final List<Color> bgColor = [
    const Color(0xFFFFE2C2),
    const Color(0xFFD9839F),
    const Color(0xFFFFE2C2)
  ];

  @override
  Widget build(BuildContext context) {
    var theme = Get.theme;
    //printInfo(info: portalNews.toString());
    if (portalNews.length > 5) {
      portalNews = portalNews.sublist(5);
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListView.builder(
            padding: EdgeInsets.zero,
            scrollDirection: Axis.horizontal,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: portalNews.length,
            itemBuilder: (ctx, index) {
              return InkWell(
                  onTap: () {
                    //Get.to(() => SingleCongVanNhanScreen(portalNews[index], heroKey), preventDuplicates: true, transition: Transition.fadeIn);
                  },
                  child: Container(
                    margin: EdgeInsets.zero,
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 13),
                    // border only from top and bottom
                    decoration: BoxDecoration(
                        border: Border(
                      bottom: BorderSide(
                          color: theme.dividerColor.withOpacity(0.4)),
                      top: BorderSide(
                        color: Get.isDarkMode
                            ? const Color(0xFF414141)
                            : const Color(0xFFF6F6F6),
                      ),
                    )),
                    child: Row(
                      children: [
                        Expanded(
                            child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              portalNews[index].document!.trichYeu.toString(),
                              style: theme.textTheme.titleMedium,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            MySpacing.height(4),
                            Row(
                              children: [
                                Icon(LucideIcons.calendar),
                                MySpacing.width(4),
                                Text(
                                  portalNews[index].ngayCapNhat.toString(),
                                  style: theme.textTheme.titleSmall,
                                ),
                              ],
                            ),
                            MySpacing.height(6),
                          ],
                        ))
                      ],
                    ),
                  ));
            }),
      ],
    );
  }
}
