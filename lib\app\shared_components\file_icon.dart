import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:tvumobile/app/utils/extensions/string.dart';

class FileIcon extends StatelessWidget {
  final String fileName;
  final double size;
  final Color? color;

  FileIcon(String fileName, {super.key, required this.size, this.color})
      : fileName = fileName.toLowerCase();

  @override
  Widget build(BuildContext context) {
    if(color != null) {
    }
    var tmp = fileName.getFileExt;
    switch(tmp.toUpperCase()) {
      case "PDF":
        return FaIcon(FontAwesomeIcons.filePdf, size: size, color: color,);
      case "DOCX":
      case "DOC":
        return FaIcon(FontAwesomeIcons.fileWord, size: size, color: color,);
      case "XLS":
      case "XLSX":
        return FaIcon(FontAwesomeIcons.fileExcel, size: size, color: color,);

    }
    return FaIcon(FontAwesomeIcons.gamepad, size: size, color: color,);
  }
}