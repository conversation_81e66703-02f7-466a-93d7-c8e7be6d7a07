// intro_guide_screen.dart
import 'package:flutter/material.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';

class IntroGuideScreen extends StatelessWidget {
  const IntroGuideScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: MyText.titleMedium('Hướng dẫn ký tài liệu',
            fontWeight: 900, color: Colors.white),
        backgroundColor: theme.primaryColor,
        iconTheme: IconThemeData(color: Colors.white), // Set back button color
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            _buildInstructionTile(
              icon: LucideIcons.fileBox,
              title: '1. <PERSON><PERSON><PERSON> li<PERSON> PDF đã được tải.',
              subtitle: '<PERSON><PERSON><PERSON> có thể xem nội dung tài liệu.',
              theme: theme,
            ),
            _buildInstructionTile(
              icon: LucideIcons.mousePointerClick,
              title: '2. Thêm chữ ký.',
              subtitle: 'Chạm vào vị trí bất kỳ trên tài liệu để thêm chữ ký.',
              theme: theme,
            ),
            _buildInstructionTile(
              icon: LucideIcons.signature,
              title: '3. Chọn chữ ký khác.',
              subtitle:
                  'Nhấn vào nút "Chọn chữ ký khác" để vẽ chữ ký mới, chọn ảnh chữ ký hoặc thêm thông tin chữ ký.',
              theme: theme,
            ),
            _buildInstructionTile(
              icon: LucideIcons.move,
              title: '4. Di chuyển chữ ký.',
              subtitle: 'Kéo chữ ký để di chuyển đến vị trí mong muốn.',
              theme: theme,
            ),
            _buildInstructionTile(
              icon: LucideIcons.rectangleEllipsis,
              title: '5. Thay đổi kích thước chữ ký.',
              subtitle:
                  'Kéo các chấm tròn ở góc chữ ký để thay đổi kích thước.',
              theme: theme,
            ),
            _buildInstructionTile(
              icon: LucideIcons.circleCheck,
              title: '6. Xác nhận ký.',
              subtitle:
                  'Nhấn nút "Xác nhận ký" để hoàn tất và thêm chữ ký vào tài liệu.',
              theme: theme,
            ),
            _buildInstructionTile(
              icon: LucideIcons.circleX,
              title: '7. Hủy ký.',
              subtitle: 'Nhấn nút "Hủy ký" để hủy bỏ quá trình ký tài liệu.',
              theme: theme,
            ),
            _buildInstructionTile(
              icon: LucideIcons.arrowLeftRight,
              title: '8. Điều hướng trang.',
              subtitle:
                  'Sử dụng các nút điều hướng để chuyển đổi giữa các trang.',
              theme: theme,
            ),
            _buildInstructionTile(
              icon: LucideIcons.zoomIn,
              title: '9. Phóng to/Thu nhỏ.',
              subtitle:
                  'Sử dụng các nút phóng to và thu nhỏ để xem tài liệu chi tiết hơn.',
              theme: theme,
            ),
            SizedBox(height: 20),
            Center(
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Go back to the previous screen
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.primaryColor,
                ),
                child: MyText.bodyMedium('Đã hiểu', color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required ThemeData theme,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: ListTile(
        leading: Icon(icon, color: theme.primaryColor, size: 32),
        title: MyText.bodyLarge(title),
        subtitle: MyText.bodyMedium(subtitle),
      ),
    );
  }
}

// Tour Guide Style Help Dialog Widget (One Step Per Page)
void showHelpDialog(BuildContext context) {
  final PageController pageController = PageController();
  int currentPageIndex = 0;

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return StatefulBuilder(
        builder: (BuildContext context, StateSetter setState) {
          return AlertDialog(
            title: Text(
              'Hướng dẫn ký tài liệu',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            content: SizedBox(
              height: 450, // Increased height to accommodate more text per page
              width: 400,
              child: PageView(
                controller: pageController,
                physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (index) {
                  setState(() {
                    currentPageIndex = index;
                  });
                },
                children: [
                  _buildHelpPage(
                    // Page 1: Choose Signature
                    context: context,
                    stepNumber: 1,
                    title: 'Chọn chữ ký của bạn',
                    description:
                        'Để bắt đầu ký tài liệu, bạn cần chọn chữ ký mà bạn muốn sử dụng.',
                    helperText:
                        'Nhấn vào nút "Chọn chữ ký khác" ngay bên dưới chữ ký hiện tại của bạn. Nút này sẽ mở ra các tùy chọn để bạn tạo hoặc chọn chữ ký.',
                    icon: LucideIcons.signature,
                  ),
                  _buildHelpPage(
                    // Page 2: Navigate to Page
                    context: context,
                    stepNumber: 2,
                    title: 'Tìm trang cần ký',
                    description:
                        'Di chuyển đến trang của tài liệu PDF mà bạn muốn thêm chữ ký vào.',
                    helperText:
                        'Sử dụng các nút điều hướng trang (Trang đầu, Trang trước, Trang sau, Trang cuối) ở phía trên màn hình để lướt qua tài liệu và tìm trang thích hợp.',
                    icon: LucideIcons.file,
                  ),
                  _buildHelpPage(
                    // Page 3: Add Signature
                    context: context,
                    stepNumber: 3,
                    title: 'Thêm chữ ký vào tài liệu',
                    description:
                        'Bây giờ, hãy đặt chữ ký của bạn vào vị trí mong muốn trên trang tài liệu.',
                    helperText:
                        'Nhấn vào bất kỳ vị trí nào trên trang PDF mà bạn muốn chữ ký xuất hiện. Chữ ký của bạn sẽ được thêm vào vị trí bạn chọn.',
                    icon: LucideIcons.circlePlus,
                  ),
                  _buildHelpPage(
                    // Page 4: Adjust Signature
                    context: context,
                    stepNumber: 4,
                    title: 'Điều chỉnh vị trí và kích thước',
                    description:
                        'Bạn có thể di chuyển hoặc thay đổi kích thước chữ ký để nó vừa vặn với tài liệu.',
                    helperText:
                        '• Để di chuyển chữ ký: Chạm và giữ vào chữ ký, sau đó kéo nó đến vị trí mới.\n• Để thay đổi kích thước: Sử dụng các chấm tròn nhỏ ở góc chữ ký để kéo và điều chỉnh kích thước cho phù hợp.',
                    icon: LucideIcons.move,
                  ),
                  _buildHelpPage(
                    // Page 5: Confirm Signature
                    context: context,
                    stepNumber: 5,
                    title: 'Xác nhận chữ ký của bạn',
                    description:
                        'Khi bạn đã hài lòng với chữ ký và vị trí của nó, hãy xác nhận để lưu chữ ký vào tài liệu.',
                    helperText:
                        'Tìm và nhấn vào biểu tượng dấu kiểm "✓" (Xác nhận ký) ở góc trên bên phải của màn hình (trên thanh AppBar). Thao tác này sẽ nhúng chữ ký vào tài liệu PDF.',
                    icon: LucideIcons.circleCheck,
                  ),
                  _buildHelpPage(
                    // Page 6: Cancel Signing (Optional)
                    context: context,
                    stepNumber: 6,
                    title: 'Hủy bỏ nếu cần',
                    description:
                        'Nếu bạn quyết định không ký tài liệu này, bạn có thể hủy bỏ quá trình ký.',
                    helperText:
                        'Nếu bạn không muốn lưu các thay đổi, hãy nhấn vào nút "Hủy ký" ở cuối màn hình. Điều này sẽ đưa bạn trở lại mà không lưu chữ ký vào tài liệu.',
                    icon: LucideIcons.circleX,
                    isLastPage:
                        true, // Indicate this is the last page for button logic
                    additionalContent: [
                      // Example of additional content on the last page
                      const SizedBox(height: 20),
                      Text(
                        'Mẹo nhỏ:',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      Text(
                        'Bạn có thể tùy chỉnh thông tin hiển thị trên chữ ký (ví dụ: tên, chức danh) thông qua tùy chọn "Chọn chữ ký khác".',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            actions: <Widget>[
              ButtonBar(
                // Using ButtonBar for better alignment control
                alignment: MainAxisAlignment
                    .spaceBetween, // Align buttons to the end (right)
                children: [
                  TextButton(
                    // "Đóng" button is now last to appear visually on the right
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary),
                    // "Đóng" button is now last to appear visually on the right
                    child: const Text('Đóng', style: TextStyle(fontSize: 16)),
                  ),
                  SizedBox(width: 40),
                  if (currentPageIndex > 0)
                    TextButton(
                      onPressed: () {
                        pageController.previousPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      },
                      style: TextButton.styleFrom(
                          foregroundColor:
                              Theme.of(context).colorScheme.primary),
                      child:
                          const Text('« Trước', style: TextStyle(fontSize: 16)),
                    ),
                  if (currentPageIndex <
                      5) // Check against the last page index (5 for 6 pages)
                    TextButton(
                      onPressed: () {
                        pageController.nextPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      },
                      style: TextButton.styleFrom(
                          foregroundColor:
                              Theme.of(context).colorScheme.primary),
                      child: const Text('Tiếp theo »',
                          style: TextStyle(fontSize: 16)),
                    ),
                ],
              ),
            ],
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          );
        },
      );
    },
  );
}

// Helper widget for a help page (now with step number, title, helper text)
Widget _buildHelpPage({
  required BuildContext context,
  required int stepNumber,
  required String title,
  required String description,
  required String helperText,
  required IconData icon,
  bool isLastPage = false,
  List<Widget> additionalContent = const [],
}) {
  return SingleChildScrollView(
    child: Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: ListBody(
        children: [
          Text(
            'Bước $stepNumber: $title', // Step number and title
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(fontWeight: FontWeight.bold), // Bold title
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Icon(icon,
              color: Theme.of(context).colorScheme.primary,
              size: 64), // Larger step icon
          const SizedBox(height: 20),
          Text(
            description, // Main description
            style: Theme.of(context)
                .textTheme
                .bodyLarge
                ?.copyWith(fontSize: 16), // Slightly larger body text
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Container(
            // Box for helper text to visually separate it
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade100, // Light background for helper text
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300), // Subtle border
            ),
            child: Text(
              helperText, // Helper text - more instructional
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontStyle: FontStyle.italic,
                  fontSize: 16), // Italic style for helper text
              textAlign: TextAlign.justify,
            ),
          ),
          ...additionalContent, // Add any additional content, like tips on the last page
        ],
      ),
    ),
  );
}

  // (Keep _buildHelpStep widget removed as it's no longer directly used - all content in _buildHelpPage)