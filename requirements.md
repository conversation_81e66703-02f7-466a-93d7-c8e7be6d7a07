# Flutter Application Architecture Restructuring Requirements

## 1. Current Architecture Analysis

The current Flutter application located at `/d:/TVU_Mobile/Projects/uppgrade/tvumobile/lib/` exhibits a mixed architectural pattern. It combines elements of feature-based organization within the `features` directory with a more traditional layered approach for common functionalities.

### Directory Structure Overview:

-   **`app/`**: This seems to be the core application directory, containing various subdirectories for different concerns.
    -   **`config/`**: Contains application-wide configurations such as routing (`routes`), theming (`themes`), and internationalization (`translation`). This aligns with a layered approach for cross-cutting concerns.
    -   **`constans/`**: Houses various constants like API paths, app constants, assets paths, and authentication configurations. This is a common utility layer.
    -   **`data/`**: Divided into `local` (for local storage like Hive and SharedPreferences) and `models` (for data models, further categorized by domain like `auth`, `edusoft`, `fcm`, `portal`, `response`, `sch`, `tasky`, `tms`, `wordpress`). This represents the data layer, but models are tightly coupled with specific domains.
    -   **`features/`**: This is the primary feature-based organization. Each subdirectory within `features` (e.g., `danhgia`, `dashboard`, `giangvien`, `kyso`, `login`, `navbar`, `onboarding`, `projects`, `setting`, `sinhvien`, `splash`, `tasky`) represents a distinct feature module. Inside each feature, there are often `bindings`, `controllers`, and `views` (or `screens`), following a pattern like GetX or similar MVVM/MVC variants.
    -   **`services/`**: Contains various service classes like `api_provider`, `connectivity_controller`, `exception_handler`, `local_storage_services`, `native_api_services`, `notification_service`, `rest_api_services`, `url_handle_service`. This is a clear service layer.
    -   **`shared/`**: Includes shared UI components and styles that are used across multiple features.
    -   **`shared_components/`**: Similar to `shared/`, containing reusable UI widgets and components.
    -   **`utils/`**: Provides utility functions and extensions, further categorized into `extensions`, `helpers`, `localizations`, `mixins`, `oauth2`, `theme`, `ui`, `ui_utils`.
-   **Root `lib/` files**: `authmiddleware.dart`, `deeplinkparser.dart`, `loading_effect.dart`, `connection_fail.dart`, `firebase_options.dart`, `main.dart` are directly under `lib`, indicating some global or entry-point level files.

### Observations and Challenges:

-   **Mixed Organization**: While `features` promotes modularity, other top-level directories (`config`, `constans`, `data`, `services`, `shared`, `shared_components`, `utils`) suggest a more traditional layered approach. This can lead to confusion about where new code should reside.
-   **Coupling**: Models within `data/models` are grouped by domain, but their usage might be spread across multiple features, potentially leading to tight coupling between the data layer and specific features.
-   **Lack of Clear Domain Layer**: There isn't a distinct "domain" or "business logic" layer explicitly defined, which can lead to business rules being scattered across controllers or views.
-   **Scalability**: As the application grows, managing dependencies and ensuring separation of concerns might become challenging with the current mixed approach.

## 2. Proposed Clean Architecture with Feature-Based Organization

To simplify and improve the maintainability, testability, and scalability of the application, we propose adopting a Clean Architecture pattern with a strong emphasis on feature-based organization.

### Core Principles:

-   **Separation of Concerns**: Clearly define responsibilities for each layer.
-   **Dependency Rule**: Dependencies should always point inwards. The inner layers should not know anything about the outer layers.
-   **Testability**: Business rules should be testable independently of UI, database, or external services.
-   **Feature-First**: Organize code primarily by feature, with each feature being a self-contained module.

### Proposed Layered Structure:

Each feature will ideally contain the following layers:

1.  **Presentation Layer (UI & ViewModels/Controllers)**:
    -   Responsible for displaying data and handling user input.
    -   Contains `views` (widgets/screens) and `controllers` (or `view_models` / `blocs` / `providers`) that manage UI state and interact with the Domain layer.
    -   Dependencies: Depends on the Domain layer.

2.  **Domain Layer (Entities, Use Cases, Repositories Interfaces)**:
    -   The core of the application, containing business logic and rules.
    -   **Entities**: Plain Dart objects representing the core business concepts (e.g., `User`, `Product`). These should be independent of any framework or data source.
    -   **Use Cases (Interactors)**: Encapsulate specific business operations. They orchestrate the flow of data to and from the entities and repositories. (e.g., `LoginUserUseCase`, `GetProductListUseCase`).
    -   **Repositories Interfaces**: Abstract contracts defining how data is retrieved and stored. These are defined in the Domain layer but implemented in the Data layer.
    -   Dependencies: No dependencies on outer layers. It's the innermost circle.

3.  **Data Layer (Repositories Implementations, Data Sources, Models)**:
    -   Responsible for data retrieval and persistence.
    -   **Repositories Implementations**: Concrete implementations of the repository interfaces defined in the Domain layer. They coordinate data from various data sources.
    -   **Data Sources**: Abstract the origin of data (e.g., `LocalDataSource` for Hive/SharedPreferences, `RemoteDataSource` for API calls).
    -   **Models**: Data structures that map directly to external data formats (e.g., JSON from an API, database schema). These models are then converted to Domain Entities.
    -   Dependencies: Depends on the Domain layer (for entities and interfaces) and external packages/APIs.

### Proposed Feature-Based Directory Structure:

```
lib/
├── core/ (Application-wide common components, independent of specific features)
│   ├── config/ (App-wide configurations, themes, routes, translations)
│   ├── constants/ (Global constants)
│   ├── errors/ (Failure/Exception handling)
│   ├── network/ (Dio setup, interceptors, base API client)
│   ├── utils/ (General utility functions, extensions)
│   └── shared_components/
│       ├── atoms/
│       ├── molecules/
│       └── organisms/
├── features/
│   ├── <feature_name_1>/ (e.g., auth, dashboard, product)
│   │   ├── models/
│   │   ├── apis/
│   │   ├── controllers/
│   │   ├── views/
│   │   └── widgets/
│   ├── <feature_name_2>/
│   │   ├── ...
│   └── ...
├── main.dart
└── app_bindings.dart (Global bindings/dependency setup)
```

## 3. Transformation Outline

This section will detail the steps to migrate the existing codebase to the new architecture.

### General Steps:

1.  **Create `core/` directory**: Move existing application-wide configurations, constants, utilities, and truly shared UI components into this new directory.
    -   `app/config/` -> `core/config/`
    -   `app/constans/` -> `core/constants/`
    -   `app/utils/` -> `core/utils/`
    -   `app/shared/` and `app/shared_components/` -> `core/shared_components/` (consolidate and refactor)
    -   `app/services/` -> `core/network/` (for API related services), `core/utils/` (for connectivity, local storage, notification services, etc. - these might become part of data sources or domain use cases).
2.  **Refactor `data/models`**: For each domain model currently in `app/data/models`, identify its primary feature. Create corresponding `entities` in the `domain` layer of that feature and `models` in the `data` layer of that feature. Implement conversion methods between models and entities.
3.  **Migrate Existing Features**: For each feature under `app/features/`:
    -   Create the `data/`, `domain/`, and `presentation/` subdirectories within the feature's folder.
    -   **Presentation**: Move existing `views`, `controllers`, and feature-specific widgets into `presentation/pages`, `presentation/controllers`, and `presentation/widgets` respectively.
    -   **Domain**: Extract business logic from controllers into `usecases`. Define `entities` and `repositories` interfaces based on the feature's domain.
    -   **Data**: Implement the `repositories` interfaces. Create `datasources` (remote and local) and move feature-specific data models into `data/models`.
4.  **Dependency Injection**: Establish a consistent dependency injection strategy (e.g., GetX bindings, GetIt, Provider) to manage dependencies between layers and features.
5.  **Error Handling**: Implement a centralized error handling mechanism using `core/errors/` for custom exceptions and failures.
6.  **Routing**: Ensure the routing system (`app/config/routes`) is updated to reflect the new feature-based structure.

### Specific Migration Examples (to be detailed further in `tasks.md`):

-   **Auth/Login Feature**: Migrate `app/features/login/` and related `app/data/models/auth/` to the new structure.
-   **Dashboard Feature**: Migrate `app/features/dashboard/`.
-   **GiangVien (Lecturer) Feature**: Migrate `app/features/giangvien/` and its sub-modules (chamcong, congvan, dieuxe, donnghiphep, home, luongthue, tkbgiangvien).
-   **KySo Feature**: Migrate `app/features/kyso/`.

## 4. Acceptance Criteria

-   The application successfully builds and runs with the new architecture.
-   All existing functionalities are preserved and work as expected.
-   Codebase adheres to the defined Clean Architecture principles.
-   Each feature is self-contained and can be developed/tested independently.
-   Improved testability of business logic.
-   Clear separation of concerns across all layers and features.