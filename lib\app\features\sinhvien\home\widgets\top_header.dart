

import 'package:avatar_glow/avatar_glow.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/sch/tkb/tkb_tuan_giangvien.dart';
import 'package:tvumobile/app/shared_components/circular_cached_network_image.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import '../sv_home_controller.dart';

Widget topHeader(BuildContext context, SvHomeController controller) {
  var theme = Theme.of(context);
  var height = MediaQuery.of(context).size.height;
  var width = MediaQuery.of(context).size.width;
  UserInfo? userInfo = LocalStorageServices.getUserInfo();

  void changeTheme(BuildContext context) {

  }

  Widget getProfileImage() {
    if (userInfo?.picture == null) {
      return Image.asset(
        'assets/images/app_icon_150.png',
        height: 100,
        width: 100,
        opacity: const AlwaysStoppedAnimation(.89),
      );
    }
    return CircularCachedNetworkImage(
      imageURL: userInfo!.picture.toString(),
      size: 100,
      borderColor: Colors.transparent,
      fit: BoxFit.cover,
      alignment: Alignment.topCenter,
    );
  }

  Widget schedulingCard(double width, ThemeData theme, TkbTuanGiangVien tkb) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Image.asset(
          'assets/vectors/icons8-task-100.png',
          width: 100,
          height: 100,
        ),
        SizedBox(
          width: (width * 0.50),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText.bodySmall(
                "Lịch trình hôm nay: ${DateFormat("dd/MM/yyyy").format(tkb.batDau!)}",
                muted: true,
              ),
              MyText.bodyMedium(
                controller.tkbs[0].tenMon.toString(),
                maxLines: 2,
              ),
              MyText.bodyMedium("Bắt đầu từ ${DateFormat("HH:mm").format(tkb.batDau!)} - ${DateFormat("HH:mm").format(tkb.ketThuc!)}",
                  color: theme.primaryColor),
            ],
          ),
        ),
        GestureDetector(
          onTap: () {
            controller.onTapFunctions("tkb");
          },
          child: Icon(
            LucideIcons.chevronRight,
            size: 32,
            color: theme.primaryColor,
          ),
        )
      ],
    );
  }

  Widget schedulingCardEmpty(double width) {
    var theme = Get.theme;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Image.asset(
          'assets/vectors/icons8-task-100.png',
          width: 100,
          height: 100,
        ),
        SizedBox(
          height: 100,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Spacer(),
              MyText.bodySmall(
                "Lịch trình hôm nay: ${DateFormat("dd/MM/yyyy").format(DateTime.now())}",
                muted: true,
              ),

              MyText.bodyMedium(
                "Bạn chưa có lịch trình 😆",
              ),
              Spacer(),
            ],
          ),
        ),
        Spacer(),
        GestureDetector(
          onTap: () {
            controller.onTapFunctions("tkb");
          },
          child: Icon(
            LucideIcons.chevronRight,
            size: 32,
            color: theme.primaryColor,
          ),
        )
      ],
    );
  }


  return SizedBox(
    height: 230,
    child: Stack(
      alignment: Alignment.topCenter,
      fit: StackFit.expand,
      children: [
        Positioned(
          top: 0,
          child: Container(
            height: height * 0.22,
            width: width,
            decoration: BoxDecoration(color: theme.primaryColor),
            child: Stack(
              fit: StackFit.expand,
              children: [
                //----------------white circles decor----------------//
                Positioned(
                  right: 0,
                  top: -80,
                  child: CircleAvatar(
                      backgroundColor: Colors.white.withOpacity(0.05),
                      radius: 111,
                      child: Image.asset(
                        'assets/images/app_icon.png',
                        height: 150,
                        width: 150,
                        opacity: const AlwaysStoppedAnimation(.15),
                      )),
                ),
                Positioned(
                  right: -7,
                  top: -160,
                  child: CircleAvatar(
                    backgroundColor: Colors.white.withOpacity(0.05),
                    radius: 111,
                  ),
                ),
                Positioned(
                  right: -21,
                  top: -195,
                  child: CircleAvatar(
                    backgroundColor: Colors.white.withOpacity(0.05),
                    radius: 111,
                  ),
                ),
                //----------------Data row----------------//
                Positioned(
                  top: 24,
                  right: 16,
                  left: 16,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      AvatarGlow(
                        //startDelay: const Duration(milliseconds: 1000),
                        glowColor: Colors.white,
                        glowShape: BoxShape.circle,
                        animate: true,
                        curve: Curves.fastOutSlowIn,
                        glowRadiusFactor: 0.16,
                        child: Material(
                          elevation: 8.0,
                          shape: const CircleBorder(),
                          color: Colors.transparent,
                          child: CircleAvatar(
                            //backgroundImage: NetworkImage(homeController.userInfo!.picture.toString()),
                            radius: 32.0,
                            child: getProfileImage(),
                          ),
                        ),
                      ),
                      MySpacing.width(12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          MyText.labelMedium(
                            "Xin chào",
                            color: theme.colorScheme.onPrimary
                                .withOpacity(0.85),
                          ),
                          MyText.titleMedium(
                            userInfo!.hoten.toString(),
                            color: theme.colorScheme.onPrimary,
                          ),
                        ],
                      ),
                      const Spacer(),

                      //----------------Theme Button----------------//
                      InkWell(
                        onTap: () => changeTheme(context),
                        child: Ink(
                          child: SizedBox(
                            height: 39,
                            width: 39,
                            child: Icon(
                              Get.isDarkMode
                                  ? LucideIcons.moon
                                  : LucideIcons.sun,
                              size: 18,
                              color: theme.colorScheme.onPrimary,
                            ),
                          ),
                        ),
                      ),
                      MySpacing.width(5),
                      //----------------Language Button----------------//
                      InkWell(
                        onTap: () {},
                        child: Ink(
                          child: SizedBox(
                            height: 39,
                            width: 39,
                            child: Stack(
                              clipBehavior: Clip.none,
                              children: <Widget>[
                                SizedBox(
                                  height: 39,
                                  width: 39,
                                  child: Icon(
                                    LucideIcons.bell,
                                    size: 18,
                                    color:
                                    theme.colorScheme.onPrimary,
                                  ),
                                ),
                                Positioned(
                                  right: 9,
                                  top: 7,
                                  child: MyContainer.rounded(
                                    paddingAll: 4,
                                    color: theme.colorScheme.error
                                        .withAlpha(500),
                                    child: Container(),
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
        Positioned(
          top: height * 0.20,
          child: Container(
            width: width,
            decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(0),
                    topRight: Radius.circular(0))),
            height: height * 1.2,
          ),
        ),
        Positioned(
            top: height * 0.14,
            child: Container(
              width: width * 0.85,
              height: height * 0.12,
              decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                      color: theme.primaryColor.withOpacity(0.5),
                      width: 1),
                  boxShadow: [
                    BoxShadow(
                        color: theme.colorScheme.surface
                            .withOpacity(0.3),
                        offset: const Offset(2, -2)),
                    BoxShadow(
                        color: theme.colorScheme.surface
                            .withOpacity(0.3),
                        offset: const Offset(3, -3)),
                  ]),
              child: Obx(() {
                if (controller.tkbs.isEmpty) {
                  return schedulingCardEmpty(width);
                }
                var tkb = controller.tkbs[0];
                if (controller.tkbs.length > 1) {
                  var diff = DateTime.now().difference(tkb.batDau!);
                  if (diff.inHours > 1) {
                    tkb = controller.tkbs[1];
                  }
                }
                return schedulingCard(width, theme, tkb);
              }),
            )),
      ],
    ),
  );
}