import 'dart:async';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/features/giangvien/congvan/congvan_view.dart';
// ignore: library_prefixes
import 'package:tvumobile/app/features/giangvien/home/<USER>'
    // ignore: library_prefixes
    deferred as gvHome;
import 'package:tvumobile/app/features/giangvien/tkbgiangvien/tkb_giangvien_view.dart';
import 'package:tvumobile/app/features/setting/settings_page.dart';
import 'package:tvumobile/app/features/sinhvien/diemsinhvien/diem_sinhvien_view.dart';
import 'package:tvumobile/app/features/sinhvien/home/<USER>';
import 'package:tvumobile/app/features/sinhvien/tkbsinhvien/tkb_sinhvien_view.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

enum MConnectivityResult { none, waiting, wifi, mobile }

class NavbarController extends GetxController {
  ApiProvider apiProvider = Get.find();
  final heart = false.obs;
  late StreamSubscription<List<ConnectivityResult>> _streamSubscription;
  final _connectionType = MConnectivityResult.waiting.obs;
  final Connectivity _connectivity = Connectivity();
  MConnectivityResult get connectionType => _connectionType.value;

  set connectionType(value) {
    _connectionType.value = value;
  }

  List navigation = [
    Container(),
    const CongVanView(),
    TkbGiangVienView(),
    const SettingsPage(),
  ];

  List svNavigation = [
    const SvHome(),
    TkbSinhVienView(),
    DiemSinhVienView(),
    //HpSinhVienView(),
    const SettingsPage(),
  ];
  RxInt selectedIndex = 0.obs;

  bool isTeacher = true;
  UserInfo? userInfo = LocalStorageServices.getUserInfo();

  @override
  void onInit() {
    super.onInit();
    getConnectivityType();
    _streamSubscription = _connectivity.onConnectivityChanged.listen(
      _updateState,
    );
    try {
      if (userInfo == null) {
        Get.offAllNamed(AppPages.LOGIN);
      }
    } finally {
      if (userInfo == null) {
        Get.offAllNamed(AppPages.LOGIN);
      }
    }
    isTeacher = userInfo!.isActualTeacher();
    if (Get.parameters["path"] != null) {
      switch (Get.parameters["path"]) {
        case Routes.HOME:
        case Routes.NAV:
          selectedIndex.value = 0;
          break;
        case Routes.CONGVAN:
          selectedIndex.value = 1;
          break;
        case Routes.TKBGIANGVIEN:
          selectedIndex.value = 2;
          break;
      }
    }
    //print(Get.arguments);
  }

  void onTap(int index) {
    selectedIndex.value = index;
    update();
  }

  @override
  Future<void> onReady() async {
    //Logger().i('onReady: '+Platform.isWindows.toString());
    //odatatest();
    await gvHome.loadLibrary();
    navigation = [
      gvHome.GvHome(),
      const CongVanView(),
      TkbGiangVienView(),
      const SettingsPage(),
    ];
    if (!kIsWeb) {
      if (!Platform.isWindows) {
        //initNotificationService();
        // Permission.notification.status.then((value) async {
        //   Logger().i('openNotificationDialog isGrained: $value');
        //   if(value == PermissionStatus.granted) {
        //     await NotificationHelper().initNotification();
        //   } else {
        //     await NotificationHelper().openNotificationDialog();
        //   }
        // });
        // var fcmtoken = LocalStorageServices.getFcmToken();
        // //Logger().i('onReady: '+ fcmtoken!.toString());
        // if (fcmtoken == null) {
        //   // var isGrained = await NotificationHelper().requestPermission();
        //   // Logger().i('openNotificationDialog isGrained: $isGrained');
        //   // if (isGrained) {
        //   //   NotificationHelper().initNotification();
        //   // } else {
        //   //   NotificationHelper().openNotificationDialog().then((
        //   //       value) async => await NotificationHelper().updateFCM());
        //   // }
        // } else {
        //   Logger().i('updateFCM');
        //   NotificationHelper().updateFCM();
        // }
      }
    }
  }

  Future<void> getConnectivityType() async {
    late List<ConnectivityResult> connectivityResult;
    try {
      connectivityResult = await (_connectivity.checkConnectivity());
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
    return _updateState(connectivityResult);
  }

  _updateState(List<ConnectivityResult> connectivityResult) {
    if (connectivityResult.contains(ConnectivityResult.mobile)) {
      connectionType = MConnectivityResult.mobile;
    } else if (connectivityResult.contains(ConnectivityResult.wifi)) {
      connectionType = MConnectivityResult.wifi;
    } else if (connectivityResult.contains(ConnectivityResult.ethernet)) {
      connectionType = MConnectivityResult.wifi;
    } else if (connectivityResult.contains(ConnectivityResult.vpn)) {
      connectionType = MConnectivityResult.wifi;
    } else if (connectivityResult.contains(ConnectivityResult.bluetooth)) {
      if (kDebugMode) {
        print('Failed to get connection type');
      }
      connectionType = MConnectivityResult.none;
    } else if (connectivityResult.contains(ConnectivityResult.other)) {
      if (kDebugMode) {
        print('Failed to get connection type');
      }
      connectionType = MConnectivityResult.none;
    } else if (connectivityResult.contains(ConnectivityResult.none)) {
      connectionType = MConnectivityResult.none;
    }
    //connectionType(connectionType);
    if (kDebugMode) {
      print(connectionType);
    }
  }

  @override
  void onClose() {
    _streamSubscription.cancel();
  }
}
