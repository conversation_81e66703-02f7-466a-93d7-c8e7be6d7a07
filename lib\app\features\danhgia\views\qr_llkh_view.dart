import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:tvumobile/app/features/danhgia/controllers/qr_canhan_controller.dart';
import 'package:tvumobile/app/features/danhgia/controllers/qr_llkh_controller.dart';
import 'package:tvumobile/app/features/danhgia/widgets/qr_appbar.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class QrLLKHView extends GetView<QrLLKHController> {
  const QrLLKHView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // Get.put(QrLLKHController());
    return Scaffold(
      appBar: ScannerAppBar(context, "mã qr lý lịch khoa học"),
      body: Center(
        child: Obx(
          () => controller.isLoading.value
              ? const CircularProgressIndicator()
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Hiển thị mã QR
                    QrImageView(
                      data:
                          "https://tms.tvu.edu.vn/info?id=${controller.qrLLKH}",
                      version: QrVersions.auto,
                      size: 250.0, // Kích thước mã QR
                      gapless: false,
                      foregroundColor: Colors.black87, // Màu mã QR
                      backgroundColor: Colors.white, // Màu nền mã QR
                      errorStateBuilder: (context, error) {
                        return const Text(
                          'Có lỗi khi tạo mã QR!',
                          style: TextStyle(color: Colors.red),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    // Hiển thị mô tả hoặc thông tin bổ sung
                    MyText.bodyMedium(
                      'Quét mã QR này để xem thông tin cá nhân',
                      color: Colors.black87,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
        ),
      ),
    );
  }
}
