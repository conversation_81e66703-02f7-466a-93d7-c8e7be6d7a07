class DiemSinhVienModel {
  DiemSinhVienModel({
    required this.totalItems,
    required this.totalPages,
    required this.isKkbd,
    required this.dsDiemHocky,
    required this.dsFieldAnCotDiem,
    required this.hienThi<PERSON>hoa<PERSON>hi,
    required this.hienThiCotDiemTp,
    required this.hienThiCotDiemK1,
    required this.hienThiCotMhtt,
    required this.hienThiCotStctt,
    required this.hienThiCotDiemtk10,
    required this.hienThiCotDiemtk4,
    required this.hienThiCotDiemThi,
    required this.hienThiCotMhNganh,
    required this.mesageDiemtk4,
    required this.mesageDiemtkc,
    required this.mesageDiemtk10,
  });

  final num? totalItems;
  final num? totalPages;
  final bool? isKkbd;
  final List<DsDiemHocky> dsDiemHocky;
  final List<dynamic> dsFieldAnCotDiem;
  final bool? hienThiKhoaThi;
  final bool? hienThiCotDiemTp;
  final bool? hienThiCotDiemK1;
  final bool? hienThiCotMhtt;
  final bool? hienThiCotStctt;
  final bool? hienThiCotDiemtk10;
  final bool? hienThiCotDiemtk4;
  final bool? hienThiCotDiemThi;
  final bool? hienThiCotMhNganh;
  final String? mesageDiemtk4;
  final String? mesageDiemtkc;
  final String? mesageDiemtk10;

  DiemSinhVienModel copyWith({
    num? totalItems,
    num? totalPages,
    bool? isKkbd,
    List<DsDiemHocky>? dsDiemHocky,
    List<dynamic>? dsFieldAnCotDiem,
    bool? hienThiKhoaThi,
    bool? hienThiCotDiemTp,
    bool? hienThiCotDiemK1,
    bool? hienThiCotMhtt,
    bool? hienThiCotStctt,
    bool? hienThiCotDiemtk10,
    bool? hienThiCotDiemtk4,
    bool? hienThiCotDiemThi,
    bool? hienThiCotMhNganh,
    String? mesageDiemtk4,
    String? mesageDiemtkc,
    String? mesageDiemtk10,
  }) {
    return DiemSinhVienModel(
      totalItems: totalItems ?? this.totalItems,
      totalPages: totalPages ?? this.totalPages,
      isKkbd: isKkbd ?? this.isKkbd,
      dsDiemHocky: dsDiemHocky ?? this.dsDiemHocky,
      dsFieldAnCotDiem: dsFieldAnCotDiem ?? this.dsFieldAnCotDiem,
      hienThiKhoaThi: hienThiKhoaThi ?? this.hienThiKhoaThi,
      hienThiCotDiemTp: hienThiCotDiemTp ?? this.hienThiCotDiemTp,
      hienThiCotDiemK1: hienThiCotDiemK1 ?? this.hienThiCotDiemK1,
      hienThiCotMhtt: hienThiCotMhtt ?? this.hienThiCotMhtt,
      hienThiCotStctt: hienThiCotStctt ?? this.hienThiCotStctt,
      hienThiCotDiemtk10: hienThiCotDiemtk10 ?? this.hienThiCotDiemtk10,
      hienThiCotDiemtk4: hienThiCotDiemtk4 ?? this.hienThiCotDiemtk4,
      hienThiCotDiemThi: hienThiCotDiemThi ?? this.hienThiCotDiemThi,
      hienThiCotMhNganh: hienThiCotMhNganh ?? this.hienThiCotMhNganh,
      mesageDiemtk4: mesageDiemtk4 ?? this.mesageDiemtk4,
      mesageDiemtkc: mesageDiemtkc ?? this.mesageDiemtkc,
      mesageDiemtk10: mesageDiemtk10 ?? this.mesageDiemtk10,
    );
  }

  factory DiemSinhVienModel.fromJson(Map<String, dynamic> json) {
    return DiemSinhVienModel(
      totalItems: json["total_items"],
      totalPages: json["total_pages"],
      isKkbd: json["is_kkbd"],
      dsDiemHocky: json["ds_diem_hocky"] == null
          ? []
          : List<DsDiemHocky>.from(
              json["ds_diem_hocky"]!.map((x) => DsDiemHocky.fromJson(x))),
      dsFieldAnCotDiem: json["ds_field_an_cot_diem"] == null
          ? []
          : List<dynamic>.from(json["ds_field_an_cot_diem"]!.map((x) => x)),
      hienThiKhoaThi: json["hien_thi_khoa_thi"],
      hienThiCotDiemTp: json["hien_thi_cot_diem_tp"],
      hienThiCotDiemK1: json["hien_thi_cot_diem_k1"],
      hienThiCotMhtt: json["hien_thi_cot_mhtt"],
      hienThiCotStctt: json["hien_thi_cot_stctt"],
      hienThiCotDiemtk10: json["hien_thi_cot_diemtk10"],
      hienThiCotDiemtk4: json["hien_thi_cot_diemtk4"],
      hienThiCotDiemThi: json["hien_thi_cot_diem_thi"],
      hienThiCotMhNganh: json["hien_thi_cot_mh_nganh"],
      mesageDiemtk4: json["mesage_diemtk4"],
      mesageDiemtkc: json["mesage_diemtkc"],
      mesageDiemtk10: json["mesage_diemtk10"],
    );
  }

  Map<String, dynamic> toJson() => {
        "total_items": totalItems,
        "total_pages": totalPages,
        "is_kkbd": isKkbd,
        "ds_diem_hocky": dsDiemHocky.map((x) => x.toJson()).toList(),
        "ds_field_an_cot_diem": dsFieldAnCotDiem.map((x) => x).toList(),
        "hien_thi_khoa_thi": hienThiKhoaThi,
        "hien_thi_cot_diem_tp": hienThiCotDiemTp,
        "hien_thi_cot_diem_k1": hienThiCotDiemK1,
        "hien_thi_cot_mhtt": hienThiCotMhtt,
        "hien_thi_cot_stctt": hienThiCotStctt,
        "hien_thi_cot_diemtk10": hienThiCotDiemtk10,
        "hien_thi_cot_diemtk4": hienThiCotDiemtk4,
        "hien_thi_cot_diem_thi": hienThiCotDiemThi,
        "hien_thi_cot_mh_nganh": hienThiCotMhNganh,
        "mesage_diemtk4": mesageDiemtk4,
        "mesage_diemtkc": mesageDiemtkc,
        "mesage_diemtk10": mesageDiemtk10,
      };

  @override
  String toString() {
    return "$totalItems, $totalPages, $isKkbd, $dsDiemHocky, $dsFieldAnCotDiem, $hienThiKhoaThi, $hienThiCotDiemTp, $hienThiCotDiemK1, $hienThiCotMhtt, $hienThiCotStctt, $hienThiCotDiemtk10, $hienThiCotDiemtk4, $hienThiCotDiemThi, $hienThiCotMhNganh, $mesageDiemtk4, $mesageDiemtkc, $mesageDiemtk10, ";
  }

  static List<DiemSinhVienModel> parseList(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed
        .map<DiemSinhVienModel>((json) => DiemSinhVienModel.fromJson(json))
        .toList();
  }
}

class DsDiemHocky {
  DsDiemHocky({
    required this.loaiNganh,
    required this.hocKy,
    required this.tenHocKy,
    required this.dtbHkHe10,
    required this.dtbHkHe4,
    required this.dtbTichLuyHe10,
    required this.dtbTichLuyHe4,
    required this.soTinChiDatHk,
    required this.soTinChiDatTichLuy,
    required this.hienThiTkHe10,
    required this.hienThiTkHe4,
    required this.dsDiemMonHoc,
  });

  final num? loaiNganh;
  final String? hocKy;
  final String? tenHocKy;
  final String? dtbHkHe10;
  final String? dtbHkHe4;
  final String? dtbTichLuyHe10;
  final String? dtbTichLuyHe4;
  final String? soTinChiDatHk;
  final String? soTinChiDatTichLuy;
  final bool? hienThiTkHe10;
  final bool? hienThiTkHe4;
  final List<DsDiemMonHoc> dsDiemMonHoc;

  DsDiemHocky copyWith({
    num? loaiNganh,
    String? hocKy,
    String? tenHocKy,
    String? dtbHkHe10,
    String? dtbHkHe4,
    String? dtbTichLuyHe10,
    String? dtbTichLuyHe4,
    String? soTinChiDatHk,
    String? soTinChiDatTichLuy,
    bool? hienThiTkHe10,
    bool? hienThiTkHe4,
    List<DsDiemMonHoc>? dsDiemMonHoc,
  }) {
    return DsDiemHocky(
      loaiNganh: loaiNganh ?? this.loaiNganh,
      hocKy: hocKy ?? this.hocKy,
      tenHocKy: tenHocKy ?? this.tenHocKy,
      dtbHkHe10: dtbHkHe10 ?? this.dtbHkHe10,
      dtbHkHe4: dtbHkHe4 ?? this.dtbHkHe4,
      dtbTichLuyHe10: dtbTichLuyHe10 ?? this.dtbTichLuyHe10,
      dtbTichLuyHe4: dtbTichLuyHe4 ?? this.dtbTichLuyHe4,
      soTinChiDatHk: soTinChiDatHk ?? this.soTinChiDatHk,
      soTinChiDatTichLuy: soTinChiDatTichLuy ?? this.soTinChiDatTichLuy,
      hienThiTkHe10: hienThiTkHe10 ?? this.hienThiTkHe10,
      hienThiTkHe4: hienThiTkHe4 ?? this.hienThiTkHe4,
      dsDiemMonHoc: dsDiemMonHoc ?? this.dsDiemMonHoc,
    );
  }

  factory DsDiemHocky.fromJson(Map<String, dynamic> json) {
    return DsDiemHocky(
      loaiNganh: json["loai_nganh"],
      hocKy: json["hoc_ky"],
      tenHocKy: json["ten_hoc_ky"],
      dtbHkHe10: json["dtb_hk_he10"],
      dtbHkHe4: json["dtb_hk_he4"],
      dtbTichLuyHe10: json["dtb_tich_luy_he_10"],
      dtbTichLuyHe4: json["dtb_tich_luy_he_4"],
      soTinChiDatHk: json["so_tin_chi_dat_hk"],
      soTinChiDatTichLuy: json["so_tin_chi_dat_tich_luy"],
      hienThiTkHe10: json["hien_thi_tk_he_10"],
      hienThiTkHe4: json["hien_thi_tk_he_4"],
      dsDiemMonHoc: json["ds_diem_mon_hoc"] == null
          ? []
          : List<DsDiemMonHoc>.from(
              json["ds_diem_mon_hoc"]!.map((x) => DsDiemMonHoc.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "loai_nganh": loaiNganh,
        "hoc_ky": hocKy,
        "ten_hoc_ky": tenHocKy,
        "dtb_hk_he10": dtbHkHe10,
        "dtb_hk_he4": dtbHkHe4,
        "dtb_tich_luy_he_10": dtbTichLuyHe10,
        "dtb_tich_luy_he_4": dtbTichLuyHe4,
        "so_tin_chi_dat_hk": soTinChiDatHk,
        "so_tin_chi_dat_tich_luy": soTinChiDatTichLuy,
        "hien_thi_tk_he_10": hienThiTkHe10,
        "hien_thi_tk_he_4": hienThiTkHe4,
        "ds_diem_mon_hoc": dsDiemMonHoc.map((x) => x.toJson()).toList(),
      };

  @override
  String toString() {
    return "$loaiNganh, $hocKy, $tenHocKy, $dtbHkHe10, $dtbHkHe4, $dtbTichLuyHe10, $dtbTichLuyHe4, $soTinChiDatHk, $soTinChiDatTichLuy, $hienThiTkHe10, $hienThiTkHe4, $dsDiemMonHoc, ";
  }

  static List<DsDiemHocky> parseList(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed
        .map<DsDiemHocky>((json) => DsDiemHocky.fromJson(json))
        .toList();
  }
}

class DsDiemMonHoc {
  DsDiemMonHoc({
    required this.maMon,
    required this.maMonTt,
    required this.nhomTo,
    required this.tenMon,
    required this.monHocNganh,
    required this.soTinChi,
    required this.diemThi,
    required this.diemGiuaKy,
    required this.diemTk,
    required this.diemTkSo,
    required this.diemTkChu,
    required this.ketQua,
    required this.hienThiKetQua,
    required this.loaiNganh,
    required this.khoaThi,
    required this.khongTinhDiemTbtl,
    required this.lyDoKhongTinhDiemTbtl,
    required this.dsDiemThanhPhan,
  });

  final String? maMon;
  final String? maMonTt;
  final String? nhomTo;
  final String? tenMon;
  final bool? monHocNganh;
  final String? soTinChi;
  final String? diemThi;
  final String? diemGiuaKy;
  final String? diemTk;
  final String? diemTkSo;
  final String? diemTkChu;
  final num? ketQua;
  final bool? hienThiKetQua;
  final num? loaiNganh;
  final num? khoaThi;
  final num? khongTinhDiemTbtl;
  final String? lyDoKhongTinhDiemTbtl;
  final List<DsDiemThanhPhan> dsDiemThanhPhan;

  DsDiemMonHoc copyWith({
    String? maMon,
    String? maMonTt,
    String? nhomTo,
    String? tenMon,
    bool? monHocNganh,
    String? soTinChi,
    String? diemThi,
    String? diemGiuaKy,
    String? diemTk,
    String? diemTkSo,
    String? diemTkChu,
    num? ketQua,
    bool? hienThiKetQua,
    num? loaiNganh,
    num? khoaThi,
    num? khongTinhDiemTbtl,
    String? lyDoKhongTinhDiemTbtl,
    List<DsDiemThanhPhan>? dsDiemThanhPhan,
  }) {
    return DsDiemMonHoc(
      maMon: maMon ?? this.maMon,
      maMonTt: maMonTt ?? this.maMonTt,
      nhomTo: nhomTo ?? this.nhomTo,
      tenMon: tenMon ?? this.tenMon,
      monHocNganh: monHocNganh ?? this.monHocNganh,
      soTinChi: soTinChi ?? this.soTinChi,
      diemThi: diemThi ?? this.diemThi,
      diemGiuaKy: diemGiuaKy ?? this.diemGiuaKy,
      diemTk: diemTk ?? this.diemTk,
      diemTkSo: diemTkSo ?? this.diemTkSo,
      diemTkChu: diemTkChu ?? this.diemTkChu,
      ketQua: ketQua ?? this.ketQua,
      hienThiKetQua: hienThiKetQua ?? this.hienThiKetQua,
      loaiNganh: loaiNganh ?? this.loaiNganh,
      khoaThi: khoaThi ?? this.khoaThi,
      khongTinhDiemTbtl: khongTinhDiemTbtl ?? this.khongTinhDiemTbtl,
      lyDoKhongTinhDiemTbtl:
          lyDoKhongTinhDiemTbtl ?? this.lyDoKhongTinhDiemTbtl,
      dsDiemThanhPhan: dsDiemThanhPhan ?? this.dsDiemThanhPhan,
    );
  }

  factory DsDiemMonHoc.fromJson(Map<String, dynamic> json) {
    return DsDiemMonHoc(
      maMon: json["ma_mon"],
      maMonTt: json["ma_mon_tt"],
      nhomTo: json["nhom_to"],
      tenMon: json["ten_mon"],
      monHocNganh: json["mon_hoc_nganh"],
      soTinChi: json["so_tin_chi"],
      diemThi: json["diem_thi"],
      diemGiuaKy: json["diem_giua_ky"],
      diemTk: json["diem_tk"],
      diemTkSo: json["diem_tk_so"],
      diemTkChu: json["diem_tk_chu"],
      ketQua: json["ket_qua"],
      hienThiKetQua: json["hien_thi_ket_qua"],
      loaiNganh: json["loai_nganh"],
      khoaThi: json["KhoaThi"],
      khongTinhDiemTbtl: json["khong_tinh_diem_tbtl"],
      lyDoKhongTinhDiemTbtl: json["ly_do_khong_tinh_diem_tbtl"],
      dsDiemThanhPhan: json["ds_diem_thanh_phan"] == null
          ? []
          : List<DsDiemThanhPhan>.from(json["ds_diem_thanh_phan"]!
              .map((x) => DsDiemThanhPhan.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "ma_mon": maMon,
        "ma_mon_tt": maMonTt,
        "nhom_to": nhomTo,
        "ten_mon": tenMon,
        "mon_hoc_nganh": monHocNganh,
        "so_tin_chi": soTinChi,
        "diem_thi": diemThi,
        "diem_giua_ky": diemGiuaKy,
        "diem_tk": diemTk,
        "diem_tk_so": diemTkSo,
        "diem_tk_chu": diemTkChu,
        "ket_qua": ketQua,
        "hien_thi_ket_qua": hienThiKetQua,
        "loai_nganh": loaiNganh,
        "KhoaThi": khoaThi,
        "khong_tinh_diem_tbtl": khongTinhDiemTbtl,
        "ly_do_khong_tinh_diem_tbtl": lyDoKhongTinhDiemTbtl,
        "ds_diem_thanh_phan": dsDiemThanhPhan.map((x) => x.toJson()).toList(),
      };

  @override
  String toString() {
    return "$maMon, $maMonTt, $nhomTo, $tenMon, $monHocNganh, $soTinChi, $diemThi, $diemGiuaKy, $diemTk, $diemTkSo, $diemTkChu, $ketQua, $hienThiKetQua, $loaiNganh, $khoaThi, $khongTinhDiemTbtl, $lyDoKhongTinhDiemTbtl, $dsDiemThanhPhan, ";
  }
}

class DsDiemThanhPhan {
  DsDiemThanhPhan({
    required this.kyHieu,
    required this.tenThanhPhan,
    required this.trongSo,
    required this.diemThanhPhan,
  });

  final String? kyHieu;
  final String? tenThanhPhan;
  final String? trongSo;
  final String? diemThanhPhan;

  DsDiemThanhPhan copyWith({
    String? kyHieu,
    String? tenThanhPhan,
    String? trongSo,
    String? diemThanhPhan,
  }) {
    return DsDiemThanhPhan(
      kyHieu: kyHieu ?? this.kyHieu,
      tenThanhPhan: tenThanhPhan ?? this.tenThanhPhan,
      trongSo: trongSo ?? this.trongSo,
      diemThanhPhan: diemThanhPhan ?? this.diemThanhPhan,
    );
  }

  factory DsDiemThanhPhan.fromJson(Map<String, dynamic> json) {
    return DsDiemThanhPhan(
      kyHieu: json["ky_hieu"],
      tenThanhPhan: json["ten_thanh_phan"],
      trongSo: json["trong_so"],
      diemThanhPhan: json["diem_thanh_phan"],
    );
  }

  Map<String, dynamic> toJson() => {
        "ky_hieu": kyHieu,
        "ten_thanh_phan": tenThanhPhan,
        "trong_so": trongSo,
        "diem_thanh_phan": diemThanhPhan,
      };

  @override
  String toString() {
    return "$kyHieu, $tenThanhPhan, $trongSo, $diemThanhPhan, ";
  }
}
