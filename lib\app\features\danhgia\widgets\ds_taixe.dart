import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/data/models/portal/tms_danhgia_xe.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class DsTaiXeBox extends StatelessWidget {

  const DsTaiXeBox({
    super.key,
    required this.dstaixe,
    required this.selected_changed,
  });

  final List<DsTaixe> dstaixe;
  final void Function(int) selected_changed;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: DropdownButtonFormField(
          decoration: const InputDecoration(
            prefixIcon: Icon(LucideIcons.personStanding),
          ),
        hint: const Row(
          children: [
            MyText.bodyLarge("vui lòng chọn...", xMuted: true,),
          ],
        ),
        items: dropdownItems,
        onChanged: (value) {
          selected_changed(value!);
        },
      ),
    );
  }

  List<DropdownMenuItem<int>> get dropdownItems{
    return dstaixe.map((e) => DropdownMenuItem(value: e.id!, child: Text("${e.ho!} ${e.tenDem!} ${e.ten!}".replaceAll("  ", " ")))).toList();
  }
}