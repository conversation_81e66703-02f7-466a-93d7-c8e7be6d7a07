import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepvienchuc.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/empty_widget.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/duyetdnp_controller.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/date_time_extension.dart';

// ----------------- TAB CHỜ DUYỆT DNP ------------------
Widget tabChoDuyet(DuyetDNPController controller) {
  return Obx(() {
    List<DonNghiPhepVienChuc> filteredDNPs = <DonNghiPhepVienChuc>[].obs;
    // L<PERSON><PERSON> danh sách đơn nghỉ phép có trạng thái chứa "chưa duyệt"

    // -------------------- ĐƠN VỊ ---------------------
    if (controller.duyetai.value == "dv") {
      filteredDNPs = controller.dsDNPDonVi
          .where((dnp) =>
              dnp.trangThai != null &&
              dnp.trangThai!.toLowerCase().contains('chờ duyệt'))
          .toList();
    }
    // ----------------- BAN GIÁM HIỆU -----------------
    else if (controller.duyetai.value == "bgh") {
      filteredDNPs = controller.dsDNPDonVi
          .where((dnp) =>
              (dnp.trangThai!.toLowerCase().contains('tcns đã duyệt') &&
                  dnp.soNgay! >= 5 &&
                  (dnp.bghDuyetTrucTiep == false ||
                      dnp.bghDuyetTrucTiep == null)) ||
              dnp.trangThai!.toLowerCase().contains('tcns đã duyệt') &&
                  (dnp.soNgay! >= 5 &&
                      (dnp.loaiNghiPhep!.id == 4 ||
                          dnp.loaiNghiPhep!.id == 5 ||
                          dnp.loaiNghiPhep!.id == 7) &&
                      (dnp.bghDuyetTrucTiep == false ||
                          dnp.bghDuyetTrucTiep == null)) ||
              (dnp.trangThai!.toLowerCase().contains('tcns đã duyệt') &&
                  (dnp.loaiNghiPhep!.id == 3 ||
                      dnp.loaiNghiPhep!.id == 8 ||
                      dnp.loaiNghiPhep!.id == 9) &&
                  (dnp.bghDuyetTrucTiep == false ||
                      dnp.bghDuyetTrucTiep == null)) ||
              (dnp.trangThai!.toLowerCase().contains('tcns đã duyệt') &&
                  dnp.bghDuyetTrucTiep == true))
          .toList();
    }
    // -------------------- NHÂN SỰ --------------------
    else if (controller.duyetai.value == "ns") {
      filteredDNPs = controller.dsDNPDonVi
          .where((dnp) =>
              (dnp.trangThai!.toLowerCase().contains('đơn vị đã duyệt') &&
                  dnp.soNgay! >= 3) ||
              (dnp.trangThai!.toLowerCase().contains('đơn vị đã duyệt') &&
                  (dnp.loaiNghiPhep!.id == 3 ||
                      dnp.loaiNghiPhep!.id == 4 ||
                      dnp.loaiNghiPhep!.id == 5 ||
                      dnp.loaiNghiPhep!.id == 7)) ||
              (dnp.trangThai!.toLowerCase().contains('công đoàn đã duyệt') &&
                  (dnp.loaiNghiPhep!.id == 8 || dnp.loaiNghiPhep!.id == 9)) ||
              (!dnp.trangThai!.toLowerCase().contains('tcns đã duyệt') &&
                  !dnp.trangThai!.toLowerCase().contains('bgh đã duyệt') &&
                  dnp.bghDuyetTrucTiep == true))
          .toList();
    }
    // ------------------- CÔNG ĐOÀN -------------------
    else if (controller.duyetai.value == "cd") {
      filteredDNPs = controller.dsDNPDonVi
          .where((dnp) =>
              (dnp.trangThai!.toLowerCase().contains('đơn vị đã duyệt') &&
                  (dnp.loaiNghiPhep!.id == 8 || dnp.loaiNghiPhep!.id == 9)) ||
              (dnp.bghDuyetTrucTiep == true &&
                  (dnp.loaiNghiPhep!.id == 8 || dnp.loaiNghiPhep!.id == 9) &&
                  !dnp.trangThai!.toLowerCase().contains('công đoàn đã duyệt')))
          .toList();
    }

    return filteredDNPs.isEmpty
        ? EmptyWidget(
            message: 'Chưa có đơn chờ duyệt',
            imageAsset: 'tasky/no_inbox.png',
          )
        : ListView.builder(
            shrinkWrap: true, // Đảm bảo ListView chiếm đúng kích thước nội dung
            physics: NeverScrollableScrollPhysics(), // Tắt cuộn của ListView
            itemCount: filteredDNPs.length,
            itemBuilder: (BuildContext context, int index) {
              var dnp = filteredDNPs[index];
              return _buildDNPList(
                  context, dnp, controller.duyetai.value, "choduyet");
            });
  });
}

// ----------------- TAB ĐÃ DUYỆT DNP ------------------
Widget tabDaDuyet(DuyetDNPController controller) {
  return Obx(() {
    List<DonNghiPhepVienChuc> filteredDNPs = <DonNghiPhepVienChuc>[].obs;
    // Lọc danh sách đơn nghỉ phép có trạng thái chứa "chưa duyệt"

    // -------------------- ĐƠN VỊ ---------------------
    if (controller.duyetai.value == "dv") {
      filteredDNPs = controller.dsDNPDonVi
          .where((dnp) =>
              dnp.trangThai != null &&
              dnp.trangThai!.toLowerCase().contains('đã duyệt'))
          .toList();
    }
    // ----------------- BAN GIÁM HIỆU -----------------
    else if (controller.duyetai.value == "bgh") {
      filteredDNPs = controller.dsDNPDonVi
          .where((dnp) =>
              dnp.trangThai != null &&
              dnp.trangThai!.toLowerCase().contains('bgh đã duyệt'))
          .toList();
    }
    // -------------------- NHÂN SỰ --------------------
    else if (controller.duyetai.value == "ns") {
      filteredDNPs = controller.dsDNPDonVi
          .where((dnp) =>
              dnp.trangThai != null &&
              (dnp.soNgay! < 3 ||
                  dnp.trangThai!.toLowerCase().contains('tcns đã duyệt') ||
                  dnp.trangThai!.toLowerCase().contains('bgh đã duyệt')))
          .toList();
    }
    // ------------------- CÔNG ĐOÀN -------------------
    else if (controller.duyetai.value == "cd") {
      filteredDNPs = controller.dsDNPDonVi
          .where((dnp) =>
              dnp.trangThai!.toLowerCase().contains('công đoàn đã duyệt') ||
              dnp.trangThai!.toLowerCase().contains('bgh đã duyệt'))
          .toList();
    }

    return filteredDNPs.isEmpty
        ? EmptyWidget(
            message: 'Chưa có đơn đã duyệt',
            imageAsset: 'tasky/no_inbox.png',
          )
        : ListView.builder(
            shrinkWrap: true, // Đảm bảo ListView chiếm đúng kích thước nội dung
            physics: NeverScrollableScrollPhysics(), // Tắt cuộn của ListView
            itemCount: filteredDNPs.length,
            itemBuilder: (BuildContext context, int index) {
              var dnp = filteredDNPs[index];
              return _buildDNPList(
                  context, dnp, controller.duyetai.value, "daduyet");
            });
  });
}

// ------------- HIỂN THỊ DANH SÁCH DNP -----------------
_buildDNPList(BuildContext context, DonNghiPhepVienChuc dnp, String duyettai,
    String status) {
  return SizedBox(
    height: 120,
    child: MyContainer.none(
      padding: const EdgeInsets.fromLTRB(3, 0, 3, 2),
      margin: const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
      border: Border.all(color: Colors.black),
      borderRadius: BorderRadius.circular(16),
      onTap: () {
        Get.toNamed(
          AppPages.CHITIETDNP,
          parameters: {
            'dnpId': dnp.id.toString(),
            'duyettai': duyettai,
            'trangthai': status
          },
        );
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Phần đầu màu đề phân biệt đơn nghỉ phép
          Container(
            width: 10,
            height: 120,
            decoration: BoxDecoration(
              color: getRoleColors(duyettai, status),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                bottomLeft: Radius.circular(16),
              ),
            ),
          ),

          // Phần sau của đơn nghỉ phép (thông tin)
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 10),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.2),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ), // Bo góc nhẹ cho nền
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // -------------- LOẠI NGHỈ PHÉP -------------
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: MyText.titleMedium(
                                dnp.loaiNghiPhep!.tenLoaiNghiPhep.toString(),
                                overflow: TextOverflow.ellipsis,
                                fontWeight: 700,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),

                        // ------------------ HỌ TÊN -----------------
                        SizedBox(height: 4),
                        MyText.bodyMedium(
                          "Họ tên: ${getHoTen(dnp.vienChuc)}",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          color: Colors.black,
                        ),

                        // ------------------ LÝ DO ------------------
                        SizedBox(height: 4),
                        MyText.bodyMedium(
                          "Đơn vị: ${dnp.vienChuc!.donVi}",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          color: Colors.black,
                        ),

                        // ---------------- THỜI GIAN ----------------
                        SizedBox(height: 4),
                        MyText.bodyMedium(
                          "Từ: ${dnp.tuNgay!.toFormat(format: "dd/MM/yy")} đến ${dnp.denNgay!.toFormat(format: "dd/MM/yy")}",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          color: Colors.black,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

getRoleColors(String role, String status) {
  final lowerRole = role.toLowerCase();
  final lowerStatus = status.toLowerCase();

  switch (lowerRole) {
    case "ns":
    case "cd":
    case "dv":
    case "bgh":
      if (lowerStatus.contains("choduyet")) {
        return Colors.orange; // Gray
      } else {
        return Colors.green;
      }
  }
}

String getHoTen(VienChuc? nguoi) {
  if (nguoi == null) return "";
  var ht = "";
  ht = nguoi.ho!.isEmpty ? "" : "${nguoi.ho} ";
  ht += nguoi.tenDem!.isEmpty ? "" : "${nguoi.tenDem} ";
  ht += nguoi.ten!.isEmpty ? "" : nguoi.ten.toString();
  return ht;
}
