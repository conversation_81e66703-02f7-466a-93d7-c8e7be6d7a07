import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_detail_controller.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_home_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

AppBar DieuXeAppbar(BuildContext context, DieuXeHomeController controller) {
  //UserInfo? userInfo = LocalStorageServices.getUserInfo();
  ThemeData theme = Theme.of(context);
  return AppBar(
    toolbarHeight: 48, // Set this height
    foregroundColor: theme.colorScheme.onPrimary,
    iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
    actions: [
      Obx(() {
        final selectedCount = controller.selectedIdDieuXe.length;
        if (selectedCount >= 2) {
          return IconButton(
            icon: Icon(LucideIcons.link),
            tooltip: 'Ghép phiếu',
            onPressed: () {
              Get.toNamed(Routes.DIEUXE_CREATE,
                  arguments: {'listID': controller.selectedIdDieuXe});
            },
          );
        } else {
          return IconButton(
            icon: Icon(LucideIcons.menu),
            onPressed: () {
              controller.scaffoldKey.currentState?.openEndDrawer();
            },
          );
        }
      }),
    ],
    flexibleSpace: Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      //color: theme.primaryColor,
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -80,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset(
                  'assets/images/app_icon.png',
                  height: 150,
                  width: 150,
                  opacity: const AlwaysStoppedAnimation(.15),
                )),
          ),
          Positioned(
            right: -7,
            top: -160,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: -21,
            top: -195,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
        ],
      ),
    ),
    title: Obx(() {
      return MyText.titleMedium(
        controller.appbarTitle.value.toUpperCase(),
        color: theme.colorScheme.onPrimary,
        letterSpacing: 0.01,
        fontWeight: 900,
      );
    }),
    centerTitle: true,
  );
}

// APPBAR VỚI ACTION XÓA
AppBar DieuXeDetailAppBar(
    BuildContext context, DieuXeDetailController controller) {
  ThemeData theme = Theme.of(context);
  return AppBar(
    foregroundColor: theme.colorScheme.onPrimary,
    iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
    flexibleSpace: Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -80,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset(
                  'assets/images/app_icon.png',
                  height: 150,
                  width: 150,
                  opacity: const AlwaysStoppedAnimation(.15),
                )),
          ),
          Positioned(
            right: -7,
            top: -160,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: -21,
            top: -195,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          //----------------Data row----------------//
        ],
      ),
    ),
    title: MyText.titleMedium(
      "Chi tiết giấy điều xe".toUpperCase(),
      fontWeight: 700,
      color: theme.colorScheme.onPrimary,
      letterSpacing: 0.01,
    ),
    centerTitle: true,
    actions: [
      if (controller.xoa.value.contains("Khởi tạo") ||
          controller.xoa.value.contains("Hủy")) ...[
        IconButton(
            onPressed: () {
              // Hiển thị hộp thoại xác nhận trước khi chuyển đơn
              controller.showConfirmDialog("xóa", "", "", () async {
                await controller.deleteDieuXe();
              });
            },
            icon: const Icon(Icons.delete_forever_outlined))
      ],
    ],
  );
}
