import 'dart:convert';


class LuongThangInfo {
  LuongThangInfo({
    required this.id,
    required this.nam,
    required this.thang,
    required this.vienChucId,
    required this.mavienchuc,
    required this.hovaten,
    required this.sotaikhoan,
    required this.tenkhoa,
    required this.tenbomon,
    required this.chucvu,
    required this.soCmnd,
    required this.thanhtienPctnng,
    required this.thanhtien,
    required this.tlluongvienchucmoi,
    required this.tlpcgvvienchucmoi,
    required this.phucapuudai,
    required this.tlnangluong,
    required this.truylanhnangphantramhopdong,
    required this.truylanhPccv,
    required this.truylanhPctnng,
    required this.truylanhkhac,
    required this.bhyt,
    required this.bhxh,
    required this.bhtn,
    required this.kpcd,
    required this.thueTncn,
    required this.tienquy,
    required this.truythukhac,
    required this.tonglanh,
    required this.ghiChu,
  });

  final int? id;
  final int? nam;
  final int? thang;
  final int? vienChucId;
  final String? mavienchuc;
  final String? hovaten;
  final String? sotaikhoan;
  final String? tenkhoa;
  final String? tenbomon;
  final String? chucvu;
  final String? soCmnd;
  final num? thanhtienPctnng;
  final num? thanhtien;
  final num? tlluongvienchucmoi;
  final num? tlpcgvvienchucmoi;
  final num? phucapuudai;
  final num? tlnangluong;
  final num? truylanhnangphantramhopdong;
  final num? truylanhPccv;
  final num? truylanhPctnng;
  final num? truylanhkhac;
  final num? bhyt;
  final num? bhxh;
  final num? bhtn;
  final num? kpcd;
  final num? thueTncn;
  final num? tienquy;
  final num? truythukhac;
  final num? tonglanh;
  final dynamic ghiChu;

  LuongThangInfo copyWith({
    int? id,
    int? nam,
    int? thang,
    int? vienChucId,
    String? mavienchuc,
    String? hovaten,
    String? sotaikhoan,
    String? tenkhoa,
    String? tenbomon,
    String? chucvu,
    String? soCmnd,
    num? thanhtienPctnng,
    num? thanhtien,
    num? tlluongvienchucmoi,
    num? tlpcgvvienchucmoi,
    num? phucapuudai,
    num? tlnangluong,
    num? truylanhnangphantramhopdong,
    num? truylanhPccv,
    num? truylanhPctnng,
    num? truylanhkhac,
    num? bhyt,
    num? bhxh,
    num? bhtn,
    num? kpcd,
    num? thueTncn,
    num? tienquy,
    num? truythukhac,
    num? tonglanh,
    dynamic ghiChu,
  }) {
    return LuongThangInfo(
      id: id ?? this.id,
      nam: nam ?? this.nam,
      thang: thang ?? this.thang,
      vienChucId: vienChucId ?? this.vienChucId,
      mavienchuc: mavienchuc ?? this.mavienchuc,
      hovaten: hovaten ?? this.hovaten,
      sotaikhoan: sotaikhoan ?? this.sotaikhoan,
      tenkhoa: tenkhoa ?? this.tenkhoa,
      tenbomon: tenbomon ?? this.tenbomon,
      chucvu: chucvu ?? this.chucvu,
      soCmnd: soCmnd ?? this.soCmnd,
      thanhtienPctnng: thanhtienPctnng ?? this.thanhtienPctnng,
      thanhtien: thanhtien ?? this.thanhtien,
      tlluongvienchucmoi: tlluongvienchucmoi ?? this.tlluongvienchucmoi,
      tlpcgvvienchucmoi: tlpcgvvienchucmoi ?? this.tlpcgvvienchucmoi,
      phucapuudai: phucapuudai ?? this.phucapuudai,
      tlnangluong: tlnangluong ?? this.tlnangluong,
      truylanhnangphantramhopdong: truylanhnangphantramhopdong ?? this.truylanhnangphantramhopdong,
      truylanhPccv: truylanhPccv ?? this.truylanhPccv,
      truylanhPctnng: truylanhPctnng ?? this.truylanhPctnng,
      truylanhkhac: truylanhkhac ?? this.truylanhkhac,
      bhyt: bhyt ?? this.bhyt,
      bhxh: bhxh ?? this.bhxh,
      bhtn: bhtn ?? this.bhtn,
      kpcd: kpcd ?? this.kpcd,
      thueTncn: thueTncn ?? this.thueTncn,
      tienquy: tienquy ?? this.tienquy,
      truythukhac: truythukhac ?? this.truythukhac,
      tonglanh: tonglanh ?? this.tonglanh,
      ghiChu: ghiChu ?? this.ghiChu,
    );
  }

  factory LuongThangInfo.fromJson(Map<String, dynamic> json){
    //Logger().i(json);
    return LuongThangInfo(
      id: json["id"],
      nam: json["nam"],
      thang: json["thang"],
      vienChucId: json["vienChucId"],
      mavienchuc: json["mavienchuc"],
      hovaten: json["hovaten"],
      sotaikhoan: json["sotaikhoan"],
      tenkhoa: json["tenkhoa"],
      tenbomon: json["tenbomon"],
      chucvu: json["chucvu"],
      soCmnd: json["soCmnd"],
      thanhtienPctnng:json["thanhtienPctnng"],
      thanhtien:json["thanhtien"],
      tlluongvienchucmoi:json["tlluongvienchucmoi"],
      tlpcgvvienchucmoi:json["tlpcgvvienchucmoi"],
      phucapuudai:json["phucapuudai"],
      tlnangluong:json["tlnangluong"],
      truylanhnangphantramhopdong:json["truylanhnangphantramhopdong"],
      truylanhPccv:json["truylanhPccv"],
      truylanhPctnng:json["truylanhPctnng"],
      truylanhkhac:json["truylanhkhac"],
      bhyt:json["bhyt"],
      bhxh:json["bhxh"],
      bhtn:json["bhtn"],
      kpcd:json["kpcd"],
      thueTncn:json["thueTncn"],
      tienquy:json["tienquy"],
      truythukhac:json["truythukhac"],
      tonglanh:json["tonglanh"],
      ghiChu: json["ghiChu"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "nam": nam,
    "thang": thang,
    "vienChucId": vienChucId,
    "mavienchuc": mavienchuc,
    "hovaten": hovaten,
    "sotaikhoan": sotaikhoan,
    "tenkhoa": tenkhoa,
    "tenbomon": tenbomon,
    "chucvu": chucvu,
    "soCmnd": soCmnd,
    "thanhtienPctnng": thanhtienPctnng,
    "thanhtien": thanhtien,
    "tlluongvienchucmoi": tlluongvienchucmoi,
    "tlpcgvvienchucmoi": tlpcgvvienchucmoi,
    "phucapuudai": phucapuudai,
    "tlnangluong": tlnangluong,
    "truylanhnangphantramhopdong": truylanhnangphantramhopdong,
    "truylanhPccv": truylanhPccv,
    "truylanhPctnng": truylanhPctnng,
    "truylanhkhac": truylanhkhac,
    "bhyt": bhyt,
    "bhxh": bhxh,
    "bhtn": bhtn,
    "kpcd": kpcd,
    "thueTncn": thueTncn,
    "tienquy": tienquy,
    "truythukhac": truythukhac,
    "tonglanh": tonglanh,
    "ghiChu": ghiChu,
  };
  factory LuongThangInfo.fromJsonString(String source) =>
      LuongThangInfo.fromJson(json.decode(source));

  @override
  String toString(){
    return "$id, $nam, $thang, $vienChucId, $mavienchuc, $hovaten, $sotaikhoan, $tenkhoa, $tenbomon, $chucvu, $soCmnd, $thanhtienPctnng, $thanhtien, $tlluongvienchucmoi, $tlpcgvvienchucmoi, $phucapuudai, $tlnangluong, $truylanhnangphantramhopdong, $truylanhPccv, $truylanhPctnng, $truylanhkhac, $bhyt, $bhxh, $bhtn, $kpcd, $thueTncn, $tienquy, $truythukhac, $tonglanh, $ghiChu, ";
  }
}
