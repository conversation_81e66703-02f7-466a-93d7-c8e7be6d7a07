import 'package:get/get.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:tvumobile/app/services/api_provider.dart';

class QrScannerController extends GetxController {
  ApiProvider apiProvider = Get.find();
  // final MobileScannerController cameraController = MobileScannerController();
  final MobileScannerController cameraController = MobileScannerController(
    formats: const [BarcodeFormat.qrCode],
  );

  RxInt selectedIndex = 1.obs;
  bool isCameraDisposed = false;
  RxBool isScanCompleted = false.obs;

  @override
  Future<void> onReady() async {
    if (selectedIndex.value == 1) {
      startCamera();
    }
  }

  void startCamera() {
    if (isCameraDisposed) return;
    try {
      if (!cameraController.value.isRunning) {
        cameraController.start();
      }
    } catch (e) {
      print('Error starting camera: $e');
    }
  }

  void stopCamera() {
    if (isCameraDisposed) return;
    try {
      if (cameraController.value.isRunning) {
        cameraController.stop();
      }
    } catch (e) {
      print('Error stopping camera: $e');
    }
  }

  Future<void> resetScan() async {
    isScanCompleted.value = false;
    startCamera();
  }

  @override
  void dispose() {
    isCameraDisposed = true;
    cameraController.dispose();
    super.dispose();
  }
}
