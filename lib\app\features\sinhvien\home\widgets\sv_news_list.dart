import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/data/models/portal/portal_news_model.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class SvNewsList extends StatelessWidget {
  SvNewsList({super.key, required this.portalNews});
  final List<PortalNewsModel>? portalNews;

  final List<Color> bgColor = [
    const Color(0xFFFFE2C2),
    const Color(0xFFD9839F),
    const Color(0xFFFFE2C2)
  ];
  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    //var portalNews = homeState.state1;
    //printInfo(info: portalNews.toString());
    if (portalNews!.isEmpty) {
      return Container();
    }
    return Column(
      children: [
        ListView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: portalNews?.length,
            itemBuilder: (ctx, index) {
              String heroKey = "portalNews$index";
              return InkWell(
                  onTap: () {
                    //Get.to(() => SinglePortalNewsScreen(portalNews![index], heroKey, portalNews!), preventDuplicates: true, transition: Transition.fadeIn);
                  },
                  child: Container(
                    margin: EdgeInsets.zero,
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    // border only from top and bottom
                    decoration: BoxDecoration(
                        color: theme.colorScheme
                            .secondaryContainer, //newsItemTheme?.backgroundColor,
                        border: Border(
                          bottom: BorderSide(
                              color: theme.dividerColor.withOpacity(0.4)),
                          top: BorderSide(
                            color: Get.isDarkMode
                                ? const Color(0xFF414141)
                                : const Color(0xFFF6F6F6),
                          ),
                        )),
                    child: Row(
                      children: [
                        Hero(
                            tag: heroKey,
                            child: Container(
                              height: 6 * 15,
                              width: 9 * 15,
                              decoration: BoxDecoration(
                                  color: Colors
                                      .transparent, //bgColor[_random.nextInt(bgColor.length)],
                                  borderRadius: BorderRadius.circular(8),
                                  image: DecorationImage(
                                      image: NetworkImage(
                                          portalNews?[index].image == ""
                                              ? AppImages.defaultImgUrl
                                              : portalNews![index]
                                                  .image
                                                  .toString()),
                                      fit: BoxFit.cover)),
                            )),
                        MySpacing.width(17),
                        Expanded(
                            child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            MyText.bodySmall(
                              portalNews![index].title.toString(),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            MySpacing.height(4),
                            Row(
                              children: [
                                Icon(Icons.edit_calendar),
                                MySpacing.width(6),
                                MyText.bodySmall(
                                    portalNews![index].publishDate.toString()),
                              ],
                            ),
                            MySpacing.height(6),
                          ],
                        ))
                      ],
                    ),
                  ));
            }),
      ],
    );
  }
}
