

import 'package:flutter/material.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/luongthuethang_controller.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

Widget buildThueNamInfo(LuongThueController controller, ThemeData theme) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Center(
          child: Column(
            children: [
              const Divider(),
              MyText.titleMedium(
                "Thông tin thuế năm ${controller.thueNam}".toUpperCase(),
                fontWeight: 900,
              ),
              const Divider(),
            ],
          ),
        ),
      ),
      if (controller.thueNamInfo.isEmpty)
        controller.currentDataAvaliable.value ?
        SizedBox(
          height: 200,
          child: SkeletonListView(),
        ) : Center(child: MyText.titleLarge("KHÔNG CÓ DỮ LIỆU")),
      if (controller.thueNamInfo.isNotEmpty)
        _buildLuongThangDetails(controller, theme),
    ],
  );
}

Widget _buildLuongThangDetails(LuongThueController controller, ThemeData theme) {

  final thueThang = controller.thueNamInfo.first;
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Center(
        child: Column(
          children: [
            MyText.titleMedium("Số thuế còn phải nộp".toUpperCase()),
            MyText.titleLarge(
              controller.viFormat.format(thueThang.sothueconphainop),
              fontWeight: 900,
              fontSize: 24,
            ),
            const SizedBox(height: 16,),
            MyText.titleMedium("Số thuế đã nộp thừa".toUpperCase()),
            MyText.titleLarge(
              controller.viFormat.format(thueThang.sothuedanopthua),
              fontWeight: 900,
              fontSize: 24,
            ),
          ],
        ),
      ),
      MySpacing.height(16),
      _buildSectionHeader(theme, "NỘP THUẾ"),
      _buildLuongItem(controller,
          theme, "Tổng số tiền phải nộp:", thueThang.tongsothuephainop!.toDouble()),
      _buildLuongItem(controller, theme, "Thuế TNCN đã khấu trừ:", thueThang.tongsothueTncndakhautru!.toDouble()),
      _buildSectionHeader(theme, "THU NHẬP"),
      _buildLuongItem(controller, theme, "Tổng thu nhập chịu thuế:", thueThang.tongthunhapchiuthue!.toDouble()),
      _buildLuongItem(controller, theme, "Thu nhập tính thuế:", thueThang.thunhaptinhthue!.toDouble()),
      _buildSectionHeader(theme, "GIẢM TRỪ"),
      _buildLuongItem(controller,
          theme, "Tổng giảm trừ gia cảnh:", thueThang.tongsotiengiamtrugiacanh!.toDouble()),
      _buildLuongItem(controller, theme, "Bảo hiểm được trừ:", thueThang.baohiemduoctru!.toDouble()),
      _buildLuongItem(controller, theme, "Các loại quỹ:",
          thueThang.cacloaiquy!.toDouble()),
    ],
  );
}

Widget _buildSectionHeader(ThemeData theme, String title) {
  return Align(
    alignment: Alignment.centerLeft,
    child: MyContainer(
      paddingAll: 6,
      width: double.infinity,
      color: theme.secondaryHeaderColor,
      child: MyText.titleMedium(title),
    ),
  );
}

Widget _buildLuongItem(LuongThueController controller, ThemeData theme, String label, double value) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4.0),
    child: Row(
      children: [
        SizedBox(
          width: 200,
          child: MyText.titleMedium(label),
        ),
        MyText.titleMedium(controller.viFormat.format(value)),
      ],
    ),
  );
}