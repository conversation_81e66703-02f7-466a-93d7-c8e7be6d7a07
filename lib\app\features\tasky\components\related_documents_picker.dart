import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'dart:async';

import 'package:tvumobile/app/shared_components/mytext.dart';

class RelatedDocumentsPicker extends StatefulWidget {
  final List<dynamic> initialDocuments;
  final List<dynamic> availableDocuments;
  final Future<Map<String, dynamic>> Function(
      String query, int pageNumber, int pageSize) onSearch;
  final void Function(List<dynamic>) onChanged;
  final int? maxSelections;
  final String attachTitleText;
  final String buttonText;
  final String emptyText;

  const RelatedDocumentsPicker({
    super.key,
    required this.initialDocuments,
    required this.availableDocuments,
    required this.onSearch,
    required this.onChanged,
    this.maxSelections,
    this.buttonText = 'Chọn văn bản',
    this.emptyText = 'Chưa có văn bản nào được chọn.',
    this.attachTitleText = '<PERSON><PERSON> văn bản liên quan không?',
  });

  @override
  // ignore: library_private_types_in_public_api
  _RelatedDocumentsPickerState createState() => _RelatedDocumentsPickerState();
}

class _RelatedDocumentsPickerState extends State<RelatedDocumentsPicker> {
  List<dynamic> _selectedDocuments = [];

  @override
  void initState() {
    super.initState();
    _selectedDocuments = List.from(widget.initialDocuments);
  }

  void _openDocumentSelectionDialog() async {
    List<dynamic>? selectedDocuments = await showDialog<List<dynamic>>(
      context: context,
      builder: (context) {
        return Dialog(
          insetPadding: EdgeInsets.all(16),
          child: _DocumentSelectionDialog(
            selectedDocuments: List.from(_selectedDocuments),
            availableDocuments: widget.availableDocuments,
            onSearch: widget.onSearch,
            maxSelections: widget.maxSelections,
          ),
        );
      },
    );

    if (selectedDocuments != null) {
      setState(() {
        _selectedDocuments = selectedDocuments;
      });
      widget.onChanged(_selectedDocuments);
    }
  }

  void _removeSelectedDocument(dynamic document) {
    setState(() {
      _selectedDocuments
          .removeWhere((selected) => selected["id"] == document["id"]);
    });
    widget.onChanged(_selectedDocuments);
  }

  Widget _buildSelectedDocuments() {
    if (_selectedDocuments.isEmpty) {
      return Text(widget.emptyText, style: const TextStyle(color: Colors.grey));
    }

    return Wrap(
      spacing: 8,
      runSpacing: 0,
      children: _selectedDocuments.map((document) {
        String name = document["soCongVan"]?.toString() ?? '';
        name = document["kyHieuGoc"].toString().isNotEmpty
            ? '$name - ${document["kyHieuGoc"]}'
            : name;
        return Chip(
          avatar: CircleAvatar(
            child: Icon(
              Icons.description,
              size: 15,
            ),
          ),
          label: Text(name),
          deleteIcon: const Icon(Icons.close, size: 18),
          onDeleted: () => _removeSelectedDocument(document),
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            MyText.bodyMedium(
              widget.attachTitleText,
              fontWeight: 700,
            ),
            Spacer(),
            MyButton.small(
              onPressed: _openDocumentSelectionDialog,
              backgroundColor: theme.colorScheme.primaryContainer,
              elevation: 2,
              child: Row(
                children: [
                  Icon(
                    Icons.add_link,
                    size: 20,
                    color: theme.colorScheme.onPrimary,
                  ),
                  SizedBox(width: 6),
                  MyText.labelLarge(
                    widget.buttonText,
                    color: theme.colorScheme.onPrimary,
                  ),
                ],
              ),
            )
          ],
        ),
        _buildSelectedDocuments(),
      ],
    );
  }
}

class _DocumentSelectionDialog extends StatefulWidget {
  final List<dynamic> selectedDocuments;
  final List<dynamic> availableDocuments;
  final Future<Map<String, dynamic>> Function(
      String query, int pageNumber, int pageSize) onSearch;
  final int? maxSelections;

  const _DocumentSelectionDialog({
    required this.selectedDocuments,
    required this.availableDocuments,
    required this.onSearch,
    this.maxSelections,
  });

  @override
  __DocumentSelectionDialogState createState() =>
      __DocumentSelectionDialogState();
}

class __DocumentSelectionDialogState extends State<_DocumentSelectionDialog> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  Timer? _debounce;
  List<dynamic> _selectedDocuments = [];
  List<dynamic> _displayedDocuments = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  int _currentPage = 1;
  // ignore: prefer_final_fields
  int _pageSize = 20;

  @override
  void initState() {
    super.initState();
    _selectedDocuments = List.from(widget.selectedDocuments);
    _displayedDocuments = List.from(widget.availableDocuments);
    _searchController.addListener(_onSearchTextChanged);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchTextChanged);
    _searchController.dispose();
    _debounce?.cancel();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onSearchTextChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 700), () {
      _performSearch(_searchController.text);
    });
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore &&
        _hasMoreData) {
      _loadMoreData();
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.isEmpty) {
      setState(() {
        _displayedDocuments = List.from(widget.availableDocuments);
        _isLoading = false;
        _hasMoreData = false;
        _currentPage = 1;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _currentPage = 1;
      _hasMoreData = true;
    });

    try {
      final response = await widget.onSearch(query, _currentPage, _pageSize);
      List<dynamic> results = response['results'];
      int totalPages = response['totalPages'];

      setState(() {
        _displayedDocuments = results;
        _hasMoreData = _currentPage < totalPages;
      });
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Có lỗi xảy ra khi tìm kiếm: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreData() async {
    if (!_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    try {
      final response = await widget.onSearch(
          _searchController.text, _currentPage, _pageSize);
      List<dynamic> newResults = response['results'];
      int totalPages = response['totalPages'];

      setState(() {
        _displayedDocuments.addAll(newResults);
        _hasMoreData = _currentPage < totalPages;
      });
    } catch (e) {
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Có lỗi xảy ra khi tải thêm dữ liệu: $e')),
      );
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  void _selectDocument(dynamic document) {
    if (widget.maxSelections != null &&
        _selectedDocuments.length >= widget.maxSelections! &&
        !_selectedDocuments.any((d) => d["id"] == document["id"])) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content:
                Text('Chỉ có thể chọn tối đa ${widget.maxSelections} văn bản')),
      );
      return;
    }

    setState(() {
      if (_selectedDocuments
          .any((selected) => selected["id"] == document["id"])) {
        _selectedDocuments
            .removeWhere((selected) => selected["id"] == document["id"]);
      } else {
        _selectedDocuments.add(document);
      }
    });
  }

  void _onSave() {
    Navigator.of(context).pop(_selectedDocuments);
  }

  Widget _buildSearchField() {
    return TextField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: 'Tìm kiếm văn bản...',
        prefixIcon: const Icon(Icons.search),
        suffixIcon: _isLoading
            ? const Padding(
                padding: EdgeInsets.all(10.0),
                child: SizedBox(
                  width: 15,
                  height: 15,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            : _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        _displayedDocuments =
                            List.from(widget.availableDocuments);
                        _hasMoreData = false;
                        _currentPage = 1;
                      });
                    },
                  )
                : null,
        border: const OutlineInputBorder(),
      ),
    );
  }

  Widget _buildSelectedDocuments() {
    if (_selectedDocuments.isEmpty) {
      return Text('Chưa có văn bản nào được chọn.');
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _selectedDocuments.map((document) {
        String name = document["soCongVan"].toString();
        name = document["kyHieuGoc"].toString().isNotEmpty
            ? '$name - ${document["kyHieuGoc"]}'
            : name;
        return Chip(
          avatar: CircleAvatar(
            child: Icon(
              Icons.description,
              size: 15,
            ),
          ),
          label: Text(name),
          deleteIcon: const Icon(Icons.close, size: 18),
          onDeleted: () => _selectDocument(document),
        );
      }).toList(),
    );
  }

  Widget _buildDocumentList() {
    if (_displayedDocuments.isEmpty && !_isLoading) {
      return Center(child: Text('Không có văn bản nào.'));
    }

    return ListView.builder(
      controller: _scrollController,
      itemCount: _displayedDocuments.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _displayedDocuments.length) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final document = _displayedDocuments[index];
        String name = document["soCongVan"]?.toString() ?? '';
        name = document["kyHieuGoc"].toString().isNotEmpty
            ? '$name - ${document["kyHieuGoc"]}'
            : name;
        String trichYeu = document["trichYeu"] ?? '';
        final isSelected =
            _selectedDocuments.any((d) => d["id"] == document["id"]);

        return ListTile(
          selected: isSelected,
          //dense: true,
          leading: CircleAvatar(child: Icon(Icons.description)),
          title: Text(name),
          subtitle: Text(
            trichYeu,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          trailing: isSelected ? Icon(Icons.check, color: Colors.green) : null,
          onTap: () => _selectDocument(document),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Material(
      type: MaterialType.transparency,
      child: Container(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Text(
              'Chọn văn bản liên quan',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            _buildSearchField(),
            const SizedBox(height: 8),
            _buildSelectedDocuments(),
            const SizedBox(height: 8),
            Expanded(
              child: _isLoading
                  ? Center(child: CircularProgressIndicator())
                  : _buildDocumentList(),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                MyButton.small(
                  onPressed: () => Navigator.of(context).pop(),
                  backgroundColor:
                      theme.colorScheme.primaryContainer.withOpacity(0.2),
                  borderRadiusAll: 2,
                  borderColor: theme.colorScheme.secondary,
                  child: MyText.labelLarge('Hủy',
                      color: theme.colorScheme.onSurface),
                ),
                const SizedBox(width: 8),
                MyButton.small(
                  onPressed: _onSave,
                  child: MyText.labelLarge(
                    'Lưu',
                    color: theme.colorScheme.onPrimary,
                    fontWeight: 700,
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
