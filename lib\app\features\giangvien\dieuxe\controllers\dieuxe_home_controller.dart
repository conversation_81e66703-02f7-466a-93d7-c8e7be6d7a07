import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxeinfo.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/giaydieuxe.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/utils/helpers/pagination_filter.dart';

class DieuXeHomeController extends GetxController {
  ApiProvider apiProvider = Get.find();
  late DieuXeInfo dieuXeInfo;
  final donvi = ''.obs;
  final isLanhDaoOrBGH = ''.obs;
  List<GiayDieuXe> dsDX = <GiayDieuXe>[].obs;
  List<GiayDieuXe> dxDaLoc = <GiayDieuXe>[].obs;

// Khai báo biến sử dụng cho lazyloadview
  final _paginationFilter = PaginationFilter().obs;
  final _lastPage = false.obs;
  int get limit => _paginationFilter.value.limit;
  int get page => _paginationFilter.value.page;
  bool get lastPage => _lastPage.value;
  final isLoading = false.obs;
  final appbarTitle = "".obs;

  // Trạng thái chế độ chọn
  final selectMode = false.obs;
  final selectedDieuXe = <String, bool>{}.obs;
  final selectedIdDieuXe = <String>[].obs; // Lưu ID của 2 phiếu khi chọn đúng 2

  // Khai báo drawer
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final RxString currentView = "".obs;
  final RxString danhMuc = "".obs; // Lưu danhMuc trong controller
  final RxString phanQuyen = "".obs; // Lưu phanQuyen trong controller
  final currentDataAvaliable = true.obs;

  // Khai báo filter
  // Lọc giấy điều xe mới (phân quyền: cá nhân)
  final RxList<String> statusListNew = <String>[
    "Tất cả",
    "Khởi tạo",
    "Chờ duyệt",
    "Đơn vị đã duyệt",
    "Đơn vị yêu cầu điều chỉnh",
    "PHCTH yêu cầu điều chỉnh",
  ].obs;

  // Lọc giấy điều xe đã duyệt (phân quyền: cá nhân)
  final RxList<String> statusListApproved = <String>[
    "Tất cả",
    "Phòng HC-TC đã duyệt",
    "Thuê xe ngoài",
  ].obs;

  // Lọc giấy điều xe mới (phân quyền: trưởng đơn vị)
  final RxList<String> statusListDVNew = <String>[
    "Tất cả",
    "Khởi tạo",
    // "Chờ duyệt",
    // "Đơn vị đã duyệt",
    // "Đơn vị yêu cầu điều chỉnh",
    "PHCTH yêu cầu điều chỉnh",
  ].obs;

  // Lọc giấy điều xe chờ đơn vị hoặc đã được đơn vị duyệt (phân quyền: trưởng đơn vị)
  final RxList<String> statusListDVApproved = <String>[
    "Tất cả",
    "Chờ duyệt",
    "Đơn vị đã duyệt",
  ].obs;

  // Lọc giấy điều xe chờ duyệt hoặc đã được phòng hcth duyệt (phân quyền: phòng hcth)
  final RxList<String> statusListHCTHApproved = <String>[
    "Tất cả",
    "Đơn vị đã duyệt",
    "Phòng HC-TC đã duyệt",
    "Thuê xe ngoài",
  ].obs;

  final RxString currentStatus = "Tất cả".obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    if (kDebugMode) {
      print("DieuXeHomeController onInit");
    }
    // ever(currentStatus, (_) => loadDieuXe());
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    if (kDebugMode) {
      print("DieuXeHomeController onReady");
    }
    await checkPhanQuyen();
    currentView.listen(changeView);
    currentView.value = "moi";
  }

  // ------------------------- DRAWER -------------------------
  // Mở drawer
  void openDrawer() {
    scaffoldKey.currentState?.openDrawer();
  }

  // Đóng drawer
  void closeDrawer() {
    if (scaffoldKey.currentState!.isEndDrawerOpen) {
      scaffoldKey.currentState!.closeEndDrawer();
    } else {
      scaffoldKey.currentState!.closeDrawer();
    }
  }

  // Xử lý sự kiện khi người dùng chọn 1 mục trong drawer
  void drawerClick(String view) {
    currentView.value = view;
    closeDrawer();
    update();
  }
  // ----------------------------------------------------------

  // ---------------------- THAY ĐỔI VIEW ---------------------
  // Thay đổi chế độ xem và cập nhật dữ liệu tương ứng
  Future<void> changeView(data) async {
    currentDataAvaliable.value = true;
    currentStatus.value = "Tất cả";
    isLoading.value = true; // Bắt đầu loading

    switch (currentView.value) {
      case "moi":
        danhMuc.value = "moi";
        appbarTitle.value = "giấy điều xe mới";
        break;
      case "duyet":
        danhMuc.value = "duyet";
        appbarTitle.value = "giấy điều xe đã duyệt";
        break;
      case "huy":
        danhMuc.value = "huy";
        appbarTitle.value = "giấy điều xe đã hủy";
        break;
      case "donviduyet":
        danhMuc.value = "donviduyet";
        appbarTitle.value = "đơn vị duyệt điều xe";
        break;
      case "hcthduyet":
        danhMuc.value = "hcthduyet";
        appbarTitle.value = "phòng hcth duyệt điều xe";
        break;
    }
    await loadDieuXe();
    isLoading.value = false; // Kết thúc loading
    update();
  }

  // Kiểm tra phân quyền
  Future<void> checkPhanQuyen() async {
    try {
      var result = await apiProvider.checkPhanQuyenDieuXe();
      if (result.contains("taichinh") || result.contains("bgh")) {
        phanQuyen.value = "truongdonvi";
      } else {
        phanQuyen.value = result;
      }
      // phanQuyen.value = "phonghcth";
    } catch (e) {
      if (kDebugMode) {
        print("Error checking LanhDao or BGH: $e");
      }
    }
    update();
  }

  // -- Làm mới danh sách phiếu điều xe (reset về trang đầu) --
  Future<void> loadDieuXe() async {
    _paginationFilter.value.page = 1;
    _lastPage.value = false;
    dsDX.clear();

    var tmp = await apiProvider.getDsPhieuDieuXe(
      page: _paginationFilter.value.page,
      pageSize: _paginationFilter.value.limit,
      phanQuyen: phanQuyen.value,
      danhMuc: danhMuc.value,
      trangThai: currentStatus.value,
    );

    if (tmp.isEmpty) {
      _lastPage.value = true;
      // showLastPage();
      update();
    } else {
      dsDX.addAll(tmp);
      // Khởi tạo trạng thái chọn cho từng phiếu
      selectedDieuXe.assignAll({for (var dx in dsDX) dx.id.toString(): false});
      update();
    }
  }

  // --- Tải thêm phiếu điều xe khi cuộn đến cuối danh sách ---
  Future<void> loadMoreDieuXe() async {
    if (kDebugMode) {
      print("TaskyTaskViewController loadMoreTasks");
    }
    if (_lastPage.value) {
      return;
    }
    _paginationFilter.value.page += 1;

    var tmp = await apiProvider.getDsPhieuDieuXe(
      page: _paginationFilter.value.page,
      pageSize: _paginationFilter.value.limit,
      phanQuyen: phanQuyen.value,
      danhMuc: danhMuc.value,
      trangThai: currentStatus.value,
    );

    if (tmp.isEmpty) {
      _lastPage.value = true;
    } else {
      dsDX.addAll(tmp);
      // Cập nhật trạng thái chọn cho các phiếu mới
      selectedDieuXe.addAll({for (var dx in tmp) dx.id.toString(): false});
      update();
    }
  }

  // Bật/tắt chế độ chọn khi long press
  void toggleSelection(String dxId) {
    // Kiểm tra xem phiếu có phải là phiếu ghép không
    final dx = dsDX.firstWhereOrNull((dx) => dx.id.toString() == dxId);
    if (dx != null && dx.laPhieuGhep == true) {
      Get.snackbar(
        'Thông báo',
        'Không thể chọn phiếu ghép để ghép thêm.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.orange.withOpacity(0.8),
        colorText: Colors.white,
      );
      return;
    }

    // selectMode.value = true; // Kích hoạt chế độ chọn khi có hành động chọn
    selectedDieuXe[dxId] = !(selectedDieuXe[dxId] ?? false);
    // // Kiểm tra nếu không còn phiếu nào được chọn, tắt chế độ chọn
    // if (!selectedDieuXe.containsValue(true)) {
    //   selectMode.value = false;
    // }
    // selectedDieuXe.refresh();

    // Cập nhật selectedIdDieuXe khi có đúng 2 phiếu
    final selectedIds = selectedDieuXe.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();
    if (selectedIds.length >= 2) {
      selectedIdDieuXe.assignAll(selectedIds);
    } else {
      selectedIdDieuXe.clear();
    }

    // Cập nhật chế độ chọn
    selectMode.value = selectedDieuXe.containsValue(true);
    selectedDieuXe.refresh();
    selectedIdDieuXe.refresh();
  }

  // ------------- CHỌN THEO TRẠNG THÁI PHIẾU ĐX --------------
  // Future<void> _filterDXByStatus() async {
  //   if (currentStatus.value == "Tất cả") {
  //     dxDaLoc.assignAll(dsDX);
  //   } else {
  //     dxDaLoc.assignAll(
  //       dsDX.where((dx) => dx.trangThai == currentStatus.value).toList(),
  //     );
  //   }
  //   update();
  // }

  // Thêm phương thức để làm mới dữ liệu khi quay về
  void refreshData() async {
    _lastPage.value = false;
    dsDX.clear();
    selectedDieuXe.clear();
    selectedIdDieuXe.clear();
    selectMode.value = false; // Đặt lại chế độ chọn khi làm mới
    await loadDieuXe();
  }
}
