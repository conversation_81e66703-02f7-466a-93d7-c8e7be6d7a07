import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/giaydieuxe.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_detail_controller.dart';

// Widget hiển thị dialog để nhập lý do không duyệt
void showDieuXeReasonDialog(DieuXeDetailController controller,
    {required GiayDieuXe pdx}) {
  // Kiểm tra xem phiếu điều xe có hợp lệ không
  if (pdx.id == -1) {
    Get.snackbar(
      'Lỗi',
      'Không thể trả về: Đơn nghỉ phép không hợp lệ',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.withOpacity(0.8),
      colorText: Colors.white,
    );
    return;
  }
  TextEditingController lydo = TextEditingController();

  Get.dialog(
    barrierColor: Colors.black.withOpacity(0.5),
    barrierDismissible: true, // Cho phép đóng dialog khi nhấn ngoài
    Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: StatefulBuilder(
        builder: (context, setState) {
          return Container(
            constraints: BoxConstraints(
              maxWidth: Get.width * 0.8, // Chiều rộng tối đa 80% màn hình
              maxHeight: Get.height * 0.8, // Chiều cao tối đa 60% màn hình
            ),
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 2,
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Hủy/Điều chỉnh?',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close, color: Colors.grey),
                      onPressed: () => Get.back(),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                TextField(
                  controller: lydo,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'Lý do hủy/Yêu cầu điều chỉnh',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Color(0xFF1157FA)),
                    ),
                  ),
                ),
                SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () async {
                        await controller.traDieuXe(lydo.text);
                        Get.back(); // Đóng dialog sau khi hủy
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        padding:
                            EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Hủy phiếu',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        // Điều chỉnh yêu cầu lý do
                        if (lydo.text.trim().isEmpty) {
                          Get.snackbar(
                            'Lỗi',
                            'Vui lòng nhập lý do điều chỉnh',
                            snackPosition: SnackPosition.TOP,
                            backgroundColor: Colors.red.withOpacity(0.8),
                            colorText: Colors.white,
                          );
                          return;
                        }
                        await controller.dieuchinhDieuXe(lydo.text);
                        Get.back(); // Đóng dialog sau khi điều chỉnh
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        padding:
                            EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Điều chỉnh',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    ),
  );
}

// Widget hiển thị dialog để nhập lý do không duyệt
void showHuyDieuXeReasonDialog(DieuXeDetailController controller,
    {required GiayDieuXe pdx}) {
  // Kiểm tra xem phiếu điều xe có hợp lệ không
  if (pdx.id == -1) {
    Get.snackbar(
      'Lỗi',
      'Không thể trả về: Đơn nghỉ phép không hợp lệ',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.withOpacity(0.8),
      colorText: Colors.white,
    );
    return;
  }
  TextEditingController lydo = TextEditingController();

  Get.dialog(
    barrierColor: Colors.black.withOpacity(0.5),
    barrierDismissible: true, // Cho phép đóng dialog khi nhấn ngoài
    Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: StatefulBuilder(
        builder: (context, setState) {
          return Container(
            constraints: BoxConstraints(
              maxWidth: Get.width * 0.8, // Chiều rộng tối đa 80% màn hình
              maxHeight: Get.height * 0.8, // Chiều cao tối đa 60% màn hình
            ),
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 2,
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Hủy giấy đề nghị điều xe?',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    // IconButton(
                    //   icon: Icon(Icons.close, color: Colors.grey),
                    //   onPressed: () => Get.back(),
                    // ),
                  ],
                ),
                SizedBox(height: 8),
                TextField(
                  controller: lydo,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'Lý do hủy',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Color(0xFF1157FA)),
                    ),
                  ),
                ),
                SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () => Get.back(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey,
                        padding:
                            EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Đóng',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        await controller.traDieuXe(lydo.text);
                        Get.back(); // Đóng dialog sau khi hủy
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        padding:
                            EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Hủy phiếu',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    ),
  );
}
