import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:syncfusion_flutter_pdf/pdf.dart'
    show PdfBitmap, PdfDocument, PdfPage;
import 'package:uuid/uuid.dart';

import '../utils/helpers.dart';
import 'signature_model.dart';
import 'utils.dart';

class PdfViewerController extends GetxController {
  final pdfDocument = Rx<File?>(null);
  final rect = Rx<Rect?>(null);
  final viewRect = Rx<Rect?>(null);
  final viewOffset = Rx<Offset?>(null);
  final currentPage = RxInt(1);
  final pageSize = Rx<Size>(const Size(0, 0));
  final totalPage = RxInt(1);
  final pdfBytesDocument = Rx<Uint8List?>(null);
  String? pdfUrl;
  String? signatureUrl;
  //for signature
  final signatureModel = Rx<SignatureModel?>(null);
  final listSignature = RxList<SignatureModel>([]);
  final error = RxString('');
  double toolbarSize = 120;
  final viewerInited = Rx<bool>(false);
  final pdwViewerPadding = 8.0;
  final GlobalKey pdfWidgetKey = GlobalKey();
  final viewerHeight = RxDouble(510);
  final viewerZoom = RxDouble(1.0);
  final signatureBytes = Rx<Uint8List?>(null);

  bool displayFullName = false;
  bool displayDepartment = false;
  bool displayPosition = false;
  bool displayDatetime = false;
  bool stampStyle = false; // Re-introduce _stampStyle variable
  bool isKyTat = false;
  bool isCondau = false;
  List<Map<String, String>> availableSignatures = [];
  Map<String, String> signerInfo = {};

  @override
  Future<void> onInit() async {
    debugPrint("PdfViewerController call");
    super.onInit();
    debugPrint('Arguments: ${Get.arguments}');
    pdfUrl = Get.parameters['pdfUrl'] ?? Get.arguments['pdfUrl'] ?? "";
    signatureUrl =
        Get.parameters['signatureUrl'] ?? Get.arguments['signatureUrl'] ?? "";
    availableSignatures = Get.arguments['availableSignatures'] ?? [];
    signerInfo = Get.arguments['signerInfo'] ?? [];
    if (pdfUrl != "" && signatureUrl != "") {
      loadInitFiles(pdfUrl);
      loadInitSign(signatureUrl);
    }
    pdfBytesDocument.listen((onData) {
      debugPrint("On data pdfBytesDocument");
      if (onData != null) {
        pdfDocument.value = File.fromRawPath(onData);
      }
    });
  }

  Future<void> loadInitFiles(pdfUrl) async {
    pdfBytesDocument.value = await loadPdfFromURL(pdfUrl);
    //pdfDocument.value = File.fromRawPath(pdfBytesDocument.value!);
    update();
  }

  Future<void> loadInitSign(pdfUrl) async {
    signatureBytes.value = await loadSignatureImageFromURL(pdfUrl);
    //getSignature(signature: signer, currentPage: 1);
    update();
  }

  void loadFilePdf(
      {File? fileDocument,
      Rect? rect,
      int? currentPage,
      Rect? viewRect,
      Offset? viewOffset}) {
    try {
      if (fileDocument != null) pdfDocument.value = fileDocument;
      if (rect != null) this.rect.value = rect;
      if (currentPage != null) this.currentPage.value = currentPage;
      if (viewRect != null) this.viewRect.value = viewRect;
      if (viewOffset != null) this.viewOffset.value = viewOffset;
      // print("viewRect: $viewRect");
      // print("viewOffset: $viewOffset");
      //update();
    } catch (e) {
      throw e.toString();
    }
  }

  Future<void> getPageSizeDocument() async {
    if (pdfDocument.value != null) {
      final documentSize = await Utils.getSizeDocument(
        pdfFile: pdfBytesDocument.value!,
        currentPage: currentPage.value,
      );
      if (documentSize != null) {
        pageSize.value = documentSize;
      }
    }
  }

  void getTotalPage(int? totalPageCount) {
    if (pdfDocument.value != null && totalPageCount != null) {
      totalPage.value = totalPageCount;
    }
  }

  img.Image? imageScale;
  void getSignature(
      {Uint8List? signature,
      int? currentPage = 0,
      Size? documentSize = const Size(0, 0),
      Size? documentViewSize = const Size(0, 0),
      Offset? localPosition}) {
    try {
      print("*" * 40);
      print("zoom $viewerZoom");
      // Resize signature image
      imageScale ??= Utils.resizeImage(
          image: signature ?? signatureBytes.value!, height: isKyTat ? 20 : 40);
      if (imageScale == null) return;
      var width = imageScale!.width.toDouble();
      var height = imageScale!.height.toDouble();
      double? fixedLeft;
      double? fixedTop;
      if (localPosition != null) {
        fixedLeft = localPosition.dx - width / 2;
        fixedTop = localPosition.dy - height / 2;
        //fixedTop -= (toolbarSize + 24) / 2;
      } else {
        fixedLeft = documentSize!.width / 2 - width / 2;
        fixedTop = documentSize.height / 2 - height / 2;
        //fixedTop -= (toolbarSize + 24) / 2;
      }
      print("*" * 40);
      print("documentSize: $documentSize");
      print("documentViewSize: $documentViewSize");
      print("*" * 40);
      // Calculate scale
      fixedLeft = fixedLeft * viewerZoom.value;
      fixedTop = fixedTop * viewerZoom.value;
      // documentViewSize =
      //     Size(documentViewSize!.width, documentViewSize.height - toolbarSize);
      if (stampStyle) {
        height = 14 * 4;
      }
      final modelSignature = SignatureModel(
        id: const Uuid().v4(),
        signature: signatureBytes.value,
        onSelected: true,
        isDelete: false,
        currentPage: currentPage,
        fixedWidth: width,
        fixedHeight: height,
        documentSize: documentSize,
        documentViewSize: documentViewSize,
        fixedLeft: fixedLeft,
        fixedTop: fixedTop,
        signerFullName: signerInfo['fullName'],
        signerDepartment: signerInfo['department'],
        signerPosition: signerInfo['position'],
        signerDatetime: DateTime.now().toString(),
        isStampStyle: stampStyle,
      );

      signatureModel.value = modelSignature;
      addSignatureToList(modelSignature: modelSignature);
    } catch (e) {
      error.value = e.toString();
    }
  }

  void addSignatureToList({required SignatureModel modelSignature}) {
    debugPrint("addSignatureToList");
    try {
      listSignature.add(modelSignature);
      debugPrint('ADD : ${listSignature.length}');
    } catch (e) {
      error.value = e.toString();
    }
  }

  void onSelectedSignature({
    required bool onSelected,
    SignatureModel? signatureModel,
  }) {
    try {
      if (signatureModel != null) {
        // Selected one item signature
        for (var i = 0; i < listSignature.length; i++) {
          if (this.signatureModel.value?.id == listSignature[i].id) {
            listSignature[i] = signatureModel.copyWith(onSelected: onSelected);
          } else {
            listSignature[i] =
                listSignature[i].copyWith(onSelected: !onSelected);
          }
        }
        listSignature.refresh();
        update();
      } else {
        if (this.signatureModel.value != null &&
            this.signatureModel.value!.onSelected == true &&
            listSignature.isNotEmpty) {
          final index = listSignature.indexWhere((e) => e.onSelected == true);

          listSignature[index] =
              listSignature[index].copyWith(onSelected: false);
          listSignature.refresh();

          this.signatureModel.value = this.signatureModel.value?.copyWith(
                onSelected: false,
              );
          update();
        }
      }
    } catch (e) {
      error.value = e.toString();
    }
    debugPrint("onSelectedSignature");
    update();
  }

  void deleteSignatureScaling({required SignatureModel signature}) {
    try {
      final index = listSignature.indexWhere((e) => e.id == signature.id);
      listSignature.removeAt(index);

      signatureModel.value = signature.copyWith(
        isDelete: true,
        onSelected: false,
      );

      debugPrint('DELETE : ${listSignature.length}');
    } catch (e) {
      error.value = e.toString();
    }
  }

  void removeSignature() {
    try {
      signatureModel.value = null;
      debugPrint(
          'SIGNATURE : ${signatureModel.value} - LIST : ${listSignature.length}');
    } catch (e) {
      error.value = e.toString();
    }
  }

  void replaceValueLTWH({
    required SignatureModel signatureModel,
    double? scaleLeftValue = 0.0,
    double? scaleTopValue = 0.0,
    double? scaleWidthValue = 0.0,
    double? scaleHeightValue = 0.0,
    double? fixedLeftValue = 0.0,
    double? fixedTopValue = 0.0,
    double? fixedWidthValue = 0.0,
    double? fixedHeightValue = 0.0,
  }) {
    try {
      final index = listSignature.indexWhere((e) => e.id == signatureModel.id);

      listSignature[index] = signatureModel.copyWith(
        scaleLeft: scaleLeftValue,
        scaleTop: scaleTopValue,
        scaleWidth: scaleWidthValue,
        scaleHeight: scaleHeightValue,
        fixedLeft: fixedLeftValue,
        fixedTop: fixedTopValue,
        fixedWidth: fixedWidthValue,
        fixedHeight: fixedHeightValue,
      );

      this.signatureModel.value = signatureModel.copyWith(
        scaleLeft: scaleLeftValue,
        scaleTop: scaleTopValue,
        scaleWidth: scaleWidthValue,
        scaleHeight: scaleHeightValue,
        fixedLeft: fixedLeftValue,
        fixedTop: fixedTopValue,
        fixedWidth: fixedWidthValue,
        fixedHeight: fixedHeightValue,
      );
      listSignature.refresh();
      //update();
    } catch (e) {
      error.value = e.toString();
      print("*" * 40);
      print(e.toString());
    }
  }

  void onModelSignatureChange({required SignatureModel signatureModel}) {
    try {
      this.signatureModel.value = signatureModel.copyWith(onSelected: true);
    } catch (e) {
      error.value = e.toString();
    }
  }

  /// This function is used to insert signature into PDF file
  ///
  /// [listSignature] to enter the list of [SignatureModel] for assign signature.
  /// [pdfFile] to enter a PDF file that will be added a signature.
  Future<bool> addSigantureToPdf() async {
    try {
      final PdfDocument document = PdfDocument(
        inputBytes: pdfBytesDocument.value!,
      );

      // Get page count
      // final pageCount = document.pages.count;

      // Draw the image to the PDF page
      for (var i = 0; i < listSignature.length; i++) {
        // Get the existing PDF page.
        final PdfPage page = document.pages[listSignature[i].currentPage! - 1];

        final PdfBitmap image = PdfBitmap(listSignature[i].signature!);
        page.graphics.drawImage(
          image,
          Rect.fromLTWH(
            listSignature[i].scaleLeft!,
            listSignature[i].scaleTop!,
            listSignature[i].scaleWidth!,
            listSignature[i].scaleHeight!,
          ),
        );
      }
      pdfBytesDocument.value = Uint8List.fromList(document.saveSync());
      return true;
    } on Exception catch (e) {
      e.printError();
    }
    return false;
  }

  void setTextDisplay(
    bool displayFullName,
    bool displayDepartment,
    bool displayPosition,
    bool displayDatetime,
  ) {
    this.displayFullName = displayFullName;
    this.displayDepartment = displayDepartment;
    this.displayPosition = displayPosition;
    this.displayDatetime = displayDatetime;
    update();
  }

  void setStampStyle(bool stampStyle) {
    this.stampStyle = stampStyle;
    update();
  }

  void onSignatureSaved(List<int>? signature, String signType) {
    if (signature != null) {
      signatureBytes.value = Uint8List.fromList(signature);
      if (signType == "imageChuKyTat") {
        isKyTat = true;
        isCondau = false;
      } else if (signType == "imageMoc") {
        isCondau = true;
        isKyTat = false;
      } else {
        isKyTat = false;
        isCondau = false;
      }
      update();
    }
  }
}
