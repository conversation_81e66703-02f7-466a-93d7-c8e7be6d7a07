import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/empty_widget.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/lichnghiphep_controller.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/nghiphep_home_appbar.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/date_time_extension.dart';

class LichNghiPhepView extends GetView<LichNghiPhepController> {
  const LichNghiPhepView({super.key});

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    DateTime today = DateTime.now();

    return Scaffold(
      appBar: DonNghiPhepAppBar(context, "Lịch nghỉ phép"),
      body: Padding(
        padding: EdgeInsets.all(8),
        child: Obx(
          () => Column(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(8, 0, 8, 10),
                child: TableCalendar(
                  locale: "vi_VN",
                  rowHeight: 43,
                  focusedDay: controller.focusedDay,
                  firstDay: DateTime.utc(2000, 1, 1),
                  lastDay: DateTime.utc(2090, 1, 1),
                  headerStyle: HeaderStyle(
                    titleCentered: true,
                    formatButtonVisible: false,
                    titleTextStyle: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 17,
                    ),
                  ),
                  availableGestures: AvailableGestures.all,
                  selectedDayPredicate: (day) =>
                      isSameDay(controller.selectedDay, day),
                  onDaySelected: (selectedDay, focusedDay) {
                    controller.selectedDay = selectedDay;
                    controller.focusedDay = focusedDay;
                    _showEventDetails(context,
                        selectedDay); // Hiển thị thông tin khi chọn ngày
                  },
                  onPageChanged: (focusedDay) {
                    controller.focusedDay = focusedDay;
                    // Tự động chọn ngày đầu tiên của tháng
                    DateTime firstDayOfMonth =
                        DateTime(focusedDay.year, focusedDay.month, 1);
                    controller.selectedDay = firstDayOfMonth;
                    _showEventDetails(context, firstDayOfMonth);
                  },
                  calendarFormat: CalendarFormat.month,
                  startingDayOfWeek: StartingDayOfWeek.monday,
                  calendarStyle: CalendarStyle(
                    todayDecoration: BoxDecoration(
                      color: theme.primaryColor.withOpacity(0.5),
                      shape: BoxShape.rectangle,
                    ),
                    selectedDecoration: BoxDecoration(
                      color: theme.primaryColor,
                      shape: BoxShape.rectangle,
                    ),
                    outsideDaysVisible: false,
                  ),
                  eventLoader: (day) {
                    DateTime normalizedDay =
                        DateTime(day.year, day.month, day.day);
                    final events = controller.events[normalizedDay] ?? [];
                    return events;
                  },
                  calendarBuilders: CalendarBuilders(
                    markerBuilder: (context, day, events) {
                      if (events.isNotEmpty) {
                        return Positioned(
                          right: 1,
                          bottom: 1,
                          left: 1,
                          child: Container(
                            width: 5,
                            height: 5,
                            decoration: BoxDecoration(
                              color: Colors.red, // Chấm cho ngày có sự kiện
                              shape: BoxShape.circle,
                            ),
                          ),
                        );
                      }
                      return null;
                    },
                  ),
                ),
              ),
              Divider(
                color: Colors.grey[500],
                height: 8,
              ),

              // Hiển thị thông tin đơn nghỉ phép khi chọn ngày
              Expanded(
                child: Obx(() {
                  if (controller.selectedDay != null) {
                    // Chuẩn hóa selectedDay
                    DateTime normalizedSelectedDay = DateTime(
                        controller.selectedDay!.year,
                        controller.selectedDay!.month,
                        controller.selectedDay!.day);
                    final events =
                        controller.events[normalizedSelectedDay] ?? [];

                    if (events.isNotEmpty) {
                      return Column(
                        children: [
                          Expanded(
                            child: ListView.builder(
                              itemCount: events.length,
                              itemBuilder: (context, index) {
                                final event = events[index];
                                return Card(
                                  margin: EdgeInsets.symmetric(
                                      vertical: 4, horizontal: 8),
                                  child: ListTile(
                                    title: MyText.bodyLarge(
                                      "${event.vienChuc?.ho} ${event.vienChuc?.tenDem} ${event.vienChuc?.ten}",
                                      fontWeight: 700,
                                    ),
                                    subtitle: MyText.bodySmall(
                                      "${event.loaiNghiPhep!.tenLoaiNghiPhep} \nTừ ${event.tuNgay!.toFormat(format: "dd/MM/yy")} đến ${event.denNgay!.toFormat(format: "dd/MM/yy")} (Số ngày nghỉ: ${event.soNgay})",
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      );
                    } else if (events.isEmpty) {
                      return Center(
                        child: MyText.titleMedium(
                          "Không có ai nghỉ hôm nay",
                          fontWeight: 800,
                        ),
                      );
                    }
                  }
                  return Container(); // Không hiển thị gì nếu chưa chọn ngày
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Hàm hiển thị chi tiết sự kiện khi chọn ngày
  void _showEventDetails(BuildContext context, DateTime selectedDay) {
    // Chuẩn hóa selectedDay
    DateTime normalizedSelectedDay =
        DateTime(selectedDay.year, selectedDay.month, selectedDay.day);
    final events = controller.events[normalizedSelectedDay] ?? [];
    if (events.isNotEmpty) {
      controller.selectedDay = selectedDay;
    } else {
      // Xóa thông tin sự kiện nếu ngày mới không có sự kiện
      controller.selectedDay = selectedDay;
    }
  }
}
