import 'dart:convert';

class TmsFcmGiangVienModel {
  TmsFcmGiangVienModel({
    required this.id,
    required this.taiKhoanId,
    required this.vienChucId,
    required this.fcmToken,
    required this.vienChuc,
    required this.taiKhoan,
  });

  final int? id;
  final int? taiKhoanId;
  final int? vienChucId;
  final String? fcmToken;
  final dynamic vienChuc;
  final dynamic taiKhoan;

  factory TmsFcmGiangVienModel.fromJson(Map<String, dynamic> json){
    return TmsFcmGiangVienModel(
      id: json["id"],
      taiKhoanId: json["taiKhoanId"],
      vienChucId: json["vienChucId"],
      fcmToken: json["fcmToken"],
      vienChuc: json["vienChuc"],
      taiKhoan: json["taiKhoan"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "taiKhoanId": taiKhoanId,
    "vienChucId": vienChucId,
    "fcmToken": fcmToken,
    "vienChuc": vienChuc,
    "taiKhoan": taiKhoan,
  };

  @override
  String toString(){
    return "$id, $taiKhoanId, $vienChucId, $fcmToken, $vienChuc, $taiKhoan, ";
  }

  String toJsonString() => json.encode(toJson());
  factory TmsFcmGiangVienModel.fromJsonString(String source) => TmsFcmGiangVienModel.fromJson(json.decode(source));
  static List<TmsFcmGiangVienModel> parseNews(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed.map<TmsFcmGiangVienModel>((json) => TmsFcmGiangVienModel.fromJson(json)).toList();
  }
}
