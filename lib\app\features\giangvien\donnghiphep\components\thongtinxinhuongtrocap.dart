// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/donnghiphep_controller.dart';
import 'package:tvumobile/app/shared_components/form_builder_segmented_control.dart';
import 'package:tvumobile/app/shared_components/input_decoration_theme.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class ThongTinXinHuongTroCapWidget extends StatefulWidget {
  final DonNghiPhepController dnpManager;
  const ThongTinXinHuongTroCapWidget({
    super.key,
    required this.dnpManager,
  });

  @override
  State<ThongTinXinHuongTroCapWidget> createState() => _ThongtinWidgetState();
}

class _ThongtinWidgetState extends State<ThongTinXinHuongTroCapWidget> {
  final formKey = GlobalKey<FormBuilderState>();
  int id = 0;
  Map<String, dynamic> formdata = {
    'hvtvo': '',
    'ngaysinhvo': DateTime.now(),
    'cccdvo': '',
    'nvs': DateTime.now(),
    'stpt': 'SinhThuong',
    'socon': 1,
    'cannang': '',
    'bv': '',
  };

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    if (widget.dnpManager.thongtinTrocap.isNotEmpty) {
      formdata = {};
      formdata.addAll(widget.dnpManager.thongtinTrocap);
    }
    final myDecoration =
        const InputDecoration().applyDefaults(GlobalInputDecoration(context));
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.primaryColor.withOpacity(0.95),
        title: MyText.titleMedium(
          "Xin hưởng trợ cấp vợ sinh".tr.toUpperCase(),
          color: theme.colorScheme.onPrimary,
        ),
        iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(10),
              child: Column(
                children: [
                  const MyContainer(
                    child: MyText.titleLarge("Vui lòng nhập thông tin vợ sinh"),
                  ),
                  FormBuilder(
                    key: formKey,
                    child: Column(
                      children: [
                        // ----------------------------- HỌ TÊN VỢ -----------------------------
                        FormBuilderTextField(
                          name: 'hvtvo',
                          validator: FormBuilderValidators.required(
                              errorText: 'Vui lòng nhập họ tên vợ'),
                          decoration: myDecoration.copyWith(
                            labelText: 'Họ tên vợ',
                          ),
                          initialValue: formdata['hvtvo'],
                          maxLines: 1,
                          onChanged: (value) {
                            formdata['hvtvo'] = value ?? '';
                          },
                          onSaved: (newValue) {
                            formdata['hvtvo'] = newValue ?? '';
                          },
                        ),
                        const SizedBox(height: 14),

                        // -------------------------- NGÀY SINH CỦA VỢ -------------------------
                        FormBuilderDateTimePicker(
                          name: 'ngaysinhvo',
                          initialEntryMode: DatePickerEntryMode.input,
                          initialValue: formdata['ngaysinhvo'],
                          locale: Locale('vi'),
                          inputType: InputType.date,
                          format: DateFormat("dd/MM/yyyy"),
                          fieldHintText: "dd/mm/yyyy",
                          decoration: myDecoration.copyWith(
                            labelText: 'Ngày sinh của vợ',
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () {
                                formKey.currentState!.fields['ngaysinhvo']
                                    ?.didChange(null);
                              },
                            ),
                            contentPadding: EdgeInsets.all(8),
                          ),
                          initialTime: const TimeOfDay(hour: 8, minute: 0),
                          onChanged: (value) {
                            formdata['ngaysinhvo'] = value!;
                          },
                          onSaved: (newValue) {
                            formdata['ngaysinhvo'] = newValue;
                          },
                          // locale: const Locale.fromSubtags(languageCode: 'fr'),
                        ),
                        const SizedBox(height: 14),

                        // ------------------------------ SỐ CCCD ------------------------------
                        FormBuilderTextField(
                          name: 'cccdvo',
                          validator: FormBuilderValidators.required(
                              errorText: 'Vui lòng nhập CCCD của vợ'),
                          decoration: myDecoration.copyWith(
                            labelText: 'CCCD/CMND',
                          ),
                          initialValue: formdata['cccdvo'],
                          maxLines: 1,
                          onChanged: (value) {
                            formdata['cccdvo'] = value ?? '';
                          },
                          onSaved: (newValue) {
                            formdata['cccdvo'] = newValue ?? '';
                          },
                          keyboardType: TextInputType.number,
                        ),
                        const SizedBox(height: 14),

                        // ------------------------- NGÀY VỢ SINH CON --------------------------
                        FormBuilderDateTimePicker(
                          name: 'nvs',
                          initialEntryMode: DatePickerEntryMode.input,
                          initialValue: formdata['nvs'],
                          locale: Locale('vi'),
                          inputType: InputType.date,
                          format: DateFormat("dd/MM/yyyy"),
                          decoration: myDecoration.copyWith(
                            labelText: 'Ngày vợ sinh con',
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () {
                                formKey.currentState!.fields['nvs']
                                    ?.didChange(null);
                              },
                            ),
                            contentPadding: EdgeInsets.all(8),
                          ),
                          initialTime: const TimeOfDay(hour: 8, minute: 0),
                          onChanged: (value) {
                            formdata['nvs'] = value!;
                          },
                          onSaved: (newValue) {
                            formdata['nvs'] = newValue ?? '';
                          },
                          // locale: const Locale.fromSubtags(languageCode: 'fr'),
                        ),
                        const SizedBox(height: 14),

                        // --------------------- SINH THƯỜNG / PHẪU THUẬT ----------------------
                        FormBuilderSegmentedControl(
                          name: 'stpt',
                          initialValue: formdata['stpt'] ?? "SinhThuong",
                          padding: const EdgeInsets.all(8),
                          segmentPadding: const EdgeInsets.only(top: 8),
                          widgetPadding: const EdgeInsets.all(8),
                          options: const [
                            FormBuilderFieldOption(
                              value: "SinhThuong",
                              child: Text("Sinh thường"),
                            ),
                            FormBuilderFieldOption(
                              value: "PhauThuat",
                              child: Text("Phẩu thuật"),
                            )
                          ],
                          onChanged: (value) {
                            formdata['stpt'] = value!;
                            //calculationDiem();
                          },
                          onSaved: (newValue) {
                            formdata['stpt'] = newValue ?? '';
                          },
                          decoration: myDecoration.copyWith(
                            labelText: 'Sinh thường/phẫu thuật',
                            // contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const SizedBox(height: 14),

                        FormBuilderTextField(
                          name: 'bv',
                          validator: FormBuilderValidators.required(
                              errorText: 'Vui lòng nhập thông tin bệnh viện'),
                          decoration: myDecoration.copyWith(
                            labelText: 'Tại bệnh viện',
                          ),
                          initialValue: formdata['bv'],
                          maxLines: 2,
                          onChanged: (value) {
                            formdata['bv'] = value ?? '';
                          },
                          onSaved: (newValue) {
                            formdata['bv'] = newValue ?? '';
                          },
                        ),
                        const SizedBox(height: 14),

                        FormBuilderSegmentedControl<int>(
                          name: 'socon',
                          initialValue: formdata['socon'] ?? 1,
                          padding: const EdgeInsets.all(8),
                          segmentPadding: const EdgeInsets.only(top: 8),
                          widgetPadding: const EdgeInsets.all(8),
                          options: [1, 2, 3, 4, 5, 6, 7, 8]
                              .map((ele) => FormBuilderFieldOption(
                                    value: ele,
                                    child: Text(ele.toString()),
                                  ))
                              .toList(growable: true),
                          onChanged: (value) {
                            formdata['socon'] = value!;
                            //calculationDiem();
                          },
                          onSaved: (newValue) {
                            formdata['socon'] = newValue!;
                          },
                          decoration: myDecoration.copyWith(
                            labelText: 'Số con',
                            // contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const SizedBox(height: 14),

                        FormBuilderTextField(
                          name: 'cannang',
                          validator: FormBuilderValidators.required(
                              errorText: 'Vui lòng nhập cân nặng của con'),
                          decoration: myDecoration.copyWith(
                            labelText: 'Cân nặng (gam)',
                          ),
                          initialValue: formdata['cannang'],
                          maxLines: 1,
                          keyboardType: const TextInputType.numberWithOptions(
                              decimal: true),
                          onChanged: (value) {
                            formdata['cannang'] = value ?? '';
                          },
                          onSaved: (newValue) {
                            formdata['cannang'] = newValue ?? '';
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              border: Border(
                top: BorderSide(color: theme.dividerColor, width: 0.1),
              ),
            ),
            child: MyButton.block(
                backgroundColor: theme.primaryColor.withOpacity(0.95),
                onPressed: () {
                  bool isValid = formKey.currentState!.saveAndValidate();
                  debugPrint(formKey.currentState?.value.toString());
                  if (isValid) {
                    widget.dnpManager.thongtinTrocap =
                        formKey.currentState!.value;
                    widget.dnpManager.tinhNgayNghi();
                    widget.dnpManager.tinhSoNgayThuc();
                    //widget.dnpManager.update();
                    Navigator.pop(context);
                  }
                },
                child: MyText.labelLarge(
                  'Xác nhận thông tin',
                  color: theme.colorScheme.onPrimary,
                )),
          ),
        ],
      ),
    );
  }
}
