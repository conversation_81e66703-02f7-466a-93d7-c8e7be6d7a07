import 'dart:ui';

import 'package:get/get.dart';

part 'app_messages.dart';
part 'en.dart';
part 'vi.dart';

class AppTranslations extends Translations {
  static Locale? get locale => const Locale('vi');
  static Locale? get fallbackLocale => const Locale('vi');
  static Locale get defaultLocale => const Locale('vi');

  static Map<String, Locale> get localeKeys => {
    'en': const Locale('en'),
    'vi': const Locale('vi'),
  };

  @override
  Map<String, Map<String, String>> get keys => {
        'en': en.messages,
        'vi': vi.messages,
      };
}
