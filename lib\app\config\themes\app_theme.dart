import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tvumobile/app/config/themes/colors.dart';

part 'MLColors.dart';
part 'dark_theme.dart';
part 'light_theme.dart';

/// all custom application theme
class AppTheme {
  /// default application theme
  static ThemeData get basic => ThemeData(
        primarySwatch: Colors.blue,
      );

  // you can add other custom theme in this class like  light theme, dark theme ,etc.

  // example :
  static ThemeData get light => lightTheme;
  static ThemeData get dark => darkTheme;
}
