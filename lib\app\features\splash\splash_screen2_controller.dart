import 'package:get/get.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';



class SplashScreen2Controller extends GetxController {

  late UserInfo? userInfo;

  @override
  Future<void> onInit() async {
    super.onInit();
    //await goToFullApp();
    userInfo = LocalStorageServices.getUserInfo();
  }


  @override
  Future<void> onReady() async {
    var fcmtoken = LocalStorageServices.getFcmToken();
    if(fcmtoken != null) {
      //TODO: send FCM
      //await NotificationHelper().updateFCM();
    }
  }

  goToFullApp() async {
    await Future.delayed(const Duration(seconds: 1));
    //bool isUpdateAvailable = false;
    Get.offAllNamed(Routes.NAV);
  }

  Future<void> goToUpdater() async {
    await Future.delayed(const Duration(milliseconds: 500));
    Get.offAllNamed(AppPages.UPDATER);
  }
}
