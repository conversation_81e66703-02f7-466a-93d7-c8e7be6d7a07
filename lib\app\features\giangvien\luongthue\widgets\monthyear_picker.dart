import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class YearMonthPicker extends StatefulWidget {
  final Function(int year, int month) onSelectionChanged;
  final int startYear;
  final int endYear;
  final double spacing;
  final EdgeInsets padding;
  final EdgeInsets margin;
  final YearMonthPickerStyle style;
  final double itemSpacing; // New property to control spacing between items

  const YearMonthPicker({super.key, 
    required this.onSelectionChanged,
    this.startYear = 2000,
    this.endYear = 2100,
    this.spacing = 10.0,
    this.padding = const EdgeInsets.all(10.0),
    this.margin = const EdgeInsets.all(0.0),
    this.style = YearMonthPickerStyle.lightTheme,
    this.itemSpacing = 10.0, // Default spacing between items
  });

  @override
  // ignore: library_private_types_in_public_api
  _YearMonthPickerState createState() => _YearMonthPickerState();
}

class _YearMonthPickerState extends State<YearMonthPicker> {
  int selectedYear = DateTime.now().year;
  int selectedMonth = DateTime.now().month;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      margin: widget.margin,
      child: Column(
        children: [
          // Year Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(widget.endYear - widget.startYear + 1, (index) {
              int year = widget.startYear + index;
              bool isSelected = year == selectedYear;
              return Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedYear = year;
                        widget.onSelectionChanged(selectedYear, selectedMonth);
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 8.0,
                        horizontal: 16.0,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? widget.style.selectedBoxBackgroundColor
                            : widget.style.boxBackgroundColor,
                        borderRadius: BorderRadius.circular(widget.style.borderRadius),
                      ),
                      child: Text(
                        year.toString(),
                        style: TextStyle(
                          color: isSelected ? widget.style.selectedYearColor : widget.style.yearColor,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                  if (index < widget.endYear - widget.startYear)
                    SizedBox(width: widget.itemSpacing), // Horizontal spacing between years
                ],
              );
            }),
          ),
          SizedBox(height: widget.spacing),
          // Month Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(12, (index) {
              int month = index + 1;
              bool isSelected = month == selectedMonth;
              return Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedMonth = month;
                        widget.onSelectionChanged(selectedYear, selectedMonth);
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 8.0,
                        horizontal: 16.0,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? widget.style.selectedBoxBackgroundColor
                            : widget.style.boxBackgroundColor,
                        borderRadius: BorderRadius.circular(widget.style.borderRadius),
                      ),
                      child: Text(
                        DateFormat.MMM().format(DateTime(0, month)),
                        style: TextStyle(
                          color: isSelected ? widget.style.selectedMonthColor : widget.style.monthColor,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                  if (index < 11)
                    SizedBox(width: widget.itemSpacing), // Horizontal spacing between months
                ],
              );
            }),
          ),
        ],
      ),
    );
  }
}

class YearMonthPickerStyle {
  final Color yearColor;
  final Color selectedYearColor;
  final Color monthColor;
  final Color selectedMonthColor;
  final Color boxBackgroundColor;
  final Color selectedBoxBackgroundColor;
  final double borderRadius;

  const YearMonthPickerStyle({
    required this.yearColor,
    required this.selectedYearColor,
    required this.monthColor,
    required this.selectedMonthColor,
    required this.boxBackgroundColor,
    required this.selectedBoxBackgroundColor,
    required this.borderRadius,
  });

  // Predefined Styles
  static const YearMonthPickerStyle lightTheme = YearMonthPickerStyle(
    yearColor: Colors.black,
    selectedYearColor: Colors.white,
    monthColor: Colors.black,
    selectedMonthColor: Colors.white,
    boxBackgroundColor: Colors.grey,
    selectedBoxBackgroundColor: Colors.blue,
    borderRadius: 8.0,
  );

  static const YearMonthPickerStyle darkTheme = YearMonthPickerStyle(
    yearColor: Colors.white,
    selectedYearColor: Colors.black,
    monthColor: Colors.white,
    selectedMonthColor: Colors.black,
    boxBackgroundColor: Colors.black45,
    selectedBoxBackgroundColor: Colors.teal,
    borderRadius: 8.0,
  );

  static const YearMonthPickerStyle customTheme = YearMonthPickerStyle(
    yearColor: Colors.purple,
    selectedYearColor: Colors.orange,
    monthColor: Colors.purple,
    selectedMonthColor: Colors.orange,
    boxBackgroundColor: Colors.purple,
    selectedBoxBackgroundColor: Colors.orange,
    borderRadius: 16.0,
  );
}
