// ignore_for_file: deprecated_member_use

part of 'app_theme.dart';

final ThemeData lightTheme = ThemeData(
  useMaterial3: true,
  fontFamily: GoogleFonts.ibmPlexSans().fontFamily,
  scaffoldBackgroundColor: const Color(0xfffefefe),
  primaryColor: tPrimaryColor,
  primaryColorDark: appColorPrimary,
  //errorColor: Colors.red,
  hoverColor: Colors.white54,
  dividerColor: dividerDarkColor,
  //fontFamily: GoogleFonts.openSans().fontFamily,
  appBarTheme: const AppBarTheme(
    color: white,
    iconTheme: IconThemeData(color: textPrimaryColor),
    systemOverlayStyle:
        SystemUiOverlayStyle(statusBarIconBrightness: Brightness.dark),
  ),
  textSelectionTheme: const TextSelectionThemeData(cursorColor: Colors.black),
  colorScheme: const ColorScheme.light(
    primary: appColorPrimary,
    error: Colors.red,
  ),
  cardTheme: const CardThemeData(color: Colors.white),
  cardColor: Colors.white,
  iconTheme: const IconThemeData(color: textPrimaryColor),
  bottomSheetTheme: const BottomSheetThemeData(backgroundColor: whiteColor),
  textTheme: const TextTheme(),
  dividerTheme:
      DividerThemeData(color: viewLineColor.withOpacity(0.5), thickness: 0.7),
  visualDensity: VisualDensity.adaptivePlatformDensity,
).copyWith(
  pageTransitionsTheme: const PageTransitionsTheme(
      builders: <TargetPlatform, PageTransitionsBuilder>{
        TargetPlatform.android: OpenUpwardsPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        TargetPlatform.linux: OpenUpwardsPageTransitionsBuilder(),
        TargetPlatform.macOS: OpenUpwardsPageTransitionsBuilder(),
      }),
);
