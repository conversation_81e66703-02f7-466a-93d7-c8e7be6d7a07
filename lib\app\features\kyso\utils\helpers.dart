import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;

// Helper function to fetch image bytes from network
Future<Uint8List> networkImageToByte(String url) async {
  final response = await http.get(Uri.parse(url));
  if (response.statusCode == 200) {
    return response.bodyBytes;
  } else {
    throw Exception('Failed to load image: ${response.statusCode}');
  }
}

Future<Uint8List?> loadPdfFromURL(pdfUrl) async {
  // Load the PDF file from the URL
  try {
    debugPrint('Loading PDF from URL: $pdfUrl');
    Uint8List pdfBytes = await networkImageToByte(pdfUrl);
    return pdfBytes;
  } catch (e) {
    debugPrint('Error loading PDF: $e');
    // Consider showing an error message to the user
  }
  return null;
}

Future<Uint8List> loadSignatureImageFromURL(signatureUrl) async {
  // Load the signature image from the URL
  debugPrint('Loading SignatureImage from URL: $signatureUrl');
  Uint8List signatureImageBytes;
  try {
    signatureImageBytes = await networkImageToByte(signatureUrl);
    return signatureImageBytes;
  } catch (e) {
    debugPrint('Error loading signature from URL: $e');
    signatureImageBytes =
        await loadPlaceHolderFromAsset(); // Fallback to placeholder
    return signatureImageBytes;
  }
}

Future<Uint8List> loadPlaceHolderFromAsset() async {
  // Load the placeholder image from assets
  // Load placeholder image from assets and convert to Uint8List
  final ByteData data = await rootBundle.load('assets/images/app_icon_150.png');
  return data.buffer.asUint8List();
}
