import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:sticky_headers/sticky_headers/widget.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/config/themes/custom_theme.dart';
import 'package:tvumobile/app/features/sinhvien/diemsinhvien/diem_sinhvien_controller.dart';
import 'package:tvumobile/app/shared_components/appbar.dart';
import 'package:tvumobile/app/shared_components/loading_effect.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';


class DiemSinhVienView  extends GetView<DiemSinhVienController> {
  const DiemSinhVienView({super.key});


  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return GetBuilder<DiemSinhVienController>(
        init: controller,
        tag: 'diem_sinhvien_controller',
        builder: (controller) {
          return _buildBody(context, theme);
        });
  }

  Widget _buildBody(BuildContext context, ThemeData theme) {

    if (controller.uiLoading) {
      return Scaffold(
        body: Padding(
          padding: MySpacing.top(MySpacing.safeAreaTop(context) + 20),
          child: LoadingEffect.getSearchLoadingScreen(
            context,
          ),
        ),
      );
    } else {
      return Scaffold(
        appBar: appBarTitle(context, "Kết quả học tập"),
        body: InteractiveViewer(
          minScale: 0.01,
          maxScale: 1.6,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: theme.dividerColor, width: 0.4),
            ),
            margin: const EdgeInsets.all(2),
            width: double.infinity,
            child: ListView.builder(
                itemCount: controller.diemSvLists.length,
                itemBuilder: (context, index) {
                  return StickyHeader(
                    header: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.dividerColor, width: 0.1),
                          color: CustomTheme.skyBlue,
                      ),
                      height: 55,
                      width: double.infinity,
                      //color: theme.colorScheme.background,
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.only(top: 2),
                      child: Column(
                        children: [
                          MyText.titleSmall(
                            controller.diemSvLists[index].tenHocKy.toString(),
                            color: theme.colorScheme.onSurface, fontWeight: 600,),
                          TopRow(theme)
                        ],
                      ),
                    ),
                    content: SizedBox(
                      width: double.infinity,
                      child: ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: controller.diemSvLists[index].dsDiemMonHoc.length,
                        itemBuilder: (context, dindex) {
                          return TableRow(index, dindex, theme);
                        },
                      ),
                    ),
                  );
                }
            ),
          ),
        )
      );
    }
  }



  Widget TopRow(ThemeData theme) {

    return Container(
      color: CustomTheme.skyBlue,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: 8,
          ),
          Container(
            alignment: Alignment.center,
            width: 60,
            height: 30,
            child: MyText.labelMedium('Mã', fontWeight: 900,),
          ),
          Container(
            width: 210,
              alignment: Alignment.center,
              padding: const EdgeInsets.only(left: 8),
              child: MyText.labelMedium('Tên môn học', fontWeight: 900)),
          SizedBox(
            width: 5,
          ),
          Container(
            width: 40,
            alignment: Alignment.center,
            child: MyText.labelMedium('TC', fontWeight: 900),
          ),
          SizedBox(
            width: 5,
          ),
          /*Container(
            width: 20,
            alignment: Alignment.center,
            child:  MyText.labelSmall('L1'),
          ),
          SizedBox(
            width: 5,
          ),
          Container(
            width: 20,
            alignment: Alignment.center,
            child:  MyText.labelSmall('L2'),
          ),
          SizedBox(
            width: 5,
          ),*/
          Container(
            width: 40,
            alignment: Alignment.center,
            child: MyText.labelMedium('ĐTK', fontWeight: 900, ),
          ),
          SizedBox(
            width: 5,
          ),
        ],
      ),
    );
  }

  Widget TableRow(int hkindex, int diemindex, ThemeData theme) {
    var hk = controller.diemSvLists[hkindex];

    var dhp = hk.dsDiemMonHoc[diemindex];

    return Container(
      width: double.infinity,
      height: 30,
      decoration: BoxDecoration(
        border: Border.all(color: theme.dividerColor, width: 0.2),
        color: dhp.ketQua == 1 ? mlPrimaryTextColor.withOpacity(0.2) : theme.colorScheme.surface,
      ),
      child: InkWell(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              width: 5,
              color: dhp.ketQua == 1 ? mlPrimaryTextColor.withOpacity(0.2) : theme.colorScheme.surface,
            ),
            Container(
              alignment: Alignment.center,
              width: 60,
              height: 30,
              color: dhp.ketQua == 1 ? mlPrimaryTextColor.withOpacity(0.2) : theme.colorScheme.surface,
              child: MyText.bodyMedium(dhp.maMon.toString()),
              ),
            Container(
              width: 210,
              padding: const EdgeInsets.only(left: 8),
              child: MyText.bodyMedium(dhp.tenMon.toString(), maxLines: 1, overflow: TextOverflow.ellipsis,),
            ),
            SizedBox(
              width: 5,
            ),
            Container(
              width: 40,
              alignment: Alignment.center,
              child: MyText.bodyMedium(dhp.soTinChi.toString()),
            ),
            /*SizedBox(
              width: 5,
            ),
            Container(
              width: 20,
              alignment: Alignment.center,
              child: MyText.labelSmall(dhp.diemThi.toString() ),
            ),
            SizedBox(
              width: 5,
            ),
            Container(
              width: 20,
              alignment: Alignment.center,
              child: MyText.labelSmall(dhp.diemGiuaKy.toString(), ),
            ),*/
            SizedBox(
              width: 5,
            ),
            Container(
              width: 40,
              alignment: Alignment.center,
              child: MyText.bodyMedium(dhp.diemTk.toString()),
            ),
            SizedBox(
              width: 5,
            ),

          ],
        ),
      ),
    );
  }

}
