import 'package:flutter/material.dart';
import 'package:get/get.dart';



class PathBar extends StatelessWidget implements PreferredSizeWidget {
  final List paths;
  final Function(int) onChanged;
  final IconData? icon;

  const PathBar({
    super.key,
    required this.paths,
    required this.onChanged,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    var theme = Get.theme;
    return Container(
      decoration: BoxDecoration(
        color: theme.primaryColor.withAlpha(1000),
      ),
      height: 50,
      child: Align(
        alignment: Alignment.centerLeft,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          shrinkWrap: true,
          itemCount: paths.length,
          itemBuilder: (BuildContext context, int index) {
            String i = paths[index];
            List splited = i.split('/');
            if (index == 0) {
              return IconButton(
                alignment: Alignment.centerRight,
                icon: Icon(
                  icon ?? Icons.smartphone,
                  color: theme.colorScheme.onPrimary,
                ),
                onPressed: () => onChanged(0),
              );
            }
            return InkWell(
              onTap: () => onChanged(index),
              child: SizedBox(
                height: 40,
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    child: Text(
                      '${splited[splited.length - 1]}',
                      style: theme.textTheme.titleMedium?.copyWith(color: theme.colorScheme.onPrimary),
                    ),
                  ),
                ),
              ),
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return Icon(Icons.chevron_right, color: theme.colorScheme.onPrimary,);
          },
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(40.0);
}