// import 'package:get/get.dart';
// import 'package:tvumobile/app/features/tasky/controllers/taskydashboard_controller.dart';
// import 'package:tvumobile/app/features/tasky/controllers/taskyoverview_controller.dart';
// import 'package:tvumobile/app/features/tasky/controllers/taskytaskview_controller.dart';

// class TaskyDashboardBinding extends Bindings {
//   @override
//   void dependencies() {
//     Get.put(
//       TaskyDashboardController(),
//     );
//     Get.put(TaskyOverviewController());
//     // Get.lazyPut<TaskyOverviewController>(() => TaskyOverviewController());
//     Get.lazyPut<TaskyTaskViewController>(() => TaskyTaskViewController());
//   }
// }
