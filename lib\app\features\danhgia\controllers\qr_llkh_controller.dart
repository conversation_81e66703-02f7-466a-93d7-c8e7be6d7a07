import 'package:flutter/foundation.dart';

import 'package:get/get.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';

import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class QrLLKHController extends GetxController {
  ApiProvider apiProvider = Get.find();

  final RxString qrLLKH = "".obs; // Lưu duyetai trong controller
  final RxBool isLoading = false.obs; // Trạng thái loading

  UserInfo? userinfo = LocalStorageServices.getUserInfo();

  @override
  void onInit() {
    super.onInit();
    if (kDebugMode) {
      print("QrLLKHController onInit");
    }
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    if (kDebugMode) {
      print("QrLLKHController onReady");
    }
    qrLLKH.value = userinfo!.email!.split('@')[0];
  }
}
