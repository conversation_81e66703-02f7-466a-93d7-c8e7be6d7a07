import 'package:flutter/material.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/chitietdnp_controller.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/duyetdnp_controller.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/nghiphep_home_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

AppBar NghiPhepAppBar(BuildContext context, NghiPhepHomeController controller) {
  ThemeData theme = Theme.of(context);
  return AppBar(
    foregroundColor: theme.colorScheme.onPrimary,
    iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
    flexibleSpace: Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -80,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset(
                  'assets/images/app_icon.png',
                  height: 150,
                  width: 150,
                  opacity: const AlwaysStoppedAnimation(.15),
                )),
          ),
          Positioned(
            right: -7,
            top: -160,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: -21,
            top: -195,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          //----------------Data row----------------//
        ],
      ),
    ),
    title: MyText.titleMedium(
      "Danh sách nghỉ phép".toUpperCase(),
      color: theme.colorScheme.onPrimary,
      letterSpacing: 0.01,
    ),
    centerTitle: true,
    actions: [
      IconButton(
          onPressed: () => Future.sync(
                () => controller.refreshData(),
              ),
          icon: const Icon(Icons.refresh))
    ],
  );
}

AppBar DonNghiPhepAppBar(BuildContext context, String tieuDe) {
  ThemeData theme = Theme.of(context);
  return AppBar(
    foregroundColor: theme.colorScheme.onPrimary,
    iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
    flexibleSpace: Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -80,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset(
                  'assets/images/app_icon.png',
                  height: 150,
                  width: 150,
                  opacity: const AlwaysStoppedAnimation(.15),
                )),
          ),
          Positioned(
            right: -7,
            top: -160,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: -21,
            top: -195,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          //----------------Data row----------------//
        ],
      ),
    ),
    title: MyText.titleMedium(
      tieuDe.toUpperCase(),
      color: theme.colorScheme.onPrimary,
      letterSpacing: 0.01,
    ),
    centerTitle: true,
  );
}

AppBar ChiTietDonNghiPhepAppBar(
    BuildContext context, ChiTietDNPController controller) {
  ThemeData theme = Theme.of(context);
  return AppBar(
    foregroundColor: theme.colorScheme.onPrimary,
    iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
    flexibleSpace: Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -80,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset(
                  'assets/images/app_icon.png',
                  height: 150,
                  width: 150,
                  opacity: const AlwaysStoppedAnimation(.15),
                )),
          ),
          Positioned(
            right: -7,
            top: -160,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: -21,
            top: -195,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          //----------------Data row----------------//
        ],
      ),
    ),
    title: MyText.titleMedium(
      "Chi tiết đơn nghỉ phép".toUpperCase(),
      color: theme.colorScheme.onPrimary,
      letterSpacing: 0.01,
    ),
    centerTitle: true,
    actions: [
      if (controller.tt.value.contains("Khởi tạo") ||
          controller.tt.value.contains("Hủy")) ...[
        IconButton(
            onPressed: () {
              // Hiển thị hộp thoại xác nhận trước khi chuyển đơn
              controller.showConfirmDialog("xóa", "", () async {
                await controller.deleteDonNghiPhep();
              });
            },
            icon: const Icon(Icons.delete_forever_outlined))
      ],
    ],
  );
}
