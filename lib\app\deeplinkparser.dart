import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
// ignore: library_prefixes
import 'package:tvumobile/app/constans/constant_data.dart' as globalConst;

class DeepLinkParser {
  DeepLinkParser._();
  static final _instance = DeepLinkParser._();
  factory DeepLinkParser() => _instance;

  final _appLinks = AppLinks();

  static String _code = "";
  static String get code => _code;
  static bool get hasCode => _code.isNotEmpty;

  static String _path = "";
  static String get path => _path;

  static void reset() => _code = "";
  static Uri initialUri = Uri();

  // ignore: unused_field
  StreamSubscription<Uri>? _linkSubscription;

  Future<void> listenLink() async {
    // Handle initial uri if app was started from background
    final uri = await getInitialLink();
    if (uri != null) {
      uniHandle(uri);
    }

    // Listen for new links
    _linkSubscription = _appLinks.uriLinkStream.listen((uri) {
      debugPrint('onAppLink: $uri');
      // Add a small delay to ensure the app is ready to handle navigation
      Future.delayed(const Duration(milliseconds: 100), () {
        uniHandle(uri);
      });
    });
  }

  Future<Uri?> getInitialLink() async {
    return _appLinks.getInitialLink();
  }

  Future<String> getFirstScreen() async {
    Uri? uri = await getInitialLink();
    if (uri == null) {
      Logger().e('no initial uri');
      return AppPages.SPLASH;
    }
    globalConst.launchFromURL = true;
    Logger().e('got initial uri: $uri');
    String fragment = uri.path.toLowerCase();
    if (kDebugMode) {
      print("got fragment: $fragment");
    }
    Map<String, String> params = uri.queryParameters;
    if (fragment.contains('/giaydieuxe/') &&
        fragment.contains("danhgiachatluong")) {
      //var lastIndexOfSlash = fragment.lastIndexOf('/');
      //if (lastIndexOfSlash == fragment.length - 1){
      //  return const ProductList();
      //}
      //String id = fragment.substring(lastIndexOfSlash + 1);
      //return ProductScreen.withId(id: id);
      String code = params['Id'] ?? params['id'] ?? "";
      //String path = "danhgiachatluong";
      return "${AppPages.DANHGIA}?donviId=$code&type=dv";
      //print("*****************: ${uri.toString().toLowerCase()}");
    }

    if (fragment.contains('congvan')) {
      if (kDebugMode) {
        print("got congvan: ${AppPages.CV_DETAIL}?cvId=${params['id']}");
      }
      return "${AppPages.CV_DETAIL}?cvId=${params['id']}";
    }

    return AppPages.SPLASH;
  }

  Future<void> uniHandle(Uri? uri) async {
    try {
      if (uri == null || uri.path.isEmpty) return;
      globalConst.launchFromURL = false;
      initialUri = uri;
      Map<String, String> params = uri.queryParameters;
      _code = params['code'] ?? "";

      // Add more robust logging
      Logger().i('Deep Link Received: $uri');
      Logger().i('Query Params: $params');

      // Ensure we have a valid context and the app is ready
      if (!Get.isRegistered<GetMaterialApp>()) {
        Logger().e('App not ready for navigation');
        //return;
      }

      final currentRoute = Get.currentRoute;

      if (initialUri
          .toString()
          .toLowerCase()
          .contains("danhgiachatluongphucvu".toLowerCase())) {
        _code = params['donviId'] ?? params['donviid'] ?? "";
        _path = "danhgiachatluongphucvu";

        final targetRoute = "${AppPages.DANHGIA}?donviId=$code&type=dv";
        if (currentRoute != targetRoute) {
          Get.offAllNamed(AppPages.DANHGIA,
              parameters: {"donviId": code, "type": "dv"});
        }
      } else if (initialUri.toString().toLowerCase().contains("giaydieuxe") &&
          initialUri.toString().toLowerCase().contains("danhgiachatluong")) {
        _code = params['Id'] ?? params['id'] ?? "";
        _path = "danhgiachatluong";

        final targetRoute = "${Routes.DANHGIA}?donviId=$code&type=xe";
        if (currentRoute != targetRoute) {
          Get.offAllNamed(Routes.DANHGIA,
              parameters: {"donviId": code, "type": "xe"});
        }
      } else if (initialUri.toString().toLowerCase().contains("congvan") &&
          initialUri.toString().toLowerCase().contains("chitiet")) {
        final targetRoute = "${Routes.CV_DETAIL}?cvId=${params['id']}";
        if (currentRoute != targetRoute) {
          Get.offAllNamed(Routes.CV_DETAIL,
              parameters: {"cvId": params['id']!});
        }
      } else if (initialUri.host.toLowerCase().contains("sso.tvu.edu.vn")) {
        return;
      } else {
        return;
      }
    } catch (e, stackTrace) {
      Logger().e('Deep Link Handling Error', error: e, stackTrace: stackTrace);
      return;
    }
  }
}
