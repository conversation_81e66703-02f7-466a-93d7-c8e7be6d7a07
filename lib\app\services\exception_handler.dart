import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/utils/helpers/dialog_helper.dart';
import 'package:tvumobile/app/utils/oauth2/src/authorization_exception.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

mixin class ExceptionHandler {
  RxBool isError = false.obs;
  RxBool isLoadingShown = false.obs;

  /// FOR REST API
  Future handleError2(error, {StackTrace? stackTrace}) {
    isError.value = true;
    hideLoading();
    //print(error);
    // ignore: unused_local_variable
    var errorText = error.toString();
    if (error is AuthorizationException) {
      if (error.error.contains('invalid_grant')) {
        LocalStorageServices.logoutUser();
        Get.offAllNamed(Routes.LOGIN);
      }
    }
    //showErrorDialog(Strings.ohNo.tr, errorText);
    //debugPrint(error);
    //debugPrintStack(stackTrace: stackTrace);
    //return Response<dynamic>(statusCode: 500, body: jsonEncode({'error': errorText}));
    return Future.error(error);
  }

  handleError(dynamic error, StackTrace stackTrace) {
    isError.value = true;
    hideLoading();
    //print(error);
    var errorText = error.toString();
    if (error is AuthorizationException) {
      if (error.error.contains('invalid_grant')) {
        LocalStorageServices.logoutUser();
        Get.offAllNamed(Routes.LOGIN);
      }
    }
    //showErrorDialog(Strings.ohNo.tr, errorText);
    //debugPrint(error);
    //debugPrintStack(stackTrace: stackTrace);
    return Response(statusCode: 500, body: jsonEncode({'error': errorText}));
  }

  handleHttpError(dynamic error) async {
    isError.value = true;
    if (isLoadingShown.value) {
      hideLoading();
    }
    if (kDebugMode) {
      print("Inner error: $error");
    }
    return Response(statusCode: 500);
  }

  showLoading() {
    isLoadingShown.value = true;
    isError.value = false;
    DialogHelper.showLoading();
  }

  hideLoading() {
    DialogHelper.hideLoading();
    isLoadingShown.value = false;
  }

  showErrorDialog(String title, String message) {
    DialogHelper.showErrorDialog(title, message);
  }
}
