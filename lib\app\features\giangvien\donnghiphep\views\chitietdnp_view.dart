import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/dnp_dialog.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/chitietdnp_controller.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/nghiphep_home_appbar.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/date_time_extension.dart';

class ChiTietDNPView extends GetView<ChiTietDNPController> {
  const ChiTietDNPView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // Biến trạng thái để theo dõi mở rộng/thu gọn
    var isExpanded = false.obs;
    return Obx(
      () => Scaffold(
        appBar: ChiTietDonNghiPhepAppBar(context, controller),
        body: GetBuilder<ChiTietDNPController>(
          builder: (controller) => controller.loaddnp.value
              ? Column(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.fromLTRB(10, 8, 10, 10),
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            // ------------------------- ĐƠN XIN NGHỈ PHÉP -------------------------
                            Align(
                              child: Padding(
                                padding: EdgeInsets.all(8.0),
                                child: MyText.titleLarge(
                                  "ĐƠN XIN NGHỈ PHÉP",
                                  fontWeight: 700,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),

                            // ------------------------------ HỌ TÊN -------------------------------
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 8),
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: 120,
                                    child: MyText.labelLarge(
                                      "Họ tên:",
                                      color: Colors.black,
                                      fontWeight: 700,
                                    ),
                                  ),
                                  Expanded(
                                    child: MyText.bodyMedium(
                                      "${controller.dnp.vienChuc?.ho} ${controller.dnp.vienChuc?.tenDem} ${controller.dnp.vienChuc?.ten}",
                                      color: Colors.black87,
                                      maxLines: 3,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // ------------------------------ ĐƠN VỊ -------------------------------
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 8),
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: 120,
                                    child: MyText.labelLarge(
                                      "Đơn vị:",
                                      color: Colors.black,
                                      fontWeight: 700,
                                    ),
                                  ),
                                  Expanded(
                                    child: MyText.bodyMedium(
                                      "${controller.dnp.vienChuc?.donVi}",
                                      color: Colors.black87,
                                      maxLines: 3,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            Divider(color: theme.dividerColor.withOpacity(0.3)),

                            // -------------------------- LOẠI NGHỈ PHÉP ---------------------------
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 8),
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: 120,
                                    child: MyText.labelLarge(
                                      "Loại nghỉ phép:",
                                      color: Colors.black,
                                      fontWeight: 700,
                                    ),
                                  ),
                                  Expanded(
                                    child: MyText.bodyMedium(
                                      "${controller.dnp.loaiNghiPhep?.tenLoaiNghiPhep}",
                                      color: Colors.black87,
                                      maxLines: 3,
                                      softWrap: true,
                                      // overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // ------------------------------ LÍ DO --------------------------------
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 8),
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: 120,
                                    child: MyText.labelLarge(
                                      "Lí do:",
                                      color: Colors.black,
                                      fontWeight: 700,
                                    ),
                                  ),
                                  Expanded(
                                    child: MyText.bodyMedium(
                                      "${controller.dnp.lyDo}",
                                      color: Colors.black87,
                                      maxLines: 3,
                                      softWrap: true,
                                      // overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // ------------------------- SỐ NGÀY THỰC NGHỈ -------------------------
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 8),
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: 120,
                                    child: MyText.labelLarge(
                                      "Số ngày nghỉ:",
                                      color: Colors.black,
                                      fontWeight: 700,
                                    ),
                                  ),
                                  Expanded(
                                    child: MyText.bodyMedium(
                                      "${controller.dnp.soNgay}",
                                      color: Colors.black87,
                                      maxLines: 3,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            if (controller.dnp.tuNgay?.year !=
                                    controller.dnp.denNgay?.year ||
                                controller.dnp.tuNgay?.month !=
                                    controller.dnp.denNgay?.month ||
                                controller.dnp.tuNgay?.day !=
                                    controller.dnp.denNgay?.day) ...[
                              // ----------------------------- TỪ NGÀY -------------------------------
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 8),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 120,
                                      child: MyText.labelLarge(
                                        "Từ ngày:",
                                        color: Colors.black,
                                        fontWeight: 700,
                                      ),
                                    ),
                                    Expanded(
                                      child: MyText.bodyMedium(
                                        "${controller.dnp.tuNgay?.toFormat(format: "dd.MM.yyyy")}",
                                        color: Colors.black87,
                                        maxLines: 3,
                                        softWrap: true,
                                        // overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // ---------------------------- ĐẾN NGÀY -------------------------------
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 8),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 120,
                                      child: MyText.labelLarge(
                                        "Đến ngày:",
                                        color: Colors.black,
                                        fontWeight: 700,
                                      ),
                                    ),
                                    Expanded(
                                      child: MyText.bodyMedium(
                                        "${controller.dnp.denNgay?.toFormat(format: "dd.MM.yyyy")}",
                                        color: Colors.black87,
                                        maxLines: 3,
                                        softWrap: true,
                                        // overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ] else ...[
                              // -------------------------------- BUỔI -------------------------------
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 8),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 120,
                                      child: MyText.labelLarge(
                                        "Buổi:",
                                        color: Colors.black,
                                        fontWeight: 700,
                                      ),
                                    ),
                                    if (controller.dnp.buoi == 'Sang') ...[
                                      Expanded(
                                        child: MyText.bodyMedium(
                                          "Sáng",
                                          color: Colors.black87,
                                          maxLines: 3,
                                          softWrap: true,
                                          // overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ] else if (controller.dnp.buoi ==
                                        'Chieu') ...[
                                      Expanded(
                                        child: MyText.bodyMedium(
                                          "Chiều",
                                          color: Colors.black87,
                                          maxLines: 3,
                                          softWrap: true,
                                          // overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ] else ...[
                                      Expanded(
                                        child: MyText.bodyMedium(
                                          "Cả ngày",
                                          color: Colors.black87,
                                          maxLines: 3,
                                          softWrap: true,
                                          // overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),

                              // ----------------------------- NGÀY NGHỈ -----------------------------
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 8),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 120,
                                      child: MyText.labelLarge(
                                        "Ngày:",
                                        color: Colors.black,
                                        fontWeight: 700,
                                      ),
                                    ),
                                    Expanded(
                                      child: MyText.bodyMedium(
                                        "${controller.dnp.tuNgay?.toFormat(format: "dd.MM.yyyy")}",
                                        color: Colors.black87,
                                        maxLines: 3,
                                        softWrap: true,
                                        // overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],

                            // ------------------------------ GHI CHÚ ------------------------------
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 8),
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: 120,
                                    child: MyText.labelLarge(
                                      "Ghi chú:",
                                      color: Colors.black,
                                      fontWeight: 700,
                                    ),
                                  ),
                                  Expanded(
                                    child: MyText.bodyMedium(
                                      controller.dnp.ghiChu ?? "",
                                      color: Colors.black87,
                                      maxLines: 3,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // ---------------------------- TRẠNG THÁI -----------------------------
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 8),
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: 120,
                                    child: MyText.labelLarge(
                                      "Trạng thái:",
                                      color: Colors.black,
                                      fontWeight: 700,
                                    ),
                                  ),
                                  Expanded(
                                    child: MyText.bodyMedium(
                                      "${controller.dnp.trangThai!.contains("Hủy") ? (controller.dnp.ghiChuTraDon1?.isNotEmpty == true || controller.dnp.ghiChuTraDon2?.isNotEmpty == true || controller.dnp.ghiChuTraDon3?.isNotEmpty == true || controller.dnp.ghiChuTraDonCongDoan?.isNotEmpty == true) ? 'Trả về' : 'Thu hồi' : controller.dnp.trangThai}",
                                      color: Colors.black87,
                                      maxLines: 3,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // Nút mở rộng/thu gọn khi trạng thái là "Đã duyệt" và "Trả về"
                            if (controller.dnp.trangThai != null &&
                                controller.dnp.trangThai!
                                    .contains("đã duyệt")) ...[
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 8),
                                child: GestureDetector(
                                  onTap: () {
                                    isExpanded.value = !isExpanded.value;
                                  },
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      MyText.labelMedium(
                                        "Thông tin duyệt",
                                        color: Colors.green.shade700,
                                        fontWeight: 700,
                                      ),
                                      const SizedBox(width: 8),
                                      Obx(
                                        () => Icon(
                                          isExpanded.value
                                              ? LucideIcons.chevronUp
                                              : LucideIcons.chevronDown,
                                          color: Colors.green.shade700,
                                          size: 20,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            ] else if (controller.dnp.trangThai != null &&
                                controller.dnp.trangThai!.contains("Hủy") &&
                                (controller.dnp.ghiChuTraDon1 != null ||
                                    controller.dnp.ghiChuTraDon2 != null ||
                                    controller.dnp.ghiChuTraDon3 != null ||
                                    controller.dnp.ghiChuTraDonCongDoan !=
                                        null)) ...[
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 8),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 120,
                                      child: MyText.labelLarge(
                                        "Lí do trả về:",
                                        color: Colors.black,
                                        fontWeight: 700,
                                      ),
                                    ),
                                    Expanded(
                                      child: MyText.bodyMedium(
                                        "${controller.dnp.ghiChuTraDon1?.isNotEmpty == true ? controller.dnp.ghiChuTraDon1 : controller.dnp.ghiChuTraDon2?.isNotEmpty == true ? controller.dnp.ghiChuTraDon2 : controller.dnp.ghiChuTraDon3?.isNotEmpty == true ? controller.dnp.ghiChuTraDon3 : controller.dnp.ghiChuTraDonCongDoan ?? ''}",
                                        color: Colors.red,
                                        maxLines: 3,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],

                            // -------------------------- THÔNG TIN DUYỆT --------------------------
                            Obx(
                              () => Visibility(
                                visible: isExpanded.value,
                                child: Column(
                                  children: [
                                    // ---------------------------- ĐƠN VỊ DUYỆT ---------------------------
                                    if (controller.dnp.nguoiDuyet1 != null) ...[
                                      // --------------------------- NGƯỜI DUYỆT 1 ---------------------------
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8, horizontal: 8),
                                        child: Row(
                                          children: [
                                            SizedBox(
                                              width: 120,
                                              child: MyText.labelLarge(
                                                "Đơn vị duyệt:",
                                                color: Colors.black,
                                                fontWeight: 700,
                                              ),
                                            ),
                                            Expanded(
                                              child: MyText.bodyMedium(
                                                "${controller.dnp.nguoiDuyet1?.ho} ${controller.dnp.nguoiDuyet1?.tenDem} ${controller.dnp.nguoiDuyet1?.ten} (${controller.dnp.ngayNguoiDuyet1?.toFormat(format: "dd.MM.yyyy")})",
                                                color: Colors.black87,
                                                maxLines: 3,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                      // --------------------------- NGÀY DUYỆT 1 ----------------------------
                                      // Padding(
                                      //   padding: const EdgeInsets.symmetric(
                                      //       vertical: 8, horizontal: 8),
                                      //   child: Row(
                                      //     children: [
                                      //       SizedBox(
                                      //         width: 120,
                                      //         child: MyText.labelLarge(
                                      //           "Ngày ĐV duyệt:",
                                      //           color: Colors.black,
                                      //           fontWeight: 700,
                                      //         ),
                                      //       ),
                                      //       Expanded(
                                      //         child: MyText.bodyMedium(
                                      //           "${controller.dnp.ngayNguoiDuyet1?.toFormat(format: "dd.MM.yyyy")}",
                                      //           color: Colors.black87,
                                      //           maxLines: 3,
                                      //           overflow: TextOverflow.ellipsis,
                                      //         ),
                                      //       ),
                                      //     ],
                                      //   ),
                                      // ),
                                    ],

                                    if (controller.dnp.ghiChuTraDon1 != null &&
                                        controller
                                            .dnp.ghiChuTraDon1!.isNotEmpty) ...[
                                      // ------------------------- GHI CHÚ TRẢ ĐƠN 1 -------------------------
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8, horizontal: 8),
                                        child: Row(
                                          children: [
                                            SizedBox(
                                              width: 120,
                                              child: MyText.labelLarge(
                                                "Lý do trả về:",
                                                color: Colors.black,
                                                fontWeight: 700,
                                              ),
                                            ),
                                            Expanded(
                                              child: MyText.bodyMedium(
                                                "${controller.dnp.ghiChuTraDon1}",
                                                color: Colors.black87,
                                                maxLines: 3,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],

                                    // -------------------------- CÔNG ĐOÀN DUYỆT --------------------------
                                    if (controller.dnp.nguoiDuyetCongDoan !=
                                        null) ...[
                                      // -------------------------- CÔNG ĐOÀN DUYỆT --------------------------
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8, horizontal: 8),
                                        child: Row(
                                          children: [
                                            SizedBox(
                                              width: 120,
                                              child: MyText.labelLarge(
                                                "Công đoàn duyệt:",
                                                color: Colors.black,
                                                fontWeight: 700,
                                              ),
                                            ),
                                            Expanded(
                                              child: MyText.bodyMedium(
                                                "${controller.dnp.nguoiDuyetCongDoan?.ho} ${controller.dnp.nguoiDuyetCongDoan?.tenDem} ${controller.dnp.nguoiDuyetCongDoan?.ten} (${controller.dnp.ngayNguoiDuyetCongDoan?.toFormat(format: "dd.MM.yyyy")})",
                                                color: Colors.black87,
                                                maxLines: 3,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                      // ------------------------ NGÀY DUYỆT CÔNG ĐOÀN -----------------------
                                      // Padding(
                                      //   padding: const EdgeInsets.symmetric(
                                      //       vertical: 8, horizontal: 8),
                                      //   child: Row(
                                      //     children: [
                                      //       SizedBox(
                                      //         width: 120,
                                      //         child: MyText.labelLarge(
                                      //           "Ngày duyệt \ncông đoàn:",
                                      //           color: Colors.black,
                                      //           fontWeight: 700,
                                      //         ),
                                      //       ),
                                      //       Expanded(
                                      //         child: MyText.bodyMedium(
                                      //           "${controller.dnp.ngayNguoiDuyetCongDoan?.toFormat(format: "dd.MM.yyyy")}",
                                      //           color: Colors.black87,
                                      //           maxLines: 3,
                                      //           overflow: TextOverflow.ellipsis,
                                      //         ),
                                      //       ),
                                      //     ],
                                      //   ),
                                      // ),
                                    ],

                                    if (controller.dnp.ghiChuTraDonCongDoan !=
                                            null &&
                                        controller.dnp.ghiChuTraDonCongDoan!
                                            .isNotEmpty) ...[
                                      // ------------------------- GHI CHÚ TRẢ ĐƠN 1 -------------------------
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8, horizontal: 8),
                                        child: Row(
                                          children: [
                                            SizedBox(
                                              width: 120,
                                              child: MyText.labelLarge(
                                                "Lý do trả về:",
                                                color: Colors.black,
                                                fontWeight: 700,
                                              ),
                                            ),
                                            Expanded(
                                              child: MyText.bodyMedium(
                                                "${controller.dnp.ghiChuTraDonCongDoan}",
                                                color: Colors.black87,
                                                maxLines: 3,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],

                                    // -------------------------- PHÒNG TCNS DUYỆT -------------------------
                                    if (controller.dnp.nguoiDuyet2 != null) ...[
                                      // --------------------------- NGƯỜI DUYỆT 2 ---------------------------
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8, horizontal: 8),
                                        child: Row(
                                          children: [
                                            SizedBox(
                                              width: 120,
                                              child: MyText.labelLarge(
                                                "Phòng TCNS duyệt:",
                                                color: Colors.black,
                                                fontWeight: 700,
                                              ),
                                            ),
                                            Expanded(
                                              child: MyText.bodyMedium(
                                                "${controller.dnp.nguoiDuyet2?.ho} ${controller.dnp.nguoiDuyet2?.tenDem} ${controller.dnp.nguoiDuyet2?.ten} (${controller.dnp.ngayNguoiDuyet2?.toFormat(format: "dd.MM.yyyy")})",
                                                color: Colors.black87,
                                                maxLines: 3,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                      // --------------------------- NGÀY DUYỆT 2 ----------------------------
                                      // Padding(
                                      //   padding: const EdgeInsets.symmetric(
                                      //       vertical: 8, horizontal: 8),
                                      //   child: Row(
                                      //     children: [
                                      //       SizedBox(
                                      //         width: 120,
                                      //         child: MyText.labelLarge(
                                      //           "Ngày phòng \nTCNS duyệt:",
                                      //           color: Colors.black,
                                      //           fontWeight: 700,
                                      //         ),
                                      //       ),
                                      //       Expanded(
                                      //         child: MyText.bodyMedium(
                                      //           "${controller.dnp.ngayNguoiDuyet2?.toFormat(format: "dd.MM.yyyy")}",
                                      //           color: Colors.black87,
                                      //           maxLines: 3,
                                      //           overflow: TextOverflow.ellipsis,
                                      //         ),
                                      //       ),
                                      //     ],
                                      //   ),
                                      // ),
                                    ],

                                    if (controller.dnp.ghiChuTraDon2 != null &&
                                        controller
                                            .dnp.ghiChuTraDon2!.isNotEmpty) ...[
                                      // ------------------------- GHI CHÚ TRẢ ĐƠN 2 -------------------------
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8, horizontal: 8),
                                        child: Row(
                                          children: [
                                            SizedBox(
                                              width: 120,
                                              child: MyText.labelLarge(
                                                "Lý do trả về:",
                                                color: Colors.black,
                                                fontWeight: 700,
                                              ),
                                            ),
                                            Expanded(
                                              child: MyText.bodyMedium(
                                                "${controller.dnp.ghiChuTraDon2}",
                                                color: Colors.black87,
                                                maxLines: 3,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],

                                    // ------------------------------ BGH DUYỆT ----------------------------
                                    if (controller.dnp.nguoiDuyet3 != null) ...[
                                      // --------------------------- NGƯỜI DUYỆT 3 ---------------------------
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8, horizontal: 8),
                                        child: Row(
                                          children: [
                                            SizedBox(
                                              width: 120,
                                              child: MyText.labelLarge(
                                                "BGH duyệt:",
                                                color: Colors.black,
                                                fontWeight: 700,
                                              ),
                                            ),
                                            Expanded(
                                              child: MyText.bodyMedium(
                                                "${controller.dnp.nguoiDuyet3?.ho} ${controller.dnp.nguoiDuyet3?.tenDem} ${controller.dnp.nguoiDuyet3?.ten} (${controller.dnp.ngayNguoiDuyet3?.toFormat(format: "dd.MM.yyyy")})",
                                                color: Colors.black87,
                                                maxLines: 3,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                      // --------------------------- NGÀY DUYỆT 3 ----------------------------
                                      // Padding(
                                      //   padding: const EdgeInsets.symmetric(
                                      //       vertical: 8, horizontal: 8),
                                      //   child: Row(
                                      //     children: [
                                      //       SizedBox(
                                      //         width: 120,
                                      //         child: MyText.labelLarge(
                                      //           "Ngày BGH duyệt:",
                                      //           color: Colors.black,
                                      //           fontWeight: 700,
                                      //         ),
                                      //       ),
                                      //       Expanded(
                                      //         child: MyText.bodyMedium(
                                      //           "${controller.dnp.ngayNguoiDuyet3?.toFormat(format: "dd.MM.yyyy")}",
                                      //           color: Colors.black87,
                                      //           maxLines: 3,
                                      //           overflow: TextOverflow.ellipsis,
                                      //         ),
                                      //       ),
                                      //     ],
                                      //   ),
                                      // ),
                                    ],

                                    if (controller.dnp.ghiChuTraDon3 != null &&
                                        controller
                                            .dnp.ghiChuTraDon3!.isNotEmpty) ...[
                                      // ------------------------- GHI CHÚ TRẢ ĐƠN 3 -------------------------
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8, horizontal: 8),
                                        child: Row(
                                          children: [
                                            SizedBox(
                                              width: 120,
                                              child: MyText.labelLarge(
                                                "Lý do trả về:",
                                                color: Colors.black,
                                                fontWeight: 700,
                                              ),
                                            ),
                                            Expanded(
                                              child: MyText.bodyMedium(
                                                "${controller.dnp.ghiChuTraDon3}",
                                                color: Colors.black87,
                                                maxLines: 3,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ),

                            if (controller.dnp.vienChuc!.id.toString() ==
                                controller.userinfo!.vienchucid) ...[
                              // 18279) ...[
                              Divider(
                                  color: theme.dividerColor.withOpacity(0.3)),

                              // ------------------------------ ĐÃ NGHỈ -----------------------------
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 8),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 150,
                                      child: MyText.labelLarge(
                                        "Số ngày phép đã nghỉ:",
                                        color: Colors.black,
                                        fontWeight: 700,
                                      ),
                                    ),
                                    Expanded(
                                      child: MyText.bodyMedium(
                                        "${controller.danghi}",
                                        color: Colors.black87,
                                        maxLines: 3,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // ------------------------------ CÒN LẠI -----------------------------
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 8),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 150,
                                      child: MyText.labelLarge(
                                        "Số ngày phép còn lại",
                                        color: Colors.black,
                                        fontWeight: 700,
                                      ),
                                    ),
                                    Expanded(
                                      child: MyText.bodyMedium(
                                        "${controller.conlai}",
                                        color: Colors.black87,
                                        maxLines: 3,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),

                    Divider(color: theme.dividerColor.withOpacity(0.3)),

                    // -------------------------- NÚT HOẠT ĐỘNG --------------------------
                    Padding(
                      padding: const EdgeInsets.fromLTRB(18, 8, 16, 16),
                      child: Row(
                        children: [
                          Expanded(
                            // flex: 1,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                // --------------- CHỈNH SỬA & CHUYỂN ĐƠN ----------------
                                if (controller.dnp.trangThai != null &&
                                    (controller.dnp.trangThai!
                                            .contains("Khởi tạo") ||
                                        controller.dnp.trangThai!
                                            .contains("Hủy"))) ...[
                                  // -------------------- NÚT CHỈNH SỬA --------------------
                                  MyButton.medium(
                                    backgroundColor: Colors.amber,
                                    onPressed: () async {
                                      Get.toNamed(Routes.DONNGHIPHEP,
                                          arguments: {'id': controller.dnp.id});
                                    },
                                    child: Row(
                                      children: [
                                        Icon(
                                          LucideIcons.wrench,
                                          color: Colors.white,
                                          size: 15,
                                        ),
                                        SizedBox(width: 5),
                                        MyText.labelLarge(
                                          'Chỉnh sửa',
                                          fontWeight: 900,
                                          color: theme.colorScheme.onPrimary,
                                        )
                                      ],
                                    ),
                                  ),

                                  // -------------------- NÚT CHUYỂN ĐƠN -------------------
                                  MyButton.medium(
                                    onPressed: () async {
                                      // Hiển thị hộp thoại xác nhận trước khi chuyển đơn
                                      controller.showConfirmDialog("chuyển", "",
                                          () async {
                                        await controller.chuyenDonNghiPhep();
                                      });
                                    },
                                    child: Row(
                                      children: [
                                        Icon(
                                          LucideIcons.rocket,
                                          color: Colors.white,
                                          size: 15,
                                        ),
                                        SizedBox(width: 5),
                                        MyText.labelLarge(
                                          'Chuyển đơn',
                                          fontWeight: 900,
                                          color: theme.colorScheme.onPrimary,
                                        ),
                                      ],
                                    ),
                                  ),
                                ]

                                // ----------------- THU HỒI ĐƠN Ở VIÊN CHỨC -----------------
                                // ------------------ & DUYỆT ĐƠN Ở ĐƠN VỊ -------------------
                                else if (controller.dnp.trangThai != null &&
                                    controller.dnp.trangThai!
                                        .contains("Chờ")) ...[
                                  // --------------- THU HỒI ĐƠN ĐANG CHỜ DUYỆT --------------
                                  if (controller.dnp.vienChuc!.id.toString() ==
                                      controller.userinfo!.vienchucid) ...[
                                    // '18279') ...[
                                    MyButton.medium(
                                      backgroundColor: Colors.red,
                                      onPressed: () async {
                                        controller.showConfirmDialog(
                                            "thu hồi", "", () async {
                                          await controller.chuyenDonNghiPhep();
                                        });
                                      },
                                      child: Row(
                                        children: [
                                          Icon(
                                            LucideIcons.repeat,
                                            color: Colors.white,
                                            size: 15,
                                          ),
                                          SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Thu hồi',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ]
                                  // ------------------ DUYỆT TẠI ĐƠN VỊ -------------------
                                  else ...[
                                    // ------------------- NÚT KHÔNG DUYỆT ---------------------
                                    MyButton.medium(
                                      backgroundColor: Colors.red,
                                      onPressed: () async {
                                        showReasonDialog(controller,
                                            dnp: controller.dnp);
                                      },
                                      child: Row(
                                        children: [
                                          Icon(
                                            LucideIcons.circleX,
                                            color: Colors.white,
                                            size: 15,
                                          ),
                                          SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Hủy đơn',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),

                                    // -------------------- NÚT DUYỆT ĐƠN ----------------------
                                    MyButton.medium(
                                      backgroundColor: Colors.green,
                                      onPressed: () async {
                                        controller.showConfirmDialog(
                                            "duyệt", "", () async {
                                          await controller.duyetDonNghiPhep();
                                        });
                                      },
                                      child: Row(
                                        children: [
                                          Icon(
                                            LucideIcons.circleCheck,
                                            color: Colors.white,
                                            size: 15,
                                          ),
                                          SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Duyệt đơn',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ]
                                ]

                                // ----------------- DUYỆT ĐƠN NS, CĐ, BGH -------------------
                                else if (controller.trangthai.isNotEmpty &&
                                    controller.trangthai
                                        .contains("choduyet")) ...[
                                  // ------------------- NÚT KHÔNG DUYỆT ---------------------
                                  MyButton.medium(
                                    backgroundColor: Colors.red,
                                    onPressed: () async {
                                      showReasonDialog(controller,
                                          dnp: controller.dnp);
                                    },
                                    child: Row(
                                      children: [
                                        Icon(
                                          LucideIcons.circleX,
                                          color: Colors.white,
                                          size: 15,
                                        ),
                                        SizedBox(width: 5),
                                        MyText.labelLarge(
                                          'Hủy đơn',
                                          fontWeight: 900,
                                          color: theme.colorScheme.onPrimary,
                                        ),
                                      ],
                                    ),
                                  ),

                                  // -------------------- NÚT DUYỆT ĐƠN ----------------------
                                  MyButton.medium(
                                    backgroundColor: Colors.green,
                                    onPressed: () async {
                                      controller.showConfirmDialog("duyệt", "",
                                          () async {
                                        await controller.duyetDonNghiPhep();
                                      });
                                    },
                                    child: Row(
                                      children: [
                                        Icon(
                                          LucideIcons.circleCheck,
                                          color: Colors.white,
                                          size: 15,
                                        ),
                                        SizedBox(width: 5),
                                        MyText.labelLarge(
                                          'Duyệt đơn',
                                          fontWeight: 900,
                                          color: theme.colorScheme.onPrimary,
                                        ),
                                      ],
                                    ),
                                  ),
                                ]

                                // ----------- THU HỒI DUYỆT ĐƠN ĐV, NS, CĐ, BGH -------------
                                else if (controller.trangthai.isNotEmpty &&
                                    controller.trangthai
                                        .contains("daduyet")) ...[
                                  // ----------------- THU HỒI ĐƠN ĐÃ DUYỆT ------------------
                                  if ((controller.duyetai.value == "dv" &&
                                          controller.dnp.trangThai!
                                              .toLowerCase()
                                              .contains("đơn vị đã duyệt")) ||
                                      (controller.duyetai.value == "ns" &&
                                          controller.dnp.trangThai!
                                              .toLowerCase()
                                              .contains("tcns đã duyệt")) ||
                                      (controller.duyetai.value == "cd" &&
                                          controller.dnp.trangThai!
                                              .toLowerCase()
                                              .contains(
                                                  "công đoàn đã duyệt")) ||
                                      (controller.duyetai.value == "bgh" &&
                                          controller.dnp.trangThai!
                                              .toLowerCase()
                                              .contains("bgh đã duyệt"))) ...[
                                    MyButton.medium(
                                      backgroundColor: Colors.red,
                                      onPressed: () async {
                                        controller.showConfirmDialog(
                                            "thu hồi", " đã duyệt", () async {
                                          await controller.thuHoiDonNghiPhep();
                                        });
                                      },
                                      child: Row(
                                        children: [
                                          Icon(
                                            LucideIcons.rotateCcw,
                                            color: Colors.white,
                                            size: 15,
                                          ),
                                          SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Thu hồi đơn đã duyệt',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ]
                                ]
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              : Center(child: CircularProgressIndicator()),
        ),
      ),
    );
  }
}
