import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/constans/constant_data.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/edusoft/tkb_giang_vien.dart';
import 'package:tvumobile/app/data/models/portal/portal_news_model.dart';
import 'package:tvumobile/app/data/models/sch/tkb/tkb_tuan_giangvien.dart';
import 'package:tvumobile/app/data/models/tms/congvan/cong_van_nhan.dart';
import 'package:tvumobile/app/data/models/wordpress/article.dart';
import 'package:tvumobile/app/features/navbar/navbar_controller.dart';
import 'package:tvumobile/app/utils/extensions/extensions.dart';
import 'package:tvumobile/app/utils/helpers/tkb_helper.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class GvHomeController extends GetxController {
  final congVanNhan = <TmsCongVanModel>[].obs;
  final portalNews = <PortalNewsModel>[].obs;
  final tsNews = <PortalNewsModel>[].obs;
  final tkbs = <TkbTuanGiangVien>[].obs;

  ApiProvider apiProvider = Get.find();
  UserInfo? userInfo = LocalStorageServices.getUserInfo();
  NavbarController navbarController = Get.find();
  final args = Get.arguments;
  int isDisplayCV = 0;

  @override
  void onInit() {
    super.onInit();
    if (args != null && args.containsKey('type')) {
      if (args['type'] == NotificationType.CongVan) {
        isDisplayCV = args['refid'].toString().toInt();
      }
      if (args['type'] == NotificationType.TaoMoiNghiPhep) {}
    }
  }

  @override
  Future<void> onReady() async {
    //await getDatas();
  }

  Future<void> onRefresh() async {
    //await getDatas();
  }

  // getDatas() async {
  //   final tkbData = await apiProvider.loadCurrentWeekTkbGiangVien();
  //   if (tkbData == null) {
  //     tkbs.value = [];
  //   } else {
  //     var tmp = getTodayTkb(tkbData);
  //     if (tmp != null) {
  //       tkbs.addAll(tmp);
  //     }
  //   }
  //   update();

  //   final congVanNhanData =
  //       await apiProvider.loadCongVanNhan(num: 15, docType: 0);
  //   if (congVanNhanData.isEmpty) {
  //     congVanNhan.value = [];
  //   } else {
  //     congVanNhan.addAll(congVanNhanData);
  //   }
  //   if (isDisplayCV > 0) {
  //     TmsCongVanModel? doc = congVanNhanData
  //         .firstWhereOrNull((element) => element.id == isDisplayCV);
  //     if (doc != null) {
  //       String heroKey = "doc_${doc.document!.id}";
  //       Get.toNamed(Routes.CV_DETAIL,
  //           parameters: {"heroKey": heroKey}, arguments: {"doc": doc});
  //     }
  //   }
  //   update();
  //   await fetchLatestArticles(1);
  //   // final portalNewsData = await apiProvider.loadPortalNews();
  //   // if (portalNewsData.isEmpty) {
  //   //   portalNews.value = [];
  //   // } else {
  //   //   portalNews.addAll(portalNewsData);
  //   // }
  //   update();
  //   final tsNewsData = await apiProvider.loadTuyenSinhNews();
  //   if (tsNewsData.isEmpty) {
  //     tsNews.value = [];
  //   } else {
  //     tsNews.addAll(tsNewsData);
  //   }
  //   //printError(info: portalNews.toString());
  //   //update();
  //   //EasyLoading.dismiss();
  // }

  onTapFunctions(String id) {
    if (id == "congvan") {
      navbarController.onTap(1);
    }

    if (id == "tkb") {
      navbarController.onTap(2);
    }
    if (id == "tcl") {
      Get.toNamed(AppPages.LUONGTHANG, preventDuplicates: true);
    }
    if (id == "dnp") {
      Get.toNamed(AppPages.NGHIPHEP, preventDuplicates: true);
    }

    if (id == "lichtrinh") {
      Get.toNamed(AppPages.CHAMCONG, preventDuplicates: true);
    }

    if (id == "task") {
      Get.toNamed(AppPages.TASKOVERVIEW, preventDuplicates: true);
    }

    if (id == "kyso") {
      Get.toNamed(AppPages.KYSO, preventDuplicates: true);
    }

    if (id == "dieuxe") {
      Get.toNamed(AppPages.DIEUXE, preventDuplicates: true);
    }
  }

  List<TkbTuanGiangVien>? getTodayTkb(TkbGiangVien tkbs) {
    List<TkbTuanGiangVien> lists = [];
    for (var tkbtuan in tkbs.dsTuanTkb) {
      for (var tkb in tkbtuan.dsThoiKhoaBieu) {
        //if(tkb.ngayHoc!.isToday) {
        TkbTuanGiangVien tkbTuanGiangVien = TkbTuanGiangVien();
        //DateTime ngayday = tkb.ngayHoc;
        tkbTuanGiangVien.tenMon = tkb.tenMon;
        tkbTuanGiangVien.batDau = getTietBD(tkb.ngayHoc!, tkb.tietBatDau!);
        tkbTuanGiangVien.ketThuc =
            getTietBD(tkb.ngayHoc!, tkb.tietBatDau! + tkb.soTiet!);
        tkbTuanGiangVien.tenPhong = tkb.maPhong;
        tkbTuanGiangVien.maLop = tkb.maLop;
        tkbTuanGiangVien.nhomHoc = tkb.maNhom;
        tkbTuanGiangVien.nhomTH = tkb.maToTh;
        tkbTuanGiangVien.maMon = tkb.maMon;
        tkbTuanGiangVien.soTiet = tkb.soTiet.toString();
        lists.add(tkbTuanGiangVien);
        //}
      }
    }
    return lists;
  }

  void changeTheme(BuildContext context) {
    bool isLight = LocalStorageServices.getThemeIsLight();
    Get.changeThemeMode(isLight ? ThemeMode.dark : ThemeMode.light);
    Get.changeTheme(isLight ? AppTheme.dark : AppTheme.light);
    LocalStorageServices.setThemeIsLight(!isLight);
    update();
  }

  final latestArticles = <dynamic>[].obs;
  Future<List<dynamic>> fetchLatestArticles(int page) async {
    try {
      var response = await http.get(Uri.parse(
          '${ApiPath.tvuWordpressURL}/wp-json/wp/v2/posts/?page=$page&per_page=10&_fields=id,date,title,custom,yoast_head_json.og_image,link,content,categories'));
      if (response.statusCode == 200) {
        latestArticles.addAll(json
            .decode(response.body)
            .map((m) => Article.fromJson(m))
            .toList());
        //print(latestArticles);
        return latestArticles;
      }
    } on SocketException {
      throw 'No Internet connection';
    }
    return latestArticles;
  }
}
