import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/config/themes/custom_theme.dart';
import 'package:tvumobile/app/features/tasky/controllers/taskytaskview_controller.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

Widget buildTaskyDrawer(
    BuildContext context, TaskyTaskViewController controller) {
  var theme = Theme.of(context);
  return Drawer(
    backgroundColor: Colors.transparent, // Để nền của Drawer gốc trong suốt.
    child: Align(
      alignment: Alignment.topRight,
      child: MyContainer.none(
        margin:
            MySpacing.only(right: 16, top: MySpacing.safeAreaTop(context) + 16),
        borderRadiusAll: 4,
        // height: 500,
        clipBehavior: Clip.antiAliasWithSaveLayer,
        color: theme.scaffoldBackgroundColor,

        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                padding: MySpacing.only(left: 20, bottom: 0, top: 8, right: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    MySpacing.height(16),
                    MyContainer(
                      padding: MySpacing.fromLTRB(12, 4, 12, 4),
                      borderRadiusAll: 4,
                      color: theme.primaryColor.withOpacity(0.9),
                      borderColor: theme.primaryColor,
                      bordered: true,
                      child: MyText.bodyLarge("THÔNG TIN NHIỆM VỤ",
                          color: theme.colorScheme.onPrimary,
                          fontWeight: 700,
                          letterSpacing: 0.2),
                    ),
                  ],
                ),
              ),
              MySpacing.height(8),
              const Divider(
                thickness: 2,
              ),
              MySpacing.height(5),
              Container(
                margin: MySpacing.x(20),
                child: Column(
                  children: [
                    MySpacing.height(10),
                    InkWell(
                      onTap: () {
                        controller.drawerClick("DN");
                      },
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      child: Container(
                        padding: MySpacing.all(8), // Khoảng cách bên trong Row
                        decoration: BoxDecoration(
                          color: controller.currentView.value == "DN"
                              ? CustomTheme.peach
                                  .withAlpha(30) // Màu nền khi chọn
                              : Colors
                                  .transparent, // Không màu nền khi không chọn
                          borderRadius: BorderRadius.circular(8), // Bo góc
                        ),
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 12,
                              borderRadiusAll: 4,
                              color: CustomTheme.peach.withAlpha(20),
                              child: Icon(
                                LucideIcons.inbox,
                                size: 20,
                                color: CustomTheme.peach,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Công việc được giao',
                                color: CustomTheme.peach,
                                fontWeight: 700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    MySpacing.height(10),
                    InkWell(
                      onTap: () {
                        controller.drawerClick("DG");
                      },
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      child: Container(
                        padding: MySpacing.all(8), // Khoảng cách bên trong Row
                        decoration: BoxDecoration(
                          color: controller.currentView.value == "DG"
                              ? CustomTheme.skyBlue
                                  .withAlpha(30) // Màu nền khi chọn
                              : Colors
                                  .transparent, // Không màu nền khi không chọn
                          borderRadius: BorderRadius.circular(8), // Bo góc
                        ),
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 12,
                              borderRadiusAll: 4,
                              color: CustomTheme.skyBlue.withAlpha(20),
                              child: Icon(
                                LucideIcons.send,
                                size: 20,
                                color: CustomTheme.skyBlue,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Công việc đã giao',
                                color: CustomTheme.skyBlue,
                                fontWeight: 700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    MySpacing.height(10),
                    InkWell(
                      onTap: () {
                        controller.drawerClick("GS");
                      },
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      child: Container(
                        padding: MySpacing.all(8), // Khoảng cách bên trong Row
                        decoration: BoxDecoration(
                          color: controller.currentView.value == "GS"
                              ? CustomTheme.darkGreen
                                  .withAlpha(30) // Màu nền khi chọn
                              : Colors
                                  .transparent, // Không màu nền khi không chọn
                          borderRadius: BorderRadius.circular(8), // Bo góc
                        ),
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 12,
                              borderRadiusAll: 4,
                              color: CustomTheme.darkGreen.withAlpha(20),
                              child: Icon(
                                LucideIcons.eye,
                                size: 20,
                                color: CustomTheme.darkGreen,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Công việc giám sát',
                                color: CustomTheme.darkGreen,
                                fontWeight: 700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    MySpacing.height(16),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
