import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

Future<void> handleBackgroundMessage(RemoteMessage? message) async {
  if (message == null) return;
  // await 5.delay();
  //await Get.find<HomeController>().getNotifications(
  //    MySharedPref.getLangID() ?? "2"); // update notification UI
  Logger().i("New notification has arrived in background");
}

late AndroidNotificationChannel channel;

bool isFlutterLocalNotificationsInitialized = false;
late FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin;

class NotificationHelper {
  final _firebaseMessaging = FirebaseMessaging.instance;

  handleMessage(RemoteMessage? message) {
    if (message == null) return;
    Logger().i(message.notification?.title);
  }

  void initLocalNotification() async {
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: AndroidInitializationSettings("@mipmap/ic_launcher"),
      iOS: DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      ),
    );

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse:
          (NotificationResponse notificationResponse) {
        _onNotificationTap(notificationResponse.payload);
      },
    );
  }

  static Future<void> _onNotificationTap(String? payload) async {
    Logger().i(payload);
    if (payload != null) {
      // await Get.find<HomeController>()
      //     .getNotifications(MySharedPref.getLangID() ?? "2");

      // Get.key.currentState
      //    ?.pushNamed(Routes.NOTIFICATION_HOME, arguments: payload);
      //Logger().i(payload);
    }
  }

  Future<void> setupFlutterNotifications() async {
    if (isFlutterLocalNotificationsInitialized) {
      return;
    }
    channel = const AndroidNotificationChannel(
      'tvu_everyone', // Change this to a unique channel ID
      'Thông báo chung TVU', // Change this to a channel name
      description:
          'This channel is used for push notifications.', // description
      importance: Importance.high,
    );

    flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    isFlutterLocalNotificationsInitialized = true;
  }

  initPushNotification() {
    _firebaseMessaging.getInitialMessage().then(handleMessage);
    _firebaseMessaging.subscribeToTopic("tvu_everyone");
    FirebaseMessaging.onMessageOpenedApp.listen(handleMessage);
    FirebaseMessaging.onBackgroundMessage(handleBackgroundMessage);
    FirebaseMessaging.onMessage.listen(showFlutterNotification);
  }

  Future<void> showFlutterNotification(RemoteMessage message) async {
    RemoteNotification? notification = message.notification;
    Logger().i("New notification has arrived in foreground");
    Logger().i(message.toString());
    //AndroidNotification? android = message.notification?.android;
    if (notification != null && !kIsWeb) {
      //  await 5.delay();
      //  await Get.find<HomeController>().getNotifications(
      //     MySharedPref.getLangID() ?? "2"); // update notification UI

      Logger().i("New notification has arrived in foreground");
      flutterLocalNotificationsPlugin.show(
        notification.hashCode,
        notification.title,
        notification.body,
        payload: jsonEncode(message.toMap()),
        NotificationDetails(
          android: AndroidNotificationDetails(
            channel.id,
            channel.name,
            channelDescription: channel.description,
            icon: "@mipmap/launcher_icon",
          ),
          iOS: DarwinNotificationDetails(
              subtitle: channel.description,
              categoryIdentifier: channel.id,
              threadIdentifier: channel.name),
        ),
      );
    }
  }

  Future<void> updateFCM() async {
    ApiProvider apiProvider = Get.put(ApiProvider());
    var fcmToken = LocalStorageServices.getFcmToken();
    if (fcmToken != null) {
      apiProvider.addPortalFCM(fcmToken);
      //await apiProvider.addFCMGiangVien(fcmToken);
    }
  }

  Future<bool> requestPermission() async {
    //final noti = await _firebaseMessaging.requestPermission();
    NotificationSettings noti = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    if (noti.authorizationStatus == AuthorizationStatus.authorized) {
      return true;
    }
    return false;
  }

  Future<void> initNotification() async {
    Logger().i("initNotification called");
    await _firebaseMessaging.requestPermission();
    final fcmToken = await _firebaseMessaging.getToken();
    LocalStorageServices.setFcmToken(fcmToken!);
    await updateFCM();
    await setupFlutterNotifications();
    initLocalNotification();
    initPushNotification();
  }

  Future<void> openNotificationDialog() async {
    var isGrained = await requestPermission();
    Logger().i('openNotificationDialog isGrained: $isGrained');
    if (isGrained) {
      return;
    }
    await Get.dialog(
      AlertDialog(
        title: const Text('Cung cấp quyền nhận thông báo'),
        content: const Text(
            'Nhằm tăng trải nghiệm và tăng tính tương tác, ứng dụng ần có quyền gửi thông báo cho bạn. \n\nHãy đồng ý ở họp thoại tiêp theo'),
        actions: [
          TextButton(
            child: const Text("Cho phép"),
            onPressed: () async {
              await initNotification();
              Get.back();
            },
          ),
        ],
      ),
    );
  }
}
