// ignore_for_file: no_leading_underscores_for_local_identifiers, avoid_print

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:corsac_jwt/corsac_jwt.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/edusoft/diem_sinhvien.dart';
import 'package:tvumobile/app/data/models/edusoft/tkb_edusoft_model.dart';
import 'package:tvumobile/app/data/models/edusoft/tkb_giang_vien.dart';
import 'package:tvumobile/app/data/models/fcm/tms_fcm_giangvien.dart';
import 'package:tvumobile/app/data/models/fcm/tms_portal_fcm.dart';
import 'package:tvumobile/app/data/models/portal/edu_baiviet_model.dart';
import 'package:tvumobile/app/data/models/portal/portal_news_model.dart';
import 'package:tvumobile/app/data/models/portal/tms_danhgia_xe.dart';
import 'package:tvumobile/app/data/models/portal/tms_phieudanhgiachatluongphucvu.dart';
import 'package:tvumobile/app/data/models/tasky/Task.dart';
import 'package:tvumobile/app/data/models/tms/congvan/cong_van_nhan.dart';
import 'package:tvumobile/app/data/models/tms/danhmuc.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxeinfo.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxetaixe.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/giaydieuxe.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepinfo.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepvienchuc.dart';
import 'package:tvumobile/app/data/models/tms/luongthue/luongthanginfo.dart';
import 'package:tvumobile/app/data/models/tms/luongthue/thuenaminfo.dart';
import 'package:tvumobile/app/data/models/tms/luongthue/thuethanginfo.dart';
import 'package:tvumobile/app/utils/extensions/string_extentions.dart';
import 'package:tvumobile/app/utils/helpers/device_info_helper.dart';
import 'package:tvumobile/app/utils/helpers/pagination_filter.dart';
import 'package:tvumobile/app/utils/oauth2/oauth2.dart' as oauth2;
import 'package:tvumobile/app/services/exception_handler.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:http/http.dart' as http;

class ApiProvider extends GetxService with ExceptionHandler {
  final authorizationEndpoint = Uri.parse(AuthConfig.authorization_endpoint);
  final tokenEndpoint = Uri.parse(AuthConfig.token_endpoint);
  final identifier = AuthConfig.clientId;
  final secret = AuthConfig.clientSecrect;
  static oauth2.Client? _client;
  final tokens = LocalStorageServices.getAccessToken();
  RxBool logInEd = false.obs;

  static oauth2.Client? get client => _client;

  // ----------- Khởi tạo hoặc cập nhật _client với thông tin xác thực mới. ----------
  oauth2.Client? setClient(oauth2.Credentials credentials) {
    _client = oauth2.Client(credentials,
        identifier: identifier,
        secret: secret,
        onCredentialsRefreshed: credentialsRefreshedCallback);
    return _client;
  }
  // ---------------------------------------------------------------------------------

  @override
  void onInit() {
    super.onInit();
    var credentials = LocalStorageServices.getCredential();
    Logger().i("ApiProvider onInit");
    if (credentials != null) {
      //Logger().i(credentials.refreshToken);
      if (credentials.isExpired && !credentials.canRefresh) {
        Get.offNamed(Routes.LOGIN, preventDuplicates: true);
      } else {
        //var credentials = oauth2.Credentials.fromJson(credentialsFile);
        _client = oauth2.Client(credentials,
            identifier: identifier,
            secret: secret,
            onCredentialsRefreshed: credentialsRefreshedCallback);
        logInEd.value = true;
      }
    }
  }

  @override
  void onReady() {
    //var credentialsFile = LocalStorageServices.getCredentialString();
    var credentials = LocalStorageServices.getCredential();
    Logger().i("ApiProvider onReady");
    if (credentials == null) {
      Get.offNamed(Routes.LOGIN, preventDuplicates: true);
    }
  }

  // ---------- Callback được gọi khi token được làm mới bởi oauth2.Client. ----------
  static credentialsRefreshedCallback(oauth2.Credentials credentials) {
    LocalStorageServices.setCredentialString(credentials.toJson());
  }
  // ---------------------------------------------------------------------------------

  @override
  Future<void> onClose() async {
    //await LocalStorageServices.setCredentialString(_client!.credentials.toJson());
  }

  // -------------------------------- HÀM LIÊN QUAN ĐẾN THÔNG TIN NGƯỜI DÙNG ------------------------------ //

  // ---------- Lấy thông tin người dùng từ token truy cập (accessToken). ------------
  Future<UserInfo?> getUserInfo() async {
    oauth2.Credentials? credential = LocalStorageServices.getCredential();
    var decodedToken = JWT.parse(credential!.accessToken);
    UserInfo tmp = UserInfo.fromJson(decodedToken.claims);
    LocalStorageServices.setUserInfo(tmp);
    return tmp;
  }

  // -------------------------------- HÀM LIÊN QUAN ĐẾN TIN TỨC VÀ BÀI VIẾT ------------------------------- //

  // ------------ Lấy danh sách tin tức từ cổng thông tin (Portal News). -------------
  Future<List<PortalNewsModel>> loadPortalNews() async {
    showLoading();
    var response =
        await _client!.get(ApiPath.portalNews.toUri()).catchError(handleError);
    //print(response.body);
    if (response.statusCode == 200) {
      hideLoading();
      return PortalNewsModel.parseNews(response.body.toJson());
    }
    hideLoading();
    return [];
  }

  // ----------------------- Lấy danh sách tin tức tuyển sinh. -----------------------
  Future<List<PortalNewsModel>> loadTuyenSinhNews() async {
    showLoading();
    var response =
        await _client!.get(ApiPath.tsNews.toUri()).catchError(handleError);
    //print(response.body);
    if (response.statusCode == 200) {
      hideLoading();
      return PortalNewsModel.parseNews(response.body.toJson());
    }
    hideLoading();
    return [];
  }

  // ----- Lấy danh sách bài viết từ hệ thống Edu (có thể là hệ thống giáo dục). -----
  Future<List<EduBaiVietModel>> loadBaiVietEdu({int num = 15}) async {
    showLoading();

    var response = await _client!.post(ApiPath.eduNewsContent.toUri(),
        headers: {'Content-Type': "application/json"},
        body: jsonEncode({
          "filter": {
            "ky_hieu": "tb",
            "is_hien_thi": true,
            "is_hinh_dai_dien": true,
            "is_noi_dung": true
          },
          "additional": {
            "paging": {"limit": num, "page": 1},
            "ordering": [
              {"name": "do_uu_tien", "order_type": 1},
              {"name": "ngay_dang_tin", "order_type": 1}
            ]
          }
        }));
    //print(response.body);
    if (response.statusCode == 200) {
      hideLoading();
      return EduBaiVietModel.parseNews(
          response.body.toJson()["data"]["ds_bai_viet"]);
    }
    hideLoading();
    return [];
  }

  // ---------------------------------------- HÀM LIÊN QUAN ĐẾN CÔNG VĂN ---------------------------------- //

  // --------------------- Lấy chi tiết một công văn dựa trên ID. --------------------
  Future<TmsCongVanModel?> loadCongVanchitiet(String Ids) async {
    //showLoading();
    var tmp = ApiPath.apiGiangVienCTCV.toUri();
    var urls = Uri(
        scheme: 'https',
        host: tmp.host,
        path: tmp.path,
        queryParameters: {'docID': Ids});
    var response = await _client!.get(urls).catchError(handleError);
    if (response.statusCode == 200) {
      //Logger().i(response.body.toJson());
      //hideLoading();
      return TmsCongVanModel.fromJson(response.body.toJson());
    }
    //hideLoading();
    return null;
  }

  // ------------- Lấy danh sách công văn nhận với phân trang và bộ lọc. -------------
  Future<List<TmsCongVanModel>> loadCongVanNhan(
      {int num = 10,
      int page = 1,
      int docType = 0,
      String searchQuery = "",
      PaginationFilter? pagefileter}) async {
    if (pagefileter != null) {
      num = pagefileter.limit;
      page = pagefileter.page;
      docType = pagefileter.docType;
      searchQuery = pagefileter.searchQuery;
    }
    showLoading();
    var urls = ApiPath.apiGiangVienCVN.toUri();
    urls = urls.replace(queryParameters: {
      'limit': num.toString(),
      'page': (page - 1).toString(),
      'doctype': docType.toString(),
      'search': searchQuery
    });
    var response = await _client!.get(urls).catchError(handleError);
    if (response.statusCode == 200) {
      //print(response.body.toJson());
      hideLoading();
      return TmsCongVanModel.parseNews(response.body.toJson());
    }
    hideLoading();
    return [];
  }

  // --------------------------------- HÀM LIÊN QUAN ĐẾN THỜI KHÓA BIỂU ----------------------------------- //

  // ---------------------- Lấy thời khóa biểu của giảng viên. -----------------------
  Future<TkbGiangVien?> loadTkbGiangVien({int num = 10}) async {
    showLoading();
    var response = await _client!
        .get(ApiPath.apiGiangVienTKB.toUri())
        .catchError(handleError);
    if (response.statusCode == 200) {
      hideLoading();
      return TkbGiangVien.fromJson(response.body.toJson());
    }
    hideLoading();
    return null;
  }

  // -------------- Lấy thời khóa biểu của giảng viên cho tuần hiện tại. -------------
  Future<TkbGiangVien?> loadCurrentWeekTkbGiangVien({int num = 10}) async {
    showLoading();
    var response = await _client!
        .get(ApiPath.apiGiangVienCurrentWeekTKB.toUri())
        .catchError(handleError);
    if (response.statusCode == 200) {
      hideLoading();
      //Logger().i(response.body);
      return TkbGiangVien.fromJson(response.body.toJson());
    }
    hideLoading();
    return null;
  }

  // ---------------------- Lấy thời khóa biểu của sinh viên. ------------------------
  Future<List<TkbEdusoftModel>> loadTkbSinhVien({int num = 10}) async {
    showLoading();
    var response = await _client!
        .get(ApiPath.apiSinhVienTKB.toUri())
        .catchError(handleError);
    if (response.statusCode == 200) {
      hideLoading();
      return TkbEdusoftModel.parseList(response.body.toJson());
    }
    hideLoading();
    return [];
  }

  // -------------- Lấy thời khóa biểu của sinh viên cho tuần hiện tại. --------------
  Future<List<TkbEdusoftModel>> loadCurrentWeekTkbSinhVien(
      {int num = 10}) async {
    showLoading();
    var response = await _client!
        .get(ApiPath.apiSinhVienCurrentWeekTKB.toUri())
        .catchError(handleError);
    if (response.statusCode == 200) {
      hideLoading();
      return TkbEdusoftModel.parseList(response.body.toJson());
    }
    hideLoading();
    return [];
  }

  // ----------------------------------- HÀM LIÊN QUAN ĐẾN ĐIỂM SINH VIÊN --------------------------------- //

  // ---------------------- Lấy danh sách điểm của sinh viên. ------------------------
  Future<List<DsDiemHocky>?> loadDiemSinhVien({int num = 10}) async {
    showLoading();
    var response = await _client!
        .get(ApiPath.apiSinhVienDiem.toUri())
        .catchError(handleError);
    if (response.statusCode == 200) {
      hideLoading();
      print(response.body);
      print(response.body.toJson()['diemHocKy']);
      return DsDiemHocky.parseList(response.body.toJson()['diemHocKy']);
    }
    hideLoading();
    return null;
  }

  // ------------------------ HÀM LIÊN QUAN ĐẾN FCM (Firebase Cloud Messaging) ---------------------------- //

  // ----------------------- Đăng ký token FCM cho giảng viên. -----------------------
  Future<TmsFcmGiangVienModel?> addFCMGiangVien(String tokens2) async {
    //showLoading();
    if (kDebugMode) {
      print("addFCMGiangVien");
    }
    //var acctokens = LocalStorageServices.getAccessToken();
    var dvid = DeviceInfoHelper.deviceID;
    var _response = await _client!
        .post(ApiPath.apiGiangVienFCM.toUri(),
            headers: {'Content-Type': "application/json"},
            body: jsonEncode({"token": tokens2, "deviceid": dvid}))
        .catchError(handleError);
    if (_response.statusCode == 200) {
      return TmsFcmGiangVienModel.fromJson(_response.body.toJson());
    } else {
      Logger().i(_response.body);
      if (kDebugMode) {
        print({"token": tokens2, "deviceid": dvid});
      }
    }
    //hideLoading();
    return null;
  }

  // ---------------- Đăng ký token FCM cho cổng thông tin (Portal). -----------------
  Future<TmsPortalFcmModel?> addPortalFCM(String tokens2) async {
    //showLoading();
    if (kDebugMode) {
      print("addPortalFCM");
    }
    //Logger().i(_client!.credentials.refreshToken);
    //Logger().i(_client!.credentials.refreshToken);
    //Logger().i(_client!.credentials.accessToken);
    //var acctokens = LocalStorageServices.getAccessToken();
    var dvid = DeviceInfoHelper.deviceID;
    if (_client != null) {
      Logger().i(_client!.credentials.refreshToken);
      Logger().i(_client!.credentials.accessToken);
      return null;
    }
    var _response = await _client
        ?.post(ApiPath.apiPortalFCM.toUri(),
            headers: {'Content-Type': "application/json"},
            body: jsonEncode({"token": tokens2, "deviceid": dvid}))
        .catchError(handleError);
    if (_response?.statusCode == 200) {
      return TmsPortalFcmModel.fromJson(_response?.body.toJson());
    } else {
      if (kDebugMode) {
        print(_response?.body);
        print({"token": tokens2, "deviceid": dvid});
      }
    }
    //hideLoading();
    return null;
  }

  // --------------------------------- HÀM LIÊN QUAN ĐẾN ĐÁNH GIÁ ĐƠN VỊ/XE ------------------------------- //

  // ------------------------- Lấy thông tin đơn vị từ mã QR. ------------------------
  Future<dynamic> getDonviQR(String idDonvi) async {
    //showLoading();
    //print("addFCMGiangVien");
    //var acctokens = LocalStorageServices.getAccessToken();
    var _response = await _client!.get(
      ("${ApiPath.apiGetDonviByQr}?donviId=$idDonvi").toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      Logger().i('got initial uri: ${_response.body.toJson()}');
      return _response.body.toJson();
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    //hideLoading();
    return null;
  }

  // --------------------------- Lấy thông tin xe từ mã QR. --------------------------
  Future<PhieuDanhGiaXeModel?> getThongtinXeQR(String idDonvi) async {
    //showLoading();
    if (kDebugMode) {
      print("getThongtinXeQR");
    }
    var _response = await _client!.get(
      ("${ApiPath.apiGetXeByQr}?Id=$idDonvi").toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      Logger().i('got initial uri: ${_response.body.toJson()}');
      return PhieuDanhGiaXeModel.fromJson(_response.body.toJson());
      //return _response.body.toJson();
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    //hideLoading();
    return null;
  }

  // ---------------- Gửi phiếu đánh giá chất lượng phục vụ của đơn vị.---------------
  Future<PhieuDanhGiaChatLuongPhucVuModel?> postDanhGiaDonVi(
      Map<String, dynamic> phieuDanhGia) async {
    //print("postDanhGiaDonVi");
    showLoading();
    var _response = await _client!
        .post(ApiPath.apiPostDGDonvi.toUri(),
            headers: {'Content-Type': "application/json"},
            body: jsonEncode(phieuDanhGia))
        .catchError(handleError);
    if (_response.statusCode == 200) {
      hideLoading();
      return PhieuDanhGiaChatLuongPhucVuModel.fromJson(_response.body.toJson());
    } else {
      //print(_response.body);
      //print(phieuDanhGia);
    }
    hideLoading();
    return null;
  }

  // ------------------------------ Lấy mã QR cá nhân. -------------------------------
  Future<String> getQrCaNhan({bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls = Uri.parse(ApiPath.apiGetQrCaNhan);

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      final jsonResponse = _response.body;
      return jsonResponse;
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return "";
  }

  // ---------------------------------- Tải file từ URL và lưu vào thiết bị. ------------------------------ //
  // ignore: unused_element
  Future<File> _downloadFile(String url, String filename) async {
    var req = await _client!.get(Uri.parse(url));
    var bytes = req.bodyBytes;
    String dir = (await getApplicationDocumentsDirectory()).path;
    File file = File('$dir/$filename');
    await file.writeAsBytes(bytes);
    return file;
  }
  // ------------------------------------------------------------------------------------------------------ //

  // ------------------------------------- HÀM LIÊN QUAN ĐẾN NGHỈ PHÉP ------------------------------------ //

  // ------------------------ Kiểm tra vai trò của viên chức. ------------------------
  Future<String> checkLanhDaoOrBGH() async {
    try {
      final response = await client!.get(
        ApiPath.apiCheckLanhDaoOrBGH.toUri(),
        headers: {'Content-Type': "application/json"},
      ).catchError(handleError);

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        return jsonResponse['role'] as String;
      }

      return "vc"; // Trả về mặc định nếu không thành công
    } catch (e) {
      print("Error in checkLanhDaoOrBGH: $e");
      return "vc"; // Trả về mặc định nếu có lỗi
    }
  }

  // ------------------- Lấy danh sách đơn nghỉ phép của viên chức. ------------------
  Future<List<DonNghiPhepVienChuc>?> getDsNghiPhep(
      {bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }
    print("getDsDonNghiPhep");
    var _response = await _client!.get(
      ApiPath.apiGetdsDNP.toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      // Logger().i(_response.body);
      if (waiting) {
        hideLoading();
      }
      return DonNghiPhepVienChuc.parse(_response.body.toJson());
      //return _response.body.toJson();
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return null;
  }

  // --------------------- Lấy danh sách đơn nghỉ phép của đơn vị. -------------------
  Future<List<DonNghiPhepVienChuc>?> getDsNghiPhepDonVi(
      {int? page,
      int? pageSize,
      String? dnpDuyetTai,
      int? month,
      int? year,
      bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    var uris = ApiPath.apiGetdsDNPDonVi.toUri();
    var queryMap = {
      'page': '0',
      'pageSize': '0',
      'dnpDuyetTai': '',
      'month': '0',
      'year': '0'
    };

    if (dnpDuyetTai != null && dnpDuyetTai.isNotEmpty) {
      queryMap = queryMap.copyWith({'dnpDuyetTai': dnpDuyetTai});
    }

    if (page != null && page > 0) {
      queryMap = queryMap.copyWith({'page': page.toString()});
    }

    if (pageSize != null && pageSize > 0) {
      queryMap = queryMap.copyWith({'pageSize': pageSize.toString()});
    }

    if (month != null) {
      queryMap = queryMap.copyWith({'month': month.toString()});
    }

    if (year != null) {
      queryMap = queryMap.copyWith({'year': year.toString()});
    }

    uris = uris.replace(queryParameters: queryMap);

    print("getDsDonNghiPhepDonVi");
    var _response = await _client!.get(
      uris,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      // Logger().i(_response.body);
      if (waiting) {
        hideLoading();
      }
      return DonNghiPhepVienChuc.parse(_response.body.toJson());
      //return _response.body.toJson();
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return null;
  }

  // -------------------- Lấy chi tiết đơn nghỉ phép của viên chức.-------------------
  Future<List<DonNghiPhepVienChuc>?> getChiTietDonNghiPhep(
      {String? dnpId, bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls = Uri.parse(ApiPath.apiGetChiTietDNP).replace(queryParameters: {
      'dnpId': dnpId.toString(),
    });

    print("getChiTietDonNghiPhep");

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      // Logger().i(_response.body);
      if (waiting) {
        hideLoading();
      }
      return DonNghiPhepVienChuc.parse(_response.body.toJson());
      //return _response.body.toJson();
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return null;
  }

  // ------------------- Lấy thông tin về nghỉ phép của viên chức. -------------------
  Future<DonNghiPhepInfo> getThongtinNghiPhep({bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }
    print("getThongtinNghiPhep");
    var _response = await _client!.get(
      ApiPath.apiGetDNPinfo.toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      Logger().i('getThongtinNghiPhep: ${_response.body}');
      if (waiting) {
        hideLoading();
      }
      if (_response.body.toJson() == null) {
        return DonNghiPhepInfo.empty();
      }
      return DonNghiPhepInfo.fromJson(_response.body.toJson());
    } else {
      Logger().i('getThongtinNghiPhep: ${_response.body}');
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return DonNghiPhepInfo.empty();
  }

  // ----------------------- Gửi đơn nghỉ phép của viên chức. ------------------------
  Future<bool> postDonNghiPhep(Map<String, dynamic> donnghiphep) async {
    //print("postDanhGiaDonVi");
    showLoading();
    String data = "";
    try {
      data = jsonEncode(donnghiphep);
    } catch (ex) {
      Logger().i(ex);
    }
    var _response = await _client!
        .post(ApiPath.apiPostDNP.toUri(),
            headers: {'Content-Type': "application/json"}, body: data)
        .catchError(handleError);
    if (_response.statusCode == 200) {
      hideLoading();
      Logger().i(_response.body);
      // var ret = _response.body.toJson();
      // if (ret["status"] == 200) {
      //   return true;
      // } else {
      //   return false;
      // }
      return true;
    } else {
      if (_response.statusCode == 500) {
        hideLoading();
        Get.showSnackbar(GetSnackBar(
          title: "Thông báo",
          message: "Đã phát sinh lỗi hệ thống, vui lòng thử lại sau",
          onTap: (snack) {
            Get.back();
          },
          duration: Duration(seconds: 5),
          showProgressIndicator: true,
          snackPosition: SnackPosition.TOP,
        ));
        return false;
      }
      //print(phieuDanhGia);
    }
    hideLoading();
    return false;
  }

  // ----------------------- Sửa đơn nghỉ phép của viên chức. ------------------------
  Future<bool> putDonNghiPhep(int id, Map<String, dynamic> donnghiphep) async {
    showLoading();
    String data = "";
    try {
      data = jsonEncode(donnghiphep);
    } catch (ex) {
      Logger().i(ex);
    }

    try {
      final urls = Uri.parse(ApiPath.apiPutDNP).replace(queryParameters: {
        'dnpId': id.toString(),
      });

      final response = await _client!
          .put(urls, headers: {'Content-Type': 'application/json'}, body: data);
      hideLoading();

      if (response.statusCode == 200) {
        Get.showSnackbar(GetSnackBar(
          title: 'Thông báo',
          message: 'Cập nhật đơn nghỉ phép thành công',
          duration: Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        ));
        return true;
      } else {
        Logger().e('Lỗi HTTP: ${response.statusCode} - ${response.body}');
        Get.showSnackbar(GetSnackBar(
          title: 'Lỗi',
          message: 'Không thể cập nhật đơn nghỉ phép',
          duration: Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        ));
        return false;
      }
    } catch (ex) {
      hideLoading();
      Logger().e('Ngoại lệ: $ex');
      Get.showSnackbar(GetSnackBar(
        title: 'Lỗi',
        message: 'Đã xảy ra lỗi khi cập nhật đơn nghỉ phép',
        duration: Duration(seconds: 3),
        snackPosition: SnackPosition.TOP,
      ));
      return false;
    }
  }

  // ------------------ Chuyển và Thu hồi đơn nghỉ phép chờ duyệt. -------------------
  Future<String> chuyenDonNghiPhep(
      {String? dnpId, bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls = Uri.parse(ApiPath.apiChuyenDNP).replace(queryParameters: {
      'dnpId': dnpId.toString(),
    });

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      final jsonResponse = jsonDecode(_response.body);
      return jsonResponse['message'] as String;
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return "";
  }

  // ----------------------------- Duyệt đơn nghỉ phép. ------------------------------
  Future<String> duyetDonNghiPhep(
      {String? dnpId, String? duyettai, bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls = Uri.parse(ApiPath.apiDuyetDNP).replace(queryParameters: {
      'dnpId': dnpId.toString(),
      'duyettai': duyettai.toString(),
    });

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      final jsonResponse = jsonDecode(_response.body);
      return jsonResponse['message'] as String;
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return "";
  }

  // ------------------------------ Trả đơn nghỉ phép. -------------------------------
  Future<String> traDonNghiPhep(
      {String? dnpId,
      String? lydo,
      String? duyettai,
      bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls = Uri.parse(ApiPath.apiTraDNP).replace(queryParameters: {
      'dnpId': dnpId.toString(),
      'lydo': lydo.toString(),
      'duyettai': duyettai.toString(),
    });

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      final jsonResponse = jsonDecode(_response.body);
      return jsonResponse['message'] as String;
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return "";
  }

  // ------------------------ Thu hồi đơn nghỉ phép đã duyệt. ------------------------
  Future<String> thuHoiDonNghiPhep(
      {String? dnpId, String? duyettai, bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls = Uri.parse(ApiPath.apiTraDNPDaDuyet).replace(queryParameters: {
      'dnpId': dnpId.toString(),
      'duyettai': duyettai.toString(),
    });

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      final jsonResponse = jsonDecode(_response.body);
      return jsonResponse['message'] as String;
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return "";
  }

  // ------------------------------ Xóa đơn nghỉ phép. -------------------------------
  Future<String> xoaDonNghiPhep({String? dnpId, bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls = Uri.parse(ApiPath.apiXoaDNP).replace(queryParameters: {
      'dnpId': dnpId.toString(),
    });

    var _response = await _client!.delete(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      final jsonResponse = jsonDecode(_response.body);
      return jsonResponse['message'] as String;
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return "";
  }

  // --------- Gửi dữ liệu JSON (có thể là widget hoặc cấu hình) lên server. ---------
  Future<bool> postJsonWidgets(String donnghiphep) async {
    //print("postDanhGiaDonVi");
    showLoading();
    var _data = {"jsonData": donnghiphep};
    String data = "";
    try {
      data = jsonEncode(_data);
    } catch (ex) {
      Logger().i(ex);
    }
    var _response = await _client!
        .post(ApiPath.apiPostWidgets.toUri(),
            headers: {'Content-Type': "application/json"}, body: data)
        .catchError(handleError);
    if (_response.statusCode == 200) {
      hideLoading();
      Logger().i(_response.body);
      return true;
    } else {
      Logger().i(_response.body);
      //print(phieuDanhGia);
    }
    hideLoading();
    return false;
  }

  // -------------------------------------- HÀM LIÊN QUAN ĐẾN ĐIỀU XE ------------------------------------- //

  // --------------------------- Lấy thông tin về điều xe. ---------------------------
  Future<DieuXeInfo> getDieuXeInfo({bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }
    print("getDieuXeInfo");

    var _response = await _client!.get(
      ApiPath.apiGetDXinfo.toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      Logger().i('getDieuXeInfo: ${_response.body}');
      if (waiting) {
        hideLoading();
      }
      if (_response.body.toJson() == null) {
        return DieuXeInfo.empty();
      }
      return DieuXeInfo.fromJson(_response.body.toJson());
    } else {
      Logger().i('getDieuXeInfo: ${_response.body}');
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return DieuXeInfo.empty();
  }

  // ---------------------- Lấy danh sách xe và nhân viên lái. -----------------------
  Future<XeNvLai> getDsXeNvLai({bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }
    print("getDieuXeInfo");

    var response = await _client!.get(
      ApiPath.apiGetdsXeNvLai.toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      final jsonData = jsonDecode(response.body);
      if (jsonData == null || jsonData.isEmpty) {
        return XeNvLai.empty();
      }
      return XeNvLai.fromJson(jsonData);
    } else {
      if (kDebugMode) {
        print(response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return XeNvLai.empty();
  }

  // ------------------------- Kiểm tra phân quyền điều xe. --------------------------
  Future<String> checkPhanQuyenDieuXe() async {
    try {
      final response = await client!.get(
        ApiPath.apiCheckPhanQuyenDX.toUri(),
        headers: {'Content-Type': "application/json"},
      ).catchError(handleError);

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        return jsonResponse['role'] as String;
      }

      return "vc"; // Trả về mặc định nếu không thành công
    } catch (e) {
      print("Error in checkLanhDaoOrBGH: $e");
      return "vc"; // Trả về mặc định nếu có lỗi
    }
  }

  // ---------------------- Lấy danh sách điều xe của đơn vị. ------------------------
  Future<List<GiayDieuXe>> getDsPhieuDieuXe(
      {int? page,
      int? pageSize,
      String? phanQuyen,
      String? danhMuc,
      String? trangThai,
      bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    var uris = ApiPath.apiGetdsPhieuDX.toUri();
    var queryMap = {
      'page': '0',
      'pageSize': '0',
      'phanQuyen': '',
      'danhMuc': '',
      'trangThai': '',
    };

    if (phanQuyen != null && phanQuyen.isNotEmpty) {
      queryMap = queryMap.copyWith({'phanQuyen': phanQuyen});
    }

    if (danhMuc != null && danhMuc.isNotEmpty) {
      queryMap = queryMap.copyWith({'danhMuc': danhMuc});
    }

    if (trangThai != null && trangThai.isNotEmpty) {
      queryMap = queryMap.copyWith({'trangThai': trangThai});
    }

    if (page != null && page > 0) {
      queryMap = queryMap.copyWith({'page': page.toString()});
    }

    if (pageSize != null && pageSize > 0) {
      queryMap = queryMap.copyWith({'pageSize': pageSize.toString()});
    }

    uris = uris.replace(queryParameters: queryMap);

    print("getDsPhieuDieuXe");
    var _response = await _client!.get(
      uris,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      // Logger().i(_response.body);
      if (waiting) {
        hideLoading();
      }
      return GiayDieuXe.parse(_response.body.toJson());
      //return _response.body.toJson();
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return [];
  }

  // ---------------------- Lấy danh sách thành viên trong nhóm. ---------------------
  Future<List<dynamic>> getDsVienChuc(
      {required int donviID, bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls = Uri.parse(ApiPath.apiGetdsVienChuc).replace(queryParameters: {
      'donviID': donviID.toString(),
    });

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      //Logger().i('getTaskStatistics: ${_response.body.toJson()}');
      if (waiting) {
        hideLoading();
      }
      return _response.body.toJson();
    } else {
      print(_response.body);
    }
    if (waiting) {
      hideLoading();
    }
    return [];
  }

  // -------------------- Lấy chi tiết đơn nghỉ phép của viên chức.-------------------
  Future<List<GiayDieuXe>?> getChiTietPhieuDX(
      {String? pdxId, bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls =
        Uri.parse(ApiPath.apiGetChiTietPhieuDX).replace(queryParameters: {
      'pdxId': pdxId.toString(),
    });

    print("getChiTietPhieuDX");

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      // Logger().i(_response.body);
      if (waiting) {
        hideLoading();
      }
      return GiayDieuXe.parse(_response.body.toJson());
      //return _response.body.toJson();
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return null;
  }

  // -------------------- Lấy điều xe tài xế theo ngày đi ngày về. -------------------
  Future<List<DieuXeTaiXe>?> getDXTXTheoNgay(
      {DateTime? ngayDi, bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls = Uri.parse(ApiPath.apiGetDXTXTheoNgay)
        .replace(queryParameters: {'ngayDi': ngayDi.toString()});

    print("getDXTXTheoNgay");

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      // Logger().i(_response.body);
      if (waiting) {
        hideLoading();
      }
      return DieuXeTaiXe.parse(_response.body.toJson());
      //return _response.body.toJson();
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return null;
  }

  // ---------------------------- Tạo phiếu điều xe mới. -----------------------------
  Future<String?> postDieuXe({
    required Map<String, dynamic> formValue,
    List<PlatformFile>? selectedFiles,
  }) async {
    try {
      // Tạo yêu cầu MultipartRequest
      var request = http.MultipartRequest(
        'POST',
        ApiPath.apiCreatePhieuDX.toUri(),
      );

      // Chỉ gửi jsonData như API mong đợi
      request.fields['jsonData'] = jsonEncode(formValue);

      // Thêm file nếu có
      if (selectedFiles != null && selectedFiles.isNotEmpty) {
        for (var file in selectedFiles) {
          if (file.bytes != null) {
            request.files.add(http.MultipartFile.fromBytes(
              'files', // Tên field phải khớp với API
              file.bytes!,
              filename: file.name,
            ));
          } else if (file.path != null) {
            request.files.add(await http.MultipartFile.fromPath(
              'files',
              file.path!,
              filename: file.name,
            ));
          }
        }
      }

      if (kDebugMode) {
        print("Fields: ${request.fields}");
        print("Files: ${request.files.map((f) => f.filename).toList()}");
      }

      // Gửi yêu cầu
      var response = await client?.send(request);

      // Lấy phản hồi
      var responseBody = await response?.stream.bytesToString();
      if (response?.statusCode == 200 || response?.statusCode == 201) {
        if (kDebugMode) {
          print("PhieuDieuXe created successfully: $responseBody");
        }
        return responseBody; // Trả về phản hồi nếu thành công
      } else {
        if (kDebugMode) {
          print(
              "Failed to create PhieuDieuXe: ${response?.statusCode} - $responseBody");
        }
        throw Exception(
            "Failed to create PhieuDieuXe: ${response?.statusCode} - $responseBody");
      }
    } catch (e) {
      if (kDebugMode) {
        print("Error creating PhieuDieuXe: $e");
      }
      throw Exception("Error creating PhieuDieuXe: $e");
    }
  }

  // ------------------------------ Sửa phiếu điều xe. -------------------------------
  Future<String?> putDieuXe({
    required int pdxId,
    required Map<String, dynamic> formValue,
  }) async {
    var request = http.MultipartRequest(
      'PUT',
      Uri.parse(ApiPath.apiUpdatePhieuDX),
    );

    request.fields['pdxId'] = pdxId.toString(); // Thêm pdxId vào request
    request.fields['jsonData'] = jsonEncode(formValue);

    try {
      var response = await client!.send(request);
      var responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        // Giải mã responseBody thành dynamic để trả về dữ liệu (Id, TrangThai, Status)
        return responseBody;
      } else {
        if (kDebugMode) {
          print(
              "Failed to update PhieuDieuXe: ${response.statusCode} - $responseBody");
        }
        throw Exception('Failed to update dieuxe: $responseBody');
      }
    } catch (e) {
      throw Exception('Error updating dieuxe: $e');
    }
  }

  // ------------------------------- Xóa phiếu điều xe. ------------------------------
  Future<String?> deleteDieuXe(int pdxId) async {
    try {
      final Uri urls =
          Uri.parse(ApiPath.apiDeletePhieuDX).replace(queryParameters: {
        'pdxId': pdxId.toString(),
      });

      var response = await _client!.delete(urls, headers: {
        'Content-Type': "application/json"
      }).catchError(handleHttpError);

      if (response.statusCode == 200) {
        return response.body;
      } else {
        print(response.body);
      }
    } catch (e) {
      print('Error deleting task: $e');
      throw Exception('Failed to delete task: $e');
    }
    return null;
  }

  // ------------------ Chuyển và Thu hồi phiếu điều xe chờ duyệt. -------------------
  Future<String> chuyenPhieuDieuXe(
      {String? pdxId, bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls = Uri.parse(ApiPath.apiChuyenPhieuDX).replace(queryParameters: {
      'pdxId': pdxId.toString(),
    });

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      final jsonResponse = jsonDecode(_response.body);
      return jsonResponse['message'] as String;
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return "";
  }

  // ---------------------- Duyệt phiếu điều xe tại phòng HCTC. ----------------------
  Future<String?> duyetDieuXeHcTc({
    required String pdxId,
    required Map<String, dynamic> formValue,
  }) async {
    var request = http.MultipartRequest(
      'POST',
      Uri.parse(ApiPath.apiDuyetDXHcTc),
    );

    request.fields['pdxId'] = pdxId.toString(); // Thêm pdxId vào request
    request.fields['jsonData'] = jsonEncode(formValue);

    try {
      var response = await client!.send(request);
      var responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        // Giải mã responseBody thành dynamic để trả về dữ liệu (Id, TrangThai, Status)
        return responseBody;
      } else {
        if (kDebugMode) {
          print(
              "Failed to update PhieuDieuXe: ${response.statusCode} - $responseBody");
        }
        throw Exception('Failed to update dieuxe: $responseBody');
      }
    } catch (e) {
      throw Exception('Error updating dieuxe: $e');
    }
  }

  // ------------------------ Duyệt phiếu điều xe tại đơn vị. ------------------------
  Future<String> duyetDieuXeDV({String? pdxId, bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls = Uri.parse(ApiPath.apiDuyetDXDonVi).replace(queryParameters: {
      'pdxId': pdxId.toString(),
    });

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      final jsonResponse = jsonDecode(_response.body);
      return jsonResponse['message'] as String;
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return "";
  }

  // --------------------------- Trả (Hủy) phiếu điều xe. ----------------------------
  Future<String> traPhieuDieuXe(
      {String? pdxId, String? lydo, bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls = Uri.parse(ApiPath.apiTraPhieuDX).replace(queryParameters: {
      'pdxId': pdxId.toString(),
      'lydo': lydo.toString(),
    });

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      final jsonResponse = jsonDecode(_response.body);
      return jsonResponse['message'] as String;
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return "";
  }

  // --------------------------- Điều chỉnh phiếu điều xe. ---------------------------
  Future<String> dieuchinhPhieuDieuXe(
      {String? pdxId, String? lydo, bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }

    final urls =
        Uri.parse(ApiPath.apiDieuChinhPhieuDX).replace(queryParameters: {
      'pdxId': pdxId.toString(),
      'lydo': lydo.toString(),
    });

    var _response = await _client!.get(
      urls,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    if (_response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      final jsonResponse = jsonDecode(_response.body);
      return jsonResponse['message'] as String;
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    if (waiting) {
      hideLoading();
    }
    return "";
  }

  // ----------------------------- Ghép phiếu điều xe. -------------------------------
  Future<String?> mergeDieuXe({
    required List<int> listPDXid,
    required Map<String, dynamic> formValue,
  }) async {
    try {
      // Tạo yêu cầu MultipartRequest
      var request = http.MultipartRequest(
        'POST',
        ApiPath.apiMergePhieuDX.toUri(),
      );

      // Chỉ gửi jsonData như API mong đợi
      request.fields['jsonData'] = jsonEncode(formValue);
      // Gửi listPDXid dưới dạng các trường listPDXid[]

      request.fields['listPDXid'] = listPDXid.join(',');

      print("Fields: ${request.fields["listPDXid"]}");
      print("listPDX: ${request.fields}");

      // Gửi yêu cầu
      var response = await client?.send(request);

      // Lấy phản hồi
      var responseBody = await response?.stream.bytesToString();
      if (response?.statusCode == 200) {
        if (kDebugMode) {
          print("PhieuDieuXe megred successfully: $responseBody");
        }
        return responseBody; // Trả về phản hồi nếu thành công
      } else {
        if (kDebugMode) {
          print(
              "Failed to megred PhieuDieuXe: ${response?.statusCode} - $responseBody");
        }
        throw Exception(
            "Failed to megred PhieuDieuXe: ${response?.statusCode} - $responseBody");
      }
    } catch (e) {
      if (kDebugMode) {
        print("Error megred PhieuDieuXe: $e");
      }
      throw Exception("Error megred PhieuDieuXe: $e");
    }
  }

  // ----------------------------- Tạo hợp đồng thuê xe. -----------------------------
  Future<String?> createHDThueXe({
    required int pdxId,
    required Map<String, dynamic> formValue,
  }) async {
    try {
      // Tạo yêu cầu MultipartRequest
      var request = http.MultipartRequest(
        'POST',
        ApiPath.apiCreateHDThueXe.toUri(),
      );

      // Chỉ gửi jsonData như API mong đợi
      request.fields['pdxId'] = pdxId.toString(); // Thêm pdxId vào request
      request.fields['jsonData'] = jsonEncode(formValue);

      // Gửi yêu cầu
      var response = await client?.send(request);

      // Lấy phản hồi
      var responseBody = await response?.stream.bytesToString();
      if (response?.statusCode == 200) {
        if (kDebugMode) {
          print("HopDongThueXe created successfully: $responseBody");
        }
        return responseBody; // Trả về phản hồi nếu thành công
      } else {
        if (kDebugMode) {
          print(
              "Failed to create HopDongThueXe: ${response?.statusCode} - $responseBody");
        }
        throw Exception(
            "Failed to create HopDongThueXe: ${response?.statusCode} - $responseBody");
      }
    } catch (e) {
      if (kDebugMode) {
        print("Error creating HopDongThueXe: $e");
      }
      throw Exception("Error creating HopDongThueXe: $e");
    }
  }

  // ----------------------------- Sửa hợp đồng thuê xe. -----------------------------
  Future<String?> updateHDThueXe({
    required int pdxId,
    required Map<String, dynamic> formValue,
  }) async {
    var request = http.MultipartRequest(
      'PUT',
      Uri.parse(ApiPath.apiUpdateHDThueXe),
    );

    request.fields['pdxId'] = pdxId.toString(); // Thêm pdxId vào request
    request.fields['jsonData'] = jsonEncode(formValue);

    try {
      var response = await client!.send(request);
      var responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        // Giải mã responseBody thành dynamic để trả về dữ liệu (Id, TrangThai, Status)
        return responseBody;
      } else {
        if (kDebugMode) {
          print(
              "Failed to update HopDongThueXe: ${response.statusCode} - $responseBody");
        }
        throw Exception('Failed to update HopDongThueXe: $responseBody');
      }
    } catch (e) {
      throw Exception('Error updating HopDongThueXe: $e');
    }
  }

  // ------------------------------ Xóa hợp đồng thuê xe. ----------------------------
  Future<String?> deleteHDThueXe(int pdxId) async {
    try {
      final Uri urls =
          Uri.parse(ApiPath.apiDeleteHDThueXe).replace(queryParameters: {
        'pdxId': pdxId.toString(),
      });

      var response = await _client!.delete(urls, headers: {
        'Content-Type': "application/json"
      }).catchError(handleHttpError);

      if (response.statusCode == 200) {
        return response.body;
      } else {
        print(response.body);
      }
    } catch (e) {
      print('Error deleting HopDongThueXe: $e');
      throw Exception('Failed to delete HopDongThueXe: $e');
    }
    return null;
  }

  // -------------------------------------- HÀM LIÊN QUAN ĐẾN DANH MỤC ------------------------------------ //
  // --------------------------- Lấy danh mục chấm công. -----------------------------
  Future<List<DanhMuc>> getDanhMucChamCong() async {
    //showLoading();
    if (kDebugMode) {
      print("getThongtinXeQR");
    }
    var _response = await _client!.get(
      ApiPath.apiGetDMChamCong.toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      //Logger().i('got initial uri: ${_response.body.toJson()}');
      return DanhMuc.parse(_response.body.toJson());
      //return _response.body.toJson();
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    //hideLoading();
    return [];
  }

  // ---------- Lấy danh sách các danh mục dựa trên một mã danh mục cụ thể -----------
  Future<List<DanhMuc>> getDanhMucs(int madanhmuc,
      {bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }
    Uri apit = ("${ApiPath.apiGetDanhMucs}?loaidm=$madanhmuc").toUri();
    var _response = await _client!.get(
      apit,
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      return DanhMuc.parse(_response.body.toJson());
    } else {
      print(_response.body);
    }
    if (waiting) {
      hideLoading();
    }
    return [];
  }

  // ------------------------------------ HÀM LIÊN QUAN ĐẾN GIAO VIỆC ------------------------------------- //

  // ------------------ Lấy tổng quan về nhiệm vụ (task overview). -------------------
  Future<Map<String, dynamic>> getTaskOverview({int taskLimit = 10}) async {
    //showLoading();
    var _response = await _client!.get(
      "${ApiPath.apiTaskOverview}?limit=$taskLimit".toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);

    print('Status Code: ${_response.statusCode}'); // In mã trạng thái
    print('Response Body: ${_response.body}'); // In nội dung phản hồi

    if (_response.statusCode == 200) {
      Logger().i('getTaskStatistics: ${_response.body.toJson()}');
      return _response.body.toJson();
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    //hideLoading();
    return {};
  }

  // ---------------------- Lấy danh sách trạng thái nhiệm vụ. -----------------------
  Future<List<DanhMuc>> getTaskStatus({int limit = 10}) async {
    //showLoading();
    var _response = await _client!.get(
      "${ApiPath.apiTaskStatuses}?limit=$limit".toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      //Logger().i('getTaskStatus: ${_response.body.toJson()}');
      var data = _response.body.toJson();
      if (data is Map<String, dynamic>) {
        var list = data.values.toList();
        Logger().i('getTaskStatus: $list');
        return DanhMuc.parse(list);
      } else {
        return [];
      }
    } else {
      return _response.body.toJson();
    }
    //hideLoading();
  }

  // ---------------------- Lấy danh sách đơn vị tính hoạt động. ---------------------
  Future<List<DanhMuc>> getDonViTinh({int limit = 10}) async {
    //showLoading();
    var _response = await _client!.get(
      "${ApiPath.apiDonViTinh}?limit=$limit".toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      //Logger().i('getTaskStatus: ${_response.body.toJson()}');
      var data = _response.body.toJson();
      if (data is Map<String, dynamic>) {
        var list = data.values.toList();
        Logger().i('getDonViTinh: $list');
        return DanhMuc.parse(list);
      } else {
        return [];
      }
    } else {
      return _response.body.toJson();
    }
    //hideLoading();
  }

  // ----------------------------- Lấy thống kê nhiệm vụ. ----------------------------
  Future<Map<String, dynamic>> getTaskStatistics() async {
    //showLoading();
    var _response = await _client!.get(
      ApiPath.apiTaskStatistics.toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      //Logger().i('getTaskStatistics: ${_response.body.toJson()}');
      return _response.body.toJson();
    } else {
      if (kDebugMode) {
        print(_response.body);
      }
    }
    //hideLoading();
    return {};
  }

  // ---------------- Lấy danh sách tất cả nhiệm vụ với các bộ lọc. ------------------
  // Future<List<Task>> getAllTasks(
  //     {int? taskId,
  //     int? tag,
  //     int? status,
  //     int? taskType,
  //     String? searchQuery,
  //     int? page,
  //     int? pageSize,
  //     bool waiting = false}) async {
  //   if (waiting) {
  //     showLoading();
  //   }
  //   var uris = ApiPath.apiAllTasks.toUri();
  //   var queryMap = {
  //     'tag': '0',
  //     'page': '1',
  //     'pageSize': '10',
  //     'status': '0',
  //     'taskType': '0',
  //     'searchQuery': '',
  //     'taskId': '0'
  //   };
  //   if (tag != null && tag > 0) {
  //     queryMap = queryMap.copyWith({'tag': tag.toString()});
  //   }
  //   if (status != null && status > 0) {
  //     queryMap = queryMap.copyWith({'status': status.toString()});
  //   }
  //   if (taskType != null && taskType > 0) {
  //     queryMap = queryMap.copyWith({'taskType': taskType.toString()});
  //   }
  //   if (searchQuery != null && searchQuery.isNotEmpty) {
  //     queryMap = queryMap.copyWith({'searchQuery': searchQuery});
  //   }
  //   if (page != null && page > 0) {
  //     queryMap = queryMap.copyWith({'page': page.toString()});
  //   }
  //   if (pageSize != null && pageSize > 0) {
  //     queryMap = queryMap.copyWith({'pageSize': pageSize.toString()});
  //   }
  //   if (taskId != null && taskId > 0) {
  //     queryMap = queryMap.copyWith({'taskId': taskId.toString()});
  //   }
  //   uris = uris.replace(queryParameters: queryMap);
  //   Logger().i('getAllTasks: $uris');
  //   var response = await _client!.get(
  //     uris,
  //     headers: {'Content-Type': "application/json"},
  //   ).catchError((error) => handleHttpError(error));
  //   if (response.statusCode == 200) {
  //     if (waiting) {
  //       hideLoading();
  //     }
  //     Logger().i('getAllTasks: ${response.body.toJson()}');
  //     var data = Task.parse(response.body.toJson());
  //     return data;
  //   } else {
  //     print(response.body);
  //   }
  //   if (waiting) {
  //     hideLoading();
  //   }
  //   return [];
  // }

  Future<List<Task>> getAllTasks({
    int? taskId,
    int? tag,
    int? status,
    int? taskType,
    String? searchQuery,
    int? page,
    int? pageSize,
    bool waiting = false,
    int? month, // Thêm tham số tháng
    int? year, // Thêm tham số năm
    String? taskRole, // Thêm tham số này
  }) async {
    if (waiting) {
      showLoading();
    }
    // Sửa điều kiện để kiểm tra null thay vì 0
    // month ??= DateTime.now().month;
    // year ??= DateTime.now().year;
    var uris = ApiPath.apiAllTasks.toUri();
    var queryMap = {
      'tag': '0',
      'page': '1',
      'pageSize': '10',
      'status': '0',
      'taskType': '0',
      'searchQuery': '',
      'taskId': '0',
      'month': '0',
      'year': '0',
      // 'month': month.toString(), // Sử dụng giá trị đã được gán
      // 'year': year.toString(), // Sử dụng giá trị đã được gán
      'taskRole': '',
    };
    if (tag != null && tag > 0) {
      queryMap = queryMap.copyWith({'tag': tag.toString()});
    }
    if (status != null && status > 0) {
      queryMap = queryMap.copyWith({'status': status.toString()});
    }
    if (taskType != null && taskType > 0) {
      queryMap = queryMap.copyWith({'taskType': taskType.toString()});
    }
    if (searchQuery != null && searchQuery.isNotEmpty) {
      queryMap = queryMap.copyWith({'searchQuery': searchQuery});
    }
    if (page != null && page > 0) {
      queryMap = queryMap.copyWith({'page': page.toString()});
    }
    if (pageSize != null && pageSize > 0) {
      queryMap = queryMap.copyWith({'pageSize': pageSize.toString()});
    }
    if (taskId != null && taskId > 0) {
      queryMap = queryMap.copyWith({'taskId': taskId.toString()});
    }
    if (taskRole != null && taskRole.isNotEmpty) {
      queryMap = queryMap.copyWith({'taskRole': taskRole});
    }
    if (month != null) {
      queryMap = queryMap.copyWith({'month': month.toString()});
    }
    if (year != null) {
      queryMap = queryMap.copyWith({'year': year.toString()});
    }

    uris = uris.replace(queryParameters: queryMap);

    Logger().i('getAllTasks: $uris');

    var response = await _client!.get(
      uris,
      headers: {'Content-Type': "application/json"},
    ).catchError((error) => handleHttpError(error));

    if (response.statusCode == 200) {
      if (waiting) {
        hideLoading();
      }
      Logger().i('getAllTasks respons: ${response.body.toJson()}');
      var data = Task.parse(response.body.toJson());
      return data;
    } else {
      print(response.body);
    }

    if (waiting) {
      hideLoading();
    }
    return [];
  }

  // -- Cập nhật thông tin nhiệm vụ (trạng thái, tự đánh giá và giao việc đánh giá). --
  Future<void> updateTaskInfo(
      {required int taskId,
      int? vienchucId,
      int? status,
      int? tudanhgia,
      int? nguoiduyetdanhgia,
      String? nguoiduyetghichu,
      DateTime? endDate,
      bool waiting = true}) async {
    if (waiting) {
      showLoading();
    }

    endDate = DateTime.now();
    final Uri urls =
        Uri.parse(ApiPath.apiUpdateTaskInfo).replace(queryParameters: {
      'taskId': taskId.toString(),
      if (vienchucId != null) 'vienchucId': vienchucId.toString(),
      if (status != null) 'status': status.toString(),
      if (tudanhgia != null) 'tudanhgia': tudanhgia.toString(),
      if (nguoiduyetdanhgia != null)
        'nguoiduyetdanhgia': nguoiduyetdanhgia.toString(),
      if (nguoiduyetghichu != null && nguoiduyetghichu.isNotEmpty)
        'nguoiduyetghichu': nguoiduyetghichu,
      'endDate': endDate.toIso8601String()
    });

    Logger().i('updateTaskInfo URL: ${urls.toString()}');

    var response = await _client!.post(urls, headers: {
      'Content-Type': "application/json"
    }).catchError(handleHttpError);

    if (response.statusCode == 200) {
      Logger().i('updateTaskInfo: ${response.body.toJson()}');
    } else {
      print(response.body);
    }
    if (waiting || isLoadingShown.value) {
      hideLoading();
    }
  }

  // ------------------------ THAO TÁC VỚI TASK UPDATE INFO --------------------------
  // ---------------------------- Thêm thông tin trao đổi. ---------------------------
  Future<void> addTaskUpdateInfo(
      {required int taskId, String? updateInfo, bool waiting = true}) async {
    if (waiting) {
      showLoading();
    }

    final Uri urls =
        Uri.parse(ApiPath.apiAddTaskUpdateInfo).replace(queryParameters: {
      'taskId': taskId.toString(),
      if (updateInfo != null && updateInfo.isNotEmpty) 'updateInfo': updateInfo,
    });

    Logger().i('updateTaskUpdateInfo URL: ${urls.toString()}');
    var response = await _client!.post(urls, headers: {
      'Content-Type': "application/json"
    }).catchError(handleHttpError);
    if (response.statusCode == 200) {
      Logger().i('updateTaskUpdateInfo: ${response.body.toJson()}');
    } else {
      print(response.body);
    }
    if (waiting || isLoadingShown.value) {
      hideLoading();
    }
  }

  // ----------------------- Chỉnh sửa thông tin trao đổi. ---------------------------
  Future<void> updateTaskUpdateInfo({
    required int updateInfoId,
    required String? updateInfo,
  }) async {
    final urls =
        Uri.parse(ApiPath.apiEditTaskUpdateInfo).replace(queryParameters: {
      'taskUpdateInfoId': updateInfoId.toString(),
      if (updateInfo != null) 'updateInfo': updateInfo,
    });

    final response = await _client!.put(urls, headers: {
      'Content-Type': "application/json"
    }).catchError(handleHttpError);

    if (response.statusCode == 200) {
      Logger().i('updateTaskInfo: ${response.body.toJson()}');
    } else {
      print(response.body);
    }
  }

  // ---------------------------- Xóa thông tin trao đổi. ----------------------------
  Future<void> deleteTaskUpdateInfo(int updateInfoId) async {
    final urls =
        Uri.parse(ApiPath.apiDeleteTaskUpdateInfo).replace(queryParameters: {
      'taskUpdateInfoId': updateInfoId.toString(),
    });
    final response = await _client!.delete(urls, headers: {
      'Content-Type': "application/json"
    }).catchError(handleHttpError);

    if (response.statusCode == 200) {
      Logger().i('updateTaskInfo: ${response.body.toJson()}');
    } else {
      print(response.body);
    }
  }

  // --------------------------- THAO TÁC VỚI TASK ACTION. ----------------------------
  // ---------------------------- Thêm hành động thực hiện. --------------------------
  Future<void> addTaskAction(
      {required int taskId,
      String? moTa,
      String? lyDoChuaHoanThanh,
      int? khoiLuongCongViec,
      int? tongSoGioThucHien,
      DateTime? ngayKetThuc,
      int? donViTinhId,
      int? tiLeHoanThanh, // Thêm trường tỉ lệ hoàn thành
      bool waiting = true}) async {
    if (waiting) {
      showLoading();
    }

    final Uri urls =
        Uri.parse(ApiPath.apiAddTaskAction).replace(queryParameters: {
      'taskId': taskId.toString(),
      if (moTa != null && moTa.isNotEmpty) 'moTa': moTa,
      if (lyDoChuaHoanThanh != null && lyDoChuaHoanThanh.isNotEmpty)
        'lyDoChuaHoanThanh': lyDoChuaHoanThanh,
      if (khoiLuongCongViec != null)
        'khoiLuongCongViec': khoiLuongCongViec.toString(),
      if (tongSoGioThucHien != null)
        'tongSoGioThucHien': tongSoGioThucHien.toString(),
      if (ngayKetThuc != null) 'ngayKetThuc': ngayKetThuc.toString(),
      if (donViTinhId != null) 'donViTinh': donViTinhId.toString(),
      if (tiLeHoanThanh != null) 'tiLeHoanThanh': tiLeHoanThanh.toString(),
    });

    Logger().i('updateTaskAction URL: ${urls.toString()}');
    var response = await _client!.post(urls, headers: {
      'Content-Type': "application/json"
    }).catchError(handleHttpError);
    if (response.statusCode == 200) {
      Logger().i('updateAction: ${response.body.toJson()}');
    } else {
      print(response.body);
    }
    if (waiting || isLoadingShown.value) {
      hideLoading();
    }
  }

  // ------------------------- Chỉnh sửa hành động thực hiện. ------------------------
  Future<void> updateTaskAction({
    required int actionId,
    required String? moTa,
    required String? lyDoChuaHoanThanh,
    required int? khoiLuongCongViec,
    required int? tongSoGioThucHien,
    required int? tiLeHoanThanh,
    required DateTime? ngayKetThuc,
    required int? donViTinhId,
  }) async {
    final urls = Uri.parse(ApiPath.apiEditTaskAction).replace(queryParameters: {
      'taskActionId': actionId.toString(),
      if (moTa != null) 'moTa': moTa,
      if (lyDoChuaHoanThanh != null) 'lyDoChuaHoanThanh': lyDoChuaHoanThanh,
      if (khoiLuongCongViec != null)
        'khoiLuongCongViec': khoiLuongCongViec.toString(),
      if (tongSoGioThucHien != null)
        'tongSoGioThucHien': tongSoGioThucHien.toString(),
      if (tiLeHoanThanh != null) 'tiLeHoanThanh': tiLeHoanThanh.toString(),
      if (ngayKetThuc != null) 'ngayKetThuc': ngayKetThuc.toIso8601String(),
      if (donViTinhId != null) 'donViTinh': donViTinhId.toString(),
    });
    final response = await _client!.put(urls, headers: {
      'Content-Type': "application/json"
    }).catchError(handleHttpError);

    if (response.statusCode == 200) {
      Logger().i('updateTaskInfo: ${response.body.toJson()}');
    } else {
      print(response.body);
    }
  }

  // -------------------------- Xóa hành động thực hiện. -----------------------------
  Future<void> deleteTaskAction(int actionId) async {
    final urls =
        Uri.parse(ApiPath.apiDeleteTaskAction).replace(queryParameters: {
      'taskActionId': actionId.toString(),
    });
    final response = await _client!.delete(urls, headers: {
      'Content-Type': "application/json"
    }).catchError(handleHttpError);

    if (response.statusCode == 200) {
      Logger().i('updateTaskInfo: ${response.body.toJson()}');
    } else {
      print(response.body);
    }
  }

  // -------------------------- THAO TÁC VỚI TASK RESOURCE. --------------------------
  // ------------------------------ Thêm file đính kèm. ------------------------------
  Future<void> addTaskResource(
      {required int taskId, required File file, bool waiting = true}) async {
    if (waiting) {
      showLoading();
    }

    // final Uri urls =
    //     Uri.parse(ApiPath.apiAddTaskResources).replace(queryParameters: {
    //   'taskId': taskId.toString(),
    //   if (filePath != null && filePath.isNotEmpty) 'filePath': filePath,
    // });

    // Logger().i('updateTaskResource URL: ${urls.toString()}');
    // var response = await _client!.post(urls, headers: {
    //   'Content-Type': "application/json"
    // }).catchError(handleHttpError);

    final uri = Uri.parse(ApiPath.apiAddTaskResources);

    // Tạo yêu cầu multipart
    var request = http.MultipartRequest('POST', uri);

    // Thêm taskId vào form data
    request.fields['taskId'] = taskId.toString();

    // Thêm file vào yêu cầu
    request.files.add(
      await http.MultipartFile.fromPath(
        'file', // Tên trường trong form data, phải khớp với backend
        file.path,
        filename: file.path.split('/').last, // Lấy tên file gốc
      ),
    );

    // Gửi yêu cầu
    final response = await _client!.send(request).catchError(handleHttpError);

    if (response.statusCode == 200) {
      Logger().i('File uploaded successfully for TaskId: $taskId');
    } else {
      final responseBody = await response.stream.bytesToString();
      print(responseBody);
    }

    if (waiting || isLoadingShown.value) {
      hideLoading();
    }
  }

  // ------------------------------- Xóa file đính kèm. ------------------------------
  Future<void> deleteTaskResource(int resourceId) async {
    final urls =
        Uri.parse(ApiPath.apiDeleteTaskResources).replace(queryParameters: {
      'taskResourceId': resourceId.toString(),
    });
    final response = await _client!.delete(urls, headers: {
      'Content-Type': "application/json"
    }).catchError(handleHttpError);

    if (response.statusCode == 200) {
      Logger().i('updateTaskInfo: ${response.body.toJson()}');
    } else {
      print(response.body);
    }
  }

  // ---------------------------------- Thêm TASK. -----------------------------------
  Future<String?> createTask({
    required Map<String, dynamic> formValue,
    List<PlatformFile>? selectedFiles,
  }) async {
    try {
      // Tạo yêu cầu MultipartRequest
      var request = http.MultipartRequest(
        'POST',
        ApiPath.apiCreateTask.toUri(),
      );

      // Chỉ gửi jsonData như API mong đợi
      request.fields['jsonData'] = jsonEncode(formValue);

      // Thêm file nếu có
      if (selectedFiles != null && selectedFiles.isNotEmpty) {
        for (var file in selectedFiles) {
          if (file.bytes != null) {
            request.files.add(http.MultipartFile.fromBytes(
              'files', // Tên field phải khớp với API
              file.bytes!,
              filename: file.name,
            ));
          } else if (file.path != null) {
            request.files.add(await http.MultipartFile.fromPath(
              'files',
              file.path!,
              filename: file.name,
            ));
          }
        }
      }

      if (kDebugMode) {
        print("Fields: ${request.fields}");
        print("Files: ${request.files.map((f) => f.filename).toList()}");
      }

      // Gửi yêu cầu
      var response = await client?.send(request).timeout(
            const Duration(seconds: 30),
            onTimeout: () => throw TimeoutException("Request timed out"),
          );

      // Lấy phản hồi
      var responseBody = await response?.stream.bytesToString();
      if (response?.statusCode == 200 || response?.statusCode == 201) {
        if (kDebugMode) {
          print("Task created successfully: $responseBody");
        }
        return responseBody; // Trả về phản hồi nếu thành công
      } else {
        if (kDebugMode) {
          print(
              "Failed to create task: ${response?.statusCode} - $responseBody");
        }
        throw Exception(
            "Failed to create task: ${response?.statusCode} - $responseBody");
      }
    } catch (e) {
      if (kDebugMode) {
        print("Error creating task: $e");
      }
      throw Exception("Error creating task: $e");
    }
  }

  // ----------------------------------- Sửa TASK. -----------------------------------
  Future<void> updateTask({
    // required int taskId,
    required Map<String, dynamic> formValue,
  }) async {
    var request = http.MultipartRequest(
      'PUT',
      Uri.parse(ApiPath.apiUpdateTask),
    );
    request.fields['jsonData'] = jsonEncode(formValue);

    var response = await client!.send(request).timeout(
          const Duration(seconds: 30),
          onTimeout: () => throw TimeoutException("Request timed out"),
        );
    var responseBody = await response.stream.bytesToString();
    if (response.statusCode != 200) {
      throw Exception('Failed to update task: $responseBody');
    }
  }

  // ----------------------------------- Xóa TASK. -----------------------------------
  Future<void> deleteTask(int taskId) async {
    try {
      if (_client == null) {
        throw Exception('HTTP client is not initialized');
      }

      print('Deleting task with ID: $taskId');
      final Uri urls =
          Uri.parse(ApiPath.apiDeleteTask).replace(queryParameters: {
        'taskId': taskId.toString(),
      });

      var response = await _client!.delete(urls, headers: {
        'Content-Type': "application/json"
      }).catchError(handleHttpError);

      if (response.statusCode == 200) {
        Logger().i('updateTaskResource: ${response.body.toJson()}');
      } else {
        print(response.body);
      }
    } catch (e) {
      print('Error deleting task: $e');
      throw Exception('Failed to delete task: $e');
    }
  }

  // -------------- Lấy danh sách viên chức với phân trang và tìm kiếm. --------------
  Future<Map<String, dynamic>> loadVienChucs(
      {String search = "", int page = 1, int pageSize = 10}) async {
    //showLoading();
    final Uri urls = Uri.parse(ApiPath.apigetVienChucs).replace(
        queryParameters: search.isNotEmpty
            ? {
                'search': search,
                'page': page.toString(),
                'pageSize': pageSize.toString(),
              }
            : null);

    var response = await _client!.get(urls).catchError(handleError);
    if (response.statusCode == 200) {
      //print(response.body.toJson());
      //hideLoading();
      return response.body.toJson();
    }
    //hideLoading();
    return {};
  }

  // ---------------------- Lấy danh sách thành viên trong nhóm. ---------------------
  Future<List<dynamic>> getTeamMembers({bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }
    var _response = await _client!.get(
      ApiPath.apigetTeamMembers.toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      //Logger().i('getTaskStatistics: ${_response.body.toJson()}');
      if (waiting) {
        hideLoading();
      }
      return _response.body.toJson();
    } else {
      print(_response.body);
    }
    if (waiting) {
      hideLoading();
    }
    return [];
  }

  // ------------------------- HÀM LIÊN QUAN ĐẾN LƯƠNG VÀ THUẾ ------------------------ //

  // ------------------- Lấy thông tin lương tháng của viên chức. --------------------
  Future<dynamic> getLuongthang({int month = 0, int year = 0}) async {
    if (month == 0) month = DateTime.now().month;
    if (year == 0) year = DateTime.now().year;
    showLoading();
    Uri apit = ("${ApiPath.apiGetLuongthang}?month=$month&year=$year").toUri();
    var _response = await _client!.get(apit,
        headers: {'Content-Type': "application/json"}).catchError(handleError);

    if (_response.statusCode == 200) {
      hideLoading();
      //Logger().i(_response.body);
      return LuongThangInfo.fromJsonString(_response.body);
    } else {
      Logger().i(_response.body);
      //print(phieuDanhGia);
    }
    hideLoading();
    return null;
  }

  // -------------------- Lấy thông tin thuế tháng của viên chức. --------------------
  Future<dynamic> getThuethang({int month = 0, int year = 0}) async {
    if (month == 0) month = DateTime.now().month;
    if (year == 0) year = DateTime.now().year;
    showLoading();
    Uri apit = ("${ApiPath.apiGetThuethang}?month=$month&year=$year").toUri();
    var _response = await _client!.get(apit,
        headers: {'Content-Type': "application/json"}).catchError(handleError);

    if (_response.statusCode == 200) {
      hideLoading();
      //Logger().i(_response.body);
      return ThueThangInfo.fromJsonString(_response.body);
    } else {
      Logger().i(_response.body);
      //print(phieuDanhGia);
    }
    hideLoading();
    return null;
  }

  // --------------------- Lấy thông tin thuế năm của viên chức. ---------------------
  Future<dynamic> getThuenam({int year = 0}) async {
    if (year == 0) year = DateTime.now().year;
    showLoading();
    Uri apit = ("${ApiPath.apiGetThuenam}?year=$year").toUri();
    var _response = await _client!.get(apit,
        headers: {'Content-Type': "application/json"}).catchError(handleError);

    if (_response.statusCode == 200) {
      hideLoading();
      //Logger().i(_response.body);
      return ThueNamInfo.fromJsonString(_response.body);
    } else {
      Logger().i(_response.body);
      //print(phieuDanhGia);
    }
    hideLoading();
    return null;
  }

  // --------------------------- HÀM LIÊN QUAN ĐẾN TÀI LIỆU --------------------------- //

  // -------------- Lấy danh sách tài liệu với phân trang và tìm kiếm. ---------------
  Future<Map<String, dynamic>> fetchDocuments(
      {String? searchQuery,
      int page = 1,
      int pageSize = 10,
      bool waiting = false}) async {
    try {
      final Uri uri = Uri.parse(ApiPath.apiGetDocuments).replace(
        queryParameters: searchQuery != null
            ? {
                'search': searchQuery,
                'page': page.toString(),
                'pageSize': pageSize.toString()
              }
            : null,
      );
      var _response = await _client!.get(
        uri,
        headers: {'Content-Type': "application/json"},
      ).catchError(handleError);
      if (_response.statusCode == 200) {
        if (kDebugMode) {
          print(_response.body.toJson());
        }
        return _response.body.toJson();
      } else {
        //print(_response.body);
        return {};
      }
    } catch (e) {
      return {};
    }
  }

  // -------------- Lấy danh sách tài liệu với ID. ---------------
  Future<Map<String, dynamic>> fetchDocumentById(int documentId) async {
    try {
      final Uri uri =
          Uri.parse(ApiPath.apiGetDocumentById).replace(queryParameters: {
        'docId': documentId.toString(),
      });
      var _response = await _client!.get(
        uri,
        headers: {'Content-Type': "application/json"},
      ).catchError(handleError);

      if (_response.statusCode == 200) {
        final data =
            jsonDecode(_response.body) as List; // Parse thành danh sách
        if (data.isNotEmpty) {
          return data[0] as Map<String, dynamic>; // Lấy phần tử đầu tiên
        }
        return {}; // Trả về null nếu danh sách rỗng
      } else {
        //print(_response.body);
        return {};
      }
    } catch (e) {
      return {};
    }
  }

  // ------------------------- HÀM LIÊN QUAN ĐẾN CHỮ KÝ SỐ ----------------------------- //

  // upload base64 image as json to api server
  //{
  //   "imageBase64": "string",
  //   "imageType": "string",
  //   "fieldType": "string"
  // }
  // ---------------------- Tải lên chữ ký số dưới dạng base64. ----------------------
  Future<void> uplaodChukybyJson(
      String imageBase64, String imageType, String fieldType) async {
    showLoading();
    var _response = await _client!
        .post(
          ApiPath.apiUploadImageGiangvien.toUri(),
          headers: {'Content-Type': "application/json"},
          body: jsonEncode({
            "ImageBase64": imageBase64,
            "ImageType": imageType,
            "FieldType": fieldType
          }),
        )
        .catchError(handleError);
    //Logger().i(_response.body);
    if (_response.statusCode == 200) {
      hideLoading();
      Logger().i(_response.body);
    } else {
      Logger().i(_response.body);
      hideLoading();
      throw Exception(_response.body);
    }
    hideLoading();
  }

  // -------------------- Lấy thông tin chữ ký số của giảng viên. --------------------
  Future<dynamic> loadChuky({bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }
    var _response = await _client!.get(
      ApiPath.apiGetImageChuKyGiangvien.toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      //Logger().i('getTaskStatistics: ${_response.body.toJson()}');
      if (waiting) {
        hideLoading();
      }
      return _response.body.toJson();
    } else {
      print(_response.body);
    }
    if (waiting) {
      hideLoading();
    }
    return [];
  }

  // ------------------- Lấy danh sách file chờ ký của giảng viên. -------------------
  Future<dynamic> loadDSChoky({bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }
    var url = "${ApiPath.apiGetDSFileChoKyGiangvien}?pageSize=5&page=1";
    var _response = await _client!.get(
      url.toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      Logger().i('getTaskStatistics: ${_response.body.toJson()}');
      if (waiting) {
        hideLoading();
      }
      return _response.body.toJson();
    } else {
      print(_response.body);
    }
    if (waiting) {
      hideLoading();
    }
    return [];
  }

  // ------------------- Lấy danh sách file đã ký của giảng viên. --------------------
  Future<dynamic> loadDSDaky({bool waiting = false}) async {
    if (waiting) {
      showLoading();
    }
    var url = "${ApiPath.apiGetDSFileDaKyGiangvien}?pageSize=5&page=1";
    var _response = await _client!.get(
      url.toUri(),
      headers: {'Content-Type': "application/json"},
    ).catchError(handleError);
    if (_response.statusCode == 200) {
      Logger().i('getTaskStatistics: ${_response.body.toJson()}');
      if (waiting) {
        hideLoading();
      }
      return _response.body.toJson();
    } else {
      print(_response.body);
    }
    if (waiting) {
      hideLoading();
    }
    return [];
  }
}

// EXTENSION
// Tạo một bản sao của Map<String, String> và thêm các giá trị mới từ một map khác.
extension mapCopy on Map<String, String> {
  Map<String, String> copyWith(Map<String, String> map) {
    return Map.from(this)..addAll(map);
  }
}
