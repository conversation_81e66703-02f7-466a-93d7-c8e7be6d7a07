import 'dart:convert';

class Credentials {
  Credentials({
    required this.idToken,
    required this.accessToken,
    required this.refreshToken,
    required this.expiresIn,
    required this.tokenType,
    required this.scope,
  });

  final String? idToken;
  final String? accessToken;
  final int? expiresIn;
  final String? tokenType;
  final List<String>? scope;
  final String? refreshToken;

  Credentials copyWith({
    String? idToken,
    String? accessToken,
    int? expiresIn,
    String? tokenType,
    List<String>? scope,
    String? refreshToken
  }) {
    return Credentials(
      idToken: idToken ?? this.idToken,
      accessToken: accessToken ?? this.accessToken,
      expiresIn: expiresIn ?? this.expiresIn,
      tokenType: tokenType ?? this.tokenType,
      scope: scope ?? this.scope,
      refreshToken: refreshToken ?? this.refreshToken,
    );
  }

  factory Credentials.fromJson(Map<String, dynamic> json){
    return Credentials(
      idToken: json["id_token"],
      accessToken: json["access_token"],
      expiresIn: json["expires_in"],
      tokenType: json["token_type"],
      scope: json["scope"] == null ? [] : List<String>.from(json["scope"]!.map((x) => x)),
      refreshToken: json["refresh_token"]
    );
  }

  Map<String, dynamic> toJson() => {
    "id_token": idToken,
    "access_token": accessToken,
    "expires_in": expiresIn,
    "token_type": tokenType,
    "scope": scope?.map((x) => x).toList(),
    "refresh_token": refreshToken,
  };

  @override
  String toString(){
    return "$idToken, $accessToken, $expiresIn, $tokenType, $scope, ";
  }

  String toJsonString() => json.encode(toJson());
  factory Credentials.fromJsonString(String source) => Credentials.fromJson(json.decode(source));

  bool get isExpired {
    var expiration = DateTime.fromMillisecondsSinceEpoch(expiresIn!, isUtc: true);
    return DateTime.now().isAfter(expiration);
  }
}
