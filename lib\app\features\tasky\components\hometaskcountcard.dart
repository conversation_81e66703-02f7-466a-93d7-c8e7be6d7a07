import 'package:flutter/material.dart';
import 'package:tvumobile/app/features/projects/shared/progress_card.dart';

class HomeTaskCountCard extends StatelessWidget {
  const HomeTaskCountCard({
    super.key,
    required this.size,
    required this.desc,
    required this.count,
    required this.image,
    this.color,
  });

  final Size size;
  final String desc;
  final int? count;
  final String image;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return ProgressCard(
      data: const ProgressCardData(
        totalUndone: 10,
        totalTaskInProress: 2,
      ),
      onPressedCheck: () {},
    );
  }

}