import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';
import 'package:tvumobile/app/data/models/edusoft/tkb_edusoft_model.dart';
import 'package:tvumobile/app/data/models/sch/tkb/tbk_datasource.dart';
import 'package:tvumobile/app/data/models/sch/tkb/tkb_tuan_giangvien.dart';
import 'package:tvumobile/app/services/api_provider.dart';

class TkbSinhVienController extends GetxController {
  bool uiLoading = true;

  TkbGVDataSource tkbGVDataSource = TkbGVDataSource([]);
  ApiProvider apiProvider = Get.find();
  final CalendarController calendarController = CalendarController();
  final viewStyleselection = 0.obs;
  late DateTime minDate, maxDate;

  @override
  Future<void> onInit() async {
    super.onInit();
    calendarController.selectedDate = DateTime.now();
  }

  void onViewStyleChange(index) {
    viewStyleselection.value = index;
    update();
  }
  @override
  Future<void> onReady() async {
    await getTKBFunctions();
  }

  void onViewChanged(ViewChangedDetails visibleDatesChangedDetails) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      final DateTime currentViewDate = visibleDatesChangedDetails
          .visibleDates[visibleDatesChangedDetails.visibleDates.length ~/ 2];

      if (currentViewDate.month == DateTime.now().month &&
          currentViewDate.year == DateTime.now().year) {
        calendarController.selectedDate = DateTime.now();
      } else {
        calendarController.selectedDate =
            DateTime(currentViewDate.year, currentViewDate.month, 01);
      }
    });
  }
  final List<TkbTuanGiangVien> tkbs = [];

  getTKBFunctions() async {
    List<TkbEdusoftModel>? response = await apiProvider.loadTkbSinhVien();
    if (kDebugMode) {
      print(response);
    }
    tkbs.clear();
    tkbs.addAll(_getDataSource(response));
    tkbGVDataSource = TkbGVDataSource(tkbs);
    uiLoading = false;
    //minDate = tkbs[0].ketThuc!;
    //maxDate = tkbs[tkbs.length - 1].ketThuc!;
    update();
    }

  List<TkbTuanGiangVien> _getDataSource(List<TkbEdusoftModel> tkbGiangVien) {
    ThemeData theme = Get.theme;
    List<TkbTuanGiangVien> lists = [];
    for (var tkbtuan in tkbGiangVien) {
      for (var tkb in tkbtuan.dsThoiKhoaBieu) {
        TkbTuanGiangVien tkbTuanGiangVien = TkbTuanGiangVien();
        //DateTime ngayday = tkb.ngayHoc;
        tkbTuanGiangVien.tenMon = tkb.tenMon;
        tkbTuanGiangVien.batDau = getTietBD(tkb.ngayHoc!, tkb.tietBatDau!);
        tkbTuanGiangVien.ketThuc = getTietBD(tkb.ngayHoc!, tkb.tietBatDau! + tkb.soTiet!);
        tkbTuanGiangVien.tenPhong = tkb.maPhong;
        tkbTuanGiangVien.maLop = tkb.maLop;
        tkbTuanGiangVien.background = theme.primaryColor.withOpacity(0.3);
        tkbTuanGiangVien.maMon = tkb.maMon;
        if(tkb.ngayHoc!.day == 11 && tkb.ngayHoc!.month == 3 && tkb.ngayHoc!.year == 2024) {
          if (kDebugMode) {
            print(tkb.tenMon.toString());
            print(tkb.idTkb.toString());
          }          
        }
        tkbTuanGiangVien.nhomHoc = tkb.maNhom;
        tkbTuanGiangVien.nhomTH = tkb.maToTh;
        tkbTuanGiangVien.soTiet = tkb.soTiet.toString();
        lists.add(tkbTuanGiangVien);
      }
    }return lists;
  }

  DateTime getTietBD(DateTime ngay, num tietbd) {
    switch(tietbd.toInt()) {
      case 1:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 7, 0, 0, 0, 0);
        break;
      case 2:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 7, 45, 0, 0, 0);
        break;
      case 3:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 9, 00, 0, 0, 0);
        break;
      case 4:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 9, 45, 0, 0, 0);
        break;
      case 5:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 10, 30, 0, 0, 0);
        break;
      case 6:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 13, 0, 0, 0, 0);
        break;
      case 7:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 13, 45, 0, 0, 0);
        break;
      case 8:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 15, 00, 0, 0, 0);
        break;
      case 9:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 15, 45, 0, 0, 0);
        break;
      case 10:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 16, 30, 0, 0, 0);
        break;
      case 11:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 18, 00, 0, 0, 0);
        break;
      case 12:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 18, 45, 0, 0, 0);
        break;
      case 13:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 19, 30, 0, 0, 0);
        break;
      case 14:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 20, 30, 0, 0, 0);
        break;
      case 15:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 21, 20, 0, 0, 0);
        break;
      case 16:
        ngay = DateTime(ngay.year, ngay.month, ngay.day, 22, 10, 0, 0, 0);
        break;
    }
    return ngay;
  }

  void goBack() {
    Get.back();
    // Navigator.pop(context);
  }
}