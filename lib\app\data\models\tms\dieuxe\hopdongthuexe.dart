import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepinfo.dart';

class HopDongThueXe {
  HopDongThueXe({
    this.id,
    this.donVi,
    this.donViChoThueXe,
    this.loaiXe,
    this.tuNgay,
    this.denNgay,
    this.noiDung,
    this.giaTriHopDong,
    this.bienSo,
    this.ghiChu,
  });

  final int? id;
  final DonVi? donVi;
  final String? donViChoThueXe;
  final String? loaiXe;
  final DateTime? tuNgay;
  final DateTime? denNgay;
  final String? noiDung;
  final int? giaTriHopDong;
  final String? bienSo;
  final String? ghiChu;

  HopDongThueXe copyWith({
    int? id,
    DonVi? donVi,
    String? donViChoThueXe,
    String? loaiXe,
    DateTime? tuNgay,
    DateTime? denNgay,
    String? noiDung,
    int? giaTriHopDong,
    String? bienSo,
    String? ghiChu,
  }) {
    return HopDongThueXe(
      id: id ?? this.id,
      donVi: donVi ?? this.donVi,
      donViChoThueXe: donViChoThueXe ?? this.donViChoThueXe,
      loaiXe: loaiXe ?? this.loaiXe,
      tuNgay: tuNgay ?? this.tuNgay,
      denNgay: denNgay ?? this.denNgay,
      noiDung: noiDung ?? this.noiDung,
      giaTriHopDong: giaTriHopDong ?? this.giaTriHopDong,
      bienSo: bienSo ?? this.bienSo,
      ghiChu: ghiChu ?? this.ghiChu,
    );
  }

  factory HopDongThueXe.fromJson(Map<String, dynamic> json) {
    return HopDongThueXe(
      id: json["id"],
      donVi: json["donVi"] == null ? null : DonVi.fromJson(json["donVi"]),
      donViChoThueXe: json["donViChoThueXe"],
      loaiXe: json["loaiXe"],
      tuNgay: DateTime.tryParse(json["tuNgay"] ?? ""),
      denNgay: DateTime.tryParse(json["denNgay"] ?? ""),
      noiDung: json["noiDung"],
      giaTriHopDong: json["giaTriHopDong"],
      bienSo: json["bienSo"],
      ghiChu: json["ghiChu"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "donVi": donVi?.toJson(),
        "donViChoThueXe": donViChoThueXe,
        "loaiXe": loaiXe,
        "tuNgay": tuNgay?.toIso8601String(),
        "denNgay": denNgay?.toIso8601String(),
        "noiDung": noiDung,
        "giaTriHopDong": giaTriHopDong,
        "bienSo": bienSo,
        "ghiChu": ghiChu,
      };

  static List<HopDongThueXe> parse(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed
        .map<HopDongThueXe>((json) => HopDongThueXe.fromJson(json))
        .toList();
  }

  @override
  String toString() {
    return "$id, $donVi, $donViChoThueXe, $loaiXe, $tuNgay, $denNgay, $noiDung, $giaTriHopDong, $bienSo, $ghiChu, ";
  }
}
