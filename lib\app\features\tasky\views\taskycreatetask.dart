import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/features/tasky/components/assignee_tile_widget.dart';
import 'package:tvumobile/app/features/tasky/components/select_assignees_widget.dart';
import 'package:tvumobile/app/features/tasky/controllers/taskycreate_controller.dart';
import 'package:tvumobile/app/shared_components/appbar.dart';
import 'package:tvumobile/app/features/tasky/components/file_upload_widget.dart';
import 'package:tvumobile/app/shared_components/form_builder_segmented_control.dart';
import 'package:tvumobile/app/shared_components/input_decoration_theme.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/features/tasky/components/related_documents_picker.dart';
import 'package:tvumobile/app/utils/helpers/app_helpers.dart';

class TaskyCreateTask extends GetView<TaskyCreateController> {
  const TaskyCreateTask({super.key});

  @override
  Widget build(BuildContext context) {
    final myDecoration = const InputDecoration().applyDefaults(
      GlobalInputDecoration(context),
    );
    final theme = Theme.of(context);
    final width = MediaQuery.of(context).size.width;
    // return Scaffold(
    //   appBar: appBarTitle(context, 'Tạo mới nhiệm vụ/Công việc'),
    //   body: GetBuilder<TaskyCreateController>(
    //     builder: (TaskyCreateController controller) {
    //       return Column(
    //         children: [
    //           Expanded(
    //               child: Padding(
    //                   padding: const EdgeInsets.only(
    //                       left: 16.0, right: 16.0, bottom: 16.0, top: 4),
    //                   child: FormBuilder(
    //                     key: controller.formKey,
    //                     child: ListView(
    //                       shrinkWrap: true,
    //                       children: [
    //                         Align(
    //                           alignment: Alignment.center,
    //                           child: Padding(
    //                             padding: EdgeInsets.all(8.0),
    //                             child: MyText.bodyMedium(
    //                               "Hãy cung cấp thông tin cho việc cần tạo",
    //                               decoration: TextDecoration.underline,
    //                               color: theme.colorScheme.onSurface,
    //                               fontSize: 15,
    //                               fontWeight: 700,
    //                             ),
    //                           ),
    //                         ),

    //                         // ---------------------------------- TÊN CÔNG VIỆC -----------------------------------
    //                         // ------------------------------------------------------------------------------------
    //                         MyText.bodyMedium(
    //                           'Tên gợi nhớ?',
    //                           fontWeight: 700,
    //                         ),
    //                         Container(
    //                           decoration: BoxDecoration(
    //                             image: DecorationImage(
    //                                 alignment: Alignment.bottomRight,
    //                                 fit: BoxFit.fitHeight,
    //                                 image: AssetImage(
    //                                   "assets/images/training.png",
    //                                 ),
    //                                 opacity: 0.01),
    //                           ),
    //                           child: FormBuilderTextField(
    //                             name: 'TaskName',
    //                             validator: FormBuilderValidators.required(
    //                                 errorText: 'Vui lòng nhập Tên Công việc'),
    //                             decoration: InputDecoration(
    //                                 hintText:
    //                                     'Nhập một tên ngắn gọn, dễ dàng liên tưởng đến công việc',
    //                                 hintStyle: TextStyle(
    //                                     color: theme.colorScheme.onSurface
    //                                         .withOpacity(0.3))),
    //                             initialValue: controller.formData['TaskName'],
    //                             maxLines: 2,
    //                             onChanged: (value) {
    //                               controller.formData['TaskName'] = value!;
    //                             },
    //                             onSaved: (newValue) {
    //                               controller.formData['TaskName'] =
    //                                   newValue ?? '';
    //                             },
    //                             style: theme.textTheme.labelLarge,
    //                             //maxLength: 200,
    //                           ),
    //                         ),
    //                         MySpacing.height(14),

    //                         // -------------------------------- MÔ TẢ CÔNG VIỆC -----------------------------------
    //                         // ------------------------------------------------------------------------------------
    //                         MyText.bodyMedium(
    //                           'Công việc là gì?',
    //                           fontWeight: 700,
    //                         ),
    //                         FormBuilderTextField(
    //                           name: 'MoTa',
    //                           validator: FormBuilderValidators.required(
    //                               errorText:
    //                                   'Vui lòng nhập Nội dung công việc'),
    //                           decoration: InputDecoration(
    //                               hintText:
    //                                   'Mô tả công việc cụ thể cần thực hiện',
    //                               hintStyle: TextStyle(
    //                                   color: theme.colorScheme.onSurface
    //                                       .withOpacity(0.3))),
    //                           initialValue: controller.formData['MoTa'],
    //                           maxLines: 2,
    //                           onChanged: (value) {
    //                             controller.formData['MoTa'] = value!;
    //                           },
    //                           onSaved: (newValue) {
    //                             controller.formData['MoTa'] = newValue ?? '';
    //                           },
    //                           style: theme.textTheme.labelLarge,
    //                           //maxLength: 255,
    //                         ),
    //                         MySpacing.height(14),

    //                         // ----------------------------- PHÂN LOẠI CÔNG VIỆC ----------------------------------
    //                         // ------------------------------------------------------------------------------------
    //                         MyText.bodyMedium(
    //                           'Phân loại của công việc?',
    //                           fontWeight: 700,
    //                         ),
    //                         FormBuilderDropdown<int>(
    //                           name: 'LoaiCongViec',
    //                           decoration: InputDecoration(
    //                               hintText: 'Chọn phân loại để dễ dàng quản lý',
    //                               hintStyle: TextStyle(
    //                                   color: theme.colorScheme.onSurface
    //                                       .withOpacity(0.3))),
    //                           initialValue: controller.formData['LoaiCongViec'],
    //                           validator: FormBuilderValidators.compose(
    //                               [FormBuilderValidators.required()]),
    //                           items: controller.dmloaiCongviec
    //                               .map((ele) => DropdownMenuItem(
    //                                     alignment:
    //                                         AlignmentDirectional.centerStart,
    //                                     value: ele.id,
    //                                     child: Text(ele.tenDanhMuc.toString()),
    //                                   ))
    //                               .toList(),
    //                           onChanged: (value) {
    //                             controller.formData['LoaiCongViec'] = value!;
    //                           },
    //                           onSaved: (newValue) {
    //                             controller.formData['LoaiCongViec'] = newValue!;
    //                           },
    //                           style: theme.textTheme.labelLarge,
    //                         ),
    //                         MySpacing.height(14),

    //                         // ---------------------------- THỜI GIAN THỰC HIỆN -----------------------------------
    //                         // ------------------------------------------------------------------------------------
    //                         MyText.bodyMedium(
    //                           'Trong khoảng thời gian nào?',
    //                           fontWeight: 700,
    //                         ),
    //                         FormBuilderDateRangePicker(
    //                           name: 'startEndDay',
    //                           firstDate: DateTime(DateTime.now().year - 1),
    //                           lastDate: DateTime(DateTime.now().year + 1),
    //                           format: DateFormat('dd/MM/yyyy'),
    //                           initialValue: controller.formData["startEndDay"],
    //                           onChanged: (value) {
    //                             if (value != null) {
    //                               controller.formData["startEndDay"] = value;
    //                             }
    //                           },
    //                           onSaved: (newValue) {
    //                             controller.formData["startEndDay"] = newValue;
    //                           },
    //                           decoration: InputDecoration(
    //                             contentPadding:
    //                                 const EdgeInsets.fromLTRB(4, 12, 4, 1),
    //                             hintText: 'Thời gian hoàn thành dự kiến',
    //                             suffixIcon: IconButton(
    //                               padding: const EdgeInsets.all(0),
    //                               icon: const Icon(Icons.close),
    //                               onPressed: () {
    //                                 controller.formKey.currentState!
    //                                     .fields['startEndDay']
    //                                     ?.didChange(null);
    //                               },
    //                             ),
    //                           ),
    //                           locale: const Locale('vi'),
    //                           cancelText: "Hủy bỏ",
    //                           confirmText: "Xác nhận",
    //                           saveText: "Lưu",
    //                           fieldStartLabelText: "Ngày bắt đầu",
    //                           fieldEndLabelText: "Ngày kết thúc",
    //                           fieldStartHintText: "Ngày bắt đầu",
    //                           fieldEndHintText: "Ngày kết thúc",
    //                           startValuePrefix: "Bắt đầu: ",
    //                           endValuePrefix: "Kết thúc: ",
    //                           //style: theme.textTheme.labelLarge,
    //                         ),
    //                         MySpacing.height(14),
    //                         //BO DI NGUOI TAO
    //                         /*
    //                           MyText.bodyMedium(
    //                             'Ai tạo?',
    //                             fontWeight: 700,
    //                           ),
    //                           FormBuilderDropdown<UserInfo>(
    //                             name: 'NguoiTao',
    //                             decoration: InputDecoration(
    //                                 hintText: 'Người tạo',
    //                                 hintStyle: TextStyle(
    //                                     color: theme.colorScheme.onSurface
    //                                         .withOpacity(0.3))),
    //                             initialValue: controller.userInfo,
    //                             validator: FormBuilderValidators.compose(
    //                                 [FormBuilderValidators.required()]),
    //                             items: [controller.userInfo!]
    //                                 .map((ele) => DropdownMenuItem(
    //                                       alignment:
    //                                           AlignmentDirectional.center,
    //                                       value: ele,
    //                                       child: Row(
    //                                         children: [
    //                                           CircleAvatar(
    //                                               radius: 15,
    //                                               backgroundImage: NetworkImage(
    //                                                   ele.getAvatar())),
    //                                           MySpacing.width(8),
    //                                           Text("${ele.fullname}"),
    //                                         ],
    //                                       ),
    //                                     ))
    //                                 .toList(),
    //                             onChanged: (value) {
    //                               controller.formData['NguoiTao'] = value!;
    //                             },
    //                             onSaved: (newValue) {
    //                               controller.formData['NguoiTao'] = newValue!;
    //                             },
    //                             style: theme.textTheme.labelLarge,
    //                           ),
    //                           */
    //                         MySpacing.height(14),

    //                         // -------------------------------- NGƯỜI GIAO VIỆC -----------------------------------
    //                         // ------------------------------------------------------------------------------------
    //                         MyText.bodyMedium(
    //                           'Ai giao việc?',
    //                           fontWeight: 700,
    //                         ),
    //                         controller.teamMembers.isEmpty
    //                             ? Padding(
    //                                 padding: const EdgeInsets.all(8.0),
    //                                 child: SkeletonLine(),
    //                               )
    //                             : FormBuilderDropdown<dynamic>(
    //                                 name: 'NguoiGiaoViec',
    //                                 initialValue:
    //                                     controller.formData['NguoiGiaoViec'],
    //                                 decoration: InputDecoration(
    //                                     hintText: 'Người giao việc',
    //                                     hintStyle: TextStyle(
    //                                         color: theme.colorScheme.onSurface
    //                                             .withOpacity(0.3))),
    //                                 validator: FormBuilderValidators.compose(
    //                                   [
    //                                     FormBuilderValidators.required(
    //                                         errorText:
    //                                             'Vui lòng chọn người giao việc')
    //                                   ],
    //                                 ),
    //                                 items: controller.teamMembers
    //                                     .map((ele) => DropdownMenuItem(
    //                                           alignment:
    //                                               AlignmentDirectional.center,
    //                                           value: ele,
    //                                           child: Row(
    //                                             children: [
    //                                               CircleAvatar(
    //                                                   radius: 15,
    //                                                   backgroundImage:
    //                                                       NetworkImage(
    //                                                           ele["hinhAnh"]
    //                                                               .toString())),
    //                                               MySpacing.width(8),
    //                                               Text(
    //                                                   "${getHoTen(ele)} (${ele["tenChucVu"] == "Không chức vụ" ? "Viên chức" : ele["tenChucVu"]})"),
    //                                             ],
    //                                           ),
    //                                         ))
    //                                     .toList(),
    //                                 onChanged: (value) {
    //                                   controller.formData['NguoiGiaoViec'] =
    //                                       value!;
    //                                 },
    //                                 onSaved: (newValue) {
    //                                   controller.formData['NguoiGiaoViec'] =
    //                                       newValue!;
    //                                 },
    //                                 style: theme.textTheme.labelLarge,
    //                               ),
    //                         MySpacing.height(14),
    //                         SelectAssignees(
    //                           initialUsers: [],
    //                           onSearch: controller.searchUsers,
    //                           onChanged: controller.onAssigneesChanged,
    //                           //maxSelections: 10,
    //                           availableUsers: controller
    //                               .teamMembers, // Tùy chọn giới hạn số lượng người dùng có thể chọn
    //                         ),
    //                         /* MyText.bodyMedium(
    //                             'Ai thực hiện?',
    //                             fontWeight: 700,
    //                           ),
    //                           MySpacing.height(4),
    //                           SizedBox(
    //                             child: SingleChildScrollView(
    //                               scrollDirection: Axis.horizontal,
    //                               child: Row(
    //                                 children: [
    //                                   if (controller.teamMembers.isNotEmpty)
    //                                     GestureDetector(
    //                                       onTap: () async {
    //                                         if (Platform.isIOS) {
    //                                           showCupertinoModalBottomSheet(
    //                                             context: context,
    //                                             builder: (context) {
    //                                               return SelectAssignee(
    //                                                   context);
    //                                             },
    //                                           ).then((_) => {});
    //                                         } else {
    //                                           showMaterialModalBottomSheet(
    //                                             context: context,
    //                                             builder: (context) {
    //                                               return SelectAssignee(
    //                                                   context);
    //                                             },
    //                                           ).then((_) => {});
    //                                         }
    //                                       },
    //                                       child: Align(
    //                                         alignment: Alignment.centerLeft,
    //                                         child: DottedBorder(
    //                                           borderType: BorderType.Circle,
    //                                           radius: const Radius.circular(6),
    //                                           dashPattern: const [6, 3, 6, 3],
    //                                           child: ClipRRect(
    //                                             borderRadius:
    //                                                 const BorderRadius.all(
    //                                                     Radius.circular(45)),
    //                                             child: SizedBox(
    //                                               height: 40,
    //                                               width: 40,
    //                                               child: const Center(
    //                                                   child: Icon(
    //                                                 Icons.person_add,
    //                                               )),
    //                                             ),
    //                                           ),
    //                                         ),
    //                                       ),
    //                                     ),
    //                                   MySpacing.width(4),
    //                                   if (controller.assignees.isNotEmpty)
    //                                     Align(
    //                                       alignment: Alignment.centerLeft,
    //                                       child: SizedBox(
    //                                         height: 46,
    //                                         child: ListView(
    //                                             shrinkWrap: true,
    //                                             scrollDirection:
    //                                                 Axis.horizontal,
    //                                             children: [
    //                                               Wrap(
    //                                                 spacing: 4.0,
    //                                                 runSpacing: 0.0,
    //                                                 children: controller
    //                                                     .assignees
    //                                                     .map((ele) {
    //                                                   return CircularProfileAvatar(
    //                                                       ele["hinhAnh"],
    //                                                       initialsText: Text(
    //                                                         ele["ten"],
    //                                                         style:
    //                                                             const TextStyle(
    //                                                                 fontSize:
    //                                                                     20,
    //                                                                 color: Colors
    //                                                                     .white),
    //                                                       ),
    //                                                       imageFit:
    //                                                           BoxFit.cover,
    //                                                       radius: 23,
    //                                                       elevation: 2,
    //                                                       onTap: () async {
    //                                                     if (Platform.isIOS) {
    //                                                       showCupertinoModalBottomSheet(
    //                                                         context: context,
    //                                                         builder: (context) {
    //                                                           return SelectAssignee(
    //                                                               context);
    //                                                         },
    //                                                       ).then((_) => {});
    //                                                     } else {
    //                                                       showMaterialModalBottomSheet(
    //                                                         context: context,
    //                                                         builder: (context) {
    //                                                           return SelectAssignee(
    //                                                               context);
    //                                                         },
    //                                                       ).then((_) => {});
    //                                                     }
    //                                                     // Logger().i(ele);
    //                                                   });
    //                                                 }).toList(),
    //                                               ),
    //                                             ]),
    //                                       ),
    //                                     ),
    //                                 ],
    //                               ),
    //                             ),
    //                           ),*/
    //                         MySpacing.height(18),

    //                         // ---------------------------------- FILE ĐÍNH KÈM -----------------------------------
    //                         // ------------------------------------------------------------------------------------
    //                         FileUploadWidget(
    //                           key: controller.uploadWidgetKey,
    //                           onFilesSelected: (files) {
    //                             // Xử lý files được chọn
    //                           },
    //                           onUploadProgress: (value) {},
    //                           theme: FileUploadTheme(
    //                             primaryColor:
    //                                 theme.colorScheme.primaryContainer,
    //                             backgroundColor: theme.scaffoldBackgroundColor,
    //                             borderColor: theme.colorScheme.secondary,
    //                             textColor: theme.colorScheme.onPrimary,
    //                             errorColor: theme.colorScheme.error,
    //                             borderRadius: 4.0,
    //                             contentPadding: EdgeInsets.fromLTRB(0, 0, 0, 0),
    //                             titleStyle: theme.textTheme.labelLarge!,
    //                             subtitleStyle: theme.textTheme.labelMedium!,
    //                           ),
    //                           //buttonText: 'Tải file lên',
    //                           maxFileSizeMB: 10,
    //                           allowMultiple: true,
    //                           allowedExtensions: [
    //                             'jpg',
    //                             'jpeg',
    //                             'png',
    //                             'pdf',
    //                             'doc'
    //                           ],
    //                         ),
    //                         // ElevatedButton(
    //                         //   onPressed: () async {
    //                         //     final state = controller.uploadWidgetKey.currentState;
    //                         //     if (state?.hasFiles ?? false) {
    //                         //       try {
    //                         //         // Your actual upload logic here
    //                         //         for (var i = 0; i <= 100; i++) {
    //                         //           await Future.delayed(Duration(milliseconds: 50));
    //                         //           state?.updateProgress(i / 100);
    //                         //         }
    //                         //         print(state?.selectedFiles);
    //                         //       } catch (e) {
    //                         //         state?.setError('Upload failed: $e');
    //                         //       }
    //                         //     }
    //                         //   },
    //                         //   child: Text('Start Upload'),
    //                         // ),
    //                         MySpacing.height(18),

    //                         // --------------------------------- VĂN BẢN LIÊN QUAN --------------------------------
    //                         // ------------------------------------------------------------------------------------
    //                         // RelatedDocumentsPicker(
    //                         //   onSearch: controller.fetchDocuments,
    //                         //   //attachTitleText: 'Đính kèm văn bản liên quan?',
    //                         //   //buttonText: 'Chọn văn bản liên quan',
    //                         //   //onDocumentsSelected: (docs) => print('Selected: ${docs.length} documents'),
    //                         //   // onSearch: (String query, int pageNumber, int pageSize) async {
    //                         //   //   // Implement your custom fetching logic here
    //                         //   //   var data = await controller.fetchDocuments(pageNumber, pageSize, query);
    //                         //   //   return data;
    //                         //   // },
    //                         //   onChanged: (List<dynamic> docs) {},
    //                         //   initialDocuments: [],
    //                         //   availableDocuments: [],
    //                         // ),
    //                         // MySpacing.height(18),

    //                         // --------------------------------- NGƯỜI GIÁM SÁT -----------------------------------
    //                         // ------------------------------------------------------------------------------------
    //                         MyText.bodyMedium(
    //                           'Có ai giám sát không?',
    //                           fontWeight: 700,
    //                         ),
    //                         controller.teamMembers.isEmpty
    //                             ? Padding(
    //                                 padding: const EdgeInsets.all(8.0),
    //                                 child: SkeletonLine(),
    //                               )
    //                             : FormBuilderDropdown<dynamic>(
    //                                 name: 'NguoiGiamSat',
    //                                 decoration: InputDecoration(
    //                                   hintText: 'Người giám sát',
    //                                   hintStyle: TextStyle(
    //                                       color: theme.colorScheme.onSurface
    //                                           .withOpacity(0.3)),
    //                                   suffixIcon: IconButton(
    //                                     padding: const EdgeInsets.all(0),
    //                                     icon: const Icon(Icons.close),
    //                                     onPressed: () {
    //                                       if (controller.formKey.currentState!
    //                                               .fields['NguoiGiamSat'] !=
    //                                           null) {
    //                                         controller.formKey.currentState!
    //                                             .fields['NguoiGiamSat']
    //                                             ?.didChange(null);
    //                                       }
    //                                     },
    //                                   ),
    //                                 ),
    //                                 initialValue:
    //                                     controller.formData['NguoiGiamSat'],
    //                                 //validator: FormBuilderValidators.compose([FormBuilderValidators.required()]),
    //                                 items: controller.teamMembers
    //                                     .map((ele) => DropdownMenuItem(
    //                                           alignment:
    //                                               AlignmentDirectional.center,
    //                                           value: ele,
    //                                           child: Row(
    //                                             children: [
    //                                               CircleAvatar(
    //                                                   radius: 15,
    //                                                   backgroundImage:
    //                                                       NetworkImage(
    //                                                           ele["hinhAnh"]
    //                                                               .toString())),
    //                                               MySpacing.width(8),
    //                                               Text(
    //                                                   "${getHoTen(ele)} (${ele["tenChucVu"] == "Không chức vụ" ? "Viên chức" : ele["tenChucVu"]})"),
    //                                             ],
    //                                           ),
    //                                         ))
    //                                     .toList(),
    //                                 onChanged: (value) {
    //                                   controller.formData['NguoiGiamSat'] =
    //                                       value;
    //                                 },
    //                                 onSaved: (newValue) {
    //                                   controller.formData['NguoiGiamSat'] =
    //                                       newValue;
    //                                 },
    //                                 style: theme.textTheme.labelLarge,
    //                               ),
    //                         MySpacing.height(18),
    //                         SizedBox(
    //                           width: width,
    //                           child: Row(children: [
    //                             // ----------------------------------- ĐỘ ƯU TIÊN ---------------------------------
    //                             // --------------------------------------------------------------------------------
    //                             SizedBox(
    //                               width: width / 2 - 22,
    //                               child: FormBuilderDropdown<int>(
    //                                 name: 'DoUuTien',
    //                                 decoration: myDecoration.copyWith(
    //                                   labelText: 'Độ ưu tiên',
    //                                 ),
    //                                 initialValue:
    //                                     controller.formData['DoUuTien'],
    //                                 validator: FormBuilderValidators.compose(
    //                                     [FormBuilderValidators.required()]),
    //                                 items: controller.dmuutien
    //                                     .map((ele) => DropdownMenuItem(
    //                                           alignment:
    //                                               AlignmentDirectional.center,
    //                                           value: ele.id,
    //                                           child: Text(
    //                                               ele.tenDanhMuc.toString()),
    //                                         ))
    //                                     .toList(),
    //                                 onChanged: (value) {
    //                                   controller.formData['DoUuTien'] = value!;
    //                                 },
    //                                 onSaved: (newValue) {
    //                                   controller.formData['DoUuTien'] =
    //                                       newValue!;
    //                                 },
    //                                 style: theme.textTheme.labelLarge,
    //                               ),
    //                             ),
    //                             MySpacing.width(8),
    //                             // ------------------------------------ MỨC ĐỘ ------------------------------------
    //                             // --------------------------------------------------------------------------------
    //                             SizedBox(
    //                               width: width / 2 - 18,
    //                               child: FormBuilderDropdown<int>(
    //                                 name: 'Mucdo',
    //                                 decoration: myDecoration.copyWith(
    //                                   labelText: 'Mức độ',
    //                                 ),
    //                                 initialValue: controller.formData['Mucdo'],
    //                                 validator: FormBuilderValidators.compose(
    //                                     [FormBuilderValidators.required()]),
    //                                 items: controller.dmMucdo
    //                                     .map((ele) => DropdownMenuItem(
    //                                           alignment:
    //                                               AlignmentDirectional.center,
    //                                           value: ele.id,
    //                                           child: Text(
    //                                               ele.tenDanhMuc.toString()),
    //                                         ))
    //                                     .toList(),
    //                                 onChanged: (value) {
    //                                   controller.formData['Mucdo'] = value!;
    //                                 },
    //                                 onSaved: (newValue) {
    //                                   controller.formData['Mucdo'] = newValue!;
    //                                 },
    //                                 style: theme.textTheme.labelLarge,
    //                               ),
    //                             ),
    //                           ]),
    //                         ),
    //                         MySpacing.height(8),

    //                         // --------------------------------- LỊCH NHẮC NHỞ ------------------------------------
    //                         // ------------------------------------------------------------------------------------
    //                         // FormBuilderSwitch(
    //                         //   title: const MyText.labelLarge(
    //                         //       'Đặt lịch nhắc nhớ'),
    //                         //   name: 'reminder',
    //                         //   initialValue: false,
    //                         //   onChanged: (value) {
    //                         //     controller.formData["reminder"] = value;
    //                         //     controller.update();
    //                         //   },
    //                         //   onSaved: (newValue) {
    //                         //     controller.formData["reminder"] = newValue;
    //                         //   },
    //                         //   decoration: myDecoration.copyWith(
    //                         //     border: InputBorder.none,
    //                         //     enabledBorder: InputBorder.none,
    //                         //     focusedBorder: InputBorder.none,
    //                         //     errorBorder: InputBorder.none,
    //                         //     disabledBorder: InputBorder.none,
    //                         //   ),
    //                         // ),
    //                         // Visibility(
    //                         //   visible: controller.formData[
    //                         //       "reminder"], //controller.formKey.currentState?.fields['reminder']?.value,
    //                         //   child: Row(children: [
    //                         //     SizedBox(
    //                         //       width: width - 150 - 32 - 16,
    //                         //       child: FormBuilderTextField(
    //                         //         name: 'ReminderTime',
    //                         //         keyboardType: TextInputType.number,
    //                         //         decoration: myDecoration.copyWith(
    //                         //           labelText: 'Nhắc nhở trước ',
    //                         //           enabledBorder: UnderlineInputBorder(
    //                         //               borderSide: BorderSide(
    //                         //                   color: theme
    //                         //                       .colorScheme.onSurface)),
    //                         //           focusedBorder: UnderlineInputBorder(
    //                         //               borderSide: BorderSide(
    //                         //                   color: theme
    //                         //                       .colorScheme.onSurface)),
    //                         //           border: UnderlineInputBorder(
    //                         //               borderSide: BorderSide(
    //                         //                   color: theme
    //                         //                       .colorScheme.onSurface)),
    //                         //         ),
    //                         //         initialValue:
    //                         //             controller.formData['ReminderTime'],
    //                         //         maxLines: 1,
    //                         //         onChanged: (value) {
    //                         //           controller.formData['ReminderTime'] =
    //                         //               value!;
    //                         //         },
    //                         //         onSaved: (newValue) {
    //                         //           controller.formData['ReminderTime'] =
    //                         //               newValue ?? '';
    //                         //         },
    //                         //         style: theme.textTheme.labelLarge,
    //                         //       ),
    //                         //     ),
    //                         //     SizedBox(
    //                         //       width: 150,
    //                         //       child: FormBuilderSegmentedControl<String>(
    //                         //         name: 'ReminderType',
    //                         //         initialValue:
    //                         //             controller.formData['ReminderType'],
    //                         //         padding: const EdgeInsets.all(8),
    //                         //         segmentPadding:
    //                         //             const EdgeInsets.only(top: 0),
    //                         //         widgetPadding: const EdgeInsets.all(8),
    //                         //         options: [
    //                         //           ['phut', 'Phút'],
    //                         //           ['gio', 'Giờ']
    //                         //         ]
    //                         //             .map((ele) => FormBuilderFieldOption(
    //                         //                   value: ele[0],
    //                         //                   child: Text(ele[1].toString()),
    //                         //                 ))
    //                         //             .toList(growable: true),
    //                         //         onChanged: (value) {
    //                         //           controller.formData['ReminderType'] =
    //                         //               value;
    //                         //           controller.update();
    //                         //         },
    //                         //         onSaved: (newValue) {
    //                         //           controller.formData['ReminderType'] =
    //                         //               newValue!;
    //                         //         },
    //                         //         decoration: myDecoration.copyWith(
    //                         //           labelText: '',
    //                         //           contentPadding: EdgeInsets.zero,
    //                         //           border: InputBorder.none,
    //                         //           enabledBorder: InputBorder.none,
    //                         //           focusedBorder: InputBorder.none,
    //                         //           errorBorder: InputBorder.none,
    //                         //           disabledBorder: InputBorder.none,
    //                         //         ),
    //                         //       ),
    //                         //     ),
    //                         //   ]),
    //                         // ),
    //                       ],
    //                     ),
    //                   ))),
    //           Container(
    //             padding: const EdgeInsets.all(16.0),
    //             color: Colors.transparent,
    //             child: Row(
    //               children: [
    //                 Expanded(
    //                   flex: 1,
    //                   child: MyButton.block(
    //                     onPressed: () async {
    //                       bool isValid =
    //                           controller.formKey.currentState!.validate();
    //                       if (isValid) {
    //                         controller.formKey.currentState!.save();
    //                         await controller.taoMoiCongViec();
    //                       }

    //                       //final map = controller.formData;
    //                       //debugPrint(map.toString());
    //                       //debugPrint(isValid.toString());
    //                     },
    //                     //backgroundColor: Get.isDarkMode ? theme.colorScheme.tertiary.withAlpha(120) : theme.colorScheme.primaryContainer,
    //                     child: MyText.labelLarge(
    //                       'Tạo công việc',
    //                       fontWeight: 900,
    //                       color: theme.colorScheme.onPrimary,
    //                     ),
    //                   ),
    //                 ),
    //               ],
    //             ),
    //           ),
    //         ],
    //       );
    //     },
    //   ),
    // );
    return Obx(() {
      return Scaffold(
        appBar: appBarTitle(
          context,
          controller.isEditMode
              ? 'Sửa nhiệm vụ/công việc'
              : 'Tạo mới nhiệm vụ/công việc',
        ),
        body: controller.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(
                        left: 16.0,
                        right: 16.0,
                        bottom: 16.0,
                        top: 4,
                      ),
                      child: FormBuilder(
                        key: controller.formKey,
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            Align(
                              alignment: Alignment.center,
                              child: Padding(
                                padding: EdgeInsets.all(8.0),
                                child: MyText.bodyMedium(
                                  controller.isEditMode
                                      ? "Chỉnh sửa thông tin nhiệm vụ theo yêu cầu"
                                      : "Hãy cung cấp thông tin cho việc cần tạo",
                                  decoration: TextDecoration.underline,
                                  color: theme.colorScheme.onSurface,
                                  fontSize: 15,
                                  fontWeight: 700,
                                ),
                              ),
                            ),

                            // TÊN CÔNG VIỆC
                            MyText.bodyMedium('Tên gợi nhớ?', fontWeight: 700),
                            Container(
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  alignment: Alignment.bottomRight,
                                  fit: BoxFit.fitHeight,
                                  image: AssetImage(
                                    "assets/images/training.png",
                                  ),
                                  opacity: 0.01,
                                ),
                              ),
                              child: FormBuilderTextField(
                                name: 'TaskName',
                                validator: FormBuilderValidators.required(
                                  errorText: 'Vui lòng nhập Tên Công việc',
                                ),
                                decoration: InputDecoration(
                                  hintText:
                                      'Nhập một tên ngắn gọn, dễ dàng liên tưởng đến công việc',
                                  hintStyle: TextStyle(
                                    color: theme.colorScheme.onSurface
                                        .withOpacity(0.3),
                                  ),
                                ),
                                initialValue:
                                    controller.formData['TaskName']
                                        ?.toString() ??
                                    '',
                                maxLines: 2,
                                onChanged: (value) =>
                                    controller.formData['TaskName'] = value,
                                onSaved: (newValue) =>
                                    controller.formData['TaskName'] =
                                        newValue ?? '',
                                style: theme.textTheme.labelLarge,
                              ),
                            ),
                            MySpacing.height(14),

                            // MÔ TẢ CÔNG VIỆC
                            MyText.bodyMedium(
                              'Công việc là gì?',
                              fontWeight: 700,
                            ),
                            FormBuilderTextField(
                              name: 'MoTa',
                              validator: FormBuilderValidators.required(
                                errorText: 'Vui lòng nhập Nội dung công việc',
                              ),
                              decoration: InputDecoration(
                                hintText:
                                    'Mô tả công việc cụ thể cần thực hiện',
                                hintStyle: TextStyle(
                                  color: theme.colorScheme.onSurface
                                      .withOpacity(0.3),
                                ),
                              ),
                              initialValue:
                                  controller.formData['MoTa']?.toString() ?? '',
                              maxLines: 2,
                              onChanged: (value) =>
                                  controller.formData['MoTa'] = value,
                              onSaved: (newValue) =>
                                  controller.formData['MoTa'] = newValue ?? '',
                              style: theme.textTheme.labelLarge,
                            ),
                            MySpacing.height(14),

                            // PHÂN LOẠI CÔNG VIỆC
                            MyText.bodyMedium(
                              'Phân loại của công việc?',
                              fontWeight: 700,
                            ),
                            Obx(
                              () => FormBuilderDropdown<int>(
                                name: 'LoaiCongViec',
                                decoration: InputDecoration(
                                  hintText: 'Chọn phân loại để dễ dàng quản lý',
                                  hintStyle: TextStyle(
                                    color: theme.colorScheme.onSurface
                                        .withOpacity(0.3),
                                  ),
                                ),
                                initialValue:
                                    controller.formData['LoaiCongViec'],
                                validator: FormBuilderValidators.required(),
                                items: controller.dmloaiCongviec
                                    .map(
                                      (ele) => DropdownMenuItem(
                                        alignment:
                                            AlignmentDirectional.centerStart,
                                        value: ele.id,
                                        child: Text(ele.tenDanhMuc.toString()),
                                      ),
                                    )
                                    .toList(),
                                onChanged: (value) =>
                                    controller.formData['LoaiCongViec'] = value,
                                onSaved: (newValue) =>
                                    controller.formData['LoaiCongViec'] =
                                        newValue,
                                style: theme.textTheme.labelLarge,
                              ),
                            ),
                            MySpacing.height(14),

                            // THỜI GIAN THỰC HIỆN
                            MyText.bodyMedium(
                              'Trong khoảng thời gian nào?',
                              fontWeight: 700,
                            ),
                            FormBuilderDateRangePicker(
                              name: 'startEndDay',
                              firstDate: DateTime(DateTime.now().year - 1),
                              lastDate: DateTime(DateTime.now().year + 1),
                              format: DateFormat('dd/MM/yyyy'),
                              initialValue: controller.formData["startEndDay"],
                              onChanged: (value) =>
                                  controller.formData["startEndDay"] = value,
                              onSaved: (newValue) =>
                                  controller.formData["startEndDay"] = newValue,
                              decoration: InputDecoration(
                                contentPadding: const EdgeInsets.fromLTRB(
                                  4,
                                  12,
                                  4,
                                  1,
                                ),
                                hintText: 'Thời gian hoàn thành dự kiến',
                                suffixIcon: IconButton(
                                  padding: const EdgeInsets.all(0),
                                  icon: const Icon(Icons.close),
                                  onPressed: () => controller
                                      .formKey
                                      .currentState!
                                      .fields['startEndDay']
                                      ?.didChange(null),
                                ),
                              ),
                              locale: const Locale('vi'),
                              cancelText: "Hủy bỏ",
                              confirmText: "Xác nhận",
                              saveText: "Lưu",
                              fieldStartLabelText: "Ngày bắt đầu",
                              fieldEndLabelText: "Ngày kết thúc",
                              fieldStartHintText: "Ngày bắt đầu",
                              fieldEndHintText: "Ngày kết thúc",
                              // startValuePrefix: "Bắt đầu: ",
                              // endValuePrefix: "Kết thúc: ",
                            ),
                            MySpacing.height(14),

                            // NGƯỜI GIAO VIỆC
                            MyText.bodyMedium('Ai giao việc?', fontWeight: 700),
                            Obx(
                              () => controller.teamMembers.isEmpty
                                  ? Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: SkeletonLine(),
                                    )
                                  : FormBuilderDropdown<dynamic>(
                                      name: 'NguoiGiaoViec',
                                      initialValue:
                                          controller.formData['NguoiGiaoViec'],
                                      decoration: InputDecoration(
                                        hintText: 'Người giao việc',
                                        hintStyle: TextStyle(
                                          color: theme.colorScheme.onSurface
                                              .withOpacity(0.3),
                                        ),
                                      ),
                                      validator: FormBuilderValidators.required(
                                        errorText:
                                            'Vui lòng chọn người giao việc',
                                      ),
                                      items: controller.teamMembers
                                          .map(
                                            (ele) => DropdownMenuItem(
                                              alignment:
                                                  AlignmentDirectional.center,
                                              value: ele,
                                              child: Row(
                                                children: [
                                                  CircleAvatar(
                                                    radius: 15,
                                                    backgroundImage:
                                                        NetworkImage(
                                                          ele["hinhAnh"]
                                                              .toString(),
                                                        ),
                                                  ),
                                                  MySpacing.width(8),
                                                  Text(
                                                    "${getHoTen(ele)} (${ele["tenChucVu"] == "Không chức vụ" ? "Viên chức" : ele["tenChucVu"]})",
                                                  ),
                                                ],
                                              ),
                                            ),
                                          )
                                          .toList(),
                                      onChanged: (value) =>
                                          controller.formData['NguoiGiaoViec'] =
                                              value,
                                      onSaved: (newValue) =>
                                          controller.formData['NguoiGiaoViec'] =
                                              newValue!,
                                      style: theme.textTheme.labelLarge,
                                    ),
                            ),
                            MySpacing.height(14),

                            // NGƯỜI THỰC HIỆN
                            SelectAssignees(
                              initialUsers: controller.assignees,
                              onSearch: controller.searchUsers,
                              onChanged: controller.onAssigneesChanged,
                              availableUsers: controller.teamMembers,
                            ),

                            // FILE ĐÍNH KÈM
                            if (!controller.isEditMode) ...[
                              MySpacing.height(18),
                              MyText.bodyMedium(
                                'File đính kèm',
                                fontWeight: 700,
                              ),
                              FileUploadWidget(
                                key: controller.uploadWidgetKey,
                                onFilesSelected: (files) {
                                  controller.selectedFiles = files;
                                },
                                onUploadProgress: (value) {},
                                theme: FileUploadTheme(
                                  primaryColor:
                                      theme.colorScheme.primaryContainer,
                                  backgroundColor:
                                      theme.scaffoldBackgroundColor,
                                  borderColor: theme.colorScheme.secondary,
                                  textColor: theme.colorScheme.onPrimary,
                                  errorColor: theme.colorScheme.error,
                                  borderRadius: 4.0,
                                  contentPadding: EdgeInsets.fromLTRB(
                                    0,
                                    0,
                                    0,
                                    0,
                                  ),
                                  titleStyle: theme.textTheme.labelLarge!,
                                  subtitleStyle: theme.textTheme.labelMedium!,
                                ),
                                maxFileSizeMB: 10,
                                allowMultiple: true,
                                allowedExtensions: [
                                  'jpg',
                                  'jpeg',
                                  'png',
                                  'pdf',
                                  'doc',
                                ],
                              ),
                              // MySpacing.height(18),

                              // VĂN BẢN LIÊN QUAN
                              RelatedDocumentsPicker(
                                onSearch: controller.fetchDocuments,
                                attachTitleText: 'Đính kèm văn bản liên quan?',
                                buttonText: 'Chọn văn bản',
                                emptyText: 'Chưa có văn bản nào được chọn',
                                initialDocuments: controller.selectedDocuments,
                                availableDocuments:
                                    controller.availableDocuments,
                                maxSelections: 1,
                                onChanged: (List<dynamic> docs) {
                                  controller.selectedDocuments.assignAll(docs);
                                  // controller.formData['DocumentId'] = docs
                                  //     .map((doc) => {"id": doc['id']})
                                  //     .toList();
                                  // controller.selectedDocuments.value =
                                  //     docs.isNotEmpty ? docs.first : null;
                                  controller.formData['DocumentId'] =
                                      docs.isNotEmpty ? docs.first['id'] : 0;
                                },
                              ),
                            ],

                            MySpacing.height(18),

                            // NGƯỜI GIÁM SÁT
                            MyText.bodyMedium(
                              'Có ai giám sát không?',
                              fontWeight: 700,
                            ),
                            Obx(
                              () => controller.teamMembers.isEmpty
                                  ? Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: SkeletonLine(),
                                    )
                                  : FormBuilderDropdown<dynamic>(
                                      name: 'NguoiGiamSat',
                                      decoration: InputDecoration(
                                        hintText: 'Người giám sát',
                                        hintStyle: TextStyle(
                                          color: theme.colorScheme.onSurface
                                              .withOpacity(0.3),
                                        ),
                                        suffixIcon: IconButton(
                                          padding: const EdgeInsets.all(0),
                                          icon: const Icon(Icons.close),
                                          onPressed: () => controller
                                              .formKey
                                              .currentState!
                                              .fields['NguoiGiamSat']
                                              ?.didChange(null),
                                        ),
                                      ),
                                      initialValue:
                                          controller.formData['NguoiGiamSat'],
                                      items: controller.teamMembers
                                          .map(
                                            (ele) => DropdownMenuItem(
                                              alignment:
                                                  AlignmentDirectional.center,
                                              value: ele,
                                              child: Row(
                                                children: [
                                                  CircleAvatar(
                                                    radius: 15,
                                                    backgroundImage:
                                                        NetworkImage(
                                                          ele["hinhAnh"]
                                                              .toString(),
                                                        ),
                                                  ),
                                                  MySpacing.width(8),
                                                  Text(
                                                    "${getHoTen(ele)} (${ele["tenChucVu"] == "Không chức vụ" ? "Viên chức" : ele["tenChucVu"]})",
                                                  ),
                                                ],
                                              ),
                                            ),
                                          )
                                          .toList(),
                                      onChanged: (value) =>
                                          controller.formData['NguoiGiamSat'] =
                                              value,
                                      onSaved: (newValue) =>
                                          controller.formData['NguoiGiamSat'] =
                                              newValue,
                                      style: theme.textTheme.labelLarge,
                                    ),
                            ),
                            MySpacing.height(18),

                            // ĐỘ ƯU TIÊN VÀ MỨC ĐỘ
                            SizedBox(
                              width: width,
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: width / 2 - 22,
                                    child: Obx(
                                      () => FormBuilderDropdown<int>(
                                        name: 'DoUuTien',
                                        decoration: myDecoration.copyWith(
                                          labelText: 'Độ ưu tiên',
                                        ),
                                        initialValue:
                                            controller.formData['DoUuTien'],
                                        validator:
                                            FormBuilderValidators.required(),
                                        items: controller.dmuutien
                                            .map(
                                              (ele) => DropdownMenuItem(
                                                alignment:
                                                    AlignmentDirectional.center,
                                                value: ele.id,
                                                child: Text(
                                                  ele.tenDanhMuc.toString(),
                                                ),
                                              ),
                                            )
                                            .toList(),
                                        onChanged: (value) =>
                                            controller.formData['DoUuTien'] =
                                                value,
                                        onSaved: (newValue) =>
                                            controller.formData['DoUuTien'] =
                                                newValue!,
                                        style: theme.textTheme.labelLarge,
                                      ),
                                    ),
                                  ),
                                  MySpacing.width(8),
                                  SizedBox(
                                    width: width / 2 - 18,
                                    child: Obx(
                                      () => FormBuilderDropdown<int>(
                                        name: 'Mucdo',
                                        decoration: myDecoration.copyWith(
                                          labelText: 'Mức độ',
                                        ),
                                        initialValue:
                                            controller.formData['Mucdo'],
                                        validator:
                                            FormBuilderValidators.required(),
                                        items: controller.dmMucdo
                                            .map(
                                              (ele) => DropdownMenuItem(
                                                alignment:
                                                    AlignmentDirectional.center,
                                                value: ele.id,
                                                child: Text(
                                                  ele.tenDanhMuc.toString(),
                                                ),
                                              ),
                                            )
                                            .toList(),
                                        onChanged: (value) =>
                                            controller.formData['Mucdo'] =
                                                value,
                                        onSaved: (newValue) =>
                                            controller.formData['Mucdo'] =
                                                newValue!,
                                        style: theme.textTheme.labelLarge,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            MySpacing.height(8),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    color: Colors.transparent,
                    child: Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: MyButton.block(
                            onPressed: () async {
                              bool isValid = controller.formKey.currentState!
                                  .validate();
                              if (isValid) {
                                controller.formKey.currentState!.save();
                                if (controller.isEditMode) {
                                  await controller
                                      .updateTask(); // Gọi updateTask nếu đang chỉnh sửa
                                } else {
                                  await controller
                                      .taoMoiCongViec(); // Gọi taoMoiCongViec nếu tạo mới
                                }
                              }
                            },
                            child: MyText.labelLarge(
                              controller.isEditMode ? 'Cập nhật' : 'Tạo mới',
                              fontWeight: 900,
                              color: theme.colorScheme.onPrimary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      );
    });
  }

  // Callback khi danh sách người dùng thay đổi
  // -------------------------- LẤY LIST VIÊN CHỨC CỦA PHÒNG ----------------------------
  // ------------------------------------------------------------------------------------
  Widget SelectAssignee(BuildContext context) {
    var theme = Theme.of(context);
    return GetBuilder<TaskyCreateController>(
      builder: (TaskyCreateController controller) {
        var leaders = controller.teamMembers
            .where(
              (e) => e["tenChucVu"].toString().toLowerCase().contains(
                "trưởng phòng",
              ),
            )
            .toList();
        var members = controller.teamMembers
            .where(
              (e) => !e["tenChucVu"].toString().toLowerCase().contains(
                "trưởng phòng",
              ),
            )
            .toList();
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 24),
          child: Column(
            children: [
              MySpacing.height(14),
              Container(
                height: 6,
                width: 40,
                decoration: BoxDecoration(
                  color: Colors.grey,
                  borderRadius: BorderRadius.circular(45),
                ),
              ),
              MySpacing.height(14),
              MyText.bodyMedium('Chọn người tham gia để thực hiện công việc'),
              MySpacing.height(8),
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 0, 24, 0),
                child: SizedBox(
                  child: Row(
                    children: [
                      MyButton.outlined(
                        padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
                        onPressed: () {
                          controller.assignees.clear();
                          if (controller.isCheckAll) {
                            controller.isCheckAll = false;
                            controller.isCheckLeader = false;
                            controller.isCheckMember = false;
                          } else {
                            controller.assignees.addAll(controller.teamMembers);
                            controller.isCheckLeader = true;
                            controller.isCheckMember = true;
                            controller.isCheckAll = true;
                          }
                          controller.update();
                        },
                        child: Row(
                          children: [
                            Icon(
                              Icons.check,
                              color: controller.isCheckAll
                                  ? theme.colorScheme.secondary
                                  : theme.colorScheme.onSurface,
                            ),
                            MySpacing.width(4),
                            MyText.labelLarge(
                              'Chọn tất cả',
                              fontWeight: controller.isCheckAll ? 700 : 400,
                              color: controller.isCheckAll
                                  ? theme.colorScheme.secondary
                                  : theme.colorScheme.onSurface,
                            ),
                          ],
                        ),
                      ),
                      MyButton.outlined(
                        padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
                        onPressed: () {
                          controller.assignees.removeWhere(
                            (e) => e["tenChucVu"]
                                .toString()
                                .toLowerCase()
                                .contains("trưởng phòng"),
                          );
                          if (controller.isCheckLeader) {
                            //controller.assignees.removeWhere((e) => e["tenChucVu"].toString().toLowerCase().contains("trưởng phòng"));
                            controller.isCheckLeader = false;
                            controller.isCheckAll = false;
                          } else {
                            controller.isCheckLeader = true;
                            controller.isCheckAll = false;
                            controller.assignees.addAll(leaders);
                          }
                          controller.update();
                        },
                        child: Row(
                          children: [
                            Icon(
                              Icons.check,
                              color: controller.isCheckLeader
                                  ? theme.colorScheme.secondary
                                  : theme.colorScheme.onSurface,
                            ),
                            MySpacing.width(4),
                            MyText.labelLarge(
                              'Lãnh đạo đơn vị',
                              fontWeight: controller.isCheckLeader ? 700 : 400,
                              color: controller.isCheckLeader
                                  ? theme.colorScheme.secondary
                                  : theme.colorScheme.onSurface,
                            ),
                          ],
                        ),
                      ),
                      MyButton.outlined(
                        padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
                        onPressed: () {
                          controller.assignees.removeWhere(
                            (e) => !e["tenChucVu"]
                                .toString()
                                .toLowerCase()
                                .contains("trưởng phòng"),
                          );
                          if (controller.isCheckMember) {
                            controller.isCheckMember = false;
                            controller.isCheckAll = false;
                          } else {
                            controller.isCheckMember = true;
                            controller.isCheckAll = false;
                            controller.assignees.addAll(members);
                          }
                          controller.update();
                        },
                        child: Row(
                          children: [
                            Icon(
                              Icons.check,
                              color: controller.isCheckMember
                                  ? theme.colorScheme.secondary
                                  : theme.colorScheme.onSurface,
                            ),
                            MySpacing.width(4),
                            MyText.labelLarge(
                              'Viên chức',
                              fontWeight: controller.isCheckMember ? 700 : 400,
                              color: controller.isCheckMember
                                  ? theme.colorScheme.secondary
                                  : theme.colorScheme.onSurface,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              ListView(
                shrinkWrap: true,
                padding: const EdgeInsets.all(24),
                controller: ModalScrollController.of(context),
                children: List.generate(controller.teamMembers.length, (index) {
                  return AssigneeTileWidget(
                    isChecked:
                        controller.assignees
                            .where(
                              (it) =>
                                  it["vienChucId"] ==
                                  controller.teamMembers[index]["vienChucId"],
                            )
                            .firstOrNull !=
                        null,
                    selectedUser: controller.teamMembers[index],
                    onTap: (Map user) {
                      if (controller.assignees
                              .where(
                                (it) => it["vienChucId"] == user["vienChucId"],
                              )
                              .firstOrNull !=
                          null) {
                        controller.assignees.remove(user);
                      } else {
                        controller.assignees.add(user);
                      }
                      controller.update();
                      //widget.taskCreateController.setAssignees(widget.users);
                    },
                  );
                }),
              ),
              const SizedBox(height: 25),
              Padding(
                padding: const EdgeInsets.only(left: 24, right: 24),
                child: MyButton.block(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: MyText.labelLarge(
                    'Done',
                    color: theme.colorScheme.onPrimary,
                  ),
                ),
              ),
              const SizedBox(height: 15),
            ],
          ),
        );
      },
    );
  }
}
