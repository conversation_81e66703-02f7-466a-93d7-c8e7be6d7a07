import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:syncfusion_flutter_signaturepad/signaturepad.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'models/sign_request_document.dart';

class HomeController extends GetxController {
  final GlobalKey<SfSignaturePadState> signaturePadKey = GlobalKey();
  final GlobalKey<SfSignaturePadState> signaturePadKeyMoc = GlobalKey();
  Rx<Uint8List?> signatureBytes = Rx<Uint8List?>(null);
  final ApiProvider apiProvider = Get.find();
  // Document lists
  final RxList<SignRequestDocument> pendingDocuments =
      <SignRequestDocument>[].obs;
  final RxList<SignRequestDocument> signedDocuments =
      <SignRequestDocument>[].obs;
  final RxString currentFilter = 'all'.obs;
  final RxString imageChuKy = ''.obs;
  final RxString imageChuKyTat = ''.obs;
  final RxString imageMoc = ''.obs;

  List<SignRequestDocument> get expiringDocuments => signedDocuments
      .where((doc) =>
          doc.expiryDate != null &&
          doc.expiryDate!.isBefore(DateTime.now().add(Duration(days: 7))))
      .toList();

  @override
  void onInit() {
    super.onInit();
    _loadMockDocuments();
    getSignature();
  }

  Future<void> _loadMockDocuments() async {
    var dsChokys = (await apiProvider.loadDSChoky()) as List;

    for (var dschoky in dsChokys) {
      var pt = ApiPath.tmsBaseUrl + cleanPath(dschoky['file']["filePath"]);
      print(pt);
      pendingDocuments.add(SignRequestDocument(
        id: dschoky['id'].toString(),
        name: dschoky['file']["moTa"],
        path: pt,
        signedPath: '',
        createdAt: DateTime.now(),
        pdfBytes: Uint8List(0),
      ));
    }
    // Add some mock data for testing
    var dsDakys = (await apiProvider.loadDSDaky()) as List;
    for (var dsDaky in dsDakys) {
      var pt = ApiPath.tmsBaseUrl + cleanPath(dsDaky['file']["filePath"]);
      signedDocuments.add(SignRequestDocument(
        id: dsDaky['id'].toString(),
        name: dsDaky['file']["moTa"],
        path: pt,
        signedPath: '',
        createdAt: DateTime.now(),
        pdfBytes: Uint8List(0),
      ));
    }
  }

  void setFilter(String filter) {
    currentFilter.value = filter;
    update();
    // Implement filtering logic if needed
  }

  String cleanPath(String path) {
    //debugPrint("Start cleanPath $path");
    path = path.replaceAll('\\\\server1\\', '');
    path = path.replaceAll('\\\\************\\', '');
    path = path.replaceAll('\\\\***********\\', '');
    path = path.replaceAll('\\\\', '/');
    path = path.replaceAll('\\', '/');
    path = path.replaceAll('/server1/', '');
    path = path.replaceAll('/************/', '');
    path = path.replaceAll('/***********/', '');
    path = path.replaceAll('//', '/');
    //debugPrint("End cleanPath $path");
    return path;
  }

  getSignature() async {
    var signature2 = await apiProvider.loadChuky();
    imageChuKy.value = signature2['imageChuKy'] != null
        ? ApiPath.tmsBaseUrl + cleanPath(signature2['imageChuKy'])
        : '';
    imageChuKyTat.value = signature2['imageChuKyTat'] != null
        ? ApiPath.tmsBaseUrl + cleanPath(signature2['imageChuKyTat'])
        : '';
    imageMoc.value = signature2['imageMoc'] != null
        ? ApiPath.tmsBaseUrl + cleanPath(signature2['imageMoc'])
        : '';
    update();

    debugPrint("Signature loaded from ${imageChuKy.value}");

    // try {
    //   var signature = LocalStorageServices.getSignature();
    //   if (signature == null) {
    //     signatureBytes.value = null;
    //   } else {
    //     signatureBytes.value = await File(signature).readAsBytes();
    //     update();
    //     Logger().i('Signature loaded from $signature');
    //   }
    //   return signature;
    // } catch (e) {
    //   Get.snackbar('Error', 'Failed to load signature: ${e.toString()}');
    //   return null;
    // }
  }

  Future<void> saveSignature() async {
    try {
      final data = await signaturePadKey.currentState!.toImage();
      final byteData = await data.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) {
        throw Exception('Failed to convert signature to bytes');
      }

      final directory = await getApplicationDocumentsDirectory();
      final filePath =
          '${directory.path}/signature_${DateTime.now().millisecondsSinceEpoch}.png';
      final signatureFile = File(filePath);
      await signatureFile.writeAsBytes(byteData.buffer.asUint8List());
      await LocalStorageServices.saveSignature(filePath);
      signatureBytes.value = byteData.buffer.asUint8List();

      Get.snackbar('Success', 'Signature saved to $filePath');
    } catch (e) {
      Get.snackbar('Error', 'Failed to save signature: ${e.toString()}');
    }
  }

  void clearSignature() {
    signaturePadKey.currentState!.clear();
    signatureBytes.value = null;
  }

  Future<void> signDocument(SignRequestDocument document) async {
    // Move document from pending to signed
    pendingDocuments.remove(document);
    signedDocuments.add(SignRequestDocument(
      id: document.id,
      name: document.name,
      path: document.path,
      signedPath: document.signedPath,
      createdAt: document.createdAt,
      signedDate: DateTime.now(),
      pdfBytes: Uint8List(0),
    ));
  }

  Future<void> uploadSignature(stringbytes) async {
    //await apiProvider.uplaodChukybyJson(stringbytes, 'png', 'chuky');
    try {
      await apiProvider.uplaodChukybyJson(stringbytes, 'png', 'chuky');
      Get.snackbar('Success', 'Signature uploaded successfully');
    } catch (e) {
      Get.snackbar('Error', 'Failed to upload signature: ${e.toString()}');
    }
  }
}
