import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import '../controllers/onboarding_controller.dart';

class OnboardingView extends GetView<OnboardingController> {
  const OnboardingView({super.key});

  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    return SafeArea(
      child: Scaffold(
        body: Obx(() => Stack(
              children: [
                SizedBox(
                  height: Get.height,
                  child: PageView.builder(
                    controller: controller.pageController,
                    onPageChanged: controller.selectedPage.call,
                    itemCount: controller.onBoardingPages.length,
                    itemBuilder: (context, index) {
                      return controller.onBoardingPages.isEmpty
                          ? Center(
                              child: CircularProgressIndicator(
                                color: theme.primaryColor,
                              ),
                            )
                          : Padding(
                            padding: const EdgeInsets.only(left: 40.0, right: 40.0),
                            child: Column(
                                children: [
                                  const SizedBox(height: 10),
                                  SizedBox(
                                    height: 350,
                                    child: Lottie.asset(
                                        controller.onBoardingPages[index].imageAsset,
                                        animate: true,
                                        height: 350),
                                  ),
                                  const SizedBox(height: 5),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 25),
                                    child: SizedBox(
                                      child: MyText.titleLarge(
                                        controller.onBoardingPages[index].title,
                                        color: Colors.blue,
                                        textAlign: TextAlign.center,
                                        fontWeight: 900,
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 20),
                                  SizedBox(
                                    child: MyText.bodyLarge(
                                      controller.onBoardingPages[index].description,
                                      textAlign: TextAlign.center,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                ],
                              ),
                          );
                    },
                  ),
                ),
                Positioned(
                  bottom: 110,
                  width: Get.width,
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                          controller.onBoardingPages.length,
                          (index) => Container(
                                margin: const EdgeInsets.symmetric(horizontal: 3),
                                height: 4,
                                width: 20,
                                decoration: BoxDecoration(
                                  color: controller.selectedPage.value == index
                                      ? theme.primaryColor
                                      : theme.primaryColor.withOpacity(.2),
                                  borderRadius: BorderRadius.all(Radius.circular(3)),
                                  shape: BoxShape.rectangle,
                                ),
                              )),
                    ),
                  ),
                ),
                Positioned(
                  top: 20,
                  right: 0,
                  child: SizedBox(
                    width: 80,
                    height: 48,
                    child: MyButton.text(
                      onPressed: () {
                        LocalStorageServices.setIsNew(false);
                        Get.offAllNamed(Routes.LOGIN);
                      },
                      child: Text(
                        "Bỏ qua",
                        style: TextStyle(
                          color: theme.colorScheme.onSecondaryContainer.withOpacity(0.8),
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          fontStyle: FontStyle.italic,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    )
                  ),
                ),
                Positioned(
                  bottom: 40,
                  left: 0,
                  right: 0,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 25),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        controller.selectedPage.value > 0
                            ? MyButton.large(
                                onPressed: () {
                                  controller.backwardAction();
                                },
                                elevation: 0,
                                splashColor:
                                    theme.colorScheme.onPrimary.withAlpha(60),
                                backgroundColor: theme.primaryColor,
                                child: MyText.bodyLarge(
                                  'Trước',
                                  color: Colors.white,
                                ),
                              )
                            : const SizedBox(),
                        MyButton.large(
                          child: MyText.bodyLarge(
                            controller.selectedPage.value >
                                    controller.onBoardingPages.length - 2
                                ? "Bắt đầu"
                                : "Tiếp",
                            color: Colors.white,
                          ),
                          onPressed: () {
                            LocalStorageServices.setIsNew(false);
                            if (controller.selectedPage.value >
                                controller.onBoardingPages.length - 2) {
                              Get.offAllNamed(Routes.LOGIN);
                            } else {
                              controller.forwardAction();
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            )),
      ),
    );
  }
}
