// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/donnghiphep_controller.dart';
import 'package:tvumobile/app/shared_components/form_builder_segmented_control.dart';
import 'package:tvumobile/app/shared_components/input_decoration_theme.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class ThongTinVoSinhWidget extends StatefulWidget {
  final DonNghiPhepController dnpManager;
  const ThongTinVoSinhWidget({
    super.key,
    required this.dnpManager,
  });

  @override
  State<ThongTinVoSinhWidget> createState() => _ThongtinWidgetState();
}

class _ThongtinWidgetState extends State<ThongTinVoSinhWidget> {
  int id = 0;
  var bhxh = {
    'a': 'a) 05 ngày làm việc',
    'b':
        'b) 07 ngày làm việc khi vợ sinh con phải phẫu thuật, sinh con dưới 32 tuần tuổi',
    'c':
        'c) Nếu vợ sinh đôi thì được nghỉ 10 ngày, từ ba trở lên thì cứ thêm mỗi con được nghỉ thêm 03 ngày',
    'd': 'd) Nếu vợ sinh đôi trở lên mà phải phẫu thuật thì được nghỉ 14 ngày'
  };
  final formKey = GlobalKey<FormBuilderState>();
  Map<String, dynamic> formdata = {
    'socon': 1,
    'stpt': 'SinhThuong',
    'nvs': DateTime.now(),
    'bv': '',
    'diem': 'a'
  };

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    if (widget.dnpManager.thongtinVosinh.isNotEmpty) {
      Logger().i(widget.dnpManager.thongtinVosinh);
      formdata = {};
      formdata.addAll(widget.dnpManager.thongtinVosinh);
    }
    final myDecoration =
        const InputDecoration().applyDefaults(GlobalInputDecoration(context));

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.primaryColor.withOpacity(0.95),
        title: MyText.titleMedium(
          "Nghỉ vợ sinh con".tr.toUpperCase(),
          color: theme.colorScheme.onPrimary,
        ),
        //leading: SizedBox(width: 0,),
        iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(10),
              child: Column(
                children: [
                  const MyContainer(
                    child: MyText.titleLarge("Vui lòng nhập thông tin vợ sinh"),
                  ),
                  FormBuilder(
                    key: formKey,
                    child: Column(
                      children: [
                        // ------------------------- NGÀY VỢ SINH CON --------------------------
                        FormBuilderDateTimePicker(
                          name: 'nvs',
                          initialEntryMode: DatePickerEntryMode.calendar,
                          initialValue: formdata['nvs'],
                          inputType: InputType.date,
                          format: DateFormat('dd.MM.yyyy'),
                          decoration: myDecoration.copyWith(
                            labelText: 'Ngày vợ sinh con',
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () {
                                formKey.currentState!.fields['nvs']
                                    ?.didChange(null);
                              },
                            ),
                            contentPadding: const EdgeInsets.all(8),
                          ),
                          initialTime: const TimeOfDay(hour: 8, minute: 0),
                          onChanged: (value) {
                            formdata['nvs'] = value ?? DateTime.now();
                          },
                          locale: const Locale('vi'),
                          cancelText: "Hủy bỏ",
                          confirmText: "Xác nhận",
                        ),
                        const SizedBox(height: 14),

                        // --------------------- SINH THƯỜNG / PHẪU THUẬT ----------------------
                        FormBuilderSegmentedControl(
                          name: 'stpt',
                          initialValue: formdata['stpt'],
                          padding: const EdgeInsets.all(8),
                          segmentPadding: const EdgeInsets.only(top: 8),
                          widgetPadding: const EdgeInsets.all(8),
                          options: const [
                            FormBuilderFieldOption(
                              value: "SinhThuong",
                              child: Text("Sinh thường"),
                            ),
                            FormBuilderFieldOption(
                              value: "PhauThuat",
                              child: Text("Phẫu thuật"),
                            )
                          ],
                          onChanged: (value) {
                            formdata['stpt'] = value ?? 'SinhThuong';
                            calculationDiem();
                          },
                          onSaved: (newValue) {
                            formdata['stpt'] = newValue ?? 'SinhThuong';
                          },
                          decoration: myDecoration.copyWith(
                            labelText: 'Sinh thường/phẫu thuật',
                          ),
                        ),
                        const SizedBox(height: 14),

                        // ------------------------------ SỐ CON -------------------------------
                        FormBuilderSegmentedControl<int>(
                          name: 'socon',
                          initialValue: formdata['socon'],
                          padding: const EdgeInsets.all(8),
                          segmentPadding: const EdgeInsets.only(top: 8),
                          widgetPadding: const EdgeInsets.all(8),
                          options: [1, 2, 3, 4, 5, 6, 7, 8]
                              .map((ele) => FormBuilderFieldOption(
                                    value: ele,
                                    child: Text(ele.toString()),
                                  ))
                              .toList(growable: true),
                          onChanged: (value) {
                            formdata['socon'] = value ?? 1;
                            calculationDiem();
                          },
                          onSaved: (newValue) {
                            formdata['socon'] = newValue ?? 1;
                          },
                          decoration: myDecoration.copyWith(
                            labelText: 'Số con',
                          ),
                        ),
                        const SizedBox(height: 14),

                        // ------------------------ THÔNG TIN BỆNH VIỆN ------------------------
                        FormBuilderTextField(
                          name: 'bv',
                          validator: FormBuilderValidators.required(
                              errorText: 'Vui lòng nhập thông tin bệnh viện'),
                          decoration: myDecoration.copyWith(
                            labelText: 'Tại bệnh viện',
                          ),
                          initialValue: formdata['bv'],
                          maxLines: 2,
                          onChanged: (value) {
                            formdata['bv'] = value ?? '';
                          },
                          onSaved: (newValue) {
                            formdata['bv'] = newValue ?? '';
                          },
                        ),
                        const SizedBox(height: 14),

                        // ----------------------------- ĐIỂM BHXH -----------------------------
                        FormBuilderRadioGroup<String>(
                          decoration: myDecoration.copyWith(
                            labelText: 'Điểm - BHXH',
                          ),
                          initialValue: formdata['diem'],
                          name: 'diem',
                          onChanged: (value) {
                            formdata['diem'] = value ?? 'a';
                          },
                          onSaved: (newValue) {
                            formdata['diem'] = newValue ?? 'a';
                          },
                          validator: FormBuilderValidators.compose(
                              [FormBuilderValidators.required()]),
                          options: bhxh.entries
                              .map((lang) => FormBuilderFieldOption(
                                    value: lang.key,
                                    child: Text(lang.value),
                                  ))
                              .toList(),
                          controlAffinity: ControlAffinity.leading,
                        ),
                        const SizedBox(height: 14),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // --------------------------- NÚT XÁC NHẬN ----------------------------
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              border: Border(
                top: BorderSide(color: theme.dividerColor, width: 0.1),
              ),
            ),
            child: MyButton.block(
              backgroundColor: theme.primaryColor.withOpacity(0.95),
              onPressed: () {
                bool isValid = formKey.currentState!.saveAndValidate();
                debugPrint(formKey.currentState?.value.toString());
                if (isValid) {
                  widget.dnpManager.thongtinVosinh = formdata;
                  widget.dnpManager.tinhNgayNghi();
                  widget.dnpManager.tinhSoNgayThuc();
                  Navigator.pop(context);
                } else {
                  Get.snackbar(
                    'Cảnh báo',
                    'Vui lòng điền đầy đủ thông tin',
                    snackPosition: SnackPosition.TOP,
                    backgroundColor: theme.colorScheme.error,
                    colorText: theme.colorScheme.onError,
                  );
                }
              },
              child: MyText.labelLarge(
                'Xác nhận thông tin',
                color: theme.colorScheme.onPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void calculationDiem() {
    int value = formdata['socon'] as int;
    if (formdata['stpt'] == "SinhThuong") {
      if (value >= 2) {
        formKey.currentState?.patchValue({
          'diem': 'c',
        });
      } else {
        formKey.currentState?.patchValue({
          'diem': 'a',
        });
      }
    } else {
      if (value >= 2) {
        formKey.currentState?.patchValue({
          'diem': 'd',
        });
      } else {
        formKey.currentState?.patchValue({
          'diem': 'b',
        });
      }
    }
  }
}
