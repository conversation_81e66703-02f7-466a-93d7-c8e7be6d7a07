import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/features/splash/splash_screen2_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';


class SplashScreen2 extends GetView<SplashScreen2Controller>  {
  const SplashScreen2({super.key});


  @override
  Widget build(BuildContext context) {

    controller.goToFullApp();
    var theme = Theme.of(context);
    bool isD = LocalStorageServices.getThemeIsLight();
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            <PERSON>(
              tag: "splash_username",
              child: MyText.titleLarge(
                "Xin chào, ${controller.userInfo?.hoten}",
                color:  isD ? theme.primaryColor : Colors.white,
              ),
            ),
            MyText.titleMedium(
              "<PERSON>ui lòng đợi chúng tôi kết nối dữ liệu!",
              color: isD ? theme.primaryColor : Colors.white ,
            ),
          ],
        ),
      ),
    );
  }
}
