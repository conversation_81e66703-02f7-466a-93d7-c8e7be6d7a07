

import 'package:flutter_timetable/flutter_timetable.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';
import 'package:tvumobile/app/features/sinhvien/tkbsinhvien/components/sfcalendar.dart';
import 'package:tvumobile/app/features/sinhvien/tkbsinhvien/components/timetable.dart';
import 'package:tvumobile/app/features/sinhvien/tkbsinhvien/tkb_sinhvien_controller.dart';
import 'package:tvumobile/app/shared/styles.dart';
import 'package:tvumobile/app/shared_components/appbar.dart';
import 'package:tvumobile/app/shared_components/loading_effect.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/ui/nb_utils_global.dart';

class TkbSinhVienView  extends GetView<TkbSinhVienController> {
  TkbSinhVienView({super.key});

  final CalendarController _calendarController = CalendarController();

  final timeTableController = TimetableController(
    start: DateUtils.dateOnly(DateTime.now()).subtract(const Duration(days: 7)),
    initialColumns: 3,
    cellHeight: 76.0,
    startHour: 7,
    endHour: 21,
  );

  @override
  Widget build(BuildContext context) {
    //ThemeData theme = Theme.of(context);
    //List colors = [getColorFromHex('#5E97F6'), getColorFromHex('#4DD0E1'), getColorFromHex('#F06292'), getColorFromHex('#F6BF26')];

    return GetBuilder<TkbSinhVienController>(
        init: controller,
        tag: 'tkb_sinhvien_controller',
        builder: (controller) {
          return Scaffold(
            appBar: appBarTitle(context, "Thời khoá biểu"),
            body: Column(
              children: [
                SizedBox(
                  height: 60,
                  child: _buildStyleWidget(context),
                ),
                _buildBody(context),
              ]
            )
          );
        });
  }

  Widget _buildBody(BuildContext context) {
    Theme.of(context);
    Size size = MediaQuery.sizeOf(context);
    if (controller.uiLoading) {
      return Padding(
        padding: MySpacing.top(MySpacing.safeAreaTop(context) + 20),
        child: LoadingEffect.getSearchLoadingScreen(
          context,
        ),
      );
    } else {
      if (controller.viewStyleselection.value == 0) {
        return Container(
          padding: const EdgeInsets.all(0),
          height: size.height - 175,
          //margin: EdgeInsets.fromLTRB(8, 0, 8, 0),
          child: getScheduleViewCalendar(
              _calendarController,
              events: controller.tkbGVDataSource,
              scheduleViewBuilder: scheduleViewBuilder, context: context),
        );
      }
      if (controller.viewStyleselection.value == 1) {
        return Container(
          padding: const EdgeInsets.all(0),
          height: size.height - 175,
          //margin: EdgeInsets.fromLTRB(8, 0, 8, 0),
          child: buildTimeTable(context, timeTableController, generateItems()),
        );
      }
      return LoadingEffect.getSearchLoadingScreen(
        context,
      );
    }
  }

  Widget _buildStyleWidget(BuildContext context) {
    ThemeData theme = Theme.of(context);
    var toneList = ["Lịch trình","Tuần","Tháng"];
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const MyText.titleMedium('Chế độ xem:'),
          MySpacing.width(8),
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: List.generate(3, (index) {
              return GestureDetector(
                onTap: () {
                  controller.onViewStyleChange(index);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: boxDecorationDefault(
                    borderRadius: radius(defaultRadius),
                    color: controller.viewStyleselection.value == index ? theme.primaryColor : theme.focusColor,
                    boxShadow: defaultBoxShadow(spreadRadius: 0, blurRadius: 0, shadowColor: Colors.transparent),
                  ),
                  child: MyText.labelLarge(
                    toneList[index],
                    color: controller.viewStyleselection.value == index ? theme.colorScheme.onPrimary : theme.colorScheme.onSurface,
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  List<TimetableItem<String>> generateItems() {
    final items = <TimetableItem<String>>[];
    var tkbs = controller.tkbs;
    for (var i = 0; i < tkbs.length; i++) {
      var tkb = tkbs[i];
      items.add(TimetableItem(
        tkb.batDau!,
        tkb.ketThuc!,
        data: "$i - ${tkb.tenMon!} (${tkb.maMon!}) - ${tkb.tenPhong!}",
      ));
    }
    return items;
  }
}