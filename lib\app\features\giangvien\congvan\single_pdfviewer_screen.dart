//import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:tvumobile/app/data/models/tms/congvan/cong_van_nhan.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/app_helpers.dart';

class SinglePdfViewerScreen extends StatelessWidget {
  SinglePdfViewerScreen(this.products, this.heroKey, {super.key});

  final int heroKey;
  final List<CvFile> products;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Get.theme;
    //List<String> paths = <String>["Home", 'Tin tức', 'Xem chi tiết tin'];
    return Scaffold(
        appBar: AppBar(
          // ignore: deprecated_member_use
          backgroundColor: theme.primaryColor.withOpacity(0.95),
          centerTitle: true,
          toolbarHeight: 42,
          leading: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Icon(
              LucideIcons.arrowLeft,
              color: theme.colorScheme.onPrimary,
            ),
          ),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              MyText.titleMedium(
                "File đính kèm".toUpperCase(),
                color: theme.colorScheme.onPrimary,
              ),
            ],
          ),
          actions: [
            Icon(
              LucideIcons.fileMinus,
              size: 16,
              color: theme.colorScheme.onPrimary,
            ),
            MySpacing.width(20),
          ],
        ),
        body: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(left: 10, right: 10),
              child: Column(children: [
                Row(children: [
                  Text("File: "),
                  Text(
                    products[heroKey].fileName.toString().getClips(40),
                  ),
                ])
              ]),
            ),
            Expanded(
              child: SfPdfViewer.network(
                "https://tms.tvu.edu.vn/${products[heroKey].getFilePath}",
              ),
            )
          ],
        ));
  }

  final List<Color> bgColor = [
    const Color(0xFFFFE2C2),
    const Color(0xFFD9839F),
    const Color(0xFFFFE2C2)
  ];
  //final _random = Random();

  void onTapItem(int index) {}
}
