import 'dart:convert';

class TmsChiTietCongVan {
  TmsChiTietCongVan({
    required this.id,
    required this.trichYeu,
    required this.cvFiles,
    required this.noiBanHanh,
    required this.category,
    required this.docType,
    required this.ngayBanHanh,
    required this.ngayCapNhat,
    required this.kyHieuGoc,
  });

  final int id;
  final String trichYeu;
  final List<CvFile> cvFiles;
  final NoiBanHanh? noiBanHanh;
  final Category? category;
  final Category? docType;
  final DateTime? ngayBanHanh;
  final DateTime? ngayCapNhat;
  final String kyHieuGoc;

  TmsChiTietCongVan copyWith({
    int? id,
    String? trichYeu,
    List<CvFile>? cvFiles,
    NoiBanHanh? noiBanHanh,
    Category? category,
    Category? docType,
    DateTime? ngayBanHanh,
    DateTime? ngayCapNhat,
    String? kyHieuGoc,
  }) {
    return TmsChiTietCongVan(
      id: id ?? this.id,
      trichYeu: trichYeu ?? this.trichYeu,
      cvFiles: cvFiles ?? this.cvFiles,
      noiBanHanh: noiBanHanh ?? this.noiBanHanh,
      category: category ?? this.category,
      docType: docType ?? this.docType,
      ngayBanHanh: ngayBanHanh ?? this.ngayBanHanh,
      ngayCapNhat: ngayCapNhat ?? this.ngayCapNhat,
      kyHieuGoc: kyHieuGoc ?? this.kyHieuGoc,
    );
  }

  factory TmsChiTietCongVan.fromJson(Map<String, dynamic> json) {
    return TmsChiTietCongVan(
      id: json["id"] ?? 0,
      trichYeu: json["trichYeu"] ?? "",
      cvFiles: json["cvFiles"] == null
          ? []
          : List<CvFile>.from(json["cvFiles"]!.map((x) => CvFile.fromJson(x))),
      noiBanHanh: json["noiBanHanh"] == null
          ? null
          : NoiBanHanh.fromJson(json["noiBanHanh"]),
      category:
          json["category"] == null ? null : Category.fromJson(json["category"]),
      docType:
          json["docType"] == null ? null : Category.fromJson(json["docType"]),
      ngayBanHanh: DateTime.tryParse(json["ngayBanHanh"] ?? ""),
      ngayCapNhat: DateTime.tryParse(json["ngayCapNhat"] ?? ""),
      kyHieuGoc: json["kyHieuGoc"] ?? "",
    );
  }

  factory TmsChiTietCongVan.fromJsonString(String source) =>
      TmsChiTietCongVan.fromJson(json.decode(source));

  Map<String, dynamic> toJson() => {
        "id": id,
        "trichYeu": trichYeu,
        "cvFiles": cvFiles.map((x) => x.toJson()).toList(),
        "noiBanHanh": noiBanHanh?.toJson(),
        "category": category?.toJson(),
        "docType": docType?.toJson(),
        "ngayBanHanh": ngayBanHanh?.toIso8601String(),
        "ngayCapNhat": ngayCapNhat?.toIso8601String(),
        "kyHieuGoc": kyHieuGoc,
      };

  @override
  String toString() {
    return "$id, $trichYeu, $cvFiles, $noiBanHanh, $category, $docType, $ngayBanHanh, $ngayCapNhat, $kyHieuGoc, ";
  }
}

class Category {
  Category({
    required this.ten,
    required this.id,
  });

  final String ten;
  final int id;

  Category copyWith({
    String? ten,
    int? id,
  }) {
    return Category(
      ten: ten ?? this.ten,
      id: id ?? this.id,
    );
  }

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      ten: json["ten"] ?? "",
      id: json["id"] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
        "ten": ten,
        "id": id,
      };

  @override
  String toString() {
    return "$ten, $id, ";
  }
}

class CvFile {
  CvFile({
    required this.id,
    required this.fileName,
    required this.filePath,
  });

  final int id;
  final String fileName;
  final String filePath;

  CvFile copyWith({
    int? id,
    String? fileName,
    String? filePath,
  }) {
    return CvFile(
      id: id ?? this.id,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
    );
  }

  factory CvFile.fromJson(Map<String, dynamic> json) {
    return CvFile(
      id: json["id"] ?? 0,
      fileName: json["fileName"] ?? "",
      filePath: json["filePath"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "fileName": fileName,
        "filePath": filePath,
      };

  @override
  String toString() {
    return "$id, $fileName, $filePath, ";
  }
}

class NoiBanHanh {
  NoiBanHanh({
    required this.id,
    required this.tenDonVi,
  });

  final int id;
  final String tenDonVi;

  NoiBanHanh copyWith({
    int? id,
    String? tenDonVi,
  }) {
    return NoiBanHanh(
      id: id ?? this.id,
      tenDonVi: tenDonVi ?? this.tenDonVi,
    );
  }

  factory NoiBanHanh.fromJson(Map<String, dynamic> json) {
    return NoiBanHanh(
      id: json["id"] ?? 0,
      tenDonVi: json["tenDonVi"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "tenDonVi": tenDonVi,
      };

  @override
  String toString() {
    return "$id, $tenDonVi, ";
  }
}
