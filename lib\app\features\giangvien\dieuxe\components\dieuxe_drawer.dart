import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/config/themes/custom_theme.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_home_controller.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

Widget DieuXeDrawer(BuildContext context, DieuXeHomeController controller) {
  var theme = Theme.of(context);
  return Drawer(
    backgroundColor: Colors.transparent, // Để nền của Drawer gốc trong suốt.
    child: Align(
      alignment: Alignment.topRight,
      child: MyContainer.none(
        margin:
            MySpacing.only(right: 16, top: MySpacing.safeAreaTop(context) + 16),
        borderRadiusAll: 4,
        // height: 500,
        clipBehavior: Clip.antiAliasWithSaveLayer,
        color: theme.scaffoldBackgroundColor,

        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                padding: MySpacing.only(left: 20, bottom: 0, top: 8, right: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    MySpacing.height(16),
                    MyContainer(
                      padding: MySpacing.fromLTRB(12, 4, 12, 4),
                      borderRadiusAll: 4,
                      color: theme.primaryColor.withOpacity(0.9),
                      borderColor: theme.primaryColor,
                      bordered: true,
                      child: MyText.bodyLarge("DANH SÁCH GIẤY ĐIỀU XE",
                          color: theme.colorScheme.onPrimary,
                          fontWeight: 700,
                          letterSpacing: 0.2),
                    ),
                  ],
                ),
              ),
              MySpacing.height(8),
              const Divider(
                thickness: 2,
              ),
              MySpacing.height(5),
              Container(
                margin: MySpacing.x(20),
                child: Column(
                  children: [
                    MySpacing.height(10),
                    // ---------------------- GIẤY ĐIỀU XE MỚI ---------------------
                    InkWell(
                      onTap: () {
                        controller.drawerClick("moi");
                      },
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      child: Container(
                        padding: MySpacing.all(8), // Khoảng cách bên trong Row
                        decoration: BoxDecoration(
                          color: controller.currentView.value == "moi"
                              ? CustomTheme.skyBlue
                                  .withAlpha(30) // Màu nền khi chọn
                              : Colors
                                  .transparent, // Không màu nền khi không chọn
                          borderRadius: BorderRadius.circular(8), // Bo góc
                        ),
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 6,
                              borderRadiusAll: 4,
                              color: CustomTheme.skyBlue.withAlpha(20),
                              child: Icon(
                                LucideIcons.filePlus2,
                                size: 20,
                                color: CustomTheme.skyBlue,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Giấy điều xe mới',
                                color: CustomTheme.skyBlue,
                                fontWeight: 700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    MySpacing.height(10),
                    // ------------------- GIẤY ĐIỀU XE ĐÃ DUYỆT -------------------
                    InkWell(
                      onTap: () {
                        controller.drawerClick("duyet");
                      },
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      child: Container(
                        padding: MySpacing.all(8), // Khoảng cách bên trong Row
                        decoration: BoxDecoration(
                          color: controller.currentView.value == "duyet"
                              ? CustomTheme.darkGreen
                                  .withAlpha(30) // Màu nền khi chọn
                              : Colors
                                  .transparent, // Không màu nền khi không chọn
                          borderRadius: BorderRadius.circular(8), // Bo góc
                        ),
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 6,
                              borderRadiusAll: 4,
                              color: CustomTheme.darkGreen.withAlpha(20),
                              child: Icon(
                                LucideIcons.fileCheck2,
                                size: 20,
                                color: CustomTheme.darkGreen,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Giấy điều xe đã duyệt',
                                color: CustomTheme.darkGreen,
                                fontWeight: 700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    MySpacing.height(10),
                    // -------------------- GIẤY ĐIỀU XE ĐÃ HỦY --------------------
                    InkWell(
                      onTap: () {
                        controller.drawerClick("huy");
                      },
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      child: Container(
                        padding: MySpacing.all(8), // Khoảng cách bên trong Row
                        decoration: BoxDecoration(
                          color: controller.currentView.value == "huy"
                              ? CustomTheme.red
                                  .withAlpha(30) // Màu nền khi chọn
                              : Colors
                                  .transparent, // Không màu nền khi không chọn
                          borderRadius: BorderRadius.circular(8), // Bo góc
                        ),
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 6,
                              borderRadiusAll: 4,
                              color: CustomTheme.red.withAlpha(20),
                              child: Icon(
                                LucideIcons.fileX2,
                                size: 20,
                                color: CustomTheme.red,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Giấy điều xe đã hủy',
                                color: CustomTheme.red,
                                fontWeight: 700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // ----------------- ĐƠN VỊ DUYỆT GIẤY ĐIỀU XE -----------------
                    if (controller.phanQuyen.value == "truongdonvi" ||
                        controller.phanQuyen.value == "phonghcth") ...[
                      Divider(
                        height: 10,
                        color: Colors.grey,
                      ),
                      InkWell(
                        onTap: () {
                          controller.drawerClick("donviduyet");
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Container(
                          padding:
                              MySpacing.all(8), // Khoảng cách bên trong Row
                          decoration: BoxDecoration(
                            color: controller.currentView.value == "donviduyet"
                                ? CustomTheme.purple
                                    .withAlpha(30) // Màu nền khi chọn
                                : Colors
                                    .transparent, // Không màu nền khi không chọn
                            borderRadius: BorderRadius.circular(8), // Bo góc
                          ),
                          child: Row(
                            children: [
                              MyContainer(
                                paddingAll: 6,
                                borderRadiusAll: 4,
                                color: CustomTheme.purple.withAlpha(20),
                                child: Icon(
                                  LucideIcons.building,
                                  size: 20,
                                  color: CustomTheme.purple,
                                ),
                              ),
                              MySpacing.width(16),
                              Expanded(
                                child: MyText.titleMedium(
                                  'Đơn vị duyệt',
                                  color: CustomTheme.purple,
                                  fontWeight: 700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      MySpacing.height(10)
                    ],
                    // --------------- PHÒNG HCTH DUYỆT GIẤY ĐIỀU XE ---------------
                    if (controller.phanQuyen.value == "phonghcth")
                      InkWell(
                        onTap: () {
                          controller.drawerClick("hcthduyet");
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Container(
                          padding:
                              MySpacing.all(8), // Khoảng cách bên trong Row
                          decoration: BoxDecoration(
                            color: controller.currentView.value == "hcthduyet"
                                ? CustomTheme.peach
                                    .withAlpha(30) // Màu nền khi chọn
                                : Colors
                                    .transparent, // Không màu nền khi không chọn
                            borderRadius: BorderRadius.circular(8), // Bo góc
                          ),
                          child: Row(
                            children: [
                              MyContainer(
                                paddingAll: 6,
                                borderRadiusAll: 4,
                                color: CustomTheme.peach.withAlpha(20),
                                child: Icon(
                                  LucideIcons.building2,
                                  size: 20,
                                  color: CustomTheme.peach,
                                ),
                              ),
                              MySpacing.width(16),
                              Expanded(
                                child: MyText.titleMedium(
                                  'Phòng HCTH duyệt',
                                  color: CustomTheme.peach,
                                  fontWeight: 700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    MySpacing.height(10),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
