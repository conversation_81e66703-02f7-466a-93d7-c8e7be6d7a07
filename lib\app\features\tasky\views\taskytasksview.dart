import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lazy_load_scrollview/lazy_load_scrollview.dart';
import 'package:tvumobile/app/features/tasky/components/build_tasklist.dart';
import 'package:tvumobile/app/features/tasky/components/empty_widget.dart';
import 'package:tvumobile/app/features/tasky/components/task_monthyear_selector.dart';
import 'package:tvumobile/app/features/tasky/components/tasky_drawer.dart';
import 'package:tvumobile/app/features/tasky/components/taskyappbar.dart';
import 'package:tvumobile/app/features/tasky/controllers/taskytaskview_controller.dart';

class TaskyTaskView extends GetView<TaskyTaskViewController> {
  const TaskyTaskView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      key: controller.scaffoldKey,
      appBar: buildAppBarTaskDetail(context, controller),
      endDrawer: buildTaskyDrawer(context, controller),
      body: Obx(() {
        // if (controller.lastPage && controller.allTasks.isEmpty) {
        //   return Center(
        //     child: MyText.titleLarge("Không có dữ liệu"),
        //   );
        // }
        return Column(children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 10, 16, 0),
            child: Column(
              children: [
                buildYearSelector(theme, controller),
                buildMonthSelector(theme, controller),
                SizedBox(
                  height: 5,
                ),
                buildStatusSelector(theme, controller),
                SizedBox(height: 5),
                // Padding(
                //   padding: const EdgeInsets.only(top: 5),
                //   child: buildTaskFilter(context),
                // ),
              ],
            ),
          ),
          Expanded(
            child: controller.isLoading.value
                ? const Center(
                    child:
                        CircularProgressIndicator(), // Hiển thị spinner khi đang tải
                  )
                : controller.allTasks.isEmpty
                    ? EmptyWidget(
                        message: 'Bạn chưa có công việc tháng này',
                        imageAsset: 'tasky/no_inbox.png',
                      )
                    : LazyLoadScrollView(
                        onEndOfPage: controller.loadMoreTasks,
                        isLoading: controller.lastPage,
                        child: RefreshIndicator(
                          onRefresh: () =>
                              Future.sync(() => controller.refreshTasks()),
                          child: ListView.builder(
                            padding: const EdgeInsets.only(top: 5, bottom: 5),
                            itemCount: controller.allTasks.length,
                            itemBuilder: (context, index) {
                              final task = controller.allTasks[index];
                              return buildTaskList(context, task);
                            },
                          ),
                        ),
                      ),
          ),
        ]);
      }),
    );
  }

  // --------------------------- THANH TÌM KIẾM --------------------------- //
  buildTaskFilter(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return SizedBox(
      height: 50,
      width: double.infinity,
      child: Padding(
        padding: const EdgeInsets.only(top: 5.0, bottom: 5.0),
        child: TextFormField(
            controller: controller.searchController,
            onChanged: (value) {
              //debounce search when typing using timer
              if (controller.debounce?.isActive ?? false) {
                controller.debounce?.cancel();
              }
              controller.debounce =
                  Timer(const Duration(milliseconds: 800), () {
                if (controller.searchController.text.length > 2) {
                  controller.loadTasksBySearch(value);
                }
                if (controller.searchController.text.isEmpty) {
                  controller.refreshTasks();
                }
              });
            },
            decoration: InputDecoration(
                hintText: "Tìm kiếm",
                prefixIcon: Icon(Icons.search),
                suffixIcon: controller.searchController.text.length > 2
                    ? IconButton(
                        icon: Icon(Icons.clear),
                        onPressed: () {
                          controller.searchController.clear();
                          controller.refreshTasks();
                        },
                      )
                    : null,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                fillColor: theme.colorScheme.surface,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide:
                      BorderSide(color: theme.primaryColor.withOpacity(0.1)),
                ))),
      ),
    );
  }
}
