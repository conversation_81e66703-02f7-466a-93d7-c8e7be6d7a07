// document_sign_screen.dart
// ignore_for_file: unused_field, no_leading_underscores_for_local_identifiers

import 'dart:io';
// ignore: library_prefixes
import 'package:image/image.dart' as IMG;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:signature/signature.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:tvumobile/app/features/kyso/components/intro.dart';
import 'package:tvumobile/app/features/kyso/enums/resize_mode.dart';
import 'package:tvumobile/app/features/kyso/models/signature_data.dart';
import 'package:tvumobile/app/features/kyso/utils/helpers.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'dart:async';
import 'dart:math' as math;

class DocumentSignScreen extends StatefulWidget {
  const DocumentSignScreen({
    super.key,
    required this.pdfUrl,
    required this.signatureUrl,
    required this.availableSignatures,
    this.fullName, // Add optional fullname parameter
    this.department, // Add optional department parameter
    this.personposition, // Add optional position parameter
  });

  final String pdfUrl;
  final String signatureUrl;
  final List<String> availableSignatures;
  final String? fullName; // Optional fullname parameter
  final String? department; // Optional department parameter
  final String? personposition; // Optional position parameter

  @override
  State<DocumentSignScreen> createState() => DocumentSignScreenState();
}

class DocumentSignScreenState extends State<DocumentSignScreen> {
  late String pdfUrl;
  late String signatureUrl;
  Uint8List? pdfBytes;
  Uint8List? signatureImageBytes;

  List<SignatureData> signatures = [];
  ResizeMode _resizeMode = ResizeMode.none;

  static const double defaultSignatureImageHeightPdfPoints = 50;
  static const double defaultSignatureImageWidthPdfPoints = 100;
  Uint8List? signatureImageBytesPlaceholder;
  Size? _imageSize;
  final Completer<Size> _imageSizeCompleter = Completer<Size>();
  Size? _pdfViewSize;
  late Size pdfPageSizePdfPoints;
  final PdfViewerController _pdfViewerController = PdfViewerController();
  UniqueKey pdfViewerKey = UniqueKey();
  int currentPage = 1;
  double _lastSignatureWidthPdfPoints = defaultSignatureImageWidthPdfPoints;
  double _lastSignatureHeightPdfPoints = defaultSignatureImageHeightPdfPoints;
  static const double _paddingTop = 120;
  final textPageNumController = TextEditingController();
  late List<String> sampleSignatureUrls;

  // State for displaying additional info
  bool displayFullName = false;
  bool displayDepartment = false;
  bool displayPosition = false;
  bool displayDatetime = false;
  bool stampStyle = false; // Re-introduce _stampStyle variable
  // Add these properties to track zoom and position changes
  double _previousZoomLevel = 1.0;
  //final Offset _lastPanOffset = Offset.zero;

  @override
  void initState() {
    super.initState();
    pdfUrl = widget.pdfUrl;
    signatureUrl = widget.signatureUrl;
    sampleSignatureUrls = widget.availableSignatures;
    _loadPdf();
    _loadSignatureImageFromUrl();
    _loadPlaceholderSignatureImage();
    _previousZoomLevel = 1.0;
  }

  List<int> resizeKeepAspect(Uint8List imgdata, int newWidth, int newHeight) {
    int originalWidth = 0;
    int originalHeight = 0;
    try {
      IMG.Image img = IMG.decodeImage(imgdata)!;
      originalWidth = img.width;
      originalHeight = img.height;
    } catch (e) {
      debugPrint('Error resizing image: $e');
    }

    double aspectRatio = originalWidth / originalHeight;

    newHeight = (newWidth / aspectRatio).round();

    return [newWidth, newHeight];
  }

  // Load placeholder image from assets
  Future<void> _loadPlaceholderSignatureImage() async {
    try {
      final ByteData signatureByteData =
          await rootBundle.load('assets/images/app_icon_150.png');
      signatureImageBytesPlaceholder = signatureByteData.buffer.asUint8List();
      // Load image size for aspect ratio calculation if needed later
      final provider = const AssetImage('assets/images/app_icon_150.png');
      final stream = provider.resolve(ImageConfiguration.empty);
      stream.addListener(ImageStreamListener((ImageInfo image, _) {
        if (!_imageSizeCompleter.isCompleted) {
          _imageSizeCompleter.complete(
            Size(image.image.width.toDouble(), image.image.height.toDouble()),
          );
        }
        _imageSize =
            Size(image.image.width.toDouble(), image.image.height.toDouble());
      }));
      await _imageSizeCompleter.future;
    } catch (e) {
      debugPrint('Error loading placeholder: $e');
    }
  }

  // Load signature image from URL, fallback to placeholder on error
  Future<void> _loadSignatureImageFromUrl() async {
    try {
      signatureImageBytes = await networkImageToByte(signatureUrl);
    } catch (e) {
      debugPrint('Error loading signature from URL: $e');
      signatureImageBytes =
          signatureImageBytesPlaceholder; // Fallback to placeholder
    } finally {
      setState(() {}); // Rebuild to show signature or placeholder
    }
  }

  // Load PDF from URL
  Future<void> _loadPdf() async {
    try {
      pdfBytes = await networkImageToByte(pdfUrl);
      final document = PdfDocument(inputBytes: pdfBytes);
      pdfPageSizePdfPoints = Size(
        document.pageSettings.size.width,
        document.pageSettings.size.height,
      );
      document.dispose();
      setState(() =>
          pdfViewerKey = UniqueKey()); // Rebuild PDF Viewer with loaded PDF
    } catch (e) {
      debugPrint('Error loading PDF: $e');
      // Consider showing an error message to the user
    }
  }

  // Calculate scale factor based on current PDF viewer size and zoom
  double get scaleFactor {
    if (_pdfViewSize == null) return 1.0;
    final currentZoom = _pdfViewerController.zoomLevel;
    final scaleX = _pdfViewSize!.width / pdfPageSizePdfPoints.width;
    final scaleY = _pdfViewSize!.height / pdfPageSizePdfPoints.height;
    return math.min(scaleX, scaleY) * currentZoom;
  }

  // Add a new signature at the tapped position
  void _addSignature(PdfGestureDetails details) {
    if (pdfBytes == null) return;
    if (_pdfViewSize == null) return;
    print("object: $details");
    final widthPdf = _lastSignatureWidthPdfPoints;
    final heightPdf = _lastSignatureHeightPdfPoints;
    final aspectRatio = widthPdf / heightPdf;

    final screenWidth = widthPdf * scaleFactor;
    final screenHeight = heightPdf * scaleFactor;
    currentPage = details.pageNumber;
    setState(() {
      signatures = [
        // Replace current signatures with a new one, if only one signature is allowed
        SignatureData(
          position:
              Offset(details.position.dx, details.position.dy + _paddingTop),
          width: screenWidth,
          height: screenHeight,
          aspectRatio: aspectRatio,
          pdfPositionPoints: details.pagePosition,
          pdfWidthPoints: widthPdf,
          pdfHeightPoints: heightPdf,
          pageNumber: details.pageNumber,
          zoomLevel: _pdfViewerController.zoomLevel,
          fullName: displayFullName ? widget.fullName : null,
          department: displayDepartment ? widget.department : null,
          personposition: displayPosition ? widget.personposition : null,
          datetime: displayDatetime
              ? DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())
              : null,
          displayFullName: displayFullName,
          displayDepartment: displayDepartment,
          displayPosition: displayPosition,
          displayDatetime: displayDatetime,
          stampStyle: stampStyle, // Pass _stampStyle to SignatureData
          signatureBytes: signatureImageBytes, // Pass signature bytes
        ),
      ];
      // Keep the last added signature size as default for next additions
    });
  }

  // ignore: unused_element
  Future<Uint8List> _readFileByte(String filePath) async {
    Uri myUri = Uri.parse(filePath);
    File audioFile = File.fromUri(myUri);
    Uint8List bytes = audioFile.readAsBytesSync();
    return bytes;
  }

  // Update signature position based on drag delta
  void _updateSignaturePosition(int index, Offset delta) {
    setState(() {
      signatures[index] = signatures[index].copyWith(
        position: signatures[index].position + delta,
        pdfPositionPoints: signatures[index].pdfPositionPoints! +
            Offset(delta.dx / scaleFactor, delta.dy / scaleFactor),
      );
    });
  }

  // Update signature size based on resize delta and mode
  void _updateSignatureSize(int index, Offset delta, ResizeMode resizeMode) {
    final signature = signatures[index];
    double newWidth = signature.width;
    double newHeight = signature.height;
    Offset newPosition = signature.position;

    switch (resizeMode) {
      case ResizeMode.topLeft:
        newWidth -= delta.dx;
        newHeight -= delta.dy;
        newPosition += delta;
        break;
      case ResizeMode.topRight:
        newWidth += delta.dx;
        newHeight -= delta.dy;
        newPosition += Offset(0, delta.dy);
        break;
      case ResizeMode.bottomLeft:
        newWidth -= delta.dx;
        newHeight += delta.dy;
        newPosition += Offset(delta.dx, 0);
        break;
      case ResizeMode.bottomRight:
        newWidth += delta.dx;
        newHeight += delta.dy;
        break;
      case ResizeMode.none:
        return;
    }

    // Maintain aspect ratio and ensure minimum size
    if (newWidth < 20 || newHeight < 20) return;

    if (delta.dx.abs() > delta.dy.abs()) {
      newHeight = newWidth / signature.aspectRatio;
      if (resizeMode == ResizeMode.topLeft ||
          resizeMode == ResizeMode.topRight) {
        newPosition =
            signature.position + Offset(0, signature.height - newHeight);
      } else if (resizeMode == ResizeMode.bottomLeft) {
        newPosition =
            signature.position + Offset(signature.width - newWidth, 0);
      }
    } else {
      newWidth = newHeight * signature.aspectRatio;
      if (resizeMode == ResizeMode.topLeft ||
          resizeMode == ResizeMode.bottomLeft) {
        newPosition =
            signature.position + Offset(signature.width - newWidth, 0);
      } else if (resizeMode == ResizeMode.topRight) {
        newPosition =
            signature.position + Offset(0, signature.height - newHeight);
      }
    }

    setState(() {
      signatures[index] = signature.copyWith(
        position: newPosition,
        width: newWidth,
        height: newHeight,
        pdfWidthPoints: newWidth / scaleFactor,
        pdfHeightPoints: newHeight / scaleFactor,
      );
      _lastSignatureWidthPdfPoints = newWidth / scaleFactor;
      _lastSignatureHeightPdfPoints = newHeight / scaleFactor;
    });
  }

  // Confirm signatures and embed them into the PDF
  Future<void> _confirmSignatures() async {
    if (signatures.isEmpty) {
      showTopSnackBar('Không có [ảnh] chữ ký nào được thêm vào',
          isSuccess: false);
      return;
    }

    final signature = signatures.first; // Assuming only one signature for now
    final document = PdfDocument(inputBytes: pdfBytes);
    final page = document.pages[signature.pageNumber! - 1];
    Uint8List imageBytes =
        signature.signatureBytes ?? signatureImageBytesPlaceholder!;
    List<int> sized = resizeKeepAspect(imageBytes,
        signature.pdfWidthPoints!.toInt(), signature.pdfHeightPoints!.toInt());

    print("original: ${signature.pdfWidthPoints} => ${sized[0]}, ${sized[1]}");
    print("signature.aspectRatio: ${signature.aspectRatio}");
    final encryptedByteData =
        await rootBundle.load('assets/fonts/times/times.ttf');
    // Use smaller font size to match UI better
    PdfFont font = PdfTrueTypeFont(encryptedByteData.buffer.asUint8List(), 10,
        style: PdfFontStyle.regular);

    // Calculate exact position in PDF coordinates
    double pdfX = signature.pdfPositionPoints!.dx;
    double pdfY = signature.pdfPositionPoints!.dy;
    print("_previousZoomLevel: $_previousZoomLevel");
    if (_previousZoomLevel > 1) {
      pdfX = pdfX - 8;
      pdfY = pdfY + 6;
    } else {
      pdfX = pdfX + 10;
      pdfY = pdfY + 12;
    }
    double pdfWidth = signature.pdfWidthPoints!;
    double pdfHeight = signature.pdfHeightPoints!;
    if (signature.stampStyle) {
      String stamp = "";
      // Add full name if available and selected or stampStyle is true
      if ((signature.displayFullName == true &&
              signature.fullName != null &&
              signature.fullName!.isNotEmpty) ||
          signature.stampStyle) {
        stamp +=
            "Người ký: ${signature.fullName ?? widget.fullName ?? '________'}";
      }
      // Add department if available and selected or stampStyle is true
      if ((signature.displayDepartment == true &&
              signature.department != null &&
              signature.department!.isNotEmpty) ||
          signature.stampStyle) {
        stamp +=
            "\nĐơn vị: ${signature.department ?? widget.department ?? '________'}";
      }
      // Add position if available and selected or stampStyle is true
      if ((signature.displayPosition == true &&
              signature.personposition != null &&
              signature.personposition!.isNotEmpty) ||
          signature.stampStyle) {
        stamp +=
            "\nChức vụ: ${signature.personposition ?? widget.personposition ?? '________'}";
      }
      // Add datetime if selected or stampStyle is true
      if ((signature.displayDatetime == true &&
              signature.datetime != null &&
              signature.datetime!.isNotEmpty) ||
          signature.stampStyle) {
        stamp +=
            "\nNgày Ký: ${signature.datetime ?? DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}";
      }

      // Draw red border for stamp style - use the exact dimensions from UI
      page.graphics.drawRectangle(
          bounds: Rect.fromLTWH(pdfX, pdfY, pdfWidth, pdfHeight),
          pen: PdfPen(PdfColor(200, 0, 0), width: 1));

      // Draw text with proper padding inside the rectangle
      PdfTextElement(
        text: stamp,
        font: font,
        brush: PdfSolidBrush(PdfColor(200, 0, 0)),
      ).draw(
          page: page,
          bounds:
              Rect.fromLTWH(pdfX + 8, pdfY + 8, pdfWidth - 16, pdfHeight - 16));
    } else {
      // For regular signature, use the exact dimensions from the UI
      page.graphics.drawImage(
        PdfBitmap(imageBytes),
        Rect.fromLTWH(pdfX, pdfY, pdfWidth, pdfHeight),
      );

      // Calculate text position below the signature
      double textYPosition = pdfY + pdfHeight + 2;
      double textFontSize = 8; // Smaller font for text to match UI better

      // Add text information below the signature if needed
      if (signature.displayFullName == true &&
          signature.fullName != null &&
          signature.fullName!.isNotEmpty) {
        PdfTextElement(
          text: signature.fullName!,
          font: PdfTrueTypeFont(
              encryptedByteData.buffer.asUint8List(), textFontSize),
          brush: PdfSolidBrush(PdfColor(0, 0, 0)),
        ).draw(
            page: page,
            bounds: Rect.fromLTWH(pdfX, textYPosition, pdfWidth, 12));
        textYPosition += 10;
      }

      if (signature.displayDatetime == true &&
          signature.datetime != null &&
          signature.datetime!.isNotEmpty) {
        PdfTextElement(
          text: "Ngày ký: ${signature.datetime!}",
          font: PdfTrueTypeFont(
              encryptedByteData.buffer.asUint8List(), textFontSize),
          brush: PdfSolidBrush(PdfColor(0, 0, 0)),
        ).draw(
            page: page,
            bounds: Rect.fromLTWH(pdfX, textYPosition, pdfWidth, 12));
        textYPosition += 10;
      }

      if (signature.displayPosition == true &&
          signature.personposition != null &&
          signature.personposition!.isNotEmpty) {
        PdfTextElement(
          text: "Chức danh: ${signature.personposition!}",
          font: PdfTrueTypeFont(
              encryptedByteData.buffer.asUint8List(), textFontSize),
          brush: PdfSolidBrush(PdfColor(0, 0, 0)),
        ).draw(
            page: page,
            bounds: Rect.fromLTWH(pdfX, textYPosition, pdfWidth, 12));
        textYPosition += 10;
      }

      if (signature.displayDepartment == true &&
          signature.department != null &&
          signature.department!.isNotEmpty) {
        PdfTextElement(
          text: "Đơn vị: ${signature.department!}",
          font: PdfTrueTypeFont(
              encryptedByteData.buffer.asUint8List(), textFontSize),
          brush: PdfSolidBrush(PdfColor(0, 0, 0)),
        ).draw(
            page: page,
            bounds: Rect.fromLTWH(pdfX, textYPosition, pdfWidth, 12));
      }
    }

    setState(() {
      pdfBytes = Uint8List.fromList(document.saveSync());
      pdfViewerKey = UniqueKey(); // Rebuild PDF Viewer to show signed PDF
      signatures.clear(); // Clear signatures after confirmation
    });
    document.dispose();
    showTopSnackBar('Chữ ký đã được thêm vào tài liệu', isSuccess: true);
  }

  // ignore: unused_element
  Future<void> _confirmSignatures2() async {
    if (signatures.isEmpty) {
      showTopSnackBar('Không có [ảnh] chữ ký nào được thêm vào',
          isSuccess: false);
      return;
    }

    final signature = signatures.first; // Assuming only one signature for now
    final document = PdfDocument(inputBytes: pdfBytes);
    final page = document.pages[signature.pageNumber! - 1];
    Uint8List imageBytes =
        signature.signatureBytes ?? signatureImageBytesPlaceholder!;

    final encryptedByteData =
        await rootBundle.load('assets/fonts/times/times.ttf');
    PdfFont font = PdfTrueTypeFont(encryptedByteData.buffer.asUint8List(), 14,
        style: PdfFontStyle.regular);

    if (signature.stampStyle) {
      String stamp = "";
      //"Người Ký: Nguyễn Văn Vũ Linh \nNgày Ký: xxx \nLý Do: xxx \nĐịa Điểm: xxx";
      // Add full name if available and selected or stampStyle is true
      if ((signature.displayFullName == true &&
              signature.fullName != null &&
              signature.fullName!.isNotEmpty) ||
          signature.stampStyle) {
        stamp +=
            "Người ký: ${signature.fullName ?? widget.fullName ?? '________'}";
      }
      // Add department if available and selected or stampStyle is true
      if ((signature.displayDepartment == true &&
              signature.department != null &&
              signature.department!.isNotEmpty) ||
          signature.stampStyle) {
        stamp +=
            "\nĐơn vị: ${signature.department ?? widget.department ?? '________'}";
      }
      // Add position if available and selected or stampStyle is true
      if ((signature.displayPosition == true &&
              signature.personposition != null &&
              signature.personposition!.isNotEmpty) ||
          signature.stampStyle) {
        stamp +=
            "\nChức vụ: ${signature.personposition ?? widget.personposition ?? '________'}";
      }
      // Add datetime if selected or stampStyle is true
      if ((signature.displayDatetime == true &&
              signature.datetime != null &&
              signature.datetime!.isNotEmpty) ||
          signature.stampStyle) {
        stamp +=
            "\nNgày Ký: ${signature.datetime ?? DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}";
      }
      Size txtSize = font.measureString(stamp);

      // Draw red border for stamp style
      page.graphics.drawRectangle(
          bounds: Rect.fromLTWH(
              signature.pdfPositionPoints!.dx, // Add some padding
              signature.pdfPositionPoints!.dy, // Add some padding
              txtSize.width + 16, // Add some padding
              txtSize.height + 16), // Adjust height for text and padding
          pen: PdfPen(PdfColor(200, 0, 0), width: 1)); // Red color

      PdfTextElement(
        text: stamp,
        font: font,
        brush: PdfSolidBrush(PdfColor(200, 0, 0)),
      ).draw(
          page: page,
          bounds: Rect.fromLTWH(
              signature.pdfPositionPoints!.dx + 8,
              signature.pdfPositionPoints!.dy + 8,
              page.getClientSize().width / 2,
              page.getClientSize().height / 2));
    }

    if (!signature.stampStyle) {
      page.graphics.drawImage(
        PdfBitmap(imageBytes),
        Rect.fromLTWH(
          signature.pdfPositionPoints!.dx,
          signature.pdfPositionPoints!.dy,
          signature.pdfWidthPoints! > 150 ? 150 : signature.pdfWidthPoints!,
          signature.pdfHeightPoints! > 50 ? 50 : signature.pdfHeightPoints!,
        ),
      );
      if (signature.displayFullName == true &&
          signature.fullName != null &&
          signature.fullName!.isNotEmpty) {}
    }

    //double textYPosition = signature.pdfPositionPoints!.dy + 2;

    setState(() {
      pdfBytes = Uint8List.fromList(document.saveSync());
      pdfViewerKey = UniqueKey(); // Rebuild PDF Viewer to show signed PDF
      signatures.clear(); // Clear signatures after confirmation
    });
    document.dispose();
    showTopSnackBar('Chữ ký đã được thêm vào tài liệu', isSuccess: true);
  }

  void showTopSnackBar(
    String message, {
    bool isSuccess = true,
    Color? color,
    Duration? duration,
  }) {
    final backgroundColor =
        color ?? (isSuccess ? Colors.green[600] : Colors.red[600]);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        duration: duration ?? const Duration(milliseconds: 500),
        backgroundColor: Colors.transparent,
        content: Align(
          alignment: Alignment.topCenter,
          child: Container(
            height: 100,
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(20),
            width: double.maxFinite,
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: const BorderRadius.all(
                Radius.circular(15),
              ),
            ),
            child: Text(
              message,
              style: const TextStyle(
                fontSize: 20,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // void _navigateToIntroGuide() {
  //   Navigator.push(
  //     context,
  //     MaterialPageRoute(builder: (context) => const IntroGuideScreen()),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('PDF Signer'),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => showHelpDialog(context),
            // Navigate to intro guide screen
          ),
          IconButton(
            icon: const Icon(Icons.check),
            tooltip: 'Confirm Signature',
            onPressed: _confirmSignatures,
          ),
        ],
      ),
      body: pdfBytes == null
          ? const Center(child: CircularProgressIndicator())
          : Stack(
              children: [
                buildHeader(),
                Positioned(
                  bottom: 40,
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        MyButton.small(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          backgroundColor: theme.cardColor,
                          child: MyText.bodyMedium(
                            "Hủy ký",
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        SizedBox(width: 20),
                        MyButton.small(
                          onPressed: () {
                            if (signatures.isNotEmpty) {
                              signatures.removeLast();
                            }
                            _loadPdf();
                          },
                          backgroundColor: theme.colorScheme.primaryContainer
                              .withOpacity(0.5),
                          child: MyText.bodyMedium(
                            "Làm lại",
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                        ),
                        SizedBox(width: 20),
                        MyButton.small(
                            onPressed: _confirmSignatures,
                            backgroundColor: theme.colorScheme.primaryContainer,
                            child: MyText.bodyMedium(
                              "Xác nhận ký",
                              color: theme.colorScheme.onPrimaryContainer,
                            )),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: _paddingTop, bottom: 82),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      // Update PDF view size on layout changes
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (_pdfViewSize != constraints.biggest) {
                          _pdfViewSize = constraints.biggest;
                        }
                      });
                      return SfPdfViewer.memory(
                        pdfBytes!,
                        key: pdfViewerKey,
                        controller: _pdfViewerController,
                        onTap: _addSignature,
                        onDocumentLoaded: (details) {
                          // Navigate to the page where signature was added, or last page if no signature
                          if (currentPage > 1) {
                            _pdfViewerController.jumpToPage(currentPage);
                          } else {
                            _pdfViewerController.lastPage();
                          }
                        },
                        onPageChanged: (details) {
                          currentPage = details.newPageNumber;
                          setState(() {});
                        },
                        onZoomLevelChanged: (details) {
                          if (signatures.isNotEmpty) {
                            _updateSignaturesOnZoom(details.newZoomLevel);
                          }
                          setState(() {
                            _previousZoomLevel = details.newZoomLevel;
                          });
                        },
                      );
                    },
                  ),
                ),
                // Render signature widgets on top of PDF viewer
                ...signatures
                    .map((signature) => _buildSignatureWidget(signature)),
              ],
            ),
    );
  }

  final _signatureController = SignatureController();

  // Add these methods to handle zoom and pan updates
  void _updateSignaturesOnZoom(double newZoomLevel) {
    if (signatures.isEmpty) return;
    signatures.removeAt(0);
    return;
    // Calculate zoom ratio
    // ignore: dead_code
    double zoomRatio = newZoomLevel / _previousZoomLevel;

    for (int i = 0; i < signatures.length; i++) {
      SignatureData signature = signatures[i];

      // Only update signatures on the current page
      if (signature.pageNumber == currentPage) {
        // Calculate new width and height
        double newWidth = signature.width * zoomRatio;
        double newHeight = signature.height * zoomRatio;

        // Calculate new position (keeping the signature centered at the same relative position)
        //Offset newPosition = Offset(
        //    signature.position.dx * (zoomRatio == 1 ? 0.5 : zoomRatio),
        //    signature.position.dy * (zoomRatio == 1 ? 0.5 : zoomRatio));

        // Update signature with new position and dimensions
        signatures[i] = signature.copyWith(
          //position: newPosition,
          width: newWidth,
          height: newHeight,
          zoomLevel: newZoomLevel,
        );
      }
    }
  }

  // bool _stampStyle = false;
  showSignatureDialogue() {
    var theme = Theme.of(context);
    bool _displayFullName = displayFullName;
    bool _displayDepartment = displayDepartment;
    bool _displayPosition = displayPosition;
    bool _displayDatetime = displayDatetime;
    stampStyle = false; // Reset _stampStyle at the beginning of the dialog

    showDialog(
        context: context,
        builder: (c) {
          return StatefulBuilder(builder: (context, setStateDialog) {
            return Dialog(
              insetPadding: EdgeInsets.symmetric(horizontal: 15, vertical: 80),
              child: Container(
                width: 400,
                padding: EdgeInsets.all(20),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          InkWell(
                            onTap: () {
                              stampStyle =
                                  false; // Set _stampStyle to false for "Vẽ mới"
                              Navigator.pop(c);
                              _showDrawSignatureDialog();
                            },
                            child: Column(
                              children: [
                                Icon(LucideIcons.signature,
                                    size: 64, color: theme.primaryColor),
                                SizedBox(height: 5),
                                MyText.bodyLarge(
                                  'Vẽ mới\nchữ ký',
                                  color: theme.colorScheme.onSurface,
                                  textAlign: TextAlign.center,
                                )
                              ],
                            ),
                          ),
                          SizedBox(height: 40),
                          InkWell(
                            onTap: () {
                              stampStyle =
                                  false; // Set _stampStyle to false for "Chọn ảnh"
                              Navigator.pop(c);
                              _showSelectSignatureUrlDialog();
                            },
                            child: Column(
                              children: [
                                Icon(LucideIcons.image,
                                    size: 64, color: theme.primaryColor),
                                SizedBox(height: 5),
                                MyText.bodyLarge(
                                  'Chọn ảnh\nchữ ký',
                                  color: theme.colorScheme.onSurface,
                                  textAlign: TextAlign.center,
                                )
                              ],
                            ),
                          ),
                          SizedBox(height: 40),
                          InkWell(
                            onTap: () {
                              stampStyle =
                                  true; // Set _stampStyle to true for "Thông tin chữ ký"
                              // Automatically check all info checkboxes for stamp style
                              _displayFullName = true;
                              _displayDepartment = true;
                              _displayPosition = true;
                              _displayDatetime = true;
                              setStateDialog(
                                  () {}); // Update dialog UI immediately to reflect checkbox changes
                              Navigator.pop(c);
                            },
                            child: Column(
                              children: [
                                Icon(LucideIcons.imagePlus,
                                    size: 64, color: theme.primaryColor),
                                SizedBox(height: 5),
                                MyText.bodyLarge(
                                  'Thông tin\nchữ ký',
                                  color: theme.colorScheme.onSurface,
                                  textAlign: TextAlign.center,
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                      Divider(),
                      SizedBox(height: 10),
                      MyText.titleMedium('Thông tin hiển thị:',
                          fontWeight: 600),
                      CheckboxListTile(
                        title: MyText.bodyMedium('Họ và tên'),
                        value: _displayFullName,
                        onChanged: (bool? value) {
                          setStateDialog(() {
                            _displayFullName = value ?? false;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      CheckboxListTile(
                        title: MyText.bodyMedium('Phòng ban'),
                        value: _displayDepartment,
                        onChanged: (bool? value) {
                          setStateDialog(() {
                            _displayDepartment = value ?? false;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      CheckboxListTile(
                        title: MyText.bodyMedium('Chức vụ'),
                        value: _displayPosition,
                        onChanged: (bool? value) {
                          setStateDialog(() {
                            _displayPosition = value ?? false;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      CheckboxListTile(
                        title: MyText.bodyMedium('Thời gian ký'),
                        value: _displayDatetime,
                        onChanged: (bool? value) {
                          setStateDialog(() {
                            _displayDatetime = value ?? false;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          MyButton.small(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child:
                                MyText.bodyMedium('Huỷ', color: Colors.white),
                          ),
                          SizedBox(width: 10),
                          MyButton.small(
                            onPressed: () {
                              displayFullName = _displayFullName;
                              displayDepartment = _displayDepartment;
                              displayPosition = _displayPosition;
                              displayDatetime = _displayDatetime;
                              Navigator.pop(context);
                              setState(() {
                                stampStyle =
                                    stampStyle; // Update main widget's _stampStyle
                              });
                            },
                            child:
                                MyText.bodyMedium('Chọn', color: Colors.white),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          });
        });
  }

  _showDrawSignatureDialog() {
    showDialog(
        context: context,
        builder: (c) {
          return Dialog(
            insetPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 100),
            child: Container(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Signature(
                    controller: _signatureController,
                    width: 300,
                    height: 300,
                    backgroundColor: Colors.yellow.withOpacity(0.2),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      TextButton(
                        onPressed: () {
                          _signatureController.clear();
                        },
                        child:
                            Text('Xoá', style: TextStyle(color: Colors.blue)),
                      ),
                      TextButton(
                        onPressed: () async {
                          final exportController = SignatureController(
                            penStrokeWidth: 2,
                            penColor: Colors.black,
                            exportBackgroundColor: Colors.white,
                            points: _signatureController.points,
                          );

                          signatureImageBytes =
                              await exportController.toPngBytes();

                          exportController.dispose();
                          setState(() {});
                          Navigator.pop(context);
                        },
                        child: Text('Xác nhận',
                            style: TextStyle(color: Colors.blue)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        });
  }

  _showSelectSignatureUrlDialog() {
    showDialog(
        context: context,
        builder: (c) {
          return Dialog(
            insetPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            child: Container(
              height: 300,
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  MyText.titleMedium('Chọn chữ ký từ URL', fontWeight: 700),
                  SizedBox(height: 10),
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: sampleSignatureUrls.length,
                      itemBuilder: (context, index) {
                        final url = sampleSignatureUrls[index];
                        return ListTile(
                          leading: Image.network(
                            url,
                            width: 200,
                            errorBuilder: (context, error, stackTrace) => Icon(
                              Icons.error_outline,
                              color: Colors.deepOrangeAccent.shade200,
                            ),
                            fit: BoxFit.contain,
                          ),
                          title: Text('Signature ${index + 1}'),
                          onTap: () async {
                            try {
                              signatureImageBytes =
                                  await networkImageToByte(url);
                              setState(() {});
                              Navigator.pop(context);
                            } catch (e) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                    content:
                                        Text('Không thể tải chữ ký từ URL')),
                              );
                              Navigator.pop(context);
                            }
                          },
                        );
                      },
                    ),
                  ),
                  SizedBox(height: 10),
                  MyButton.small(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: MyText.bodyMedium('Huỷ', color: Colors.white),
                  ),
                ],
              ),
            ),
          );
        });
  }

  Widget buildHeader() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('Chữ ký hiện tại: '),
              Image.memory(
                signatureImageBytes ?? signatureImageBytesPlaceholder!,
                height: 50,
                width: 100,
              ),
              MyButton.small(
                onPressed: () {
                  showSignatureDialogue();
                },
                borderRadiusAll: 5,
                child:
                    MyText.labelLarge('Chọn chữ ký khác', color: Colors.white),
              ),
            ],
          ),
          SizedBox(
            height: 5,
          ),
          ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            child: Container(
              margin: const EdgeInsets.only(bottom: 8.0),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey,
                    blurRadius: 8,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              height: 60,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Trang: $currentPage/${_pdfViewerController.pageCount}'),
                  SizedBox(width: 20),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          currentPage <= 1 ? Colors.grey : Colors.blue),
                    ),
                    icon: Icon(
                      Icons.first_page,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      _pdfViewerController.firstPage();
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          currentPage <= 1 ? Colors.grey : Colors.blue),
                    ),
                    icon: Icon(
                      Icons.navigate_before,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      _pdfViewerController.jumpToPage(currentPage - 1);
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          _pdfViewerController.pageCount == currentPage
                              ? Colors.grey
                              : Colors.blue),
                    ),
                    icon: Icon(
                      Icons.navigate_next,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      _pdfViewerController.jumpToPage(currentPage + 1);
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          _pdfViewerController.pageCount == currentPage
                              ? Colors.grey
                              : Colors.blue),
                    ),
                    icon: Icon(
                      Icons.last_page,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      _pdfViewerController.lastPage();
                    },
                  ),
                  SizedBox(width: 20),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Colors.lightBlue),
                    ),
                    icon: Icon(
                      Icons.zoom_out_map,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      _pdfViewerController.zoomLevel = 1.0;
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Colors.lightBlue),
                    ),
                    icon: Icon(
                      Icons.zoom_in,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      _pdfViewerController.zoomLevel = 2.0;
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Colors.lightBlue),
                    ),
                    icon: Icon(
                      Icons.zoom_out,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      _pdfViewerController.zoomLevel = 0.5;
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build the draggable and resizable signature widget
  Widget _buildSignatureWidget(SignatureData signature) {
    // ignore: unused_local_variable
    var theme = Theme.of(context);
    List<Widget> textWidgets = [];

    // Calculate font size based on signature width for responsive text
    double baseFontSize = 12.0;
    double responsiveFontSize =
        math.max(baseFontSize * (signature.width / 200), 8.0);

    if (signature.stampStyle) {
      textWidgets = [
        MyText.bodyMedium(
          'Người ký: ${widget.fullName ?? ''}',
          color: Colors.red,
          maxLines: 1,
          textAlign: TextAlign.left,
          overflow: TextOverflow.clip,
          fontSize: responsiveFontSize,
        ),
        MyText.bodyMedium(
          'Ngày ký: ${signature.datetime ?? DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
          color: Colors.red,
          maxLines: 1,
          textAlign: TextAlign.left,
          overflow: TextOverflow.clip,
          fontSize: responsiveFontSize,
        ),
        MyText.bodyMedium(
          'Chức danh: ${widget.personposition ?? ''}',
          color: Colors.red,
          maxLines: 1,
          textAlign: TextAlign.left,
          overflow: TextOverflow.clip,
          fontSize: responsiveFontSize,
        ),
        MyText.bodyMedium(
          'Đơn vị: ${widget.department ?? ''}',
          color: Colors.red,
          maxLines: 1,
          textAlign: TextAlign.left,
          overflow: TextOverflow.clip,
          fontSize: responsiveFontSize,
        ),
      ];
    } else {
      // Default signature text logic - only display if flags are set
      if (signature.displayFullName == true &&
          signature.fullName != null &&
          signature.fullName!.isNotEmpty) {
        textWidgets.add(MyText.bodyMedium(
          signature.fullName!,
          color: Colors.black,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.clip,
          fontSize: responsiveFontSize,
        ));
      }
      if (signature.displayDatetime == true &&
          signature.datetime != null &&
          signature.datetime!.isNotEmpty) {
        textWidgets.add(MyText.bodyMedium(
          'Ngày ký: ${signature.datetime!}',
          color: Colors.black,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.clip,
          fontSize: responsiveFontSize,
        ));
      }
      if (signature.displayPosition == true &&
          signature.personposition != null &&
          signature.personposition!.isNotEmpty) {
        textWidgets.add(MyText.bodyMedium(
          'Chức danh: ${signature.personposition!}',
          color: Colors.black,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.clip,
          fontSize: responsiveFontSize,
        ));
      }
      if (signature.displayDepartment == true &&
          signature.department != null &&
          signature.department!.isNotEmpty) {
        textWidgets.add(MyText.bodyMedium(
          'Đơn vị: ${signature.department!}',
          color: Colors.black,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.clip,
          fontSize: responsiveFontSize,
        ));
      }
    }

    // Calculate text height based on number of text widgets and responsive font size
    double textHeight = textWidgets.length * (responsiveFontSize * 2.0);

    return Positioned(
      left: signature.position.dx,
      top: signature.position.dy,
      child: Container(
        width: signature.width + 16,
        height: signature.stampStyle
            ? textHeight + 16 // Add padding for stamp style
            : signature.displayFullName ||
                    signature.displayDatetime ||
                    signature.displayPosition ||
                    signature.displayDepartment
                ? signature.height +
                    textHeight +
                    8 // Add text height if any text is displayed
                : signature.height + 16,
        padding: EdgeInsets.all(8),
        child: Center(
          child: GestureDetector(
            onPanUpdate: (details) =>
                _updateSignaturePosition(0, details.delta),
            onPanEnd: (_) => _resizeMode = ResizeMode.none,
            child: Stack(
              children: [
                if (signature.stampStyle)
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.red, width: 2),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: textWidgets,
                    ),
                  )
                else
                  FittedBox(
                    fit: BoxFit.contain,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          alignment: Alignment.center,
                          width: signature.width,
                          height: signature.height,
                          child: Image.memory(
                            signatureImageBytes ??
                                signatureImageBytesPlaceholder!,
                            fit: BoxFit.contain,
                          ),
                        ),
                        if (textWidgets.isNotEmpty)
                          Column(
                            mainAxisSize: MainAxisSize.min,
                            children: textWidgets,
                          ),
                      ],
                    ),
                  ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: _buildResizeHandler(ResizeMode.bottomRight),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Build resize handler widget
  Widget _buildResizeHandler(ResizeMode resizeMode) {
    return GestureDetector(
      onPanStart: (details) => _resizeMode = resizeMode,
      onPanUpdate: (details) => _updateSignatureSize(0, details.delta,
          _resizeMode), // Assuming only one signature at index 0
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color: Colors.blue,
          shape: BoxShape.circle,
          border: Border.all(
              color: Colors.white,
              width: 1), // Added white border for better visibility
        ),
      ),
    );
  }
}
