import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepvienchuc.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/app_helpers.dart';
import 'package:tvumobile/app/utils/helpers/date_time_extension.dart';

Widget buildItem(BuildContext context, DonNghiPhepVienChuc doc, int index) {
  ThemeData theme = Theme.of(context);
  //List colors = [getColorFromHex('#5E97F6'), getColorFromHex('#4DD0E1'), getColorFromHex('#F06292'), getColorFromHex('#F6BF26')];
  final width = Get.width;
  return MyContainer(
    padding: const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 8),
    onTap: () {
      Get.toNamed(
        Routes.CHITIETDNP,
        parameters: {
          'dnpId': doc.id.toString(),
          'duyettai': '',
          'trangthai': ''
        },
      );
    },
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: doc.trangThai!.contains("Chờ")
                      ? Colors.amber
                      : doc.trangThai!.contains("Hủy")
                          ? Colors.red
                          : doc.trangThai!.contains("Khởi")
                              ? Colors.blue
                              : Colors.green),
              child: Center(
                child: MyText.labelMedium(
                  doc.trangThai.toString().getAvatarDNP,
                  letterSpacing: 1,
                  color: Get.isDarkMode ? Colors.white60 : Colors.white70,
                  fontWeight: 700,
                ),
              ),
            ),
            // IconButton(
            //   onPressed: () {},
            //   icon: Icon(
            //     Icons.repeat,
            //     color: theme.disabledColor,
            //   ),
            // )
          ],
        ),
        MySpacing.width(8),
        SizedBox(
          width: width - 130,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText.bodyLarge(
                doc.loaiNghiPhep!.tenLoaiNghiPhep.toString(),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                fontWeight: 700,
              ),
              MyText.bodyMedium(
                "Thời gian: ${doc.tuNgay!.toFormat(format: "dd/MM/yy")} - ${doc.denNgay!.toFormat(format: "dd/MM/yy")}",
              ),
              MyText.bodyMedium(
                "Trạng thái: ${doc.trangThai.toString()}",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
