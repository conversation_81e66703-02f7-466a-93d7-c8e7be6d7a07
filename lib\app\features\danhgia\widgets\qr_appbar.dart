import 'package:flutter/material.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

// THANH APP BAR
AppBar ScannerAppBar(BuildContext context, String title) {
  ThemeData theme = Theme.of(context);
  final safePadding = MediaQuery.of(context).padding.top;
  return AppBar(
    toolbarHeight: safePadding + 28, // Set this height
    foregroundColor: theme.colorScheme.onPrimary,
    iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
    flexibleSpace: Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -80,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset(
                  'assets/images/app_icon.png',
                  height: 150,
                  width: 150,
                  opacity: const AlwaysStoppedAnimation(.15),
                )),
          ),
          Positioned(
            right: -7,
            top: -160,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: -21,
            top: -195,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          //----------------Data row----------------//
          Center(
            child: Padding(
              padding: const EdgeInsets.only(top: 28),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  MyText.titleMedium(
                    title.toUpperCase(),
                    color: theme.colorScheme.onPrimary,
                    letterSpacing: 0.01,
                    fontWeight: 700,
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            right: 20,
            top: 10,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
        ],
      ),
    ),
  );
}
