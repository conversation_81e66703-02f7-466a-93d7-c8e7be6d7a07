part of 'app_constants.dart';

class Font {
  // Example:
  // static const roboto = 'roboto';
  // static const arial = 'arial';
}

class ImageAnimationPath {
  // you can get free animation image from rive or lottiefiles

  // Example:
  // static const _folderPath = "assets/images/animation";
  // static const myAnim = "$_folderPath/my_anim.json";
}

class ImageRasterPath {
  static const _folderPath = "assets/raster";
  static const avatar1 = "$_folderPath/avatar-1.png";
  static const avatar2 = "$_folderPath/avatar-2.png";
  static const avatar3 = "$_folderPath/avatar-3.png";
  static const avatar4 = "$_folderPath/avatar-4.png";
  static const avatar5 = "$_folderPath/avatar-5.png";
  static const avatar6 = "$_folderPath/avatar-6.png";
  static const avatar7 = "$_folderPath/avatar-7.png";
  static const avatar8 = "$_folderPath/avatar-8.png";
  static const logo1 = "$_folderPath/logo-1.png";
  static const logo2 = "$_folderPath/logo-2.png";
  static const logo3 = "$_folderPath/logo-3.png";
  static const logo4 = "$_folderPath/logo-4.png";
}

class ImageTaskyPath {
  static const _folderPath = "assets/tasky";
  static const timeManagement = "$_folderPath/time_management.png";
}

class ImageVectorPath {
  static const _folderPath = "assets/vectors";
  static const happy = "$_folderPath/happy.svg";
  static const happy2 = "$_folderPath/happy-2.svg";
  static const wavyBus = "$_folderPath/wavy-bus.svg";
}

class Onboarding {
  static String get kBoard3 => 'assets/onboard/minhbach.json';
  static String get kBoard1 => 'assets/onboard/tantam.json';
  static String get kBoard2 => 'assets/onboard/thanthien.json';
  static String get kBoard4 => 'assets/onboard/sangtao.json';
}