// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/donnghiphep_controller.dart';
import 'package:tvumobile/app/shared_components/input_decoration_theme.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class ThongTinSauOmDauWidget extends StatefulWidget {
  final DonNghiPhepController dnpManager;
  const ThongTinSauOmDauWidget({
    super.key,
    required this.dnpManager,
  });

  @override
  State<ThongTinSauOmDauWidget> createState() => _ThongtinWidgetState();
}

class _ThongtinWidgetState extends State<ThongTinSauOmDauWidget> {
  final formKey = GlobalKey<FormBuilderState>();
  int id = 0;
  Map<String, dynamic> formdata = {};

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    final myDecoration =
        const InputDecoration().applyDefaults(GlobalInputDecoration(context));
    if (widget.dnpManager.thongtinSauOmdau.isNotEmpty) {
      formdata = {};
      formdata.addAll(widget.dnpManager.thongtinSauOmdau);
    }

    return Scaffold(
        appBar: AppBar(
          backgroundColor: theme.primaryColor.withOpacity(0.95),
          title: MyText.titleMedium(
            "Nghỉ dưỡng sau đau ốm".tr.toUpperCase(),
            color: theme.colorScheme.onPrimary,
          ),
          iconTheme:
              theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
          centerTitle: true,
        ),
        body: MyContainer(
          child: Column(
            children: [
              const MyContainer(
                child: MyText.titleLarge("Vui lòng nhập thông tin Nghỉ"),
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                      padding: const EdgeInsets.all(0.0),
                      child: FormBuilder(
                        key: formKey,
                        child: ListView(shrinkWrap: true, children: [
                          FormBuilderDateRangePicker(
                            name: 'ngaynghi',
                            firstDate: DateTime(DateTime.now().year - 1),
                            lastDate: DateTime(DateTime.now().year + 1),
                            format: DateFormat('dd.MM.yyyy'),
                            initialValue: formdata['tungay'] != null
                                ? DateTimeRange(
                                    start: formdata['tungay'],
                                    end: formdata['denngay'])
                                : DateTimeRange(
                                    start: DateTime.now(), end: DateTime.now()),
                            onChanged: (value) {
                              if (value != null) {
                                formdata['tungay'] = value.start;
                                formdata['denngay'] = value.end;
                              }
                            },
                            onSaved: (newValue) {
                              if (newValue != null) {
                                formdata['tungay'] = newValue.start;
                                formdata['denngay'] = newValue.end;
                              }
                            },
                            decoration: myDecoration.copyWith(
                              labelText: 'Đã nghỉ trước đó',
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.close),
                                onPressed: () {
                                  formKey.currentState!.fields['ngaynghi']
                                      ?.didChange(null);
                                },
                              ),
                            ),
                            locale: const Locale('vi'),
                            cancelText: "Hủy bỏ",
                            confirmText: "Xác nhận",
                            style: theme.textTheme.titleMedium,
                          ),
                        ]),
                      )),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: MyButton.block(
                          backgroundColor: theme.primaryColor.withOpacity(0.95),
                          onPressed: () {
                            bool isValid =
                                formKey.currentState!.saveAndValidate();
                            debugPrint(formKey.currentState?.value.toString());
                            if (isValid) {
                              widget.dnpManager.thongtinSauOmdau = formdata;
                              //widget.dnpManager.tinhNgayNghi();
                              widget.dnpManager.tinhSoNgayThuc();
                              //widget.dnpManager.update();
                              Navigator.pop(context);
                            }
                          },
                          child: MyText.labelLarge(
                            'Xác nhận thông tin',
                            color: theme.colorScheme.onPrimary,
                          )),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }
}
