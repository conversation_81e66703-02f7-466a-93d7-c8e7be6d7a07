

extension DoubleExtension on double? {
  String get precise {
    if (this != null) {
      return this!.toStringAsFixed(this!.truncateToDouble() == this ? 0 : 1);
    }
    return '';
  }
  int get getInt {
    if (this != null) {
      return  this!.truncate();
    }
    return 0;
  }
}

extension DoubleExtension2 on dynamic {
  int get getInt {
    if (this != null) {
      return  (this as double).truncate();
    }
    return 0;
  }
}
