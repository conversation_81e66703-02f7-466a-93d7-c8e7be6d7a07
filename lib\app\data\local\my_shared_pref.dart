// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:tvumobile/app/utils/localizations/language.dart';
import 'package:tvumobile/app/utils/oauth2/src/credentials.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '/app/data/local/storage_constants.dart';

//import '/config/translations/localization_service.dart';
//import '../models/auth/credentials.dart';
import '../models/auth/user_info.dart';

class MySharedPrefOld {
  // prevent making instance
  MySharedPrefOld._();

  // shared pref init
  static SharedPreferences? _sharedPreferences;

  static Future<void> init() async {
    _sharedPreferences = await SharedPreferences.getInstance();
  }

  // STORING KEYS
  static const String _fcmTokenKey = 'fcm_token';

  static const String _currentLocalKey = 'current_local';
  static const String _lightThemeKey = 'is_theme_light';
  static const String _NewbieKey = 'newbie';

  /// set theme current type as light theme
  static Future<bool>? setIsNew(bool lightTheme) =>
      _sharedPreferences?.setBool(_NewbieKey, lightTheme);

  /// get if the current theme type is light
  static bool getIsNew() => _sharedPreferences?.getBool(_NewbieKey) ?? true;

  /// set theme current type as light theme
  static Future<bool>? setThemeIsLight(bool lightTheme) =>
      _sharedPreferences?.setBool(_lightThemeKey, lightTheme);

  /// get if the current theme type is light
  static bool getThemeIsLight() =>
      _sharedPreferences?.getBool(_lightThemeKey) ?? true;

  /// save current locale
  static void setCurrentLanguage(String languageCode) =>
      _sharedPreferences?.setString(_currentLocalKey, languageCode);

  /// get current locale
  static Locale getCurrentLocal() {
    String? langCode = _sharedPreferences?.getString(_currentLocalKey);
    // default language is english
    if (langCode == null) {
      return Language.currentLanguage.locale;
    }
    return Language.currentLanguage.locale;
  }

  /// save generated fcm token
  static Future<bool>? setFcmToken(String token) =>
      _sharedPreferences?.setString(_fcmTokenKey, token);

  /// get generated fcm token
  static String? getFcmToken() => _sharedPreferences?.getString(_fcmTokenKey);

  /// clear all data from shared pref except the current language
  static Future<void> clearExceptLanguage() async {
    // Step 1: Retrieve the current language from shared preferences
    final currentLanguage = _sharedPreferences?.getString(_currentLocalKey);

    // Step 2: Clear all the shared preferences
    await _sharedPreferences?.clear();

    // Step 3: Set the current language back to the shared preferences
    if (currentLanguage != null) {
      setCurrentLanguage(currentLanguage);
    }
  }

  /// clear all data from shared pref
  static Future<void> clear() async => await _sharedPreferences?.clear();

  /// save generated fcm token
  static Future<bool>? setAccessToken(String token) =>
      _sharedPreferences?.setString(StorageConstants.token, token);

  /// get authorization token
  static String? getAccessToken() =>
      _sharedPreferences?.getString(StorageConstants.token);

  /// save generated fcm token
  static Future<void> setUserInfo(UserInfo token) async {
    _sharedPreferences?.setString(StorageConstants.userInfo, token.toJsonString());
  }

  /// get authorization token
  static UserInfo? getUserInfo() {
    String? user = _sharedPreferences?.getString(StorageConstants.userInfo);
    if (user != null) {
      return UserInfo.fromJsonString(user);
    }
    return null;
  }

  static Future<void> setCredential(Credentials token) => _sharedPreferences!
      .setString(StorageConstants.credential, token.toJson());

  static Future<bool>? setCredentialString(String token) =>
      _sharedPreferences?.setString(StorageConstants.credential, token);

  static Credentials? getCredential() {
    String? cred = _sharedPreferences?.getString(StorageConstants.credential);
    if (cred != null) {
      return Credentials.fromJson(cred);
    }
    return null;
  }

  static String? getCredentialString() {
    String? cred = _sharedPreferences?.getString(StorageConstants.credential);
    return cred;
  }
}
