// ignore_for_file: deprecated_member_use

import 'package:avatar_glow/avatar_glow.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:qr_scanner_overlay/qr_scanner_overlay.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/features/danhgia/qr_result.dart';
import 'package:tvumobile/app/features/danhgia/qr_scanner_controller.dart';
import 'package:tvumobile/app/features/danhgia/widgets/qr_appbar.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class MyQrScanner extends GetView<QrScannerController> {
  const MyQrScanner({super.key});

  @override
  Widget build(BuildContext context) {
    // Get.put(QrScannerController());
    return Scaffold(
      appBar: ScannerAppBar(context, "quét thông tin mã qr"),
      body: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(0),
        child: Stack(
          fit: StackFit.expand,
          children: [
            MobileScanner(
              controller: controller.cameraController,
              onDetect: (barcode) {
                if (!controller.isScanCompleted.value) {
                  controller.isScanCompleted.value = true;
                  controller.cameraController.stop();
                  String code = barcode.barcodes.single.rawValue ?? "---";
                  //print(code);
                  //Get.off(() => QRResult(code: code, closeScreen: closeScreen), preventDuplicates: true);
                  if (code.toLowerCase().contains("danhgiachatluong") &&
                      code.toLowerCase().contains("tms.tvu.edu.vn")) {
                    //_code = params['donviId'] ?? "";
                    //_path = "danhgiachatluongphucvu";
                    Uri uri = Uri.parse(code);
                    Map<String, String> params = uri.queryParameters;
                    if (uri.toString().contains("danhgiachatluongphucvu") ||
                        uri.pathSegments.contains("Danhgiachatluongdonvi")) {
                      String code = params['donviId'] ?? "";
                      if (code == "") {
                        code = params['donviid'] ?? "";
                      }
                      Get.offNamed(Routes.DANHGIA,
                          parameters: {"donviId": code, "type": "dv"},
                          preventDuplicates: true);
                    } else if ((uri.toString().contains("DanhGiaChatLuong") ||
                            uri.pathSegments.contains("danhgiachatluong")) &&
                        (uri.toString().contains("giaydieuxe") ||
                            uri.pathSegments.contains("GiayDieuXe"))) {
                      String code = params['Id'] ?? "";
                      if (code == "") {
                        code = params['id'] ?? "";
                      }
                      Get.offNamed(Routes.DANHGIA,
                          parameters: {"donviId": code, "type": "xe"},
                          preventDuplicates: true);
                    } else {
                      Get.off(
                          () => QRResult(
                              code: code,
                              closeScreen: () => controller.resetScan()),
                          preventDuplicates: true);
                    }
                    //Get.toNamed(Routes.DANHGIA, parameters: {"donviId": code}, preventDuplicates: true);
                    //Get.offNamed(Routes.DANHGIA, arguments: {"code": code}, preventDuplicates: true);
                  } else {
                    Get.off(
                        () => QRResult(
                            code: code,
                            closeScreen: () => controller.resetScan()),
                        preventDuplicates: true);
                  }
                  /*Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) {
                          return QRResult(
                            code: code,
                            closeScreen: closeScreen,
                          );
                        }
                    ),
                  );*/
                }
              },
            ),

            // Lớp phủ để hướng dẫn đặt mã QR đúng vị trí
            QRScannerOverlay(
              overlayColor: Colors.black45,
              borderColor: Theme.of(context).primaryColorLight,
              borderRadius: 10,
              borderStrokeWidth: 10,
              scanAreaWidth: 250,
              scanAreaHeight: 250,
            ),

            // Văn bản hướng dẫn
            Align(
              alignment: Alignment.topCenter,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  MySpacing.height(50),
                  const MyText.bodyLarge(
                    "Hãy đặt mã QR vào khu vực bên dưới",
                    textAlign: TextAlign.center,
                    color: Colors.white70,
                    fontWeight: 900,
                    fontSize: 18,
                  ),
                  const MyText.bodyMedium(
                    "Quá trình quét sẽ được thực hiện tự động",
                    textAlign: TextAlign.center,
                    color: Colors.white70,
                    fontWeight: 900,
                  )
                ],
              ),
            ),
            const Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: EdgeInsets.only(bottom: 70),
                child: MyText.labelLarge(
                  "|Hãy quét đúng cách để xem kết quả|",
                  color: Colors.white70,
                  fontWeight: 900,
                  fontSize: 18,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ScannerOverlay extends CustomPainter {
  const ScannerOverlay({
    required this.scanWindow,
    this.borderRadius = 12.0,
  });

  final Rect scanWindow;
  final double borderRadius;

  @override
  void paint(Canvas canvas, Size size) {
    // we need to pass the size to the custom paint widget
    final backgroundPath = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    final cutoutPath = Path()
      ..addRRect(
        RRect.fromRectAndCorners(
          scanWindow,
          topLeft: Radius.circular(borderRadius),
          topRight: Radius.circular(borderRadius),
          bottomLeft: Radius.circular(borderRadius),
          bottomRight: Radius.circular(borderRadius),
        ),
      );

    final backgroundPaint = Paint()
      ..color = Colors.black.withOpacity(0.5)
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.dstOver;

    final backgroundWithCutout = Path.combine(
      PathOperation.difference,
      backgroundPath,
      cutoutPath,
    );

    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.0;

    final borderRect = RRect.fromRectAndCorners(
      scanWindow,
      topLeft: Radius.circular(borderRadius),
      topRight: Radius.circular(borderRadius),
      bottomLeft: Radius.circular(borderRadius),
      bottomRight: Radius.circular(borderRadius),
    );

    // First, draw the background,
    // with a cutout area that is a bit larger than the scan window.
    // Finally, draw the scan window itself.
    canvas.drawPath(backgroundWithCutout, backgroundPaint);
    canvas.drawRRect(borderRect, borderPaint);
  }

  @override
  bool shouldRepaint(ScannerOverlay oldDelegate) {
    return scanWindow != oldDelegate.scanWindow ||
        borderRadius != oldDelegate.borderRadius;
  }
}
