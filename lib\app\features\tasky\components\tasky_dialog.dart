import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/data/models/tasky/Task.dart';
import 'package:tvumobile/app/data/models/tms/danhmuc.dart';
import 'package:tvumobile/app/features/tasky/controllers/taskdetail_controller.dart';
import 'package:tvumobile/app/features/tasky/views/taskpdfview.dart';
import 'package:tvumobile/app/shared_components/file_icon.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/features/tasky/components/build_tasklist.dart';
import 'package:url_launcher/url_launcher.dart';

// Widget hiển thị tab "Hoạt động"
Widget tabHoatDong(TaskDetailController controller) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          MyButton.small(
            onPressed: () {
              showActionDialog(controller);
            },
            child: MyText.labelLarge(
              "Thêm hoạt động",
              color: Colors.white,
            ),
          ),
        ],
      ),
      Divider(
        color: Colors.grey[500],
        height: 16,
      ),
      controller.task.taskAction!.isEmpty
          ? Padding(
              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Text(
                "Chưa có hoạt động",
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            )
          : SizedBox(height: 0),
      for (var action in controller.task.taskAction ?? [])
        Builder(
          builder: (BuildContext context) {
            return GestureDetector(
              onLongPressStart: (LongPressStartDetails details) {
                // Lấy vị trí nhấn giữ
                final Offset position = details.globalPosition;
                showPopupMenu(
                  context: context,
                  position: position, // Truyền vị trí nhấn giữ
                  items: [
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          SizedBox(width: 15),
                          Text("Sửa"),
                          SizedBox(width: 15),
                          Icon(LucideIcons.pen, size: 18),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          SizedBox(width: 15),
                          Text("Xóa"),
                          SizedBox(width: 15),
                          Icon(LucideIcons.trash, size: 18),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) {
                    if (value == 'edit') {
                      showActionDialog(controller,
                          isEdit: true, action: action);
                    } else if (value == 'delete') {
                      Get.defaultDialog(
                        title: 'Xác nhận xóa',
                        middleText:
                            'Bạn có chắc chắn muốn xóa hoạt động này không?',
                        textCancel: 'Hủy',
                        textConfirm: 'Xóa',
                        cancelTextColor: Color(0xFF1157FA),
                        confirmTextColor: Colors.white,
                        buttonColor: Color(0xFF1157FA),
                        onConfirm: () async {
                          await controller.deleteSubTask(action.id!);
                        },
                      );
                    }
                  },
                );
              },
              // onLongPress: () {
              //   // Hiển thị popup menu khi nhấn giữ
              //   showPopupMenu(
              //     context: Get.context!,
              //     position: Offset(0, 0), // Vị trí sẽ được điều chỉnh tự động
              //     items: [
              //       PopupMenuItem(
              //         value: 'edit',
              //         child: Row(
              //           children: [
              //             Icon(LucideIcons.pen, size: 18),
              //             SizedBox(width: 8),
              //             Text("Sửa"),
              //           ],
              //         ),
              //       ),
              //       PopupMenuItem(
              //         value: 'delete',
              //         child: Row(
              //           children: [
              //             Icon(LucideIcons.trash, size: 18),
              //             SizedBox(width: 8),
              //             Text("Xóa"),
              //           ],
              //         ),
              //       ),
              //     ],
              //     onSelected: (value) {
              //       if (value == 'edit') {
              //         // Gọi dialog sửa với dữ liệu có sẵn
              //         showActionDialog(controller,
              //             isEdit: true, action: action);
              //       } else if (value == 'delete') {
              //         // Hiển thị dialog xác nhận xóa
              //         Get.defaultDialog(
              //           title: 'Xác nhận xóa',
              //           middleText:
              //               'Bạn có chắc chắn muốn xóa hoạt động này không?',
              //           textCancel: 'Hủy',
              //           textConfirm: 'Xóa',
              //           cancelTextColor: Color(0xFF1157FA),
              //           confirmTextColor: Colors.white,
              //           buttonColor: Color(0xFF1157FA),
              //           onConfirm: () async {
              //             // await controller.deleteSubTask(action.id!);
              //           },
              //         );
              //       }
              //     },
              //   );
              // },
              child: Card(
                margin: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                child: ListTile(
                  title: Text(action.moTa ?? 'Không có mô tả'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                          'Khối lượng công việc: ${action.khoiLuongCongViec ?? ''}'),
                      Text('Tổng số giờ: ${action.tongSoGioThucHien ?? ''}'),
                      Text('Hoàn thành: ${action.tiLeHoanThanh ?? 0}%'),
                      if (action.tiLeHoanThanh != null &&
                          action.tiLeHoanThanh < 100)
                        Text(
                            'Lí do chưa hoàn thành: ${action.lyDoChuaHoanThanh ?? ''}'),
                      Text(
                          'Ngày kết thúc: ${action.ngayKetThuc != null ? controller.dateFormat.format(action.ngayKetThuc!) : ''}'),
                    ],
                  ),
                  trailing: CircularProgressIndicator(
                    strokeWidth: 6,
                    value: (action.tiLeHoanThanh ?? 0) / 100,
                  ),
                ),
              ),
            );
          },
        ),
    ],
  );
}

// Widget hiển thị tab "Trao đổi"
Widget tabTraoDoi(TaskDetailController controller) {
  ThemeData theme = Get.theme;
  DateFormat format = DateFormat("HH:mm dd/MM/yyyy");
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          MyButton.small(
            onPressed: () {
              showUpdateInfoDialog(controller);
            },
            child: MyText.labelLarge(
              "Thêm thông tin trao đổi",
              color: Colors.white,
            ),
          ),
        ],
      ),
      Divider(
        color: Colors.grey[500],
        height: 16,
      ),
      // SizedBox(height: 8),
      controller.task.taskUpdateInfo!.isEmpty
          ? Padding(
              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Text(
                "Chưa có trao đổi thông tin",
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            )
          : SizedBox(height: 0),
      for (var action in controller.task.taskUpdateInfo ?? []) ...[
        Builder(
          builder: (BuildContext context) {
            return GestureDetector(
              onLongPressStart: (LongPressStartDetails details) {
                if (action.nguoiCapNhat.id == controller.userInfo) {
                  // if (action.nguoiCapNhat.id == 18279) {
                  final Offset position = details.globalPosition;
                  showPopupMenu(
                    context: context,
                    position: position,
                    items: [
                      PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(LucideIcons.pen, size: 18),
                            SizedBox(width: 8),
                            Text("Sửa"),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(LucideIcons.trash, size: 18),
                            SizedBox(width: 8),
                            Text("Xóa"),
                          ],
                        ),
                      ),
                    ],
                    onSelected: (value) {
                      if (value == 'edit') {
                        showUpdateInfoDialog(controller,
                            isEdit: true, updateInfo: action);
                      } else if (value == 'delete') {
                        Get.defaultDialog(
                          title: 'Xác nhận xóa',
                          middleText:
                              'Bạn có chắc chắn muốn xóa tin nhắn này không?',
                          textCancel: 'Hủy',
                          textConfirm: 'Xóa',
                          cancelTextColor: Color(0xFF1157FA),
                          confirmTextColor: Colors.white,
                          buttonColor: Color(0xFF1157FA),
                          onConfirm: () async {
                            await controller.deleteMessage(action.id!);
                          },
                        );
                      }
                    },
                  );
                }
              },
              // child: Padding(
              //   padding: const EdgeInsets.all(8.0),
              //   child: Row(
              //     children: [
              //       CircleAvatar(
              //         radius: 20,
              //         backgroundImage:
              //             NetworkImage(action.nguoiCapNhat.hinhAnh.toString()),
              //       ),
              //       SizedBox(width: 8),
              //       Expanded(
              //         child: Column(
              //           crossAxisAlignment: CrossAxisAlignment.start,
              //           children: [
              //             MyText.labelSmall(
              //               getHoTen(action.nguoiCapNhat),
              //               softWrap: true,
              //             ),
              //             Text.rich(
              //               softWrap: true,
              //               style: theme.textTheme.bodyMedium,
              //               TextSpan(
              //                 children: [
              //                   TextSpan(text: action.updateInfo),
              //                 ],
              //               ),
              //             ),
              //             SizedBox(height: 4),
              //             Row(
              //               mainAxisAlignment: MainAxisAlignment.end,
              //               children: [
              //                 MyText.labelSmall(
              //                   format.format(action.ngayCapNhat),
              //                   softWrap: true,
              //                 ),
              //               ],
              //             ),
              //           ],
              //         ),
              //       ),
              //     ],
              //   ),
              // ),
              child: ListTile(
                leading: CircleAvatar(
                  radius: 20,
                  backgroundImage:
                      NetworkImage(action.nguoiCapNhat.hinhAnh.toString()),
                ),
                title: MyText.labelSmall(
                  getHoTen(action.nguoiCapNhat),
                  softWrap: true,
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text.rich(
                      softWrap: true,
                      style: theme.textTheme.bodyMedium,
                      TextSpan(
                        children: [
                          TextSpan(text: action.updateInfo),
                        ],
                      ),
                    ),
                    SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        MyText.labelSmall(
                          format.format(action.ngayCapNhat),
                          softWrap: true,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        Divider(
          color: Colors.grey[500],
          height: 16,
        ),
      ],
    ],
  );
}

// Widget hiển thị tab "Đính kèm"
Widget tabDinhKem(TaskDetailController controller) {
  ThemeData theme = Get.theme;

  // Chia taskResource thành hai danh sách: file đính kèm và công văn
  final fileResources = controller.task.taskResource
          ?.where((resource) => resource.fileDinhKemPath != null)
          .toList() ??
      [];
  final hasDocument = controller.task.taskResource?.any((resource) =>
          resource.documentId != null && resource.documentId != 0) ??
      false;

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          MyButton.small(
            onPressed: () {
              showAddResourceDialog(controller);
            },
            child: MyText.labelLarge(
              "Thêm file đính kèm",
              color: Colors.white,
            ),
          ),
        ],
      ),
      Divider(
        color: Colors.grey[500],
        height: 16,
      ),
      // Kiểm tra nếu không có tài nguyên nào (cả file và công văn)
      if (fileResources.isEmpty && !hasDocument)
        Padding(
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          child: Text(
            "Không có file đính kèm hoặc công văn",
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        )
      else ...[
        // Hiển thị danh sách file đính kèm
        if (fileResources.isNotEmpty) ...[
          MyText.titleMedium(
            "Văn bản đính kèm",
            fontWeight: 700,
          ),
          MyContainer(
            bordered: true,
            paddingAll: 2,
            child: ListView.separated(
              scrollDirection: Axis.vertical,
              shrinkWrap: true,
              itemCount: fileResources.length,
              separatorBuilder: (BuildContext context, int index) => Divider(
                height: 1,
                color: Colors.grey[300],
              ),
              itemBuilder: (BuildContext context, int index) {
                final resource = fileResources[index];
                String fileName =
                    resource.fileDinhKemPath?.toString().split('\\').last ??
                        "Tệp không tên";
                String fileExt = fileName.contains('.')
                    ? fileName.split('.').last.toLowerCase()
                    : '';
                return GestureDetector(
                  onLongPressStart: (LongPressStartDetails details) {
                    final Offset position = details.globalPosition;
                    showPopupMenu(
                      context: context,
                      position: position,
                      items: [
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(LucideIcons.trash, size: 18),
                              SizedBox(width: 8),
                              Text("Xóa"),
                            ],
                          ),
                        ),
                      ],
                      onSelected: (value) {
                        if (value == 'delete') {
                          Get.defaultDialog(
                            title: 'Xác nhận xóa',
                            middleText:
                                'Bạn có chắc chắn muốn xóa file đính kèm này không?',
                            textCancel: 'Hủy',
                            textConfirm: 'Xóa',
                            cancelTextColor: Color(0xFF1157FA),
                            confirmTextColor: Colors.white,
                            buttonColor: Color(0xFF1157FA),
                            onConfirm: () async {
                              await controller.deleteResource(resource.id!);
                            },
                          );
                        }
                      },
                    );
                  },
                  child: ListTile(
                    leading: FileIcon(
                      resource.fileDinhKemPath?.toString() ?? "",
                      size: 25,
                    ),
                    title: MyText.labelMedium(
                      fileName,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    onTap: () {
                      if (fileExt.contains('pdf')) {
                        Get.to(
                          () =>
                              TaskPdfView(controller.task.taskResource!, index),
                          transition: Transition.fadeIn,
                        );
                      } else {
                        launchUrl(
                          Uri.parse(
                              "https://tms.tvu.edu.vn/${resource.fileDinhKemPath}"),
                          mode: LaunchMode.externalApplication,
                        );
                      }
                    },
                    trailing: IconButton(
                      icon: Icon(LucideIcons.download),
                      onPressed: () {
                        launchUrl(
                          Uri.parse(
                              "https://tms.tvu.edu.vn/${resource.fileDinhKemPath}"),
                          mode: LaunchMode.externalApplication,
                        );
                      },
                      iconSize: 18,
                    ),
                  ),
                );
              },
            ),
          ),
          SizedBox(height: 8),
        ],

        if (hasDocument) ...[
          MyText.titleMedium(
            "Công văn đính kèm",
            fontWeight: 700,
          ),
          MyContainer(
            bordered: true,
            paddingAll: 2,
            child: controller.taskDocument.value == null
                ? ListTile(
                    leading: Icon(Icons.error, color: Colors.red, size: 18),
                    title: MyText.labelMedium("Không tìm thấy công văn"),
                  )
                : ListTile(
                    leading:
                        Icon(Icons.description, size: 25, color: Colors.blue),
                    title: MyText.bodyMedium(
                        controller.taskDocument.value!['kyHieuGoc'].toString()),
                    subtitle: MyText.labelMedium(
                        overflow: TextOverflow
                            .ellipsis, // Đảm bảo hiển thị "..." nếu văn bản dài
                        maxLines: 1, // Giới hạn 1 dòng,
                        controller.taskDocument.value!['trichYeu']
                                ?.toString() ??
                            "Không có trích yếu"),
                    onTap: () {
                      final document = controller.task.taskResource!.firstWhere(
                        (resource) =>
                            resource.documentId != null &&
                            resource.documentId != 0,
                      );
                      Get.toNamed(
                        Routes.CV_DETAIL,
                        parameters: {
                          'cvId': document.documentId.toString(),
                        },
                      );
                    },
                  ),
          ),
        ],
      ],
      SizedBox(height: 8),
    ],
  );
}

// Widget hiển thị dialog để chọn trạng thái công việc
void showUpdateTaskStatusDialog(TaskDetailController controller) {
  Get.dialog(
    barrierColor: Colors.black.withOpacity(0.5),
    barrierDismissible: false,
    Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 2,
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Chọn trạng thái',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close, color: Colors.grey),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            SizedBox(height: 16),
            ...controller.taskStatuses
                .map((status) => _buildStatusOption(controller, status)),
          ],
        ),
      ),
    ),
  );
}

// Widget hiển thị các tùy chọn trạng thái trong dialog updateTaskStatus
Widget _buildStatusOption(TaskDetailController controller, dynamic status) {
  return Container(
    margin: EdgeInsets.symmetric(vertical: 4),
    decoration: BoxDecoration(
      color: getStatusColors(status!),
      borderRadius: BorderRadius.circular(8),
    ),
    child: ListTile(
      minTileHeight: 10,
      dense: true,
      contentPadding: EdgeInsets.only(left: 4, right: 4, top: 2, bottom: 2),
      title: MyText.titleSmall(
        color: Colors.white,
        textAlign: TextAlign.center,
        status.tenDanhMuc ?? 'N/A',
      ),
      onTap: () {
        controller.updateTaskStatus(status);
      },
    ),
  );
}

// Widget hiển thị dialog để tự đánh giá mức độ hoàn thành
void showSelfEvaluationDialog(TaskDetailController controller,
    {required Nguoi nguoi}) {
  // Kiểm tra xem nguoi có hợp lệ không
  if (nguoi.id == -1) {
    Get.snackbar(
      'Lỗi',
      'Không thể tự đánh giá: Người thực hiện không hợp lệ',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.withOpacity(0.8),
      colorText: Colors.white,
    );
    return;
  }

  int initialValue = nguoi.tuDanhGiaMucDoHoanThanh ?? 1;
  if (initialValue == 0) initialValue = 1; // Xử lý trường hợp giá trị là 0
  double completionValue = initialValue.toDouble();

  Get.dialog(
    barrierColor: Colors.black.withOpacity(0.5),
    barrierDismissible: false,
    Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: StatefulBuilder(
        builder: (context, setState) {
          return Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 2,
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Mức độ hoàn thành',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close, color: Colors.grey),
                      onPressed: () => Get.back(),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${completionValue.round()}%',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1157FA),
                      ),
                    ),
                  ],
                ),
                Slider(
                  value: completionValue,
                  min: 1,
                  max: 100,
                  divisions: 99,
                  label: '${completionValue.round()}%',
                  onChanged: (value) {
                    setState(() {
                      completionValue = value;
                    });
                  },
                  activeColor: Color(0xFF1157FA),
                  inactiveColor: Colors.grey[300],
                ),
                SizedBox(height: 16),
                Align(
                  alignment: Alignment.center,
                  child: ElevatedButton(
                    onPressed: () {
                      controller.updateSelfEvaluation(
                        nguoi: nguoi,
                        completionValue: completionValue.round(),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFF1157FA),
                      padding:
                          EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Cập nhật',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    ),
  );
}

// Widget hiển thị dialog để người giao việc đánh giá mức độ hoàn thành
void showEvaluationDialog(TaskDetailController controller,
    {required Nguoi nguoi}) {
  // Kiểm tra xem nguoi có hợp lệ không
  if (nguoi.id == -1) {
    Get.snackbar(
      'Lỗi',
      'Không thể đánh giá: Người thực hiện không hợp lệ',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.withOpacity(0.8),
      colorText: Colors.white,
    );
    return;
  }

  int initialValue = nguoi.nguoiDuyetDanhGiaMucDoHoanThanh ?? 1;
  if (initialValue == 0) initialValue = 1; // Xử lý trường hợp giá trị là 0
  double completionValue = initialValue.toDouble();
  TextEditingController noteController =
      TextEditingController(text: nguoi.nguoiDuyetGhiChu);

  Get.dialog(
    barrierColor: Colors.black.withOpacity(0.5),
    barrierDismissible: false,
    Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: StatefulBuilder(
        builder: (context, setState) {
          return Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 2,
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Đánh giá công việc',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close, color: Colors.grey),
                      onPressed: () => Get.back(),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${nguoi.ho} ${nguoi.tenDem} ${nguoi.ten}',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1157FA),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${completionValue.round()}%',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1157FA),
                      ),
                    ),
                  ],
                ),
                Slider(
                  value: completionValue,
                  min: 1,
                  max: 100,
                  divisions: 99,
                  label: '${completionValue.round()}%',
                  onChanged: (value) {
                    setState(() {
                      completionValue = value;
                    });
                  },
                  activeColor: Color(0xFF1157FA),
                  inactiveColor: Colors.grey[300],
                ),
                SizedBox(height: 16),
                TextField(
                  controller: noteController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'Ghi chú',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Color(0xFF1157FA)),
                    ),
                  ),
                ),
                SizedBox(height: 16),
                Align(
                  alignment: Alignment.center,
                  child: ElevatedButton(
                    onPressed: () {
                      controller.updateEvaluation(
                        nguoi: nguoi,
                        completionValue: completionValue.round(),
                        note: noteController.text,
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFF1157FA),
                      padding:
                          EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Cập nhật',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    ),
  );
}

// Widget hiển thị dialog để nhập thông tin trao đổi (tin nhắn)
void showUpdateInfoDialog(TaskDetailController controller,
    {bool isEdit = false, dynamic updateInfo}) {
  TextEditingController noteController =
      TextEditingController(text: isEdit ? updateInfo?.updateInfo : '');

  Get.dialog(
    barrierColor: Colors.black.withOpacity(0.5),
    barrierDismissible: false,
    Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: StatefulBuilder(
        builder: (context, setState) {
          return Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 2,
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      isEdit ? 'Chỉnh sửa tin nhắn' : 'Trao đổi thông tin',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close, color: Colors.grey),
                      onPressed: () => Get.back(),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                TextField(
                  controller: noteController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'Nhập tin nhắn',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Color(0xFF1157FA)),
                    ),
                  ),
                ),
                SizedBox(height: 16),
                Align(
                  alignment: Alignment.center,
                  child: ElevatedButton(
                    onPressed: () {
                      // controller.addMessage(updateInfo: noteController.text);
                      if (isEdit) {
                        controller.updateMessage(
                          updateInfoId: updateInfo.id!,
                          updateInfo: noteController.text,
                        );
                      } else {
                        controller.addMessage(updateInfo: noteController.text);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFF1157FA),
                      padding:
                          EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      isEdit ? 'Cập nhật' : 'Gửi',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    ),
  );
}

// Widget hiển thị dialog để thêm hoạt động mới
void showActionDialog(TaskDetailController controller,
    {bool isEdit = false, dynamic action}) {
  ThemeData theme = Get.theme;

  // Nếu là chỉnh sửa, điền dữ liệu có sẵn vào formData
  if (isEdit && action != null) {
    controller.formData['MoTa'] = action.moTa ?? '';
    controller.formData['KhoiLuongCongViec'] =
        action.khoiLuongCongViec?.toString() ?? '';
    controller.formData['TongSoGioThucHien'] =
        action.tongSoGioThucHien?.toString() ?? '';
    controller.formData['TiLeHoanThanh'] =
        action.tiLeHoanThanh?.toDouble() ?? 0;
    controller.formData['LyDoChuaHoanThanh'] = action.lyDoChuaHoanThanh ?? '';
    controller.formData['NgayKetThuc'] = action.ngayKetThuc?.toIso8601String();
    // controller.formData['DonViTinh'] = controller.taskDonViTinh.firstWhere(
    //   (unit) => unit.id == action.donViTinhId,
    //   orElse: () => controller.taskDonViTinh.first,
    // );
    // Chỉ gán DonViTinh nếu action.donViTinhId không null
    if (action.donViTinhId != null) {
      controller.formData['DonViTinh'] = controller.taskDonViTinh.firstWhere(
        (unit) => unit.id == action.donViTinhId,
      );
    } else {
      controller.formData['DonViTinh'] =
          null; // Để trống nếu donViTinhId là null
    }
  } else {
    // Nếu là thêm mới, reset formData
    controller.formData.clear();
  }

  Get.dialog(
    barrierColor: Colors.black.withOpacity(0.5),
    barrierDismissible: false,
    Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 2,
                blurRadius: 10,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Tiêu đề và nút thoát (cố định)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    isEdit ? 'Chỉnh sửa hoạt động' : 'Thêm hoạt động',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close, color: Colors.grey),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
              Expanded(
                child: FormBuilder(
                  key: controller.formKey,
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      SizedBox(height: 16),
                      // Trường nhập mô tả
                      MyText.bodyMedium(
                        'Mô tả',
                        fontWeight: 700,
                      ),
                      FormBuilderTextField(
                        name: 'MoTa',
                        validator: FormBuilderValidators.required(
                            errorText: 'Vui lòng nhập mô tả công việc'),
                        decoration: InputDecoration(
                          hintText: 'Nhập mô tả',
                          hintStyle: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.3),
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        initialValue: controller.formData['MoTa'],
                        maxLines: 2,
                        onChanged: (value) {
                          controller.formData['MoTa'] = value ?? '';
                        },
                        style: theme.textTheme.labelLarge,
                      ),
                      SizedBox(height: 16),

                      // Trường nhập khối lượng công việc
                      MyText.bodyMedium(
                        'Khối lượng công việc',
                        fontWeight: 700,
                      ),
                      FormBuilderTextField(
                        name: 'KhoiLuongCongViec',
                        validator: FormBuilderValidators.required(
                            errorText: 'Vui lòng nhập khối lượng công việc'),
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          hintText: 'Nhập khối lượng công việc',
                          hintStyle: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.3),
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        initialValue: controller.formData['KhoiLuongCongViec'],
                        onChanged: (value) {
                          controller.formData['KhoiLuongCongViec'] =
                              value ?? '';
                        },
                        style: theme.textTheme.labelLarge,
                      ),
                      SizedBox(height: 16),

                      // Trường chọn đơn vị (combobox)
                      MyText.bodyMedium(
                        'Đơn vị',
                        fontWeight: 700,
                      ),
                      FormBuilderDropdown<DanhMuc>(
                        name: 'DonViTinh',
                        validator: FormBuilderValidators.required(
                            errorText: 'Vui lòng chọn đơn vị tính'),
                        decoration: InputDecoration(
                          hintText: 'Chọn đơn vị tính',
                          hintStyle: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.3),
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        initialValue: controller.formData['DonViTinh'],
                        items: controller.taskDonViTinh
                            .map((unit) => DropdownMenuItem<DanhMuc>(
                                  value: unit,
                                  child: Text(unit.tenDanhMuc!),
                                ))
                            .toList(),
                        onChanged: (value) {
                          controller.formData['DonViTinh'] = value;
                        },
                      ),
                      SizedBox(height: 16),

                      // Trường nhập tổng số giờ thực hiện
                      MyText.bodyMedium(
                        'Tổng số giờ thực hiện',
                        fontWeight: 700,
                      ),
                      FormBuilderTextField(
                        name: 'TongSoGioThucHien',
                        validator: FormBuilderValidators.required(
                            errorText: 'Vui lòng nhập số giờ thực hiện'),
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          hintText: 'Nhập tổng số giờ thực hiện',
                          hintStyle: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.3),
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        initialValue: controller.formData['TongSoGioThucHien'],
                        onChanged: (value) {
                          controller.formData['TongSoGioThucHien'] =
                              value ?? '';
                        },
                        style: theme.textTheme.labelLarge,
                      ),
                      SizedBox(height: 16),

                      // Trường tỉ lệ hoàn thành (slider)
                      MyText.bodyMedium(
                        'Tỉ lệ hoàn thành',
                        fontWeight: 700,
                      ),
                      FormBuilderSlider(
                        name: 'TiLeHoanThanh',
                        min: 0,
                        max: 100,
                        initialValue: controller.formData['TiLeHoanThanh'] ?? 0,
                        divisions: 100,
                        // label:
                        //     '${(controller.formData['TiLeHoanThanh'] ?? 0).round()}%',
                        activeColor: Color(0xFF1157FA),
                        inactiveColor: Colors.grey[300],
                        onChanged: (value) {
                          controller.formData['TiLeHoanThanh'] = value!.toInt();
                        },
                        decoration: InputDecoration(
                          border: InputBorder.none,
                        ),
                      ),
                      SizedBox(height: 16),

                      // Trường nhập lý do chưa hoàn thành
                      MyText.bodyMedium(
                        'Lý do chưa hoàn thành',
                        fontWeight: 700,
                      ),
                      FormBuilderTextField(
                        name: 'LyDoChuaHoanThanh',
                        decoration: InputDecoration(
                          hintText: 'Nhập lý do (nếu có)',
                          hintStyle: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.3),
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        initialValue: controller.formData['LyDoChuaHoanThanh'],
                        maxLines: 2,
                        onChanged: (value) {
                          controller.formData['LyDoChuaHoanThanh'] =
                              value ?? '';
                        },
                        style: theme.textTheme.labelLarge,
                      ),
                      SizedBox(height: 16),

                      // Trường chọn ngày kết thúc
                      MyText.bodyMedium(
                        'Ngày kết thúc',
                        fontWeight: 700,
                      ),
                      FormBuilderDateTimePicker(
                        name: 'NgayKetThuc',
                        validator: FormBuilderValidators.required(
                            errorText: 'Vui lòng chọn ngày kết thúc'),
                        inputType: InputType.date,
                        format: DateFormat('dd/MM/yyyy'),
                        decoration: InputDecoration(
                          hintText: 'Chọn ngày kết thúc',
                          hintStyle: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.3),
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        initialValue: controller.formData['NgayKetThuc'] != null
                            ? DateTime.parse(controller.formData['NgayKetThuc'])
                            : null,
                        onChanged: (value) {
                          controller.formData['NgayKetThuc'] =
                              value?.toIso8601String();
                        },
                      ),
                      SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Align(
                    alignment: Alignment.center,
                    child: ElevatedButton(
                      onPressed: () {
                        // if (controller.formKey.currentState
                        //         ?.saveAndValidate() ??
                        //     false) {
                        //   final formData =
                        //       controller.formKey.currentState!.value;
                        //   controller.addSubTask(
                        //     moTa: formData['MoTa'],
                        //     lyDoChuaHoanThanh:
                        //         formData['LyDoChuaHoanThanh'] ?? '',
                        //     khoiLuongCongViec:
                        //         int.parse(formData['KhoiLuongCongViec']),
                        //     tongSoGioThucHien:
                        //         int.parse(formData['TongSoGioThucHien']),
                        //     tiLeHoanThanh:
                        //         (formData['TiLeHoanThanh'] as double).round(),
                        //     ngayKetThuc: formData['NgayKetThuc'],
                        //     donViTinhId: formData['DonViTinh'].id,
                        //   );
                        // }
                        if (controller.formKey.currentState
                                ?.saveAndValidate() ??
                            false) {
                          final formData =
                              controller.formKey.currentState!.value;
                          if (isEdit) {
                            controller.updateSubTask(
                              actionId: action.id!,
                              moTa: formData['MoTa'],
                              lyDoChuaHoanThanh:
                                  formData['LyDoChuaHoanThanh'] ?? '',
                              khoiLuongCongViec:
                                  int.parse(formData['KhoiLuongCongViec']),
                              tongSoGioThucHien:
                                  int.parse(formData['TongSoGioThucHien']),
                              tiLeHoanThanh:
                                  (formData['TiLeHoanThanh'] as double).round(),
                              ngayKetThuc: formData['NgayKetThuc'],
                              donViTinhId: formData['DonViTinh'].id,
                            );
                          } else {
                            controller.addSubTask(
                              moTa: formData['MoTa'],
                              lyDoChuaHoanThanh:
                                  formData['LyDoChuaHoanThanh'] ?? '',
                              khoiLuongCongViec:
                                  int.parse(formData['KhoiLuongCongViec']),
                              tongSoGioThucHien:
                                  int.parse(formData['TongSoGioThucHien']),
                              tiLeHoanThanh:
                                  (formData['TiLeHoanThanh'] as double).round(),
                              ngayKetThuc: formData['NgayKetThuc'],
                              donViTinhId: formData['DonViTinh'].id,
                            );
                          }
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(0xFF1157FA),
                        padding:
                            EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        isEdit ? 'Cập nhật' : 'Thêm',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          )),
    ),
  );
}

// Widget hiển thị dialog để thêm file đính kèm
void showAddResourceDialog(TaskDetailController controller) {
  FilePickerResult? pickedFile; // Biến lưu file được chọn
  String? fileName; // Tên file hiển thị trên UI

  Get.dialog(
    barrierColor: Colors.black.withOpacity(0.5),
    barrierDismissible: false,
    Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: StatefulBuilder(
        builder: (context, setState) {
          return Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 2,
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Tiêu đề và nút thoát
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Thêm file đính kèm',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close, color: Colors.grey),
                      onPressed: () => Get.back(),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                // Nút để chọn file
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () async {
                        // Mở file picker để chọn file
                        FilePickerResult? result =
                            await FilePicker.platform.pickFiles(
                          allowMultiple: false, // Chỉ cho phép chọn 1 file
                          type: FileType.any, // Cho phép chọn mọi loại file
                        );
                        if (result != null) {
                          setState(() {
                            pickedFile = result;
                            fileName = result.files.single.name; // Lấy tên file
                          });
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.amberAccent,
                        padding:
                            EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Chọn file',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                // Hiển thị tên file đã chọn
                fileName != null
                    ? Text(
                        'File đã chọn: $fileName',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                      )
                    : Text(
                        'Chưa có file nào được chọn',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                SizedBox(height: 16),
                // Nút "Thêm" để tải file lên
                Align(
                  alignment: Alignment.center,
                  child: ElevatedButton(
                    onPressed: pickedFile != null
                        ? () async {
                            // Gọi hàm để tải file lên API
                            controller.addResource(
                              file: File(pickedFile!.files.single.path!),
                            );
                          }
                        : null, // Vô hiệu hóa nút nếu chưa chọn file
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFF1157FA),
                      padding:
                          EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Thêm',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    ),
  );
}

// Hiển thị popup menu khi nhấn giữ
// void showPopupMenu({
//   required BuildContext context,
//   required Offset position,
//   required List<PopupMenuEntry<String>> items,
//   required void Function(String) onSelected,
// }) {
//   showMenu(
//     context: context,
//     position: RelativeRect.fromLTRB(position.dx, position.dy, 0, 0),
//     items: items,
//     elevation: 8.0,
//   ).then((value) {
//     if (value != null) {
//       onSelected(value);
//     }
//   });
// }
void showPopupMenu({
  required BuildContext context,
  required Offset position,
  required List<PopupMenuEntry<String>> items,
  required void Function(String) onSelected,
}) {
  // Tính toán vị trí của menu dựa trên vị trí nhấn giữ
  final RenderBox overlay =
      Overlay.of(context).context.findRenderObject() as RenderBox;
  final RelativeRect positionRect = RelativeRect.fromLTRB(
    position.dx, // Vị trí x của điểm nhấn giữ
    position.dy, // Vị trí y của điểm nhấn giữ
    position.dx,
    position.dy,
  );

  showMenu(
    context: context,
    position: positionRect,
    items: items,
    elevation: 8.0,
  ).then((value) {
    if (value != null) {
      onSelected(value);
    }
  });
}
