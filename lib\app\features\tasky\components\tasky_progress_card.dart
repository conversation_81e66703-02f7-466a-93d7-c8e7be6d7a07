import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class ProgressCardData {
  final int totalUndone;
  final int totalTaskInProress;
  final int totalTaskDone;

  const ProgressCardData({
    required this.totalUndone,
    required this.totalTaskInProress,
    required this.totalTaskDone,
  });
}

class ProgressCard extends StatelessWidget {
  const ProgressCard({
    required this.data,
    required this.onPressedCheck,
    super.key,
  });

  final ProgressCardData data;
  final Function() onPressedCheck;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Align(
              alignment: Alignment.bottomRight,
              child: Transform.translate(
                offset: const Offset(10, 30),
                child: SizedBox(
                  height: 120,
                  width: 200,
                  child: SvgPicture.asset(
                    ImageVectorPath.happy2,
                    fit: BoxFit.fitHeight,
                  ),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              left: kSpacing,
              top: kSpacing - 10,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Bạn có ${data.totalUndone} công việc chưa hoàn thành",
                  style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(
                  "${data.totalTaskInProress} công việc đang thực hiện",
                  style: theme.textTheme.titleSmall?.copyWith(color: theme.textTheme.titleMedium?.color?.withOpacity(0.7)),
                ),
                Text(
                  "${data.totalTaskDone} công việc đã hoàn thành",
                  style: theme.textTheme.titleSmall?.copyWith(color: theme.textTheme.titleMedium?.color?.withOpacity(0.7)),
                ),
                //const SizedBox(height: kSpacing),
                MyButton.small(
                  borderRadiusAll: 8,
                  backgroundColor: theme.primaryColor,
                  onPressed: onPressedCheck,
                  child: SizedBox(
                    width: 145,
                    child: Row(
                      children: [
                        Icon(LucideIcons.workflow, color: theme.colorScheme.onPrimary,),
                        MySpacing.width(4),
                        MyText.labelLarge("Tạo mới công việc", color: theme.colorScheme.onPrimary ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
