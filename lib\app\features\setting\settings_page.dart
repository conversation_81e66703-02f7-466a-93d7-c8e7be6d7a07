import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/features/navbar/navbar_controller.dart';
import 'package:tvumobile/app/features/setting/user_card.dart';
import 'package:tvumobile/app/shared_components/appbar.dart';
import 'package:tvumobile/app/shared_components/confirm_dialog.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

import '/app/shared/babstrap_settings_screen/babstrap_settings_screen.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  UserInfo? userInfo = LocalStorageServices.getUserInfo();

  ImageProvider getProfileImage() {
    if (userInfo!.picture == null) {
      return const AssetImage('assets/images/app_icon_150.png');
    }
    return NetworkImage(userInfo!.picture.toString());
  }

  bool isDark = false;

  void changeTheme() {
    bool isLight = LocalStorageServices.getThemeIsLight();
    Get.changeThemeMode(isLight ? ThemeMode.dark : ThemeMode.light);
    Get.changeTheme(isLight ? AppTheme.dark : AppTheme.light);
    LocalStorageServices.setThemeIsLight(!isLight);
    //Get.reload();
  }
  void onBackPress() {
    Get.find<NavbarController>().onTap(0);
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    isDark = !LocalStorageServices.getThemeIsLight();
    return Scaffold(
      appBar: appBarTitle(context, "Cá nhân hoá", onBack: onBackPress),
      body: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16),
        child: ListView(
          children: [
            // user card
            UserCard(
              userName: userInfo!.hoten.toString(),
              userProfilePic: getProfileImage(),
              backgroundMotifColor: theme.primaryColor.withOpacity(0.6),
              userMoreInfo: MyText.labelLarge(
                  (userInfo!.isteacher.toString() != "true")
                      ? "Lớp:\nKhoá:"
                      : "Đơn vị:"),
            ),
            SettingsGroup(
              settingsGroupTitle: "Cá nhân",
              settingsGroupTitleStyle: theme.textTheme.titleLarge,
              items: [
                SettingsItem(
                  onTap: () {},
                  icons: Icons.fingerprint,
                  iconStyle: IconStyle(
                    iconsColor: Colors.white,
                    withBackground: true,
                    backgroundColor: Colors.red,
                  ),
                  title: 'Cài đặt riêng tư',
                  subtitle: "Cải thiện quyền riêng tư của bạn",
                  titleStyle: theme.textTheme.titleMedium
                      ?.copyWith(color: theme.colorScheme.onSurface),
                  subtitleStyle: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.85)),
                  //backgroundColor: Colors.grey,
                ),
                SettingsItem(
                  onTap: () {
                    changeTheme();
                  },
                  icons: Icons.dark_mode_rounded,
                  iconStyle: IconStyle(
                    iconsColor: Colors.white,
                    withBackground: true,
                    backgroundColor: Colors.red,
                  ),
                  title: 'Giao diện tối',
                  subtitle: "Tự động",
                  titleStyle: theme.textTheme.titleMedium
                      ?.copyWith(color: theme.colorScheme.onSurface),
                  subtitleStyle: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.85)),
                  trailing: Switch.adaptive(
                    value: isDark,
                    onChanged: (value) {
                      changeTheme();
                    },
                  ),
                ),
              ],
            ),
            SettingsGroup(
              items: [
                SettingsItem(
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) => AboutApp(),
                    );
                  },
                  icons: Icons.info_rounded,
                  iconStyle: IconStyle(
                    backgroundColor: Colors.purple,
                  ),
                  title: 'Giới thiệu',
                  subtitle: "Tìm hiểu thêm về TVU Mobile",
                  titleStyle: theme.textTheme.titleMedium
                      ?.copyWith(color: theme.colorScheme.onSurface),
                  subtitleStyle: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.85)),
                ),
              ],
            ),
            // You can add a settings title
            SettingsGroup(
              settingsGroupTitle: "Tài khoản",
              settingsGroupTitleStyle: theme.textTheme.titleLarge,
              items: [
                SettingsItem(
                  onTap: () async {
                    if (await ConfirmDialog(
                      context,
                      title: const MyText.titleLarge('Xác nhận'),
                      content: const MyText.bodyMedium(
                          'Bạn có chắc chắn muốn \nthoát tài khoản khỏi hệ thống?'
                              ),
                      textOK: MyText.labelLarge(
                        'Đồng ý',
                        color: theme.colorScheme.error,
                      ),
                      textCancel: MyText.labelLarge(
                        'Huỷ bỏ',
                        color: theme.colorScheme.onSurface,
                      ),
                    )) {
                      LocalStorageServices.clear();
                      LocalStorageServices.setIsNew(false);
                      Get.offAllNamed(Routes.LOGIN);
                    }
                    // ignore: avoid_print
                    return print('pressedCancel');
                  },
                  icons: Icons.exit_to_app_rounded,
                  title: "Thoát hệ thống",
                  titleStyle: theme.textTheme.titleMedium
                      ?.copyWith(color: theme.colorScheme.onSurface),
                  subtitleStyle: theme.textTheme.titleSmall,
                ),
                SettingsItem(
                  onTap: () {},
                  icons: CupertinoIcons.repeat,
                  title: "Thay đổi email",
                  titleStyle: theme.textTheme.titleMedium?.copyWith(decoration: TextDecoration.lineThrough),
                  subtitleStyle: theme.textTheme.bodyMedium,
                ),
                SettingsItem(
                  onTap: () {},
                  icons: CupertinoIcons.delete_solid,
                  title: "Xoá tài khoản",
                  titleStyle: theme.textTheme.titleMedium?.copyWith(
                      color: Colors.red, fontWeight: FontWeight.bold, decoration: TextDecoration.lineThrough),
                  subtitleStyle: theme.textTheme.titleSmall,
                ),
                SettingsItem(
                  onTap: () {Get.toNamed(AppPages.UPDATER);},
                  icons: CupertinoIcons.repeat,
                  title: "Cập nhật phần mềm",
                  titleStyle: theme.textTheme.titleMedium?.copyWith(color: theme.colorScheme.primary),
                  subtitleStyle: theme.textTheme.bodyMedium,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class AboutApp extends StatelessWidget {
  const AboutApp({super.key});

  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    String aboutHtml = "<div>Chào mừng đến với TVU Mobile, ứng dụng chính thức và đa năng của Trường Đại học Trà Vinh, được thiết kế để cung cấp một trải nghiệm học tập và quản lý thông tin tiện lợi ngay trên điện thoại di động của bạn. TVU Mobile mang lại một cách tiếp cận mới mẻ và hiện đại trong việc kết nối sinh viên, giảng viên và nhân viên của trường, đồng thời cung cấp các thông tin và dịch vụ quan trọng một cách nhanh chóng và an toàn.<br><br>Tính Năng Chính:<br><br>Thông Tin Học Tập: Truy cập dễ dàng vào thời khóa biểu, kết quả học tập, và thông tin về các khóa học. Sinh viên có thể theo dõi tiến trình học tập và xem xét các kết quả thi, bài tập.<br><br>Tin Tức và Thông Báo: Nhận các cập nhật mới nhất từ trường, bao gồm thông báo quan trọng, tin tức sự kiện, và các cơ hội ngoại khóa.<br><br>Tương Tác Giảng Viên - Sinh Viên: Một kênh liên lạc thuận tiện giữa sinh viên và giảng viên, giúp trao đổi thông tin, thảo luận về các vấn đề học tập và nhận phản hồi.<br><br>Quản Lý Hồ Sơ Cá Nhân: Sinh viên có thể cập nhật và quản lý thông tin cá nhân của mình, bao gồm thông tin liên lạc, lịch sử học tập, và các tài liệu liên quan.<br><br>Thư Viện Điện Tử: Truy cập vào một kho tài nguyên học thuật phong phú, bao gồm sách, tạp chí, và các ấn phẩm nghiên cứu.<br><br>Hỗ Trợ và Dịch Vụ Sinh Viên: Cung cấp thông tin về các dịch vụ hỗ trợ sinh viên, bao gồm tư vấn học tập, hỗ trợ tài chính, và sức khỏe sinh viên.<br><br>Giao Diện Thân Thiện và Tùy Chỉnh: Giao diện người dùng trực quan và dễ sử dụng, với khả năng tùy chỉnh để phù hợp với nhu cầu cá nhân.<br><br>TVU Mobile là công cụ không thể thiếu cho mọi thành viên của Trường Đại học Trà Vinh, giúp kết nối và tối ưu hóa trải nghiệm học tập và quản lý thông tin cá nhân. Tải ứng dụng ngay hôm nay để bắt đầu khám phá và tận hưởng những tiện ích tuyệt vời mà TVU Mobile mang lại!</div>";
    return Dialog(
      elevation: 0.0,
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: theme.cardColor,
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.circular(0),
          boxShadow: const [
            BoxShadow(color: Colors.black26, blurRadius: 10.0, offset: Offset(0.0, 10.0)),
          ],
        ),
        width: MediaQuery.of(context).size.width,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min, // To make the card compact
            children: <Widget>[
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(height: 150, color: Colors.indigo),
                  Column(
                    children: [
                      const Image(image: AssetImage('assets/images/app_icon_150.png'), height: 100, fit: BoxFit.cover),
                      MySpacing.height(8),
                      MyText.titleLarge('TVU Mobile', textAlign: TextAlign.center, color: theme.colorScheme.onPrimary,),
                    ],
                  )
                ],
              ),
              MySpacing.height(30),
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16),
                child: HtmlWidget(
                  aboutHtml,
                  textStyle: Theme.of(context).textTheme.bodyLarge ?? const TextStyle(),
                ),
              ),
              MySpacing.height(16),
              GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: MyButton.medium(onPressed: () {
                    Get.back();
                  }, child:  MyText.labelLarge('Đóng', color: theme.colorScheme.onPrimary,))
              ),
              MySpacing.height(16),
            ],
          ),
        ),
      ),
    );
  }
}
