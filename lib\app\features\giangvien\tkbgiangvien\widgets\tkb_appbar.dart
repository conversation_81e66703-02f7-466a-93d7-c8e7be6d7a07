
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import '../tkb_giangvien_controller.dart';

AppBar buildAppBarTKB(BuildContext context, TkbGiangVienController controller) {
  ThemeData theme = Theme.of(context);
  return AppBar(
    toolbarHeight: 48, // Set this height
    foregroundColor: theme.colorScheme.onPrimary,
    iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
    actions: [],
    flexibleSpace: Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      //color: theme.primaryColor,
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -80,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset('assets/images/app_icon.png', height: 150, width: 150,opacity: const AlwaysStoppedAnimation(.15),)
            ),
          ),
          Positioned(
            right: -7,
            top: -160,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: -21,
            top: -195,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          //----------------Data row----------------//
          Center(
            child: Padding(
              padding: const EdgeInsets.only(top: 24),
              child: InkWell(
                onTap: () {
                  //controller.openDrawer();
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Obx(() {
                      return MyText.titleMedium(
                        controller.appbarTitle.value.toUpperCase(),
                        color: theme.colorScheme.onPrimary,
                        letterSpacing: 0.01,
                      );
                    }),
                    Icon(Icons.arrow_drop_down, color: theme.colorScheme.onPrimary,)
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    ),
  );
}