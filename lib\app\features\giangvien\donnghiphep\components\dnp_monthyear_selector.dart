import 'package:flutter/material.dart';
import 'package:flutter_toggle_tab/flutter_toggle_tab.dart';
import 'package:get/get.dart';
import 'package:lazy_load_scrollview/lazy_load_scrollview.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/dnp_drawer.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/duyetdnp_controller.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/nghiphep_home_appbar.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/empty_widget.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

// -------------------------- WIDGET CHỌN NĂM --------------------------- //
// ---------------- <PERSON><PERSON><PERSON> thị một hàng (Row) để chọn năm ----------------- //
Widget buildYearSelector(ThemeData theme, DuyetDNPController controller) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.center,
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      _buildYearNavigationButton(
        theme,
        onTap: controller.currentYearMinus,
        icon: Icons.arrow_back_ios_new,
        label: "Trước".tr,
        isEnabled: controller.currentYear.value > 2006,
        islast: false,
      ),
      MyText.titleLarge(
        "Năm ${controller.currentYear.value}",
        style: theme.textTheme.titleMedium!.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.onSurface,
        ),
      ),
      _buildYearNavigationButton(
        theme,
        onTap: controller.currentYearPlus,
        icon: Icons.arrow_forward_ios,
        label: "Sau".tr,
        isEnabled: controller.currentYear.value < controller.maxYear.value,
        islast: true,
      ),
    ],
  );
}

// --------- Tạo một nút điều hướng (trước/sau) để thay đổi năm --------- //
Widget _buildYearNavigationButton(
  ThemeData theme, {
  required VoidCallback onTap,
  required IconData icon,
  required String label,
  required bool isEnabled,
  required bool islast,
}) {
  return GestureDetector(
    onTap: isEnabled ? onTap : null,
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (!islast)
          Icon(
            icon,
            size: 17,
            color:
                isEnabled ? theme.colorScheme.onSurface : theme.disabledColor,
          ),
        if (!islast) const SizedBox(width: 4),
        MyText.labelLarge(
          label,
          color: isEnabled ? theme.colorScheme.onSurface : theme.disabledColor,
          fontWeight: 700,
        ),
        if (islast) const SizedBox(width: 4),
        if (islast)
          Icon(
            icon,
            size: 17,
            color:
                isEnabled ? theme.colorScheme.onSurface : theme.disabledColor,
          ),
      ],
    ),
  );
}

// -------------------------- WIGET CHỌN THÁNG -------------------------- //
// ----- Hiển thị ds 12 tháng dạng ListView ngang để người dùng chọn ---- //
Widget buildMonthSelector(ThemeData theme, DuyetDNPController controller) {
  final months = [
    "Tất cả",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
    "11",
    "12"
  ];
  return SizedBox(
    height: 70,
    child: ListView.separated(
      controller: controller.monthScroll,
      scrollDirection: Axis.horizontal,
      itemBuilder: (context, index) {
        final month = months[index];
        return _buildMonthItem(theme, month, controller);
      },
      separatorBuilder: (context, index) => const SizedBox(width: 2),
      itemCount: months.length,
    ),
  );
}

// ----------------- Tạo một item đại diện cho một tháng ---------------- //
Widget _buildMonthItem(
    ThemeData theme, String month, DuyetDNPController controller) {
  return GestureDetector(
    onTap: () => controller.onMonthTap(month),
    child: Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(7),
        color: _getMonthColor(theme, month, controller),
        border: Border.all(
          color: theme.scaffoldBackgroundColor,
        ),
      ),
      padding: const EdgeInsets.all(8),
      child: SizedBox(
        child: Column(
          children: [
            MyText.labelSmall(
              "tháng",
              textAlign: TextAlign.center,
              color: theme.colorScheme.onPrimary,
            ),
            MyText.titleLarge(
              month == "Tất cả" ? "#" : month,
              textAlign: TextAlign.center,
              color: theme.colorScheme.onPrimary,
            ),
          ],
        ),
      ),
    ),
  );
}

// --------- Xác định màu nền cho item tháng dựa trên trạng thái -------- //
Color _getMonthColor(
    ThemeData theme, String month, DuyetDNPController controller) {
  if (controller.currentYear.value == controller.maxYear.value &&
      month != "Tất cả") {
    int monthInt = int.tryParse(month) ?? 0;
    if (monthInt > controller.maxMonth) {
      return theme.primaryColor.withOpacity(0.25);
    }
    if (month == controller.currentMonth.value) {
      return theme.primaryColor;
    }
    return theme.primaryColor.withOpacity(0.7);
  } else {
    if (month == controller.currentMonth.value) {
      return theme.primaryColor;
    }
    return theme.primaryColor.withOpacity(0.7);
  }
}
