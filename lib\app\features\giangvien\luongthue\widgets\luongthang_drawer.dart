
import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/config/themes/custom_theme.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/luongthuethang_controller.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

Widget buildLuongThangDrawer(BuildContext context, LuongThueController controller) {
  var theme = Theme.of(context);
  return MyContainer.none(
    margin:
    MySpacing.fromLTRB(16, MySpacing.safeAreaTop(context) + 16, 16, 260),
    borderRadiusAll: 4,
    clipBehavior: Clip.antiAliasWithSaveLayer,
    color: theme.scaffoldBackgroundColor,
    child: Drawer(
        child: SingleChildScrollView(
          child: Container(
            color: theme.scaffoldBackgroundColor,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  padding: MySpacing.only(left: 20, bottom: 0, top: 8, right: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      MySpacing.height(16),
                      MyContainer(
                        padding: MySpacing.fromLTRB(12, 4, 12, 4),
                        borderRadiusAll: 4,
                        color: theme.primaryColor.withOpacity(0.9),
                        borderColor: theme.primaryColor,
                        bordered: true,
                        child: MyText.bodyLarge("THÔNG TIN LƯƠNG - THUẾ",
                            color: theme.colorScheme.onPrimary,
                            fontWeight: 700,
                            letterSpacing: 0.2),
                      ),
                    ],
                  ),
                ),
                MySpacing.height(8),
                const Divider(
                  thickness: 1,
                ),
                MySpacing.height(16),
                Container(
                  margin: MySpacing.x(20),
                  child: Column(
                    children: [
                      MySpacing.height(10),
                      InkWell(
                        onTap: () {
                          controller.drawerClick("TO");
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 12,
                              borderRadiusAll: 4,
                              color: CustomTheme.peach.withAlpha(20),
                              child: Icon(LucideIcons.wallet,
                                size: 20,
                                color: CustomTheme.peach,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Thông tin Lương',
                                color: CustomTheme.peach,
                                fontWeight: 700,
                              ),
                            ),
                          ],
                        ),
                      ),
                      MySpacing.height(10),
                      InkWell(
                        onTap: () {
                          controller.drawerClick("TT");
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 12,
                              borderRadiusAll: 4,
                              color: CustomTheme.darkGreen.withAlpha(20),
                              child: Icon(LucideIcons.badgePercent,
                                size: 20,
                                color: CustomTheme.skyBlue,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Thông tin Thuế (tháng)',
                                color: CustomTheme.skyBlue,
                                fontWeight: 700,
                              ),
                            ),
                          ],
                        ),
                      ),
                      MySpacing.height(10),
                      InkWell(
                        onTap: () {
                          controller.drawerClick("TN");
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 12,
                              borderRadiusAll: 4,
                              color: CustomTheme.purple.withAlpha(20),
                              child: Icon(LucideIcons.candlestickChart,
                                size: 20,
                                color: CustomTheme.darkGreen,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Thông tin Thuế (năm)',
                                color: CustomTheme.darkGreen,
                                fontWeight: 700,
                              ),
                            ),
                          ],
                        ),
                      ),
                      MySpacing.height(5),
                    ],
                  ),
                ),
              ],
            ),
          ),
        )),
  );
}
