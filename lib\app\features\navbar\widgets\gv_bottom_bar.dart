

import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:stylish_bottom_bar/stylish_bottom_bar.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/features/navbar/navbar_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

StylishBottomBar GvBottomBar(NavbarController controller, BuildContext context) {
  final theme = Theme.of(context);
  return StylishBottomBar(
    // option: AnimatedBarOptions(
    //   // iconSize: 32,
    //   barAnimation: BarAnimation.blink,
    //   iconStyle: IconStyle.animated,

    //   // opacity: 0.3,
    // ),
    option: DotBarOptions(
      dotStyle: DotStyle.tile,
    ),
    items: [
      BottomBarItem(
        icon: const Icon(
          LucideIcons.home,
        ),
        //selectedIcon: const Icon(LucideIcons.home),
        selectedColor: mlPrimaryColor,
        unSelectedColor: mlPrimaryTextColor,
        title: MyText.labelMedium(
          'Trang chủ',
          color: mlPrimaryTextColor,
        ),
        //badge: const Text('9+'),
        //showBadge: true,
        //badgeColor: Colors.purple,
        //badgePadding: const EdgeInsets.only(left: 4, right: 4),
      ),
      BottomBarItem(
        icon: const Icon(LucideIcons.layoutDashboard),
        selectedColor: mlPrimaryColor,
        unSelectedColor: mlPrimaryTextColor,
        // unSelectedColor: Colors.purple,
        // backgroundColor: Colors.orange,
        title: MyText.labelMedium(
          'Công văn',
          color: mlPrimaryTextColor,
        ),
      ),
      BottomBarItem(
          icon: const Icon(
            LucideIcons.calendarDays,
          ),
          selectedColor: mlPrimaryColor,
          unSelectedColor: mlPrimaryTextColor,
          title: MyText.labelMedium(
            'Lịch trình',
            color: mlPrimaryTextColor,
          )),
      BottomBarItem(
          icon: const Icon(
              LucideIcons.settings
          ),
          selectedColor: mlPrimaryColor,
          unSelectedColor: mlPrimaryTextColor,
          title: MyText.labelMedium(
            'Cá nhân',
            color: mlPrimaryTextColor,
          )),

    ],
    hasNotch: true,
    fabLocation: StylishBarFabLocation.end,
    currentIndex: controller.selectedIndex.value,
    notchStyle: NotchStyle.square,
    onTap: (index) {
      if (index == controller.selectedIndex.value) return;
      controller.onTap(index);
      //controller.selectedIndex.value = index;
    },
    backgroundColor: theme.cardColor,
  );
}