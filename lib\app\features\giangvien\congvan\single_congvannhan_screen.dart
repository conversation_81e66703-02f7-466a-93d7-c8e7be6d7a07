// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/data/models/tms/congvan/cong_van_nhan.dart';
import 'package:tvumobile/app/features/giangvien/congvan/single_pdfviewer_screen.dart';
import 'package:tvumobile/app/shared_components/file_icon.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:url_launcher/url_launcher.dart';

class SingleCongVanNhanScreen extends StatefulWidget {
  const SingleCongVanNhanScreen({super.key, this.heroKey, this.doc});
  final String? heroKey;
  final TmsCongVanModel? doc;

  @override
  // ignore: library_private_types_in_public_api
  _SingleCongVanNhanScreenState createState() =>
      _SingleCongVanNhanScreenState();
}

class _SingleCongVanNhanScreenState extends State<SingleCongVanNhanScreen> {
  late String? heroKey;
  late TmsCongVanModel? doc;
  final ApiProvider apiProvider = Get.find<ApiProvider>();

  @override
  Future<void> initState() async {
    super.initState();
    heroKey = widget.heroKey;
    doc = widget.doc;
    if (doc == null) {}
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    if (doc == null) {
      return Center(
          child: MyText.displayMedium(
        "LỖI KHÔNG XÁC ĐỊNH",
        color: Colors.white,
      ));
    }
    String nguoichuyen =
        '${doc!.nguoiChuyenCongVan!.ho} ${doc?.nguoiChuyenCongVan!.tenDem.toString() == "null" ? '' : '${doc!.nguoiChuyenCongVan!.tenDem} '}${doc!.nguoiChuyenCongVan!.ten}';

    return Scaffold(
        appBar: AppBar(
          backgroundColor: theme.primaryColor.withOpacity(0.95),
          centerTitle: true,
          toolbarHeight: 42,
          leading: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Icon(
              LucideIcons.arrowLeft,
              color: theme.colorScheme.onPrimary,
            ),
          ),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText.titleMedium(
                "convannhan_chitiet".tr.toUpperCase(),
                color: theme.colorScheme.onPrimary,
              ),
            ],
          ),
          actions: [
            Icon(
              LucideIcons.fileMinus,
              size: 16,
              color: theme.colorScheme.onPrimary,
            ),
            MySpacing.width(20),
          ],
        ),
        body: MyContainer(
          child: ListView(
              padding: const EdgeInsets.only(left: 0.0),
              children: <Widget>[
                Column(
                  children: [
                    MySpacing.height(8),
                    Hero(
                        tag: heroKey.toString(),
                        child: MyText.titleMedium(
                          doc!.document!.trichYeu.toString(),
                          color: mlPrimaryTextColor,
                        )),
                    MySpacing.height(8),
                    nguoichuyen.trim() != ""
                        ? Row(
                            children: [
                              const Icon(LucideIcons.squareArrowOutUpRight),
                              MySpacing.width(10),
                              MyText.labelMedium("Người chuyển:"),
                              MySpacing.width(5),
                              MyText.labelMedium(nguoichuyen),
                            ],
                          )
                        : Container(),
                    MySpacing.height(8),
                    Row(
                      children: [
                        const Icon(LucideIcons.building2),
                        MySpacing.width(10),
                        MyText.labelMedium("Nơi ban hành:"),
                        MySpacing.width(5),
                        Expanded(
                            child: MyText.labelMedium(
                          doc!.document!.noiBanHanh!.tenDonVi.toString(),
                          maxLines: 2,
                        )),
                      ],
                    ),
                    MySpacing.height(8),
                    Row(
                      children: [
                        const Icon(LucideIcons.notebookTabs),
                        MySpacing.width(10),
                        MyText.labelMedium("Loại văn bản:"),
                        MySpacing.width(5),
                        MyText.labelMedium(
                            doc!.document!.category!.ten.toString()),
                      ],
                    ),
                    MySpacing.height(10),
                    Divider(
                      color: theme.dividerColor.withOpacity(0.6),
                      height: 1,
                    ),
                    MyText.labelLarge("Đính kèm"),
                    Divider(
                      color: theme.dividerColor.withOpacity(0.6),
                      height: 1,
                    ),
                    MySpacing.height(8),
                    MyContainer(
                      paddingAll: 0,
                      child: ListView.separated(
                        scrollDirection: Axis.vertical,
                        shrinkWrap: true,
                        itemCount: doc!.document!.cvFiles.length,
                        separatorBuilder: (BuildContext context, int index2) =>
                            Divider(
                          height: 0.7,
                          color: Colors.grey[300],
                        ),
                        itemBuilder: (BuildContext context, int index2) {
                          return ListTile(
                            leading: FileIcon(
                                '${doc!.document!.cvFiles[index2].filePath}',
                                size: 18),
                            title: MyText.labelMedium(doc!
                                .document!.cvFiles[index2].fileName
                                .toString()),
                            //subtitle: Text(product.document!.cvFiles[index2].getFilePath),
                            onTap: () {
                              if (doc!.document!.cvFiles[index2].getFileExt
                                  .contains('pdf')) {
                                onTapItem(index2);
                              } else {
                                launchUrl(
                                    Uri.parse(
                                        "https://tms.tvu.edu.vn/${doc!.document!.cvFiles[index2].getFilePath}"),
                                    mode: LaunchMode.externalApplication);
                              }
                            },
                          );
                        },
                      ),
                    )
                  ],
                ),
              ]),
        ));
  }

  final List<Color> bgColor = [
    const Color(0xFFFFE2C2),
    const Color(0xFFD9839F),
    const Color(0xFFFFE2C2)
  ];
  //final _random = Random();

  onTapItem(int index) {
    Get.to(() => SinglePdfViewerScreen(doc!.document!.cvFiles, index),
        transition: Transition.fadeIn);
  }
}
