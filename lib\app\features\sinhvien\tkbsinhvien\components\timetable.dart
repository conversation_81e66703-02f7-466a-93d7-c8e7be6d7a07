import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_timetable/flutter_timetable.dart';
import 'package:intl/intl.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

Widget buildTimeTable(BuildContext context, TimetableController controller, List<TimetableItem<String>> items) {
  // return Timetable<String>(
  //   controller: controller,
  //   items: items,
  // );
  var theme = Theme.of(context);
  return Timetable<String>(
    controller: controller,
    items: items,
    cellBuilder: (datetime) => Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blueGrey, width: 0.2),
      ),
      child: Center(
        child: MyText.labelSmall(
          DateFormat("MM/d/yyyy\nha", "vi_VN").format(datetime),
          color: Color((0xff000000 + (0x002222 * datetime.hour) + (0x110000 * datetime.day)).toInt()).withOpacity(0.2),
          textAlign: TextAlign.center,
        ),
      ),
    ),
    cornerBuilder: (datetime) => Container(
      color: Colors.accents[datetime.day % Colors.accents.length],
      child: Center(child: MyText.labelLarge("${datetime.year}",fontWeight: 700,)),
    ),
    headerCellBuilder: (datetime) {
      final color = Colors.primaries[datetime.day % Colors.accents.length];
      return Container(
        decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: color, width: 2)),
        ),
        child: Center(
          child: MyText.labelLarge(
            DateFormat("EEEE\ndd/MM", "vi_VN").format(datetime),
            color: theme.primaryColor,
            fontWeight: 600,
            textAlign: TextAlign.center,
          ),
        ),
      );
    },
    hourLabelBuilder: (time) {
      // ignore: unused_local_variable
      final hour = time.hour == 12 ? 12 : time.hour % 12;
      final isCurrentHour = time.hour == DateTime.now().hour;
      return Text(
        //"$hour$period",
        "${time.hour}:00",
        style: TextStyle(
          fontSize: 14,
          fontWeight: isCurrentHour ? FontWeight.bold : FontWeight.normal,
        ),
      );
    },
    itemBuilder: (item) {
      var dats = item.data?.split(" - ");
      return InkWell(
        onTap: () {
          if (kDebugMode) {
            print(dats[0]);
          }
        },
        child: Card(
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(color: Colors.grey.shade300, width: 0.5)
          ),
          elevation: 2,
          child: Center(
            child: Column(
              children: [
                MySpacing.height(4),
                MyText.labelMedium(
                  dats!.isNotEmpty ? dats[1].trim() : "",
                  fontWeight: 600,
                ),
                if(dats.length > 2)
                  MyText.labelMedium(
                    dats[2].trim(),
                  ),
              ],
            ),
          ),
        ),
      );
    },
    nowIndicatorColor: Colors.red,
    snapToDay: true,
  );
}