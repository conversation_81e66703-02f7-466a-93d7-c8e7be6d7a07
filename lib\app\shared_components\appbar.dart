import 'package:avatar_glow/avatar_glow.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/shared_components/circular_cached_network_image.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

PreferredSizeWidget appBar1(BuildContext context) {
  UserInfo? userInfo = LocalStorageServices.getUserInfo();
  ThemeData theme = Theme.of(context);

  Widget getProfileImage() {
    if (userInfo!.picture == null) {
      return Image.asset(
        'assets/images/app_icon_150.png',
        height: 100,
        width: 100,
        opacity: const AlwaysStoppedAnimation(.89),
      );
    }
    return CircularCachedNetworkImage(
      imageURL: userInfo.picture.toString(),
      size: 100,
      borderColor: Colors.transparent,
      fit: BoxFit.cover,
      alignment: Alignment.topCenter,
    );
  }

  return PreferredSize(
      preferredSize: const Size.fromHeight(86.0),
      child: Container(
        //color: theme.primaryColor,
        decoration: BoxDecoration(
          color: theme.primaryColor,
          boxShadow: const [
            BoxShadow(
              color: Colors.black12,
              spreadRadius: 1,
              blurRadius: 1,
              offset: Offset(0, 1), // changes position of shadow
            ),
          ],
        ),
        child: Stack(
          fit: StackFit.expand,
          children: [
            //----------------white circles decor----------------//
            Positioned(
              right: 0,
              top: -80,
              child: CircleAvatar(
                  backgroundColor: Colors.white.withOpacity(0.05),
                  radius: 111,
                  child: Image.asset(
                    'assets/images/app_icon.png',
                    height: 150,
                    width: 150,
                    opacity: const AlwaysStoppedAnimation(.15),
                  )),
            ),
            Positioned(
              right: -7,
              top: -160,
              child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
              ),
            ),
            Positioned(
              right: -21,
              top: -195,
              child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
              ),
            ),
            //----------------Data row----------------//
            Positioned(
              bottom: 12,
              right: 16,
              left: 16,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  AvatarGlow(
                    //startDelay: const Duration(milliseconds: 1000),
                    glowColor: Colors.white,
                    glowShape: BoxShape.circle,
                    animate: true,
                    curve: Curves.fastOutSlowIn,
                    glowRadiusFactor: 0.16,
                    child: Material(
                      elevation: 8.0,
                      shape: const CircleBorder(),
                      color: Colors.transparent,
                      child: CircleAvatar(
                        //backgroundImage: NetworkImage(homeController.userInfo!.picture.toString()),
                        radius: 32.0,
                        child: getProfileImage(),
                      ),
                    ),
                  ),
                  MySpacing.width(12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      MyText.labelMedium(
                        "Strings.goodMorning".tr,
                        color: theme.colorScheme.onPrimary.withOpacity(0.85),
                      ),
                      MyText.titleMedium(
                        userInfo!.hoten.toString(),
                        color: theme.colorScheme.onPrimary,
                      ),
                    ],
                  ),
                  const Spacer(),

                  //----------------Theme Button----------------//
                  InkWell(
                    onTap: () {},
                    child: Ink(
                      child: SizedBox(
                        height: 39,
                        width: 39,
                        child: Icon(
                          Get.isDarkMode ? LucideIcons.moon : LucideIcons.sun,
                          size: 18,
                          color: theme.colorScheme.onPrimary,
                        ),
                      ),
                    ),
                  ),
                  MySpacing.width(5),
                  //----------------Language Button----------------//
                  InkWell(
                    onTap: () {},
                    child: Ink(
                      child: SizedBox(
                        height: 39,
                        width: 39,
                        child: Stack(
                          clipBehavior: Clip.none,
                          children: <Widget>[
                            const SizedBox(
                              height: 39,
                              width: 39,
                              child: Icon(
                                LucideIcons.bell,
                                size: 18,
                                color: Colors.white,
                              ),
                            ),
                            Positioned(
                              right: 9,
                              top: 7,
                              child: MyContainer.rounded(
                                paddingAll: 4,
                                color: theme.colorScheme.error.withAlpha(500),
                                child: Container(),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ));
}

PreferredSizeWidget appBarTitle(BuildContext context, String title,
    {Function? onBack}) {
  ThemeData theme = Theme.of(context);
  return PreferredSize(
      preferredSize: const Size.fromHeight(48.0),
      child: Container(
        //color: theme.primaryColor,
        decoration: BoxDecoration(
          color: theme.primaryColor,
          boxShadow: const [
            BoxShadow(
              color: Colors.black12,
              spreadRadius: 1,
              blurRadius: 1,
              offset: Offset(0, 1), // changes position of shadow
            ),
          ],
          borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(10),
              bottomRight: Radius.circular(10)),
        ),
        child: Stack(
          fit: StackFit.expand,
          children: [
            //----------------white circles decor----------------//
            Positioned(
              right: 0,
              top: -80,
              child: CircleAvatar(
                  backgroundColor: Colors.white.withOpacity(0.05),
                  radius: 111,
                  child: Image.asset(
                    'assets/images/app_icon.png',
                    height: 150,
                    width: 150,
                    opacity: const AlwaysStoppedAnimation(.15),
                  )),
            ),
            Positioned(
              right: -7,
              top: -160,
              child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
              ),
            ),
            Positioned(
              right: -21,
              top: -195,
              child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
              ),
            ),
            //----------------Data row----------------//
            Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                      width: 60,
                      child: IconButton(
                          onPressed: () {
                            if (onBack != null) {
                              onBack();
                            } else {
                              //print("IconButton back pressed");
                              Get.back();
                            }
                          },
                          icon: Icon(
                            Icons.arrow_back_ios_new,
                            color: theme.colorScheme.onPrimary,
                          ))),
                  MyText.titleMedium(
                    title.toUpperCase(),
                    color: theme.colorScheme.onPrimary,
                    fontWeight: 700,
                  ),
                  MySpacing.width(60),
                ],
              ),
            )
          ],
        ),
      ));
}
