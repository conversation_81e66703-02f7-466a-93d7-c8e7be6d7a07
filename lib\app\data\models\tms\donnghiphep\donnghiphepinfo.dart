class DonNghiPhepInfo {
  DonNghiPhepInfo({
    required this.soNgayDaNghi,
    required this.soNgayConLai,
    required this.lanhDaoDonviList,
    required this.loaiNghiPhep,
    required this.donVi,
  });

  final double? soNgayDaNghi;
  final double? soNgayConLai;
  final List<LanhDaoDonviList> lanhDaoDonviList;
  final List<LoaiNghiPhep> loaiNghiPhep;
  final DonVi? donVi;

  factory DonNghiPhepInfo.empty() {
    return DonNghiPhepInfo(
      soNgayDaNghi: 0,
      soNgayConLai: 0,
      lanhDaoDonviList: [],
      loaiNghiPhep: [],
      donVi: DonVi.empty(),
    );
  }

  DonNghiPhepInfo copyWith({
    double? soNgayDaNghi,
    double? soNgayConLai,
    List<LanhDaoDonviList>? lanhDaoDonviList,
    List<LoaiNghiPhep>? loaiNghiPhep,
    DonVi? donVi,
  }) {
    return DonNghiPhepInfo(
      soNgayDaNghi: soNgayDaNghi ?? this.soNgayDaNghi,
      soNgayConLai: soNgayConLai ?? this.soNgayConLai,
      lanhDaoDonviList: lanhDaoDonviList ?? this.lanhDaoDonviList,
      loaiNghiPhep: loaiNghiPhep ?? this.loaiNghiPhep,
      donVi: donVi ?? this.donVi,
    );
  }

  factory DonNghiPhepInfo.fromJson(Map<String, dynamic> json) {
    return DonNghiPhepInfo(
      soNgayDaNghi: (json["soNgayDaNghi"] as num?)?.toDouble(),
      soNgayConLai: (json["soNgayConLai"] as num?)?.toDouble(),
      lanhDaoDonviList: json["lanhDaoDonviList"] == null
          ? []
          : List<LanhDaoDonviList>.from(json["lanhDaoDonviList"]!
              .map((x) => LanhDaoDonviList.fromJson(x))),
      loaiNghiPhep: json["loaiNghiPhep"] == null
          ? []
          : List<LoaiNghiPhep>.from(
              json["loaiNghiPhep"]!.map((x) => LoaiNghiPhep.fromJson(x))),
      donVi: json["donVi"] == null ? null : DonVi.fromJson(json["donVi"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "soNgayDaNghi": soNgayDaNghi,
        "soNgayConLai": soNgayConLai,
        "lanhDaoDonviList": lanhDaoDonviList.map((x) => x.toJson()).toList(),
        "loaiNghiPhep": loaiNghiPhep.map((x) => x.toJson()).toList(),
        "donVi": donVi?.toJson(),
      };

  @override
  String toString() {
    return "$soNgayDaNghi, $soNgayConLai, $lanhDaoDonviList, $loaiNghiPhep, $donVi, ";
  }
}

class DonVi {
  DonVi({
    required this.id,
    required this.tenDonVi,
  });

  final int? id;
  final String? tenDonVi;
  // Add this factory constructor in the DonVi class
  factory DonVi.empty() {
    return DonVi(
      id: 0,
      tenDonVi: '',
    );
  }

  DonVi copyWith({
    int? id,
    String? tenDonVi,
  }) {
    return DonVi(
      id: id ?? this.id,
      tenDonVi: tenDonVi ?? this.tenDonVi,
    );
  }

  factory DonVi.fromJson(Map<String, dynamic> json) {
    return DonVi(
      id: json["id"],
      tenDonVi: json["tenDonVi"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "tenDonVi": tenDonVi,
      };

  @override
  String toString() {
    return "$id, $tenDonVi, ";
  }
}

class LanhDaoDonviList {
  LanhDaoDonviList({
    required this.id,
    required this.ho,
    required this.tenDem,
    required this.ten,
    required this.tenChucVu,
    required this.maVienChuc,
  });

  final int? id;
  final String? ho;
  final String? tenDem;
  final String? ten;
  final String? tenChucVu;
  final String? maVienChuc;
  // Add this factory constructor in the LanhDaoDonviList class
  factory LanhDaoDonviList.empty() {
    return LanhDaoDonviList(
      id: 0,
      ho: '',
      tenDem: '',
      ten: '',
      tenChucVu: '',
      maVienChuc: '',
    );
  }

  LanhDaoDonviList copyWith({
    int? id,
    String? ho,
    String? tenDem,
    String? ten,
    String? tenChucVu,
    String? maVienChuc,
  }) {
    return LanhDaoDonviList(
      id: id ?? this.id,
      ho: ho ?? this.ho,
      tenDem: tenDem ?? this.tenDem,
      ten: ten ?? this.ten,
      tenChucVu: tenChucVu ?? this.tenChucVu,
      maVienChuc: maVienChuc ?? this.maVienChuc,
    );
  }

  factory LanhDaoDonviList.fromJson(Map<String, dynamic> json) {
    return LanhDaoDonviList(
      id: json["id"],
      ho: json["ho"],
      tenDem: json["tenDem"],
      ten: json["ten"],
      tenChucVu: json["tenChucVu"],
      maVienChuc: json["maVienChuc"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "ho": ho,
        "tenDem": tenDem,
        "ten": ten,
        "tenChucVu": tenChucVu,
        "maVienChuc": maVienChuc,
      };

  @override
  String toString() {
    return "$id, $ho, $tenDem, $ten, $tenChucVu, $maVienChuc, ";
  }
}

class LoaiNghiPhep {
  LoaiNghiPhep({
    required this.id,
    required this.tenLoaiNghiPhep,
  });

  final int? id;
  final String? tenLoaiNghiPhep;

  // Add this factory constructor in the LoaiNghiPhep class
  factory LoaiNghiPhep.empty() {
    return LoaiNghiPhep(
      id: 0,
      tenLoaiNghiPhep: '',
    );
  }

  LoaiNghiPhep copyWith({
    int? id,
    String? tenLoaiNghiPhep,
  }) {
    return LoaiNghiPhep(
      id: id ?? this.id,
      tenLoaiNghiPhep: tenLoaiNghiPhep ?? this.tenLoaiNghiPhep,
    );
  }

  factory LoaiNghiPhep.fromJson(Map<String, dynamic> json) {
    return LoaiNghiPhep(
      id: json["id"],
      tenLoaiNghiPhep: json["tenLoaiNghiPhep"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "tenLoaiNghiPhep": tenLoaiNghiPhep,
      };

  @override
  String toString() {
    return "$id, $tenLoaiNghiPhep, ";
  }
}
