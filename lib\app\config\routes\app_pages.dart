import 'package:flutter/material.dart';
import 'package:tvumobile/app/authmiddleware.dart';
import 'package:tvumobile/app/features/danhgia/binding/qr_canhan_binding.dart';
import 'package:tvumobile/app/features/danhgia/binding/qr_home_binding.dart';
import 'package:tvumobile/app/features/danhgia/binding/qr_llkh_binding.dart';
import 'package:tvumobile/app/features/danhgia/qr_scaner.dart';
import 'package:tvumobile/app/features/danhgia/qrscanner_binding.dart';
import 'package:tvumobile/app/features/danhgia/views/qr_canhan_view.dart';
import 'package:tvumobile/app/features/danhgia/views/qr_home_view.dart';
import 'package:tvumobile/app/features/danhgia/views/qr_llkh_view.dart';
import 'package:tvumobile/app/features/giangvien/chamcong/chamcong_binding.dart';
import 'package:tvumobile/app/features/giangvien/chamcong/chamcong_view.dart';
import 'package:tvumobile/app/features/giangvien/congvan/chitiet_congvan_binding.dart';
import 'package:tvumobile/app/features/giangvien/congvan/chitiet_congvan_view.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/binding/dieuxe_create_binding.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/binding/dieuxe_detail_binding.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/binding/dieuxe_home_binding.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/view/dieuxe_create_view.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/view/dieuxe_detail_view.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/view/dieuxe_home_view.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/binding/chitietdnp_binding.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/binding/donnghiphep_binding.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/binding/duyetdnp_binding.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/binding/lichnghiphep_binding.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/views/chitietdnp_view.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/views/donnghiphep_view.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/binding/nghiphep_home_binding.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/views/duyetdnp_view.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/views/lichnghiphep_view.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/views/nghiphep_home_view.dart';
import 'package:tvumobile/app/features/kyso/document_view_screen.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/luongthuethang_binding.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/luongthuethang_view.dart';
import 'package:tvumobile/app/features/kyso/kyso_binding.dart';
import 'package:tvumobile/app/features/kyso/kyso_screen.dart';
import 'package:tvumobile/app/features/kyso/v2/document_sign_binding.dart';
import 'package:tvumobile/app/features/kyso/v2/document_sign_screen2.dart';
import 'package:tvumobile/app/features/login/login_binding.dart';
import 'package:tvumobile/app/features/login/login_view.dart';
import 'package:tvumobile/app/features/navbar/bottom_navbar.dart'
    deferred as bottom_navbar;
import 'package:tvumobile/app/features/navbar/navbar_binding.dart';
import 'package:tvumobile/app/features/onboarding/bindings/onboarding_binding.dart';
import 'package:tvumobile/app/features/onboarding/views/onboarding_view.dart';
import 'package:tvumobile/app/features/splash/splash_binding.dart';
import 'package:tvumobile/app/features/splash/splash_screen.dart';
import 'package:tvumobile/app/features/splash/splash_screen2.dart';
import 'package:tvumobile/app/features/tasky/binding/taskycreate_binding.dart';
import 'package:tvumobile/app/features/tasky/binding/taskydashboard_binding.dart';
import 'package:tvumobile/app/features/tasky/binding/taskydetail_binding.dart';
import 'package:tvumobile/app/features/tasky/binding/taskyoverview_binding.dart';
import 'package:tvumobile/app/features/tasky/binding/taskytaskview_binding.dart';
import 'package:tvumobile/app/features/tasky/views/taskdetailview.dart';
import 'package:tvumobile/app/features/tasky/views/taskycreatetask.dart';
import 'package:tvumobile/app/features/tasky/views/taskydashboard.dart';

import 'package:get/get.dart';
import 'package:tvumobile/app/features/tasky/views/taskyoverview.dart';
import 'package:tvumobile/app/features/tasky/views/taskytasksview.dart';

part 'app_routes.dart';

/// contains all configuration pages
class AppPages {
  /// when the app is opened, this page will be the first to be shown
  static const initial = Routes.dashboard;
  static const ONBOARDING = Routes.ONBOARDING;
  static const NAV = Routes.NAV;
  static const HOME = Routes.HOME;
  static const CV_DETAIL = Routes.CV_DETAIL;
  static const SPLASH = Routes.SPLASH;
  static const SPLASH2 = Routes.SPLASH2;
  static const LOGIN = Routes.LOGIN;
  static const TKBGIANGVIEN = Routes.TKBGIANGVIEN;
  static const DANHGIA = Routes.DANHGIA;
  static const QRSCANER = Routes.QRSCANER;
  static const QRCANHAN = Routes.QRCANHAN;
  static const QRLLKH = Routes.QRLLKH;
  static const QRHOME = Routes.QRHOME;
  static const LUONGTHANG = Routes.LUONGTHANG;
  static const CHAMCONG = Routes.CHAMCONG;

  // ĐƠN NGHỈ PHÉP
  static const DONNGHIPHEP = Routes.DONNGHIPHEP;
  static const NGHIPHEP = Routes.NGHIPHEP;
  static const LICHNGHIPHEP = Routes.LICHNGHIPHEP;
  static const CHITIETDNP = Routes.CHITIETDNP;
  static const DUYETDNP = Routes.DUYETDNP;

  // GIAO VIỆC
  static const UPDATER = Routes.UPDATER;
  // static const TASK = Routes.TASK;
  static const TASKVIEW = Routes.TASKVIEW;
  static const TASKOVERVIEW = Routes.TASKOVERVIEW;
  static const TASK_CREATE = Routes.TASK_CREATE;
  static const TASK_DETAIL = Routes.TASK_DETAIL;

  // ĐIỀU XE
  static const DIEUXE = Routes.DIEUXE;
  static const DIEUXE_CREATE = Routes.DIEUXE_CREATE;
  static const DIEUXE_DETAIL = Routes.DIEUXE_DETAIL;

  // KÝ SỐ
  static const KYSO = Routes.KYSO;
  static const KYSO2 = "/kyso2";

  static final routes = [
    GetPage(
      name: KYSO2,
      page: () => PdfViewerScreen(),
      binding: DocumentSignBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.SPLASH,
      page: () => const SplashScreen(),
      binding: SplashBinding(),
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.SPLASH2,
      page: () => const SplashScreen2(),
      binding: Splash2Binding(),
      transition: Transition.fadeIn,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.ONBOARDING,
      page: () => const OnboardingView(),
      binding: OnboardingBinding(),
      transition: Transition.fadeIn,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.LOGIN,
      page: () => const LoginView(),
      binding: LoginBinding(),
      transition: Transition.fadeIn,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.NAV,
      page: () => FutureBuilder(
          future: bottom_navbar.loadLibrary(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              return bottom_navbar.BottomNavbar();
            } else {
              return const Center(child: CircularProgressIndicator());
            }
          }),
      binding: NavbarBinding(),
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.CV_DETAIL,
      page: () => const ChiTietCongVanView(),
      binding: ChiTietCongVanBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.LUONGTHANG,
      page: () => const LuongThueView(),
      binding: LuongThueBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),

    GetPage(
      name: Routes.CHAMCONG,
      page: () => const ChamCongView(),
      binding: ChamCongBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    // ---------------------------------------------------------

    // ---------------------- GIAO VIỆC ------------------------
    // GetPage(
    //   name: Routes.TASK,
    //   page: () => const TaskyDashboardView(),
    //   binding: TaskyDashboardBinding(),
    //   transition: Transition.cupertino,
    //   middlewares: [AuthMiddleWare()],
    // ),
    GetPage(
      name: Routes.TASKVIEW,
      page: () => const TaskyTaskView(),
      binding: TaskyTaskviewBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.TASKOVERVIEW,
      page: () => const TaskyOverview(),
      binding: TaskyOverviewBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.TASK_CREATE,
      page: () => const TaskyCreateTask(),
      binding: TaskyCreateBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.TASK_DETAIL,
      page: () => const TaskDetailView(),
      binding: TaskyDetailBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.QRSCANER,
      page: () => const MyQrScanner(),
      binding: QrScannerBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.QRCANHAN,
      page: () => const QrCaNhanView(),
      binding: QrCaNhanBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.QRLLKH,
      page: () => const QrLLKHView(),
      binding: QrLLKHBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.QRHOME,
      page: () => const QrHomeView(),
      binding: QrHomeBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    // ---------------------------------------------------------

    // --------------------- ĐƠN NGHỈ PHÉP ---------------------
    GetPage(
      name: Routes.DONNGHIPHEP,
      page: () => const DonNghiPhepView(),
      binding: DonNghiPhepBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.NGHIPHEP,
      page: () => NghiPhepHomeView(),
      binding: NghiPhepHomeBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.LICHNGHIPHEP,
      page: () => LichNghiPhepView(),
      binding: LichNghiPhepBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.CHITIETDNP,
      page: () => ChiTietDNPView(),
      binding: ChiTietDNPBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.DUYETDNP,
      page: () => DuyetDNPView(),
      binding: DuyetDNPBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    // ---------------------------------------------------------

    // ------------------------ ĐIỀU XE ------------------------
    GetPage(
      name: Routes.DIEUXE,
      page: () => DieuXeHomeView(),
      binding: DieuXeHomeBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.DIEUXE_CREATE,
      page: () => DieuXeCreateView(),
      binding: DieuXeCreateBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    GetPage(
      name: Routes.DIEUXE_DETAIL,
      page: () => DieuXeDetailView(),
      binding: DieuXeDetailBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    // ---------------------------------------------------------

    // GetPage(
    //   name: Routes.UPDATER,
    //   page: () => const Updater(title: 'Update',),
    //   transition: Transition.cupertino,
    // ),

    GetPage(
      name: Routes.KYSO,
      page: () => KysoScreen(),
      binding: KysoBinding(),
      transition: Transition.cupertino,
      middlewares: [AuthMiddleWare()],
    ),
    // GetPage(
    //   name: Routes.DOCUMENT_SIGN,
    //   page: () => DocumentSignScreen(pdfUrl: Get.arguments),
    //   transition: Transition.cupertino,
    // ),
    GetPage(
      name: Routes.DOCUMENT_VIEW,
      page: () => DocumentViewScreen(document: Get.arguments),
      transition: Transition.cupertino,
    ),
  ];
}
