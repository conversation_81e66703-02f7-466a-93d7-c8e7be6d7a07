part of 'app_helpers.dart';

extension TmsExtension on LanhDaoDonviList {
  String getHoTen() {
    var tmp = ho.toString() +
        (tenDem.toString().isEmpty ? " " : " $tenDem ") +
        ten.toString();
    return tmp;
  }
}

String getHoTen(dynamic data) {
  var hoTen = "";
  if (data is Map) {
    hoTen = "${data['ho']} ${data['tenDem']} ${data['ten']}";
  } else if (data is String) {
    hoTen = data;
  }
  return hoTen.trim().replaceAll("  ", " ");
}
