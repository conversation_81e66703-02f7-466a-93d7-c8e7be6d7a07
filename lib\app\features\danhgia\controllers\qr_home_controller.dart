import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepinfo.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepvienchuc.dart';
import 'package:tvumobile/app/features/danhgia/qr_scaner.dart';
import 'package:tvumobile/app/features/danhgia/views/qr_canhan_view.dart';
import 'package:tvumobile/app/features/danhgia/views/qr_llkh_view.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/duyetdnp_controller.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/nghiphep_home_controller.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class QrHomeController extends GetxController {
  ApiProvider apiProvider = Get.find();

  final RxString qrCaNhan = "".obs; // Lưu duyetai trong controller
  final RxBool isLoading = true.obs; // Trạng thái loading

  RxInt selectedIndex = 1.obs;

  UserInfo? userinfo = LocalStorageServices.getUserInfo();

  // Khởi tạo qrNavigation trong onInit để tránh vòng lặp đệ quy
  final List<Widget> qrNavigation = [
    QrCaNhanView(),
    MyQrScanner(),
    QrLLKHView(), // Thay bằng widget cho "Mã QR khác" nếu cần
  ];

  // Hàm dùng cho bottom bar
  void onTap(int index) {
    selectedIndex.value = index;
    update();
  }
}
