import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/features/splash/splash_controller.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class SplashScreen extends StatefulWidget {
  static String tag = '/';
  final String routeName;

  const SplashScreen({super.key, this.routeName = "/"});

  @override
  AppSplashScreenState createState() => AppSplashScreenState();
}

class AppSplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  bool _isCheckingForUpdate = false;
  final updater = ShorebirdUpdater();
  SplashController controller = Get.find();
  int? _currentPatchVersion;

  AnimationController? scaleController;
  Animation<double>? scaleAnimation;
  late ThemeData theme;
  bool _a = false;
  bool _c = false;
  bool _d = false;
  bool _e = false;
  bool secondAnim = false;

  Color boxColor = Colors.transparent;

  @override
  void initState() {
    if (!_isCheckingForUpdate) _checkForUpdate();
    super.initState();
    updater.readCurrentPatch().then((currentPatch) {
      print('The current patch number is: ${currentPatch?.number}');
    });
    theme = Get.theme;
    init();
  }

  void init() async {
    if (!mounted) return;
    //final theme = Theme.of(context);
    Timer(const Duration(milliseconds: 100), () {
      setState(() {
        boxColor = theme.cardColor;
        _a = true;
      });
    });
    Timer(const Duration(milliseconds: 1200), () {
      if (mounted) {
        setState(() {
          boxColor = theme.scaffoldBackgroundColor;
          _c = true;
        });
      }
    });
    Timer(const Duration(milliseconds: 1400), () {
      if (mounted) {
        setState(() {
          //boxColor = context.theme.primaryColor.withOpacity(0.1);
          _e = true;
        });
      }
    });
    Timer(const Duration(milliseconds: 2800), () {
      if (mounted) {
        secondAnim = true;
        scaleController = AnimationController(
          vsync: this,
          duration: const Duration(milliseconds: 1000),
        )..forward();
        scaleAnimation = Tween<double>(
          begin: 0.0,
          end: 12,
        ).animate(scaleController!);

        setState(() {
          boxColor = theme.scaffoldBackgroundColor;
          _d = true;
        });
      }
    });

    Timer(const Duration(milliseconds: 3800), () {
      if (mounted) {
        secondAnim = true;

        setState(() {
          boxColor = Colors.black;
        });
        if (controller.isAuthenticated()) {
          //Navigator.of(context).pushNamed(widget.routeName);
          Get.offNamed(Routes.SPLASH2, preventDuplicates: true);
        } else {
          if (LocalStorageServices.getIsNew()) {
            Get.offNamed(Routes.ONBOARDING, preventDuplicates: true);
          } else {
            Get.offNamed(Routes.LOGIN, preventDuplicates: true);
          }
        }
      }
    });
  }

  @override
  void dispose() {
    if (scaleController != null) {
      scaleController!.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double h = context.height;
    double w = context.width;
    //final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage("assets/images/app_icon.png"),
            fit: BoxFit.none,
            opacity: 0.04,
          ),
        ),
        child: Center(
          child: Stack(
            alignment: Alignment.center,
            children: [
              AnimatedContainer(
                duration: Duration(milliseconds: _d ? 100 : 2500),
                curve: _d ? Curves.fastLinearToSlowEaseIn : Curves.elasticOut,
                height: _d
                    ? 0
                    : _a
                    ? h / 4.5
                    : 20,
                width: 20,
              ),
              AnimatedContainer(
                duration: Duration(seconds: _c ? 2 : 0),
                curve: Curves.fastLinearToSlowEaseIn,
                height: _d
                    ? h
                    : _c
                    ? 130
                    : 20,
                width: _d
                    ? w
                    : _c
                    ? 130
                    : 20,
                decoration: BoxDecoration(
                  color: boxColor,
                  //shape: _c? BoxShape.rectangle : BoxShape.circle,
                  borderRadius: _d
                      ? const BorderRadius.only()
                      : BorderRadius.circular(30),
                ),
                child: secondAnim
                    ? Center(
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: theme.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: AnimatedBuilder(
                            animation: scaleAnimation!,
                            builder: (c, child) => Transform.scale(
                              scale: scaleAnimation!.value,
                              child: Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: theme.primaryColor,
                                ),
                              ),
                            ),
                          ),
                        ),
                      )
                    : Center(
                        child: _e
                            ? Image.asset(
                                'assets/images/app_icon.png',
                                height: 150,
                                width: 150,
                                fit: BoxFit.cover,
                              )
                            : const SizedBox(),
                      ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  margin: const EdgeInsets.only(bottom: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        "Version: $_currentPatchVersion",
                        style: theme.textTheme.labelMedium,
                      ),
                      Icon(
                        Icons.check_circle_outline,
                        color: _isCheckingForUpdate
                            ? theme.colorScheme.error
                            : theme.colorScheme.primary,
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _checkForUpdate() async {
    // Check whether a new update is available.
    setState(() {
      _isCheckingForUpdate = true;
    });
    final status = await updater.checkForUpdate();
    setState(() {
      _isCheckingForUpdate = false;
    });
    if (status == UpdateStatus.outdated) {
      try {
        // Perform the update
        _showDownloadingBanner();
        await updater.update();
        ScaffoldMessenger.of(context).hideCurrentMaterialBanner();
        _showRestartBanner();
      } on UpdateException catch (error) {
        // Handle any errors that occur while updating.
        print(error);
      }
    }
  }

  void _showDownloadingBanner() {
    ScaffoldMessenger.of(context).showMaterialBanner(
      const MaterialBanner(
        content: Text('Downloading patch...'),
        actions: [
          SizedBox(
            height: 14,
            width: 14,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ],
      ),
    );
  }

  void _showRestartBanner() {
    ScaffoldMessenger.of(context).showMaterialBanner(
      MaterialBanner(
        content: const Text('A new patch is ready!'),
        actions: [
          TextButton(
            // Restart the app for the new patch to take effect.
            //onPressed: () => Restart.restartApp(),
            onPressed: () {},
            child: const Text('Restart app'),
          ),
        ],
      ),
    );
  }
}
