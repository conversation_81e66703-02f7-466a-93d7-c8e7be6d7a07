
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/luongthuethang_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

AppBar LuongThangAppBar(BuildContext context, LuongThueController controller) {

  ThemeData theme = Theme.of(context);
  return AppBar(
    foregroundColor: theme.colorScheme.onPrimary,
    iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
    flexibleSpace: Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -80,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset('assets/images/app_icon.png', height: 150, width: 150,opacity: const AlwaysStoppedAnimation(.15),)
            ),
          ),
          Positioned(
            right: -7,
            top: -160,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: -21,
            top: -195,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          //----------------Data row----------------//
        ],
      ),
    ),
    title: Obx(() {
      return MyText.titleMedium(
        controller.appbarTitle.value.toUpperCase(),
        color: theme.colorScheme.onPrimary,
        letterSpacing: 0.01,
      );
    }),
    centerTitle: true,
  );
}

