import 'dart:convert';

class TmsCongVanModel {
  TmsCongVanModel({
    required this.id,
    required this.capDoChuyen,
    required this.daXem,
    required this.ghiChu,
    required this.huongChuyen,
    required this.ngayCapNhat,
    required this.noiDungYeuCau,
    required this.document,
    required this.nguoiChuyenCongVan,
  });

  final int? id;
  final dynamic capDoChuyen;
  final dynamic daXem;
  final dynamic ghiChu;
  final dynamic huongChuyen;
  final DateTime? ngayCapNhat;
  final dynamic noiDungYeuCau;
  final Document? document;
  final NguoiChuyenCongVan? nguoiChuyenCongVan;

  factory TmsCongVanModel.fromJson(Map<String, dynamic> json) {
    return TmsCongVanModel(
      id: json["id"],
      capDoChuyen: json["capDoChuyen"],
      daXem: json["daXem"],
      ghiChu: json["ghiChu"],
      huongChuyen: json["huongChuyen"],
      ngayCapNhat: DateTime.tryParse(json["ngayCapNhat"] ?? ""),
      noiDungYeuCau: json["noiDungYeuCau"],
      document:
          json["document"] == null ? null : Document.fromJson(json["document"]),
      nguoiChuyenCongVan: json["nguoiChuyenCongVan"] == null
          ? null
          : NguoiChuyenCongVan.fromJson(json["nguoiChuyenCongVan"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "capDoChuyen": capDoChuyen,
        "daXem": daXem,
        "ghiChu": ghiChu,
        "huongChuyen": huongChuyen,
        "ngayCapNhat": ngayCapNhat?.toIso8601String(),
        "noiDungYeuCau": noiDungYeuCau,
        "document": document?.toJson(),
        "nguoiChuyenCongVan": nguoiChuyenCongVan?.toJson(),
      };

  @override
  String toString() {
    return "$id, $capDoChuyen, $daXem, $ghiChu, $huongChuyen, $ngayCapNhat, $noiDungYeuCau, $document, $nguoiChuyenCongVan, ";
  }

  String toJsonString() => json.encode(toJson());
  factory TmsCongVanModel.fromJsonString(String source) =>
      TmsCongVanModel.fromJson(json.decode(source));
  static List<TmsCongVanModel> parseNews(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed
        .map<TmsCongVanModel>((json) => TmsCongVanModel.fromJson(json))
        .toList();
  }
}

class Document {
  Document({
    required this.id,
    required this.trichYeu,
    required this.cvFiles,
    required this.noiBanHanh,
    required this.category,
    required this.docType,
    required this.ngayBanHanh,
    required this.kyHieuGoc,
  });

  final int? id;
  final String? trichYeu;
  final List<CvFile> cvFiles;
  final NoiBanHanh? noiBanHanh;
  final Category? category;
  final DocType? docType;
  final DateTime? ngayBanHanh;
  final String? kyHieuGoc;

  factory Document.fromJson(Map<String, dynamic> json) {
    return Document(
      id: json["id"],
      trichYeu: json["trichYeu"],
      cvFiles: json["cvFiles"] == null
          ? []
          : List<CvFile>.from(json["cvFiles"]!.map((x) => CvFile.fromJson(x))),
      noiBanHanh: json["noiBanHanh"] == null
          ? null
          : NoiBanHanh.fromJson(json["noiBanHanh"]),
      category:
          json["category"] == null ? null : Category.fromJson(json["category"]),
      docType: json["docType"] == null ? null : DocType.fromJson(json["docType"]),
      ngayBanHanh:  DateTime.tryParse(json["ngayBanHanh"] ?? ""),
      kyHieuGoc: json["kyHieuGoc"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "trichYeu": trichYeu,
        "cvFiles": cvFiles.map((x) => x.toJson()).toList(),
        "noiBanHanh": noiBanHanh?.toJson(),
        "category": category?.toJson(),
        "docType": docType?.toJson(),
        "ngayBanHanh": ngayBanHanh?.toIso8601String(),
        "kyHieuGoc": kyHieuGoc,
      };

  @override
  String toString() {
    return "$id, $trichYeu, $cvFiles, $noiBanHanh, $category, ";
  }

  String toJsonString() => json.encode(toJson());
  factory Document.fromJsonString(String source) =>
      Document.fromJson(json.decode(source));
  static List<Document> parseNews(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed.map<Document>((json) => Document.fromJson(json)).toList();
  }
}

class DocType {
  DocType({
    required this.id,
    required this.ten,
  });

  final int? id;
  final String? ten;

  factory DocType.fromJson(Map<String, dynamic> json) {
    return DocType(
      id: json["id"],
      ten: json["ten"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "ten": ten,
  };

  @override
  String toString() {
    return "$id, $ten, ";
  }

  String toJsonString() => json.encode(toJson());
  factory DocType.fromJsonString(String source) =>
      DocType.fromJson(json.decode(source));
  static List<DocType> parseNews(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed.map<DocType>((json) => DocType.fromJson(json)).toList();
  }
}

class Category {
  Category({
    required this.ten,
    required this.id,
  });

  final String? ten;
  final int? id;

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      ten: json["ten"],
      id: json["id"],
    );
  }

  Map<String, dynamic> toJson() => {
        "ten": ten,
        "id": id,
      };

  @override
  String toString() {
    return "$ten, $id, ";
  }

  String toJsonString() => json.encode(toJson());
  factory Category.fromJsonString(String source) =>
      Category.fromJson(json.decode(source));
  static List<Category> parseNews(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed.map<Category>((json) => Category.fromJson(json)).toList();
  }
}

class CvFile {
  CvFile({
    required this.id,
    required this.ngayTao,
    required this.securityLevel,
    required this.moTa,
    required this.keyword,
    required this.fileName,
    required this.filePath,
    required this.documentId,
    required this.isDeleted,
    required this.cvFileAction,
    required this.cvFileAtributes,
    required this.cvFolderFiles,
    required this.document,
  });

  final int? id;
  final dynamic ngayTao;
  final dynamic securityLevel;
  final dynamic moTa;
  final dynamic keyword;
  final String? fileName;
  final String? filePath;
  final dynamic documentId;
  final dynamic isDeleted;
  final List<dynamic> cvFileAction;
  final List<dynamic> cvFileAtributes;
  final List<dynamic> cvFolderFiles;
  final dynamic document;

  factory CvFile.fromJson(Map<String, dynamic> json) {
    return CvFile(
      id: json["id"],
      ngayTao: json["ngayTao"],
      securityLevel: json["securityLevel"],
      moTa: json["moTa"],
      keyword: json["keyword"],
      fileName: json["fileName"],
      filePath: json["filePath"],
      documentId: json["documentId"],
      isDeleted: json["isDeleted"],
      cvFileAction: json["cvFileAction"] == null
          ? []
          : List<dynamic>.from(json["cvFileAction"]!.map((x) => x)),
      cvFileAtributes: json["cvFileAtributes"] == null
          ? []
          : List<dynamic>.from(json["cvFileAtributes"]!.map((x) => x)),
      cvFolderFiles: json["cvFolderFiles"] == null
          ? []
          : List<dynamic>.from(json["cvFolderFiles"]!.map((x) => x)),
      document: json["document"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "ngayTao": ngayTao,
        "securityLevel": securityLevel,
        "moTa": moTa,
        "keyword": keyword,
        "fileName": fileName,
        "filePath": filePath,
        "documentId": documentId,
        "isDeleted": isDeleted,
        "cvFileAction": cvFileAction.map((x) => x).toList(),
        "cvFileAtributes": cvFileAtributes.map((x) => x).toList(),
        "cvFolderFiles": cvFolderFiles.map((x) => x).toList(),
        "document": document,
      };

  @override
  String toString() {
    return "$id, $ngayTao, $securityLevel, $moTa, $keyword, $fileName, $filePath, $documentId, $isDeleted, $cvFileAction, $cvFileAtributes, $cvFolderFiles, $document, ";
  }

  String toJsonString() => json.encode(toJson());
  factory CvFile.fromJsonString(String source) =>
      CvFile.fromJson(json.decode(source));
  static List<CvFile> parseNews(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed.map<CvFile>((json) => CvFile.fromJson(json)).toList();
  }

  String get getFilePath {
    if (filePath == null || filePath == "") {
      return "";
    }
    String tmp = filePath!.replaceAll('\\\\', '/').replaceAll('\\', '/');
    var tmp2 = tmp.split('/').sublist(2);
    tmp = tmp2.join('/');
    return tmp;
  }

  String get getFileExt {
    if (filePath == null || filePath == "") {
      return "";
    }
    String tmp = filePath!.split('.').last;
    return tmp;
  }
}

class NoiBanHanh {
  NoiBanHanh({
    required this.id,
    required this.tenDonVi,
  });

  final int? id;
  final String? tenDonVi;

  factory NoiBanHanh.fromJson(Map<String, dynamic> json) {
    return NoiBanHanh(
      id: json["id"],
      tenDonVi: json["tenDonVi"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "tenDonVi": tenDonVi,
      };

  @override
  String toString() {
    return "$id, $tenDonVi, ";
  }

  String toJsonString() => json.encode(toJson());
  factory NoiBanHanh.fromJsonString(String source) =>
      NoiBanHanh.fromJson(json.decode(source));
  static List<NoiBanHanh> parseNews(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed.map<NoiBanHanh>((json) => NoiBanHanh.fromJson(json)).toList();
  }
}

class NguoiChuyenCongVan {
  NguoiChuyenCongVan({
    required this.id,
    required this.ho,
    required this.tenDem,
    required this.ten,
  });

  final int? id;
  final String? ho;
  final dynamic tenDem;
  final String? ten;

  factory NguoiChuyenCongVan.fromJson(Map<String, dynamic> json) {
    return NguoiChuyenCongVan(
      id: json["id"],
      ho: json["ho"],
      tenDem: json["tenDem"],
      ten: json["ten"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "ho": ho,
        "tenDem": tenDem,
        "ten": ten,
      };

  @override
  String toString() {
    return "$id, $ho, $tenDem, $ten, ";
  }

  String toJsonString() => json.encode(toJson());
  factory NguoiChuyenCongVan.fromJsonString(String source) =>
      NguoiChuyenCongVan.fromJson(json.decode(source));
  static List<NguoiChuyenCongVan> parseNews(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed
        .map<NguoiChuyenCongVan>((json) => NguoiChuyenCongVan.fromJson(json))
        .toList();
  }
}
