import 'package:flutter/foundation.dart';

import 'package:get/get.dart';
import 'package:tvumobile/app/utils/oauth2/src/credentials.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class SplashController extends GetxController {
  late Credentials? credential;

  @override
  void onInit() {
    super.onInit();
    credential = LocalStorageServices.getCredential();
    updateFCM();
  }

  Future<void> updateFCM() async {
    ApiProvider apiProvider = Get.put(ApiProvider());
    var fcmToken = LocalStorageServices.getFcmToken();
    if (fcmToken != null) {
      apiProvider.addPortalFCM(fcmToken);
    }
  }

  bool isAuthenticated() {
    if (credential != null) {
      return true;
    }
    return false;
  }

  @override
  void onReady() async {
    super.onReady();

    //await Future.delayed(const Duration(milliseconds: 2000));
    //var storage = Get.find<SharedPreferences>();
    //ApiProvider apiProvider = Get.find();
    if (credential != null) {
      if (credential!.isExpired) {
        //await ApiProvider.refreshToken();
        credential = LocalStorageServices.getCredential();
      }

      if (kDebugMode) {
        //print(credential?.toJsonString());
      }
    }
  }
}
