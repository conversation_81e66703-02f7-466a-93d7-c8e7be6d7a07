// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/features/giangvien/congvan/congvan_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

AppBar buildAppBar(BuildContext context, CongVanController controller) {
  ThemeData theme = Theme.of(context);
  var width = MediaQuery.of(context).size.width;
  return AppBar(
    //toolbarHeight: safePadding + 28, // Set this height
    foregroundColor: theme.colorScheme.onPrimary,
    iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
    actions: [
      PopupMenuButton(
          color: theme.colorScheme.onPrimary,
          iconColor: theme.colorScheme.onPrimary,
          initialValue: controller.limit,
          onSelected: (value) => controller.changeTotalPerPage(value),
          tooltip: "Số công văn/trang",
          itemBuilder: (context) {
            return [
              CheckedPopupMenuItem(
                value: 15,
                checked: controller.limit == 15,
                child: const Text("15 / trang"),
              ),
              CheckedPopupMenuItem(
                value: 25,
                checked: controller.limit == 25,
                child: const Text("25 / trang"),
              ),
              CheckedPopupMenuItem(
                value: 50,
                checked: controller.limit == 50,
                child: const Text("50 / trang"),
              )
            ];
          }),
    ],
    flexibleSpace: Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -80,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset(
                  'assets/images/app_icon.png',
                  height: 150,
                  width: 150,
                  opacity: const AlwaysStoppedAnimation(.15),
                )),
          ),
          Positioned(
            right: -7,
            top: -160,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: -21,
            top: -195,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          //----------------Data row----------------//
        ],
      ),
    ),
    title: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: width * 0.6,
          child: Obx(() {
            if (controller.appbarTitle.value == "") return Container();
            return MyText.titleMedium(
                controller.appbarTitle.value.toUpperCase(),
                color: theme.colorScheme.onPrimary,
                letterSpacing: 0.01,
                maxLines: 1,
                overflow: TextOverflow.ellipsis);
          }),
        ),
        IconButton(
          padding: EdgeInsets.zero,
          icon: Icon(
            Icons.arrow_drop_down,
            color: theme.colorScheme.onPrimary,
          ),
          onPressed: () {
            controller.openDrawer();
          },
        )
      ],
    ),
  );
}
