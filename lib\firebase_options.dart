// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAHGDYp-C-0kFl83NXhtzbUeBv9AFgUwUU',
    appId: '1:406498284377:web:08ba71949ffe1d37c2f248',
    messagingSenderId: '406498284377',
    projectId: 'tvu-mobile',
    authDomain: 'tvu-mobile.firebaseapp.com',
    storageBucket: 'tvu-mobile.appspot.com',
    measurementId: 'G-FW9XEST9PS',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDKI_mLTuTeKYgPXrdYc1m8ptUGO00VLlY',
    appId: '1:406498284377:android:4021cadb11b84da2c2f248',
    messagingSenderId: '406498284377',
    projectId: 'tvu-mobile',
    storageBucket: 'tvu-mobile.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyD-V1xtG8EMJxZranXyiz8jQ3MT9jOInRo',
    appId: '1:406498284377:ios:7dcaa70e5fe4d27dc2f248',
    messagingSenderId: '406498284377',
    projectId: 'tvu-mobile',
    storageBucket: 'tvu-mobile.appspot.com',
    iosBundleId: 'vn.edu.tvu.itx.tvumobile',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAHGDYp-C-0kFl83NXhtzbUeBv9AFgUwUU',
    appId: '1:406498284377:web:557a17a1043becc5c2f248',
    messagingSenderId: '406498284377',
    projectId: 'tvu-mobile',
    authDomain: 'tvu-mobile.firebaseapp.com',
    storageBucket: 'tvu-mobile.appspot.com',
    measurementId: 'G-P3B680ZLK0',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyD-V1xtG8EMJxZranXyiz8jQ3MT9jOInRo',
    appId: '1:406498284377:ios:7dcaa70e5fe4d27dc2f248',
    messagingSenderId: '406498284377',
    projectId: 'tvu-mobile',
    storageBucket: 'tvu-mobile.appspot.com',
    iosBundleId: 'vn.edu.tvu.itx.tvumobile',
  );

}