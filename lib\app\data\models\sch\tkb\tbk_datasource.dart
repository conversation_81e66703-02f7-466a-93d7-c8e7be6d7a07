

import 'dart:math';
import 'dart:ui';

import 'package:get/get.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';
import 'package:tvumobile/app/utils/helpers/app_helpers.dart';
import '../../sch/tkb/tkb_tuan_giangvien.dart';

class TkbGVDataSource extends CalendarDataSource {
  /// Creates a meeting data source, which used to set the appointment
  /// collection to the calendar
  TkbGVDataSource(List<TkbTuanGiangVien> source) {
    appointments = source;
  }

  @override
  DateTime getStartTime(int index) {
    return _getMeetingData(index).batDau!;
  }

  @override
  DateTime getEndTime(int index) {
    return _getMeetingData(index).ketThuc!;
  }

  @override
  String getSubject(int index) {
    return "${_getMeetingData(index).maLop!} - ${_getMeetingData(index).tenMon!} - ${_getMeetingData(index).tenPhong!}";
  }

  @override
  Color getColor(int index) {
    if(!colorNames.containsKey(_getMeetingData(index).tenMon.toString().toUpperCase())) {
      colorNames.assign(_getMeetingData(index).tenMon.toString().toUpperCase(), Random().nextInt(colors.length));
    }
    return colors[colorNames[_getMeetingData(index).tenMon.toString().toUpperCase()]].toColor;
    //_getMeetingData(index).background!;
  }

  List<String> colors = ["46A4E4", "EC84B5", "D28638", "16a058", "e55d27", "46A4E4", "EC84B5"];
  var colorNames = {};
  @override
  bool isAllDay(int index) {
    //return _getMeetingData(index).isAllDay!;
    return false;
  }

  TkbTuanGiangVien _getMeetingData(int index) {
    final dynamic meeting = appointments![index];
    late final TkbTuanGiangVien meetingData;
    if (meeting is TkbTuanGiangVien) {
      meetingData = meeting;
    }

    return meetingData;
  }
}