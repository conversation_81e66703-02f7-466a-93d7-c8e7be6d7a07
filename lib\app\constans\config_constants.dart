part of 'app_constants.dart';

const kBorderRadius = 20.0;
const kSpacing = 16.0;
const kFontColorPallets = [
  Color.fromRGBO(255, 255, 255, 1),
  Color.fromRGBO(210, 210, 210, 1),
  Color.fromRGBO(170, 170, 170, 1),
  Color.fromRGBO(244, 244, 244, 1),
];

const kNotifColor = Color.fromRGBO(74, 177, 120, 1);

class AppConfig {
  static const int sessionTimeoutThreshold =
      0; // Will refresh the access token 5 minutes before it expires
  static const bool loginWithPassword = true; // if false hide the form login
//if false hide the fields password and confirm password from signup form
//for security reason and the password generated after verification mail
  static const bool signupWithPassword = true;
  static const int kMySnackBarDuration = 3;
  static const double buttonRadius = 4;
  static const double cardRadius = 4;
  static const double containerRadius = 4;
}

class StorageConstants {
  static const String token = 'token';
  static const String userInfo = 'userInfo';
  static const String lightThemeKey = 'is_theme_light';
  static const String currentLocalKey = 'current_local';
  static const String fcmTokenKey = 'fcm_token';
  static const String credential = 'credential';
  static const String newbieKey = 'newbie';
  static const String signatureFile = 'signature_file';
}

class AppImages {
  static String get kAppIcon => 'assets/images/app_icon.png';
  static String get kAppIcon150 => 'assets/images/app_icon_150.png';
  static String get kNoImage => 'assets/images/no_image.png';
  static String get kWelcome => 'assets/onboard/welcome.json';
  static String get defaultImgUrl =>
      'https://www.tvu.edu.vn/wp-content/uploads/2017/08/cropped-logotvu-Copy-32x32.png';
}

class DocTypeId {
  static int v1aDaDen = 29;
  static int v1bDaDi = 30;
  static int v1cDaNb = 31;

  static int v2aCqDen = 32;
  static int v2bCqDi = 35;
  static int v2cCqNb = 36;

  static int v3aCdDen = 37;
  static int v3bCdDi = 38;
  static int v3cCdNb = 39;
  static int v0Khac = 40;
  static int tatCa = -1;
  static int caNhan = 0;

  static Map<int, String> defaultText = {
    DocTypeId.tatCa: "Tất cả văn bản",
    DocTypeId.caNhan: "Văn bản nhận",
    DocTypeId.v1aDaDen: "1a - Đảng - Văn bản đến",
    DocTypeId.v1bDaDi: "1b - Đảng - Văn bản đi",
    DocTypeId.v1cDaNb: "1c - Đảng - Văn bản nội bộ",
    DocTypeId.v2aCqDen: "2a - Chính quyền - Văn bản đến",
    DocTypeId.v2bCqDi: "2b - Chính quyền - Văn bản đi",
    DocTypeId.v2cCqNb: "2c - Chính quyền - Văn bản nội bộ",
    DocTypeId.v3aCdDen: "3a - Công đoàn - Văn bản đến",
    DocTypeId.v3bCdDi: "3b - Công đoàn - Văn bản đi",
    DocTypeId.v3cCdNb: "3c - Công đoàn - Văn bản nội bộ",
    DocTypeId.v0Khac: ""
  };
}
