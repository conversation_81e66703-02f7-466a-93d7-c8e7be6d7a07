import 'dart:typed_data';
import 'package:flutter/material.dart';

// Data class to hold signature information
class SignatureData {
  final Offset position;
  final double width;
  final double height;
  final double aspectRatio;
  final Offset? pdfPositionPoints;
  final double? pdfWidthPoints;
  final double? pdfHeightPoints;
  final int? pageNumber;
  final double? zoomLevel;
  final String? fullName;
  final String? department;
  final String? personposition;
  final String? datetime;
  final bool displayFullName;
  final bool displayDepartment;
  final bool displayPosition;
  final bool displayDatetime;
  final bool stampStyle; // Add stampStyle to SignatureData
  final Uint8List? signatureBytes; // Add signatureBytes

  SignatureData({
    required this.position,
    required this.width,
    required this.height,
    required this.aspectRatio,
    this.pdfPositionPoints,
    this.pdfWidthPoints,
    this.pdfHeightPoints,
    this.pageNumber,
    this.zoomLevel,
    this.fullName,
    this.department,
    this.personposition,
    this.datetime,
    required this.displayFullName,
    required this.displayDepartment,
    required this.displayPosition,
    required this.displayDatetime,
    this.stampStyle = false, // Initialize stampStyle to false by default
    this.signatureBytes, // Initialize signatureBytes
  });

  SignatureData copyWith({
    Offset? position,
    double? width,
    double? height,
    double? aspectRatio,
    Offset? pdfPositionPoints,
    double? pdfWidthPoints,
    double? pdfHeightPoints,
    int? pageNumber,
    double? zoomLevel,
    String? fullName,
    String? department,
    String? personposition,
    String? datetime,
    bool? displayFullName,
    bool? displayDepartment,
    bool? displayPosition,
    bool? displayDatetime,
    bool? stampStyle, // Add stampStyle to copyWith
    Uint8List? signatureBytes, // Add signatureBytes to copyWith
  }) {
    return SignatureData(
      position: position ?? this.position,
      width: width ?? this.width,
      height: height ?? this.height,
      aspectRatio: aspectRatio ?? this.aspectRatio,
      pdfPositionPoints: pdfPositionPoints ?? this.pdfPositionPoints,
      pdfWidthPoints: pdfWidthPoints ?? this.pdfWidthPoints,
      pdfHeightPoints: pdfHeightPoints ?? this.pdfHeightPoints,
      pageNumber: pageNumber ?? this.pageNumber,
      zoomLevel: zoomLevel ?? this.zoomLevel,
      fullName: fullName ?? this.fullName,
      department: department ?? this.department,
      personposition: personposition ?? this.personposition,
      datetime: datetime ?? this.datetime,
      displayFullName: displayFullName ?? this.displayFullName,
      displayDepartment: displayDepartment ?? this.displayDepartment,
      displayPosition: displayPosition ?? this.displayPosition,
      displayDatetime: displayDatetime ?? this.displayDatetime,
      stampStyle:
          stampStyle ?? this.stampStyle, // Include stampStyle in copyWith
      signatureBytes: signatureBytes ??
          this.signatureBytes, // Include signatureBytes in copyWith
    );
  }
}
