// ignore_for_file: file_names

import 'package:tvumobile/app/data/models/tms/danhmuc.dart';

// --------------------------------------------------------------------------------- //
// --------------------------------- CLASS TASK ------------------------------------ //

class Task {
  Task({
    this.id,
    this.taskName,
    this.moTa,
    this.ngayTao,
    this.startDay,
    this.endDateDuKien,
    this.trangThai,
    this.tag,
    this.taskType,
    this.priority,
    this.nguoiGiamSat,
    this.nguoiGiaoViec,
    this.danhSachNguoiThuc<PERSON>ien,
    this.nguoiTao,
    this.taskUpdateInfo,
    this.taskAction,
    this.taskResource,
  });

  late int? id;
  late String? taskName;
  late String? moTa;
  late DateTime? ngayTao;
  late DateTime? startDay;
  late DateTime? endDateDuKien;
  late DanhMuc? trangThai;
  late DanhMuc? tag;
  late DanhMuc? taskType;
  late DanhMuc? priority;
  late Nguoi? nguoiGiamSat;
  late Nguoi? nguoiGiaoViec;
  late List<Nguoi>? danhSachNguoiThucHien;
  late Nguoi? nguoiTao;
  late List<TaskUpdateInfo>? taskUpdateInfo;
  late List<TaskAction>? taskAction;
  late List<TaskResource>? taskResource;

  Task copyWith({
    int? id,
    String? taskName,
    String? moTa,
    DateTime? ngayTao,
    DateTime? startDay,
    DateTime? endDateDuKien,
    DanhMuc? trangThai,
    DanhMuc? tag,
    DanhMuc? taskType,
    DanhMuc? priority,
    Nguoi? nguoiGiamSat,
    List<Nguoi>? danhSachNguoiThucHien,
    Nguoi? nguoiThucHien,
    Nguoi? nguoiTao,
    List<TaskUpdateInfo>? taskUpdateInfo,
    List<TaskAction>? taskAction,
    List<TaskResource>? taskResource,
  }) {
    return Task(
      id: id ?? this.id,
      taskName: taskName ?? this.taskName,
      moTa: moTa ?? this.moTa,
      ngayTao: ngayTao ?? this.ngayTao,
      startDay: startDay ?? this.startDay,
      endDateDuKien: endDateDuKien ?? this.endDateDuKien,
      trangThai: trangThai ?? this.trangThai,
      tag: tag ?? this.tag,
      taskType: taskType ?? this.taskType,
      priority: priority ?? this.priority,
      nguoiGiamSat: nguoiGiamSat ?? this.nguoiGiamSat,
      nguoiGiaoViec: nguoiGiaoViec ?? nguoiGiaoViec,
      danhSachNguoiThucHien:
          danhSachNguoiThucHien ?? this.danhSachNguoiThucHien, // Cập nhậts
      nguoiTao: nguoiTao ?? this.nguoiTao,
      taskUpdateInfo: taskUpdateInfo ?? this.taskUpdateInfo,
      taskAction: taskAction ?? this.taskAction,
      taskResource: taskResource ?? this.taskResource,
    );
  }

  // ---------- FACTORY CONSTRUCTOR: Chuyển đổi dữ liệu JSON thành đối tượng Task ----------
  factory Task.fromJson(Map<String, dynamic> json) {
    return Task(
      id: json["id"],
      taskName: json["taskName"],
      moTa: json["moTa"],
      ngayTao: DateTime.tryParse(json["ngayTao"] ?? ""),
      startDay: DateTime.tryParse(json["startDay"] ?? ""),
      endDateDuKien: DateTime.tryParse(json["endDateDuKien"] ?? ""),
      trangThai: json["trangThai"] == null
          ? null
          : DanhMuc.fromJson(json["trangThai"]),
      tag: json["tag"] == null ? null : DanhMuc.fromJson(json["tag"]),
      taskType:
          json["taskType"] == null ? null : DanhMuc.fromJson(json["taskType"]),
      priority:
          json["priority"] == null ? null : DanhMuc.fromJson(json["priority"]),
      nguoiGiamSat: json["nguoiGiamSat"] == null
          ? null
          : Nguoi.fromJson(json["nguoiGiamSat"]),
      nguoiGiaoViec: json["nguoiGiaoViec"] == null
          ? null
          : Nguoi.fromJson(json["nguoiGiaoViec"]),
      danhSachNguoiThucHien: json["danhSachNguoiThucHien"] == null
          ? null
          : List<Nguoi>.from(json["danhSachNguoiThucHien"]!
              .map((x) => Nguoi.fromJson(x))), // Cập nhật
      nguoiTao:
          json["nguoiTao"] == null ? null : Nguoi.fromJson(json["nguoiTao"]),
      taskUpdateInfo: json["taskUpdateInfo"] == null
          ? []
          : List<TaskUpdateInfo>.from(
              json["taskUpdateInfo"]!.map((x) => TaskUpdateInfo.fromJson(x))),
      taskAction: json["taskAction"] == null
          ? []
          : List<TaskAction>.from(
              json["taskAction"]!.map((x) => TaskAction.fromJson(x))),
      taskResource: json["taskResource"] == null
          ? []
          : List<TaskResource>.from(
              json["taskResource"]!.map((x) => TaskResource.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "taskName": taskName,
        "moTa": moTa,
        "ngayTao": ngayTao?.toIso8601String(),
        "startDay": startDay?.toIso8601String(),
        "endDateDuKien": endDateDuKien?.toIso8601String(),
        "trangThai": trangThai,
        "tag": tag?.toJson(),
        "taskType": taskType?.toJson(),
        "priority": priority?.toJson(),
        "nguoiGiamSat": nguoiGiamSat?.toJson(),
        "nguoiGiaoViec": nguoiGiaoViec?.toJson(),
        "danhSachNguoiThucHien":
            danhSachNguoiThucHien?.map((x) => x.toJson()).toList(),
        "nguoiTao": nguoiTao?.toJson(),
        "taskUpdateInfo": taskUpdateInfo?.map((x) => x.toJson()).toList(),
        "taskAction": taskAction?.map((x) => x.toJson()).toList(),
        "taskResource": taskResource?.map((x) => x.toJson()).toList(),
      };

  @override
  String toString() {
    return "$id, $taskName, $moTa, $ngayTao, $startDay, $endDateDuKien, $trangThai, $tag, $taskType, $priority, $nguoiGiamSat, $nguoiGiaoViec, $danhSachNguoiThucHien, $nguoiTao, $taskUpdateInfo, $taskAction, $taskResource, ";
  }

  static List<Task> parse(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed.map<Task>((json) => Task.fromJson(json)).toList();
  }
}

// --------------------------------------------------------------------------------- //
// --------------------------------- CLASS NGƯỜI ----------------------------------- //

class Nguoi {
  Nguoi(
      {required this.id,
      required this.ho,
      required this.tenDem,
      required this.ten,
      required this.hinhAnh,
      required this.tuDanhGiaMucDoHoanThanh,
      required this.nguoiDuyetDanhGiaMucDoHoanThanh,
      required this.nguoiDuyetGhiChu});

  final int? id;
  final String? ho;
  final String? tenDem;
  final String? ten;
  final String? hinhAnh;
  late int? tuDanhGiaMucDoHoanThanh;
  late int? nguoiDuyetDanhGiaMucDoHoanThanh;
  late String? nguoiDuyetGhiChu;

  Nguoi copyWith({
    int? id,
    String? ho,
    String? tenDem,
    String? ten,
    String? hinhAnh,
    int? tuDanhGiaMucDoHoanThanh,
    int? nguoiDuyetDanhGiaMucDoHoanThanh,
    String? nguoiDuyetGhiChu,
  }) {
    return Nguoi(
      id: id ?? this.id,
      ho: ho ?? this.ho,
      tenDem: tenDem ?? this.tenDem,
      ten: ten ?? this.ten,
      hinhAnh: hinhAnh ?? this.hinhAnh,
      tuDanhGiaMucDoHoanThanh:
          tuDanhGiaMucDoHoanThanh ?? this.tuDanhGiaMucDoHoanThanh,
      nguoiDuyetDanhGiaMucDoHoanThanh: nguoiDuyetDanhGiaMucDoHoanThanh ??
          this.nguoiDuyetDanhGiaMucDoHoanThanh,
      nguoiDuyetGhiChu: nguoiDuyetGhiChu ?? this.nguoiDuyetGhiChu,
    );
  }

  factory Nguoi.fromJson(Map<String, dynamic> json) {
    return Nguoi(
      id: json["id"],
      ho: json["ho"],
      tenDem: json["tenDem"],
      ten: json["ten"],
      hinhAnh: json["hinhAnh"],
      tuDanhGiaMucDoHoanThanh: json["tuDanhGiaMucDoHoanThanh"],
      nguoiDuyetDanhGiaMucDoHoanThanh: json["nguoiDuyetDanhGiaMucDoHoanThanh"],
      nguoiDuyetGhiChu: json["nguoiDuyetGhiChu"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "ho": ho,
        "tenDem": tenDem,
        "ten": ten,
        "hinhAnh": hinhAnh,
        "tuDanhGiaMucDoHoanThanh": tuDanhGiaMucDoHoanThanh,
        "nguoiDuyetDanhGiaMucDoHoanThanh": nguoiDuyetDanhGiaMucDoHoanThanh,
        "nguoiDuyetGhiChu": nguoiDuyetGhiChu,
      };

  @override
  String toString() {
    return "$id, $ho, $tenDem, $ten, $hinhAnh, $tuDanhGiaMucDoHoanThanh, $nguoiDuyetDanhGiaMucDoHoanThanh, $nguoiDuyetGhiChu ";
  }
}

// --------------------------------------------------------------------------------- //
// ---------------------------------- CLASS TAG ------------------------------------ //
class Tag {
  Tag({
    required this.id,
    required this.tenDanhMuc,
    required this.maDanhMuc,
  });

  final int? id;
  final String? tenDanhMuc;
  final String? maDanhMuc;

  Tag copyWith({
    int? id,
    String? tenDanhMuc,
    String? maDanhMuc,
  }) {
    return Tag(
      id: id ?? this.id,
      tenDanhMuc: tenDanhMuc ?? this.tenDanhMuc,
      maDanhMuc: maDanhMuc ?? this.maDanhMuc,
    );
  }

  factory Tag.fromJson(Map<String, dynamic> json) {
    return Tag(
      id: json["id"],
      tenDanhMuc: json["tenDanhMuc"],
      maDanhMuc: json["maDanhMuc"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "tenDanhMuc": tenDanhMuc,
        "maDanhMuc": maDanhMuc,
      };

  @override
  String toString() {
    return "$id, $tenDanhMuc, $maDanhMuc, ";
  }
}

// --------------------------------------------------------------------------------- //
// ------------------------------ CLASS TASK ACTION -------------------------------- //

class TaskAction {
  TaskAction({
    required this.id,
    required this.moTa,
    required this.khoiLuongCongViec,
    required this.donViTinhId,
    required this.tongSoGioThucHien,
    required this.ngayKetThuc,
    required this.tiLeHoanThanh,
    required this.lyDoChuaHoanThanh,
    required this.donViTinh,
  });

  final int? id;
  final String? moTa;
  final int? khoiLuongCongViec;
  final int? donViTinhId;
  final int? tongSoGioThucHien;
  final DateTime? ngayKetThuc;
  final int? tiLeHoanThanh;
  final dynamic lyDoChuaHoanThanh;
  final dynamic donViTinh;

  TaskAction copyWith({
    int? id,
    String? moTa,
    int? khoiLuongCongViec,
    int? donViTinhId,
    int? tongSoGioThucHien,
    DateTime? ngayKetThuc,
    int? tiLeHoanThanh,
    dynamic lyDoChuaHoanThanh,
    dynamic donViTinh,
  }) {
    return TaskAction(
      id: id ?? this.id,
      moTa: moTa ?? this.moTa,
      khoiLuongCongViec: khoiLuongCongViec ?? this.khoiLuongCongViec,
      donViTinhId: donViTinhId ?? this.donViTinhId,
      tongSoGioThucHien: tongSoGioThucHien ?? this.tongSoGioThucHien,
      ngayKetThuc: ngayKetThuc ?? this.ngayKetThuc,
      tiLeHoanThanh: tiLeHoanThanh ?? this.tiLeHoanThanh,
      lyDoChuaHoanThanh: lyDoChuaHoanThanh ?? this.lyDoChuaHoanThanh,
      donViTinh: donViTinh ?? this.donViTinh,
    );
  }

  factory TaskAction.fromJson(Map<String, dynamic> json) {
    return TaskAction(
      id: json["id"],
      moTa: json["moTa"],
      khoiLuongCongViec: json["khoiLuongCongViec"],
      donViTinhId: json["donViTinhId"],
      tongSoGioThucHien: json["tongSoGioThucHien"],
      ngayKetThuc: DateTime.tryParse(json["ngayKetThuc"] ?? ""),
      tiLeHoanThanh: json["tiLeHoanThanh"],
      lyDoChuaHoanThanh: json["lyDoChuaHoanThanh"],
      donViTinh: json["donViTinh"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "moTa": moTa,
        "khoiLuongCongViec": khoiLuongCongViec,
        "donViTinhId": donViTinhId,
        "tongSoGioThucHien": tongSoGioThucHien,
        "ngayKetThuc": ngayKetThuc?.toIso8601String(),
        "tiLeHoanThanh": tiLeHoanThanh,
        "lyDoChuaHoanThanh": lyDoChuaHoanThanh,
        "donViTinh": donViTinh,
      };

  @override
  String toString() {
    return "$id, $moTa, $khoiLuongCongViec, $donViTinhId, $tongSoGioThucHien, $ngayKetThuc, $tiLeHoanThanh, $lyDoChuaHoanThanh, $donViTinh, ";
  }
}

// --------------------------------------------------------------------------------- //
// ------------------------------- CLASS TASK RESOURCE ----------------------------- //

class TaskResource {
  TaskResource({
    required this.id,
    required this.taskId,
    required this.documentId,
    required this.fileDinhKemPath,
  });

  final int? id;
  final int? taskId;
  final int? documentId;
  final dynamic fileDinhKemPath;

  TaskResource copyWith({
    int? id,
    int? taskId,
    int? documentId,
    dynamic fileDinhKemPath,
  }) {
    return TaskResource(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      documentId: documentId ?? this.documentId,
      fileDinhKemPath: fileDinhKemPath ?? this.fileDinhKemPath,
    );
  }

  factory TaskResource.fromJson(Map<String, dynamic> json) {
    return TaskResource(
      id: json["id"],
      taskId: json["taskId"],
      documentId: json["documentId"],
      fileDinhKemPath: json["fileDinhKemPath"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "taskId": taskId,
        "documentId": documentId,
        "fileDinhKemPath": fileDinhKemPath,
      };

  @override
  String toString() {
    return "$id, $taskId, $documentId, $fileDinhKemPath, ";
  }
}

// --------------------------------------------------------------------------------- //
// ----------------------------- CLASS TASK UPDATE INFO ---------------------------- //

class TaskUpdateInfo {
  TaskUpdateInfo({
    required this.id,
    required this.updateInfo,
    required this.ngayCapNhat,
    required this.nguoiCapNhat,
  });

  final int? id;
  final String? updateInfo;
  final DateTime? ngayCapNhat;
  final Nguoi? nguoiCapNhat;

  TaskUpdateInfo copyWith({
    int? id,
    String? updateInfo,
    DateTime? ngayCapNhat,
    Nguoi? nguoiCapNhat,
  }) {
    return TaskUpdateInfo(
      id: id ?? this.id,
      updateInfo: updateInfo ?? this.updateInfo,
      ngayCapNhat: ngayCapNhat ?? this.ngayCapNhat,
      nguoiCapNhat: nguoiCapNhat ?? this.nguoiCapNhat,
    );
  }

  factory TaskUpdateInfo.fromJson(Map<String, dynamic> json) {
    return TaskUpdateInfo(
      id: json["id"],
      updateInfo: json["updateInfo"],
      ngayCapNhat: DateTime.tryParse(json["ngayCapNhat"] ?? ""),
      nguoiCapNhat: json["nguoiCapNhat"] == null
          ? null
          : Nguoi.fromJson(json["nguoiCapNhat"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "updateInfo": updateInfo,
        "ngayCapNhat": ngayCapNhat?.toIso8601String(),
        "nguoiCapNhat": nguoiCapNhat?.toJson(),
      };

  @override
  String toString() {
    return "$id, $updateInfo, $ngayCapNhat, $nguoiCapNhat, ";
  }
}
