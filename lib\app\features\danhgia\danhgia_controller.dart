
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/constans/constant_data.dart';
import 'package:tvumobile/app/data/models/portal/tms_danhgia_xe.dart';
import 'package:tvumobile/app/services/api_provider.dart';

class DanhGiaController extends GetxController {
  ApiProvider apiProvider = Get.find();
  late String donviId;
  late String type;
  final dstaixe = <DsTaixe>[].obs;
  final donvi = "".obs;
  final loaidanggia = "dv".obs;
  final title = "Đánh giá chất lượng đơn vị".obs;
  int selectedTaixe = 0;
  String ykiens = "";
  double rating = -1;

  @override
  Future<void> onInit() async {
    super.onInit();
    //Logger().i('got initial uri: ${Get.arguments}');
    Logger().i('got initial uri: ${Get.parameters}');
    donviId = Get.parameters['donviId'] ?? "";
    type = Get.parameters['type'] ?? "";
    if(type == "xe") {
      title.value = "Đánh giá chất lượng phục vụ";
    }
    Logger().i('got initial type: $type');
    if(type == "dv") {
      await getDatas();
    } else if(type == "xe") {
      await getDatasXe();
    }
  }

  @override
  Future<void> onReady() async {
    loaidanggia.value = type;
    //update();
    initialUri = Uri();
  }

  getDatas() async {
    final tkbData = await apiProvider.getDonviQR(donviId);
    if (tkbData == null) {
      donvi.value = "Lỗi";
    } else {
      donvi.value = tkbData["tenPhongChucNang"];
    }
    update();
  }

  getDatasXe() async {
    final tkbData = await apiProvider.getThongtinXeQR(donviId);
    if (tkbData == null) {
      donvi.value = "Lỗi";
    } else {
      donvi.value = tkbData.thongtinxe!.bienKiemSoat!;
      dstaixe.addAll(tkbData.dsTaixe);
    }
    update();
  }

  guiDanhgia() async {
    FocusManager.instance.primaryFocus?.unfocus();
    if(rating <= 0) {
      Get.snackbar("Chưa nhập thông tin", "Vui lòng chọn mức độ hài lòng của bạn", backgroundColor: mlColorLightGrey100);
      return;
    }
    if(type == "xe" && selectedTaixe <= 0) {
      Get.snackbar("Chưa nhập thông tin", "Vui lòng chọn nhân viên lái xe");
      return;
    }
    Map<String, dynamic> phieudg = {"donviId":donviId, "ykien":ykiens, "rating": rating};
    final tkbData = await apiProvider.postDanhGiaDonVi(phieudg);
    if(tkbData != null) {
      Get.back();
      Get.snackbar("Đánh giá chất lượng", "Xin cảm ơn Bạn!\nPhản hồi của bạn đã được ghi nhận thành công!", backgroundColor: mlColorLightGrey100);

    } else {
      if (kDebugMode) {
        print(tkbData);
      }
    }
  }


  onDstaixeChanged(int id) {
    selectedTaixe = id;
  }
  onYKienChanged(String id) {
    ykiens = id;
  }
  onRatingChanged(double id) {
    rating = id;
  }


}