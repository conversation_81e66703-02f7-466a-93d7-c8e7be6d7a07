import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/features/danhgia/controllers/qr_home_controller.dart';
import 'package:tvumobile/app/features/danhgia/widgets/qrcode_bottom_bar.dart';

class QrHomeView extends GetView<QrHomeController> {
  const QrHomeView({super.key});

  @override
  Widget build(BuildContext context) {
    // Khởi tạo PageController
    final PageController pageController = PageController(
      initialPage: controller.selectedIndex.value,
    );
    return Scaffold(
      // body: Obx(() => controller.qrNavigation[controller
      //     .selectedIndex.value]), // <PERSON><PERSON><PERSON> thị màn hình dựa trên selectedIndex
      body: PageView(
        controller: pageController,
        physics: const BouncingScrollPhysics(), // Hiệu ứng trượt mượt
        children: controller.qrNavigation, // <PERSON>h sách các màn hình
        onPageChanged: (index) {
          // Cập nhật selectedIndex khi vuốt trang
          controller.selectedIndex.value = index;
        },
      ),
      bottomNavigationBar: QRCodeBottomBar(
        controller, context,
        pageController, // Truyền PageController vào QRCodeBottomBar
      ),
    );
  }
}
