part of 'app_pages.dart';

/// used to switch pages
class Routes {
  static const dashboard = _Paths.dashboard;
  static const NAV = _Paths.NAV;
  static const HOME = _Paths.HOME;
  static const CV_DETAIL = _Paths.CV_DETAIL;
  static const GRAPHQL = _Paths.GRAPHQL;
  static const ONBOARDING = _Paths.ONBOARDING;
  static const SPLASH = _Paths.SPLASH;
  static const SPLASH2 = _Paths.SPLASH2;
  static const LOGIN = _Paths.LOGIN;
  static const CONGVAN = _Paths.CONGVAN;
  static const TKBGIANGVIEN = _Paths.TKBGIANGVIEN;
  static const DANHGIA = _Paths.DANHGIA;
  static const QRSCANER = _Paths.QRSCANER;
  static const QRCANHAN = _Paths.QRCANHAN;
  static const QRLLKH = _Paths.QRLLKH;
  static const QRHOME = _Paths.QRHOME;
  static const LUONGTHANG = _Paths.LUONGTHANG;
  static const CHAMCONG = _Paths.CHAMCONG;

  // ĐƠN NGHỈ PHÉP
  static const DONNGHIPHEP = _Paths.DONNGHIPHEP;
  static const NGHIPHEP = _Paths.NGHIPHEP;
  static const LICHNGHIPHEP = _Paths.LICHNGHIPHEP;
  static const CHITIETDNP = _Paths.CHITIETDNP;
  static const DUYETDNP = _Paths.DUYETDNP;
  static const UPDATER = _Paths.UPDATER;

  // GIAO VIỆC
  // static const TASK = _Paths.TASK;
  static const TASKVIEW = _Paths.TASKVIEW;
  static const TASKOVERVIEW = _Paths.TASKOVERVIEW;
  static const TASK_CREATE = _Paths.TASK_CREATE;
  static const TASK_DETAIL = _Paths.TASK_DETAIL;

  // ĐIỀU XE
  static const DIEUXE = _Paths.DIEUXE;
  static const DIEUXE_CREATE = _Paths.DIEUXE_CREATE;
  static const DIEUXE_DETAIL = _Paths.DIEUXE_DETAIL;

  // KÝ SỐ
  static const KYSO = _Paths.KYSO;
  static const DOCUMENT_SIGN = _Paths.DOCUMENT_SIGN;
  static const DOCUMENT_VIEW = _Paths.DOCUMENT_VIEW;
}

/// contains a list of route names.
// made separately to make it easier to manage route naming
class _Paths {
  static const dashboard = '/dashboard';

  static const NAV = '/';
  static const CV_DETAIL = '/cv_detail';
  static const GRAPHQL = '/graphql';
  static const HOME = '/home';
  static const ONBOARDING = '/onboarding';
  static const SPLASH = '/splash';
  static const SPLASH2 = '/splash2';
  static const LOGIN = '/login';
  static const CONGVAN = '/convan';
  static const TKBGIANGVIEN = '/tkbgiangvien';
  static const DANHGIA = '/danhgiachatluong';
  static const QRSCANER = '/qrscanner';
  static const QRCANHAN = '/qrcanhan';
  static const QRLLKH = '/qrllkh';
  static const QRHOME = '/qrhome';
  static const LUONGTHANG = '/luongthang';
  static const CHAMCONG = '/chamcong';

  // GIAO VIỆC
  // static const TASK = '/tasky';
  static const TASKVIEW = '/taskytaskview';
  static const TASKOVERVIEW = '/taskyoverview';
  static const TASK_CREATE = '/tasky_createtask';
  static const TASK_DETAIL = '/tasky_detail';

  // ĐƠN NGHỈ PHÉP
  static const DONNGHIPHEP = '/donnghiphep';
  static const NGHIPHEP = '/nghiphep';
  static const LICHNGHIPHEP = '/lichnghiphep';
  static const CHITIETDNP = '/chitietdnp';
  static const DUYETDNP = '/duyetdnp';
  static const UPDATER = '/updater';

  // ĐIỀU XE
  static const DIEUXE = '/phieudieuxe';
  static const DIEUXE_CREATE = '/phieudieuxe_create';
  static const DIEUXE_DETAIL = '/phieudieuxe_detail';

  // KÝ SỐ
  static const KYSO = '/kyso';
  static const DOCUMENT_SIGN = '/document-sign';
  static const DOCUMENT_VIEW = '/document-view';
}
