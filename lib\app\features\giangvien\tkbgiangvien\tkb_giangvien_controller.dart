import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';
import 'package:tvumobile/app/data/models/edusoft/tkb_giang_vien.dart';
import 'package:tvumobile/app/data/models/sch/tkb/tbk_datasource.dart';
import 'package:tvumobile/app/data/models/sch/tkb/tkb_tuan_giangvien.dart';
import 'package:tvumobile/app/features/navbar/navbar_controller.dart';
import 'package:tvumobile/app/utils/helpers/tkb_helper.dart';
import 'package:tvumobile/app/services/api_provider.dart';

class TkbGiangVienController extends GetxController {
  bool uiLoading = true;
  final appbarTitle = "Thời khoá biểu".obs;
  TkbGVDataSource tkbGVDataSource = TkbGVDataSource([]);
  ApiProvider apiProvider = Get.find();
  final CalendarController calendarController = CalendarController();
  late DateTime minDate, maxDate;

  @override
  Future<void> onInit() async {
    super.onInit();
    calendarController.selectedDate = DateTime.now();
  }

  void onBackPress() {
    Get.find<NavbarController>().onTap(0);
  }

  @override
  Future<void> onReady() async {
    await getTKBFunctions();
  }

  void onViewChanged(ViewChangedDetails visibleDatesChangedDetails) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      final DateTime currentViewDate = visibleDatesChangedDetails
          .visibleDates[visibleDatesChangedDetails.visibleDates.length ~/ 2];

      if (currentViewDate.month == DateTime.now().month &&
          currentViewDate.year == DateTime.now().year) {
        calendarController.selectedDate = DateTime.now();
      } else {
        calendarController.selectedDate =
            DateTime(currentViewDate.year, currentViewDate.month, 01);
      }
    });
  }

  getTKBFunctions() async {
    TkbGiangVien? response = await apiProvider.loadTkbGiangVien();
    if(response != null) {
      //print(response);
      List<TkbTuanGiangVien> tkbs = _getDataSource(response);
      tkbGVDataSource = TkbGVDataSource(tkbs);
      uiLoading = false;
      minDate = tkbs[0].ketThuc!;
      maxDate = tkbs[tkbs.length - 1].ketThuc!;
      update();
    }
  }

  List<TkbTuanGiangVien> _getDataSource(TkbGiangVien tkbGiangVien) {
    ThemeData theme = Get.theme;
    List<TkbTuanGiangVien> lists = [];
    for(DsTuanTkb tkbtuan in tkbGiangVien.dsTuanTkb) {
      for (DsThoiKhoaBieu tkb in tkbtuan.dsThoiKhoaBieu) {
        TkbTuanGiangVien tkbTuanGiangVien = TkbTuanGiangVien();
        //DateTime ngayday = tkb.ngayHoc;
        tkbTuanGiangVien.tenMon = tkb.tenMon;
        tkbTuanGiangVien.batDau = getTietBD(tkb.ngayHoc!, tkb.tietBatDau!);
        tkbTuanGiangVien.ketThuc = getTietBD(tkb.ngayHoc!, tkb.tietBatDau! + tkb.soTiet!);
        tkbTuanGiangVien.tenPhong = tkb.maPhong;
        tkbTuanGiangVien.maLop = tkb.maLop;
        tkbTuanGiangVien.background = theme.primaryColor.withOpacity(0.3);
        tkbTuanGiangVien.maMon = tkb.maMon;
        tkbTuanGiangVien.nhomHoc = tkb.maNhom;
        tkbTuanGiangVien.nhomTH = tkb.maToTh;
        tkbTuanGiangVien.soTiet = tkb.soTiet.toString();
        lists.add(tkbTuanGiangVien);
      }
    }
    return lists;
  }

  List<String> getMaLops(TkbGiangVien tkbGiangVien) {
    List<String> malops = [];
    for(DsTuanTkb tkbtuan in tkbGiangVien.dsTuanTkb) {
      for (DsThoiKhoaBieu tkb in tkbtuan.dsThoiKhoaBieu) {
        if(malops.any((element) => element == tkb.maLop)) {
          continue;
        } else {
          malops.add(tkb.maLop.toString());
        }
      }
    }
    return malops;
  }



  void goBack() {
    Get.back();
    // Navigator.pop(context);
  }
}