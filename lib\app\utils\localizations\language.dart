import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tvumobile/app/utils/localizations/translator.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class Language {
  final Locale locale;
  final bool supportRTL;
  final String languageName;

  static List<Language> languages = [
    Language(const Locale('vi'), "Tiếng Việt"),
    Language(const Locale('en'), "English"),
  ];

  static Language currentLanguage = languages.first;

  Language(this.locale, this.languageName, [this.supportRTL = false]);

  static Future<bool> init() async {
    currentLanguage = await getLanguage();
    return true;
  }

  static List<Locale> getLocales() {
    return languages.map((e) => e.locale).toList();
  }

  static List<String> getLanguagesCodes() {
    return languages.map((e) => e.locale.languageCode).toList();
  }

  static Future<bool> changeLanguage(Language language1) async {
    currentLanguage = language1;
    await Translator.changeLanguage(language1);
    LocalStorageServices.setCurrentLanguage(currentLanguage.locale.languageCode);
    //SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    //sharedPreferences.setString("lang_code", language.locale.languageCode);
    return true;
  }

  static Future<bool> changeLanguageByCode(String code) async {
    return await changeLanguage(getLanguageFromCode(code));
  }

  static Future<Language> getLanguage() async {
    Language? language;
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    String? langCode = sharedPreferences.getString("lang_code");
    if (langCode != null) {
      language = findFromLocale(Locale(langCode));
    }

    return language ?? languages.first;
  }

  static Language getLanguageFromCode(String code) {
    Language language = languages.first;
    for (var element in languages) {
      if (element.locale.languageCode == code) language = element;
    }
    return language;
  }

  static Language? findFromLocale(Locale locale) {
    for (Language language in languages) {
      if (language.locale.languageCode == locale.languageCode) return language;
    }
    return null;
  }

  @override
  String toString() {
    return 'Language{locale: $locale, isRTL: $supportRTL, languageName: $languageName}';
  }
}
