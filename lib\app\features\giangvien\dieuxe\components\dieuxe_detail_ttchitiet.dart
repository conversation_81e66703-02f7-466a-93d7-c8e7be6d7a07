import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/config/themes/colors.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_divider.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_detail_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/date_time_extension.dart';

Widget ThongTinChiTietTab(
    BuildContext context, DieuXeDetailController controller) {
  final theme = Theme.of(context);
  return ListView(
    shrinkWrap: true,
    children: [
      // ------------------------------ ĐƠN VỊ -------------------------------
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        child: Row(
          children: [
            Icon(
              LucideIcons.building2,
              color: theme.primaryColor,
              size: 15,
            ),
            SizedBox(
              width: 100,
              child: MyText.labelLarge(
                " Đơn vị:",
                color: theme.primaryColor,
                fontWeight: 700,
              ),
            ),
            Expanded(
              child: MyText.bodyMedium(
                controller.pdx.donVi == null
                    ? ""
                    : controller.pdx.donVi!.tenDonVi!,
                color: Colors.black87,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),

      // ---------------------------- NGƯỜI TẠO ------------------------------
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        child: Row(
          children: [
            Icon(
              LucideIcons.userRoundPen,
              color: theme.primaryColor,
              size: 15,
            ),
            SizedBox(
              width: 100,
              child: MyText.labelLarge(
                " Người tạo:",
                color: theme.primaryColor,
                fontWeight: 700,
              ),
            ),
            Expanded(
              child: MyText.bodyMedium(
                "${controller.pdx.nguoiTao?.ho} ${controller.pdx.nguoiTao?.tenDem} ${controller.pdx.nguoiTao?.ten}",
                color: Colors.black87,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),

      // ----------------------------- NGÀY TẠO ------------------------------
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        child: Row(
          children: [
            Icon(
              LucideIcons.calendar,
              color: theme.primaryColor,
              size: 15,
            ),
            SizedBox(
              width: 100,
              child: MyText.labelLarge(
                " Ngày tạo:",
                color: theme.primaryColor,
                fontWeight: 700,
              ),
            ),
            Expanded(
              child: MyText.bodyMedium(
                "${controller.pdx.ngayTao?.toFormat(format: "dd.MM.yyyy")}",
                color: Colors.black87,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),

      // ---------------------------- TRẠNG THÁI -----------------------------
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        child: Row(
          children: [
            Icon(
              LucideIcons.settings,
              color: theme.primaryColor,
              size: 15,
            ),
            SizedBox(
              width: 93,
              child: MyText.labelLarge(
                " Trạng thái:",
                color: theme.primaryColor,
                fontWeight: 700,
              ),
            ),
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2), // Thêm padding để chữ không sát viền
                decoration: BoxDecoration(
                  color: getStatusColors(controller.pdx.trangThai!), //
                  borderRadius: BorderRadius.circular(4), // Bo góc nhẹ cho đẹp
                ),
                child: MyText.bodyMedium(
                  controller.pdx.trangThai!,
                  fontWeight: 700,
                  color: Colors.white, // Chữ màu trắng
                  maxLines: 1, // Giới hạn 1 dòng để tránh tràn
                  overflow: TextOverflow.ellipsis, // Ẩn bớt nếu quá dài
                ),
              ),
            ),
          ],
        ),
      ),

      if (controller.pdx.trangThai!.toLowerCase().contains("đơn vị")) ...[
        // ---------------------------- ĐƠN VỊ DUYỆT ---------------------------
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          child: Row(
            children: [
              Icon(
                LucideIcons.squareCheck,
                color: theme.primaryColor,
                size: 15,
              ),
              SizedBox(
                width: 100,
                child: MyText.labelLarge(
                  " Đơn vị duyệt:",
                  color: theme.primaryColor,
                  fontWeight: 700,
                ),
              ),
              Expanded(
                child: MyText.bodyMedium(
                  "${controller.pdx.donViDuyet?.ho} ${controller.pdx.donViDuyet?.tenDem ?? ""} ${controller.pdx.donViDuyet?.ten}",
                  color: Colors.black87,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ],

      if (controller.pdx.trangThai!.toLowerCase().contains("yêu cầu")) ...[
        // ------------------------ YÊU CẦU ĐIỀU CHỈNH -------------------------
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    LucideIcons.triangleAlert,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    child: MyText.labelLarge(
                      " Yêu cầu điều chỉnh:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                    child: MyText.bodyMedium(
                      "- ${controller.pdx.ketQua}",
                      color: Colors.black87,
                      maxLines: 2,
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],

      DieuXeDivider(context, "nội dung công tác"),
      //Divider(color: theme.dividerColor.withOpacity(0.3)),

      // ----------------------------- NỘI DUNG ------------------------------
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        child: Obx(() => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      LucideIcons.filePen,
                      color: theme.primaryColor,
                      size: 15,
                    ),
                    MyText.labelLarge(
                      " Nội dung công tác:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ],
                ),
                SizedBox(height: 4),
                // if (controller.isExpandedNoiDung.value)
                MyText.bodyMedium(
                  controller.phieughep.value
                      ? controller.pdx.noiDung!
                      : "- ${controller.pdx.noiDung}",
                  color: Colors.black87,
                  maxLines: null, // Hiển thị toàn bộ khi mở rộng
                  softWrap: true,
                )
                // else ...[
                //   Row(
                //     mainAxisAlignment: MainAxisAlignment.start,
                //     children: [
                //       Expanded(
                //         child: MyText.bodyMedium(
                //           controller.phieughep.value
                //               ? controller.pdx.noiDung!
                //               : "- ${controller.pdx.noiDung}",
                //           color: Colors.black87,
                //           maxLines: 2,
                //           softWrap: true,
                //           overflow: TextOverflow.ellipsis,
                //         ),
                //       ),
                //     ],
                //   ),
                //   Row(
                //     mainAxisAlignment: MainAxisAlignment.end,
                //     children: [
                //       if (controller.pdx.noiDung != null &&
                //           controller.pdx.noiDung!.length >= 90)
                //         GestureDetector(
                //           onTap: () =>
                //               controller.isExpandedNoiDung.value = true,
                //           child: MyText.bodyMedium(
                //             "Xem thêm",
                //             color: Colors.blue,
                //             fontWeight: 600,
                //           ),
                //         ),
                //     ],
                //   ),
                // ],
                // if (controller.isExpandedNoiDung.value)
                //   Row(
                //     mainAxisAlignment: MainAxisAlignment.end,
                //     children: [
                //       GestureDetector(
                //         onTap: () => controller.isExpandedNoiDung.value = false,
                //         child: MyText.bodyMedium(
                //           "Thu gọn",
                //           color: Colors.blue,
                //           fontWeight: 600,
                //         ),
                //       ),
                //     ],
                //   ),
              ],
            )),
      ),

      // --------------------------- NƠI CÔNG TÁC ----------------------------
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        child: Obx(() => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      LucideIcons.mapPin,
                      color: theme.primaryColor,
                      size: 15,
                    ),
                    MyText.labelLarge(
                      " Nơi công tác:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ],
                ),
                SizedBox(height: 4),
                // if (controller.isExpandedNoiCT.value)
                MyText.bodyMedium(
                  controller.phieughep.value
                      ? controller.pdx.noiCongTac!
                      : "- ${controller.pdx.noiCongTac}",
                  color: Colors.black87,
                  maxLines: null, // Hiển thị toàn bộ khi mở rộng
                  softWrap: true,
                )
                // else ...[
                //   Row(
                //     mainAxisAlignment: MainAxisAlignment.start,
                //     children: [
                //       Expanded(
                //         child: MyText.bodyMedium(
                //           controller.phieughep.value
                //               ? controller.pdx.noiCongTac!
                //               : "- ${controller.pdx.noiCongTac}",
                //           color: Colors.black87,
                //           maxLines: 2,
                //           softWrap: true,
                //           overflow: TextOverflow.ellipsis,
                //         ),
                //       ),
                //     ],
                //   ),
                //   Row(
                //     mainAxisAlignment: MainAxisAlignment.end,
                //     children: [
                //       if (controller.pdx.noiCongTac != null &&
                //           controller.pdx.noiCongTac!.length >= 90)
                //         GestureDetector(
                //           onTap: () => controller.isExpandedNoiCT.value = true,
                //           child: MyText.bodyMedium(
                //             "Xem thêm",
                //             color: Colors.blue,
                //             fontWeight: 600,
                //           ),
                //         ),
                //     ],
                //   ),
                // ],
                // if (controller.isExpandedNoiCT.value)
                //   Row(
                //     mainAxisAlignment: MainAxisAlignment.end,
                //     children: [
                //       GestureDetector(
                //         onTap: () => controller.isExpandedNoiCT.value = false,
                //         child: MyText.bodyMedium(
                //           "Thu gọn",
                //           color: Colors.blue,
                //           fontWeight: 600,
                //         ),
                //       ),
                //     ],
                //   ),
              ],
            )),
      ),

      // ----------------------- TỪ NGÀY - ĐẾN NGÀY --------------------------
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        child: Row(
          children: [
            Expanded(
              flex: 1, // Chia 50% không gian
              child: Row(
                children: [
                  Icon(
                    LucideIcons.calendarArrowUp,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  MyText.labelLarge(
                    " Từ ngày:",
                    color: theme.primaryColor,
                    fontWeight: 700,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.tuNgay?.toFormat(format: "dd/MM/yy") ??
                          '-',
                      color: Colors.black87,
                      maxLines: 3,
                      softWrap: true,
                    ),
                  ),
                ],
              ),
            ),

            // Khoảng cách giữa hai phần
            const SizedBox(width: 15),

            Expanded(
              flex: 1, // Chia 50% không gian
              child: Row(
                children: [
                  Icon(
                    LucideIcons.calendarArrowDown,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  MyText.labelLarge(
                    " Đến ngày:",
                    color: theme.primaryColor,
                    fontWeight: 700,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.denNgay?.toFormat(format: "dd/MM/yy") ??
                          '-',
                      color: Colors.black87,
                      maxLines: 3,
                      softWrap: true,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),

      DieuXeDivider(context, "thông tin di chuyển"),

      // Thanh tab của THÔNG TIN DI CHUYỂN
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        child: Obx(() => Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    controller.currentTab.value = 0;
                    controller.tabController.animateToPage(
                      0,
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  child: MyText.bodyMedium(
                    "Thông tin đi",
                    color: controller.currentTab.value == 0
                        ? theme.primaryColor
                        : Colors.grey,
                    fontWeight: controller.currentTab.value == 0 ? 700 : 100,
                  ),
                ),
                const SizedBox(width: 16),
                MyText.bodyMedium(
                  "|",
                  color: theme.dividerColor.withOpacity(0.3),
                ),
                const SizedBox(width: 16),
                GestureDetector(
                  onTap: () {
                    controller.currentTab.value = 1;
                    controller.tabController.animateToPage(
                      1,
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  child: MyText.bodyMedium(
                    "Thông tin về",
                    color: controller.currentTab.value == 1
                        ? theme.primaryColor
                        : Colors.grey,
                    fontWeight: controller.currentTab.value == 1 ? 700 : 100,
                  ),
                ),
              ],
            )),
      ),

      // -------------- PAGE VIEW LƯỚT (TƯƠNG TỰ NHƯ CAROUSEL) ---------------
      // PageView cho hiệu ứng vuốt
      SizedBox(
        height: 130,
        child: PageView(
          controller: controller.tabController,
          onPageChanged: (index) {
            controller.currentTab.value = index;
          },
          children: [
            // Trang thông tin đi
            SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // --------------------------- THỜI GIAN ĐI ----------------------------
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                    child: Row(
                      children: [
                        Icon(
                          LucideIcons.clockArrowUp,
                          color: theme.primaryColor,
                          size: 15,
                        ),
                        SizedBox(
                          width: 90,
                          child: MyText.labelLarge(
                            " Thời gian đi:",
                            color: theme.primaryColor,
                            fontWeight: 700,
                          ),
                        ),
                        Expanded(
                          child: MyText.bodyMedium(
                            controller.pdx.thoiGianKhoiHanh ?? "",
                            color: Colors.black87,
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // --------------------------- ĐỊA ĐIỂM ĐI -----------------------------
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    child: Obx(
                      () => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                LucideIcons.planeTakeoff,
                                color: theme.primaryColor,
                                size: 15,
                              ),
                              MyText.labelLarge(
                                " Địa điểm đi:",
                                color: theme.primaryColor,
                                fontWeight: 700,
                              ),
                            ],
                          ),
                          SizedBox(height: 4),
                          // if (controller.isExpandedDiaDiemDi.value)
                          MyText.bodyMedium(
                            controller.phieughep.value
                                ? controller.pdx.diaDiemKhoiHanh!
                                : "- ${controller.pdx.diaDiemKhoiHanh}",
                            color: Colors.black87,
                            maxLines: null,
                            softWrap: true,
                          )
                          // else ...[
                          //   Row(
                          //     mainAxisAlignment: MainAxisAlignment.start,
                          //     children: [
                          //       Expanded(
                          //         child: MyText.bodyMedium(
                          //           controller.phieughep.value
                          //               ? controller.pdx.diaDiemKhoiHanh!
                          //               : "- ${controller.pdx.diaDiemKhoiHanh}",
                          //           color: Colors.black87,
                          //           maxLines: 2,
                          //           softWrap: true,
                          //           overflow: TextOverflow.ellipsis,
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                          //   Row(
                          //     mainAxisAlignment: MainAxisAlignment.end,
                          //     children: [
                          //       if (controller.pdx.diaDiemKhoiHanh != null &&
                          //           controller.pdx.diaDiemKhoiHanh!.length >=
                          //               90)
                          //         GestureDetector(
                          //           onTap: () => controller
                          //               .isExpandedDiaDiemDi.value = true,
                          //           child: MyText.bodyMedium(
                          //             "Xem thêm",
                          //             color: Colors.blue,
                          //             fontWeight: 600,
                          //           ),
                          //         ),
                          //     ],
                          //   ),
                          // ],
                          // if (controller.isExpandedDiaDiemDi.value)
                          //   Row(
                          //     mainAxisAlignment: MainAxisAlignment.end,
                          //     children: [
                          //       GestureDetector(
                          //         onTap: () => controller
                          //             .isExpandedDiaDiemDi.value = false,
                          //         child: MyText.bodyMedium(
                          //           "Thu gọn",
                          //           color: Colors.blue,
                          //           fontWeight: 600,
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Trang thông tin về
            SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // --------------------------- THỜI GIAN VỀ ----------------------------
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                    child: Row(
                      children: [
                        Icon(
                          LucideIcons.clockArrowDown,
                          color: theme.primaryColor,
                          size: 15,
                        ),
                        SizedBox(
                          width: 90,
                          child: MyText.labelLarge(
                            " Thời gian về:",
                            color: theme.primaryColor,
                            fontWeight: 700,
                          ),
                        ),
                        Expanded(
                          child: MyText.bodyMedium(
                            controller.pdx.thoiGianVe ?? "",
                            color: Colors.black87,
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // --------------------------- ĐỊA ĐIỂM VỀ -----------------------------
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    child: Obx(
                      () => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                LucideIcons.planeLanding,
                                color: theme.primaryColor,
                                size: 15,
                              ),
                              MyText.labelLarge(
                                " Địa điểm về:",
                                color: theme.primaryColor,
                                fontWeight: 700,
                              ),
                            ],
                          ),
                          SizedBox(height: 4),
                          // if (controller.isExpandedDiaDiemVe.value)
                          MyText.bodyMedium(
                            controller.phieughep.value
                                ? controller.pdx.diaDiemVe!
                                : "- ${controller.pdx.diaDiemVe}",
                            color: Colors.black87,
                            maxLines: null,
                            softWrap: true,
                          )
                          // else ...[
                          //   Row(
                          //     mainAxisAlignment: MainAxisAlignment.start,
                          //     children: [
                          //       Expanded(
                          //         child: MyText.bodyMedium(
                          //           controller.phieughep.value
                          //               ? controller.pdx.diaDiemVe!
                          //               : "- ${controller.pdx.diaDiemVe}",
                          //           color: Colors.black87,
                          //           maxLines: 2,
                          //           softWrap: true,
                          //           overflow: TextOverflow.ellipsis,
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                          //   Row(
                          //     mainAxisAlignment: MainAxisAlignment.end,
                          //     children: [
                          //       if (controller.pdx.diaDiemVe != null &&
                          //           controller.pdx.diaDiemVe!.length >= 90)
                          //         GestureDetector(
                          //           onTap: () => controller
                          //               .isExpandedDiaDiemVe.value = true,
                          //           child: MyText.bodyMedium(
                          //             "Xem thêm",
                          //             color: Colors.blue,
                          //             fontWeight: 600,
                          //           ),
                          //         ),
                          //     ],
                          //   ),
                          // ],
                          // if (controller.isExpandedDiaDiemVe.value)
                          //   Row(
                          //     mainAxisAlignment: MainAxisAlignment.end,
                          //     children: [
                          //       GestureDetector(
                          //         onTap: () => controller
                          //             .isExpandedDiaDiemVe.value = false,
                          //         child: MyText.bodyMedium(
                          //           "Thu gọn",
                          //           color: Colors.blue,
                          //           fontWeight: 600,
                          //         ),
                          //       ),
                          //     ],
                          //   ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ],
  );
}

getStatusColors(String status) {
  String lowerCaseStatus = status.toLowerCase();

  // Kiểm tra chứa từ khóa
  if (lowerCaseStatus.contains("khởi tạo")) {
    return getColorFromHex("#64D9FF");
  } else if (lowerCaseStatus.contains("chờ duyệt")) {
    return getColorFromHex("#FAD800").withOpacity(1.0);
  } else if (lowerCaseStatus.contains("đã duyệt") ||
      lowerCaseStatus.contains("thuê xe")) {
    return getColorFromHex("#00E200");
  } else if (lowerCaseStatus.contains("đã hủy") ||
      lowerCaseStatus.contains("không duyệt")) {
    return getColorFromHex("#FF2A04");
  } else if (lowerCaseStatus.contains("điều chỉnh")) {
    return getColorFromHex("#FFAF3D");
  } else {
    return getColorFromHex("#7B8089");
  }
}
