// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/features/giangvien/congvan/single_pdfviewer_screen.dart';
import 'package:tvumobile/app/shared_components/appbar.dart';
import 'package:tvumobile/app/shared_components/file_icon.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/app_helpers.dart';
import 'package:url_launcher/url_launcher.dart';
import 'chitiet_congvan_controller.dart';

class ChiTietCongVanView extends GetView<ChiTietCongVanController> {
  const ChiTietCongVanView({super.key});

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Scaffold(
      appBar: appBarTitle(context, "Chi tiết công văn".tr.toUpperCase()),
      // AppBar(
      //   backgroundColor: theme.primaryColor.withOpacity(0.95),
      //   centerTitle: true,
      //   toolbarHeight: 42,
      //   leading: IconButton(
      //     onPressed: () {
      //       if(globalConst.launchFromURL) {
      //         globalConst.launchFromURL = false;
      //         Get.offAllNamed(AppPages.NAV);
      //       } else {
      //         Get.back();
      //       }
      //     },
      //     icon: Icon(LucideIcons.arrowLeft, color: theme.colorScheme.onPrimary,),),
      //   title: Row(
      //     mainAxisAlignment: MainAxisAlignment.start,
      //     crossAxisAlignment: CrossAxisAlignment.start,
      //     children: [
      //       MyText.titleMedium("Chi tiết công văn".tr.toUpperCase(), color: theme.colorScheme.onPrimary,),
      //     ],
      //   ),
      //   actions: [
      //     Icon(LucideIcons.fileMinus, size: 16, color: theme.colorScheme.onPrimary,),
      //     MySpacing.width(20),
      //   ],
      // ),
      body: MyContainer(
        child: Obx(() {
          if (controller.congVanNhan.isEmpty) {
            return loading();
          }
          final doc = controller.congVanNhan[0];
          String nguoichuyen =
              '${doc.nguoiChuyenCongVan!.ho} ${doc.nguoiChuyenCongVan!.tenDem.toString() == "null" ? '' : '${doc.nguoiChuyenCongVan!.tenDem} '}${doc.nguoiChuyenCongVan!.ten}';
          return ListView(
              padding: const EdgeInsets.only(left: 0.0),
              children: <Widget>[
                Column(
                  children: [
                    MySpacing.height(8),
                    MyText.titleMedium(
                      doc.document!.trichYeu.toString(),
                      color: mlPrimaryTextColor,
                    ),
                    MySpacing.height(8),
                    nguoichuyen.trim() != ""
                        ? Row(
                            children: [
                              Icon(LucideIcons.squareArrowOutUpRight),
                              MySpacing.width(10),
                              MyText.labelMedium("Người chuyển:"),
                              MySpacing.width(5),
                              MyText.labelMedium(nguoichuyen),
                            ],
                          )
                        : Container(),
                    MySpacing.height(8),
                    Row(
                      children: [
                        const Icon(LucideIcons.building2),
                        MySpacing.width(10),
                        MyText.labelMedium("Nơi ban hành:"),
                        MySpacing.width(5),
                        Expanded(
                            child: MyText.labelMedium(
                          doc.document!.noiBanHanh!.tenDonVi.toString(),
                          maxLines: 2,
                        )),
                      ],
                    ),
                    MySpacing.height(8),
                    Row(
                      children: [
                        const Icon(LucideIcons.notebookTabs),
                        MySpacing.width(10),
                        MyText.labelMedium("Loại văn bản:"),
                        MySpacing.width(5),
                        MyText.labelMedium(
                            doc.document!.category!.ten.toString()),
                      ],
                    ),
                    MySpacing.height(10),
                    Divider(
                      color: theme.dividerColor.withOpacity(0.6),
                      height: 1,
                    ),
                    MyText.labelLarge("Đính kèm"),
                    Divider(
                      color: theme.dividerColor.withOpacity(0.6),
                      height: 1,
                    ),
                    MySpacing.height(8),
                    MyContainer(
                      paddingAll: 0,
                      child: ListView.separated(
                        scrollDirection: Axis.vertical,
                        shrinkWrap: true,
                        itemCount: doc.document!.cvFiles.length,
                        separatorBuilder: (BuildContext context, int index2) =>
                            Divider(
                          height: 1,
                          color: Colors.grey[300],
                        ),
                        itemBuilder: (BuildContext context, int index2) {
                          return ListTile(
                            leading: FileIcon(
                              '${doc.document!.cvFiles[index2].filePath}',
                              size: 18,
                            ),
                            title: MyText.labelMedium(
                              doc.document!.cvFiles[index2].fileName.toString(),
                            ),
                            //subtitle: Text(product.document!.cvFiles[index2].getFilePath),
                            onTap: () {
                              if (doc.document!.cvFiles[index2].getFileExt
                                  .contains('pdf')) {
                                onTapItem(index2);
                              } else {
                                launchUrl(
                                    Uri.parse(
                                        "https://tms.tvu.edu.vn/${doc.document!.cvFiles[index2].getFilePath}"),
                                    mode: LaunchMode.externalApplication);
                              }
                            },
                            trailing: IconButton(
                              icon: Icon(LucideIcons.download),
                              onPressed: () {
                                launchUrl(
                                    Uri.parse(
                                        "https://tms.tvu.edu.vn/${doc.document!.cvFiles[index2].getFilePath}"),
                                    mode: LaunchMode.externalApplication);
                              },
                              iconSize: 22,
                            ),
                          );
                        },
                      ),
                    )
                  ],
                ),
              ]);
        }),
      ),
    );
  }

  Future<void> downloadfile(String url) async {
    try {
      if (await canLaunchUrl(url.toUri())) {
        await launchUrl(url.toUri(),
            mode: LaunchMode.externalApplication, webOnlyWindowName: "_blank");
      } else {
        throw 'Could not launch $url';
      }
    } on Exception {
      throw 'Could not launch $url';
    }
  }

  Widget loading() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(color: Colors.white),
      child: SkeletonItem(
          child: Column(
        children: [
          Row(
            children: [
              SkeletonAvatar(
                style: SkeletonAvatarStyle(
                    shape: BoxShape.circle, width: 50, height: 50),
              ),
              SizedBox(width: 8),
              Expanded(
                child: SkeletonParagraph(
                  style: SkeletonParagraphStyle(
                      lines: 3,
                      spacing: 6,
                      lineStyle: SkeletonLineStyle(
                        randomLength: true,
                        height: 10,
                        borderRadius: BorderRadius.circular(8),
                        minLength: Get.width / 6,
                        maxLength: Get.width / 3,
                      )),
                ),
              )
            ],
          ),
          SizedBox(height: 12),
          SkeletonParagraph(
            style: SkeletonParagraphStyle(
                lines: 3,
                spacing: 6,
                lineStyle: SkeletonLineStyle(
                  randomLength: true,
                  height: 10,
                  borderRadius: BorderRadius.circular(8),
                  minLength: Get.width / 2,
                )),
          ),
          SizedBox(height: 12),
          SkeletonAvatar(
            style: SkeletonAvatarStyle(
              width: double.infinity,
              minHeight: Get.height / 8,
              maxHeight: Get.height / 3,
            ),
          ),
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  SkeletonAvatar(
                      style: SkeletonAvatarStyle(width: 20, height: 20)),
                  SizedBox(width: 8),
                  SkeletonAvatar(
                      style: SkeletonAvatarStyle(width: 20, height: 20)),
                  SizedBox(width: 8),
                  SkeletonAvatar(
                      style: SkeletonAvatarStyle(width: 20, height: 20)),
                ],
              ),
              SkeletonLine(
                style: SkeletonLineStyle(
                    height: 16,
                    width: 64,
                    borderRadius: BorderRadius.circular(8)),
              )
            ],
          )
        ],
      )),
    );
  }

  onTapItem(int index) {
    Get.to(
        () => SinglePdfViewerScreen(
            controller.congVanNhan[0].document!.cvFiles, index),
        transition: Transition.fadeIn);
  }
}
