import 'dart:convert';

import 'package:tvumobile/app/constans/app_constants.dart';

class UserInfo {
  UserInfo({
    required this.sub,
    required this.email,
    required this.aspNetIdentitySecurityStamp,
    required this.httpSchemasXmlsoapOrgWs200505IdentityClaimsGivenname,
    required this.urnGoogleImage,
    required this.name,
    required this.address,
    required this.role,
    required this.preferredUsername,
    required this.emailVerified,
    required this.phoneNumber,
    required this.phoneNumberVerified,
    required this.isteacher,
    required this.mssv,
    required this.vienchucid,
    required this.taikhoanid,
    required this.fullname,
    required this.hoten,
    required this.picture,
    required this.mavienchuc,
    required this.phonenumber,
  });

  final String? sub;
  final String? email;
  final String? aspNetIdentitySecurityStamp;
  final String? httpSchemasXmlsoapOrgWs200505IdentityClaimsGivenname;
  final String? urnGoogleImage;
  final String? name;
  final String? address;
  final List<String> role;
  final String? preferredUsername;
  final bool? emailVerified;
  final String? phoneNumber;
  final bool? phoneNumberVerified;
  final String? isteacher;
  final String? mssv;
  final String? vienchucid;
  final String? taikhoanid;
  final String? fullname;
  final String? hoten;
  final String? picture;
  final String? mavienchuc;
  final String? phonenumber;

  UserInfo copyWith({
    String? sub,
    String? email,
    String? aspNetIdentitySecurityStamp,
    String? httpSchemasXmlsoapOrgWs200505IdentityClaimsGivenname,
    String? urnGoogleImage,
    String? name,
    String? address,
    List<String>? role,
    String? preferredUsername,
    bool? emailVerified,
    String? phoneNumber,
    bool? phoneNumberVerified,
    String? isteacher,
    String? mssv,
    String? vienchucid,
    String? taikhoanid,
    String? fullname,
    String? hoten,
    String? picture,
    String? mavienchuc,
    String? phonenumber,
  }) {
    return UserInfo(
      sub: sub ?? this.sub,
      email: email ?? this.email,
      aspNetIdentitySecurityStamp: aspNetIdentitySecurityStamp ?? this.aspNetIdentitySecurityStamp,
      httpSchemasXmlsoapOrgWs200505IdentityClaimsGivenname: httpSchemasXmlsoapOrgWs200505IdentityClaimsGivenname ?? this.httpSchemasXmlsoapOrgWs200505IdentityClaimsGivenname,
      urnGoogleImage: urnGoogleImage ?? this.urnGoogleImage,
      name: name ?? this.name,
      address: address ?? this.address,
      role: role ?? this.role,
      preferredUsername: preferredUsername ?? this.preferredUsername,
      emailVerified: emailVerified ?? this.emailVerified,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      phoneNumberVerified: phoneNumberVerified ?? this.phoneNumberVerified,
      isteacher: isteacher ?? this.isteacher,
      mssv: mssv ?? this.mssv,
      vienchucid: vienchucid ?? this.vienchucid,
      taikhoanid: taikhoanid ?? this.taikhoanid,
      fullname: fullname ?? this.fullname,
      hoten: hoten ?? this.hoten,
      picture: picture ?? this.picture,
      mavienchuc: mavienchuc ?? this.mavienchuc,
      phonenumber: phonenumber ?? this.phonenumber,
    );
  }
  // ignore: no_leading_underscores_for_local_identifiers
  List<String> getRoles(dynamic _roles) {
    List<String> roles = [];
    if(_roles is String ) {
      if(_roles.contains(',')) {
        _roles.split(',').forEach((tag) {
        roles.add(tag.trim());
      });
      } else {
        roles.add(_roles.trim());
      }
    } else {
      roles = List<String>.from(_roles!.map((x) => x));
    }
    return roles;
  }

  String getAvatar() {
    if(picture != null && picture!.isNotEmpty) {
      return picture.toString();
    } else {
      if(urnGoogleImage != null && urnGoogleImage!.isNotEmpty) {
        return urnGoogleImage.toString();
      }
    }
    return AppImages.defaultImgUrl;
  }

  factory UserInfo.fromJson(Map<String, dynamic> json){
    //List<String> roles = getRoles(json["role"]);
    return UserInfo(
      sub: json["sub"],
      email: json["email"],
      aspNetIdentitySecurityStamp: json["AspNet.Identity.SecurityStamp"],
      httpSchemasXmlsoapOrgWs200505IdentityClaimsGivenname: json["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname"],
      urnGoogleImage: json["urn:google:image"],
      name: json["name"],
      address: json["address"],
      role: json["role"] == null ? [] : (json["role"] is String) ? List<String>.from(json["role"]!.split(',').map((x) => x)) : List<String>.from(json["role"]!.map((x) => x)),
      preferredUsername: json["preferred_username"],
      emailVerified: json["email_verified"],
      phoneNumber: json["phone_number"],
      phoneNumberVerified: json["phone_number_verified"],
      isteacher: json["isteacher"],
      mssv: json["mssv"],
      vienchucid: json["vienchucid"],
      taikhoanid: json["taikhoanid"],
      fullname: json["fullname"],
      hoten: json["hoten"],
      picture: json["picture"],
      mavienchuc: json["mavienchuc"],
      phonenumber: json["phonenumber"],
    );
  }

  Map<String, dynamic> toJson() => {
    "sub": sub,
    "email": email,
    "AspNet.Identity.SecurityStamp": aspNetIdentitySecurityStamp,
    "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname": httpSchemasXmlsoapOrgWs200505IdentityClaimsGivenname,
    "urn:google:image": urnGoogleImage,
    "name": name,
    "address": address,
    "role": role.map((x) => x).toList(),
    "preferred_username": preferredUsername,
    "email_verified": emailVerified,
    "phone_number": phoneNumber,
    "phone_number_verified": phoneNumberVerified,
    "isteacher": isteacher,
    "mssv": mssv,
    "vienchucid": vienchucid,
    "taikhoanid": taikhoanid,
    "fullname": fullname,
    "hoten": hoten,
    "picture": picture,
    "mavienchuc": mavienchuc,
    "phonenumber": phonenumber,
  };

  @override
  String toString(){
    return "$sub, $email, $aspNetIdentitySecurityStamp, $httpSchemasXmlsoapOrgWs200505IdentityClaimsGivenname, $urnGoogleImage, $name, $address, $role, $preferredUsername, $emailVerified, $phoneNumber, $phoneNumberVerified, $isteacher, $mssv, $vienchucid, $taikhoanid, $fullname, $hoten, $picture, $mavienchuc, $phonenumber, ";
  }

  String toJsonString() => json.encode(toJson());
  factory UserInfo.fromJsonString(String source) => UserInfo.fromJson(json.decode(source));

  bool isActualTeacher() {
    if(isteacher.toString() == 'true') {
      return true;
    } else {
      return false;
    }
  }
}
