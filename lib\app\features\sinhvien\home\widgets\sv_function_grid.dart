import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/config/themes/custom_theme.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import '../sv_home_controller.dart';

// mock model
class SvFunctionGridModelMock {
  final String id;
  final String title;
  final String? titleLong;
  final String? iconPath;
  final String imagePath;
  final Color backgroundColor;
  final Color iconBackgroundColor;
  final IconData? icon;
  final bool? enabled;

  SvFunctionGridModelMock({
    required this.id,
    required this.title,
    this.titleLong,
    this.iconPath,
    this.icon,
    this.enabled,
    required this.imagePath,
    required this.backgroundColor,
    required this.iconBackgroundColor,
  });
}

class SvFunctionGrid extends StatelessWidget {
  SvFunctionGrid({super.key, required this.homeController});
  final SvHomeController homeController;

  final List<SvFunctionGridModelMock> datasv = [
    SvFunctionGridModelMock(
      id : 'tkb',
      title: "TKB",
      titleLong: "Thời khoá biểu",
      icon: LucideIcons.calendarClock,
      iconPath: 'assets/vectors/alarm.svg',
      imagePath: "assets/vectors/icons8-task-100.png",
      backgroundColor: CustomTheme.orange,
      iconBackgroundColor: CustomTheme.occur,
    ),
    SvFunctionGridModelMock(
      id : 'thi',
      title: "Lịch thi",
      titleLong: "Thi kết thúc HP",
      iconPath: 'assets/vectors/tasks.svg',
      imagePath: "assets/vectors/icons8-vertical-timeline-100.png",
      backgroundColor: CustomTheme.red,
      iconBackgroundColor: CustomTheme.peach,
      icon: LucideIcons.calendarCheck,
      enabled: false
    ),
    SvFunctionGridModelMock(
      id : 'dso',
      title: "Điểm số",
      titleLong: "Điểm kết thúc HP",
      iconPath: 'assets/vectors/vocation.svg',
      imagePath: "assets/vectors/icons8-internship-100.png",
      backgroundColor: CustomTheme.green,
      iconBackgroundColor: CustomTheme.skyBlue,
      icon: LucideIcons.bookMarked
    ),
    SvFunctionGridModelMock(
      id : 'dsotl',
      title: "Điểm TL",
      titleLong: "Điểm tích luỹ kỳ vọng",
      icon: LucideIcons.calendarHeart,
      iconPath: 'assets/vectors/alarm.svg',
      imagePath: "assets/vectors/icons8-task-100.png",
      backgroundColor: CustomTheme.orange,
      iconBackgroundColor: CustomTheme.red,
    ),
    SvFunctionGridModelMock(
        id : 'phi',
        title: "Học phí",
        iconPath: 'assets/vectors/money.svg',
        imagePath: "assets/vectors/icons8-growing-money-100.png",
        backgroundColor: CustomTheme.blue,
        iconBackgroundColor: CustomTheme.darkGreen,
        icon: LucideIcons.receipt,
        enabled: false
    ),
    SvFunctionGridModelMock(
        id : 'khaosat',
        title: "Khảo sát",
        iconPath: 'assets/vectors/tasks.svg',
        imagePath: "assets/vectors/icons8-vertical-timeline-100.png",
        backgroundColor: CustomTheme.red,
        iconBackgroundColor: CustomTheme.purple,
        icon: LucideIcons.bookCheck,
        enabled: false
    ),
    SvFunctionGridModelMock(
        id : 'diemdanh',
        title: "Điểm danh",
        iconPath: 'assets/vectors/vocation.svg',
        imagePath: "assets/vectors/icons8-internship-100.png",
        backgroundColor: CustomTheme.green,
        iconBackgroundColor: CustomTheme.green,
        icon: LucideIcons.contactRound,
        enabled: false
    ),
    SvFunctionGridModelMock(
        id : 'phananh',
        title: "Phản ánh",
        iconPath: 'assets/vectors/money.svg',
        imagePath: "assets/vectors/icons8-growing-money-100.png",
        backgroundColor: CustomTheme.blue,
        iconBackgroundColor: CustomTheme.pink,
        icon: LucideIcons.flipHorizontal,
        enabled: false
    ),
    SvFunctionGridModelMock(
        id : 'sukien',
        title: "Sự kiện",
        iconPath: 'assets/vectors/vocation.svg',
        imagePath: "assets/vectors/icons8-internship-100.png",
        backgroundColor: CustomTheme.green,
        iconBackgroundColor: CustomTheme.blue,
        icon: LucideIcons.squareChartGantt,
        enabled: false
    ),
    SvFunctionGridModelMock(
        id : 'dkt',
        title: "Tất cả",
        titleLong: "Điểm kết thúc HP",
        iconPath: 'assets/vectors/vocation.svg',
        imagePath: "assets/vectors/icons8-internship-100.png",
        backgroundColor: CustomTheme.green,
        iconBackgroundColor: CustomTheme.purple,
        icon: LucideIcons.blocks,
        enabled: false
    ),
  ];

  @override
  Widget build(BuildContext context) {
    Theme.of(context);
    SvHomeController homeController = Get.find();
    return GridView.builder(
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: datasv.length,
      shrinkWrap: true,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemBuilder: (ctx, index) {
        var gridData = datasv[index];
        var opacity = 1.0;
        bool enabled = gridData.enabled ?? true;
        if(!enabled) {
          opacity = 0.5;
        }
        return
          InkWell(
            onTap: () => {
              homeController.onTapFunctions(gridData.id)
            },
            child: Opacity(
              opacity: opacity,
              child: Container(
                //padding: const EdgeInsets.only(left: 2, right: 2, top: 8, bottom: 2),
                decoration: const BoxDecoration(
                  // color: gridData.iconBackgroundColor.withAlpha(50),
                  //borderRadius: BorderRadius.circular(8),
                  //border: Border.all(color: gridData.iconBackgroundColor)
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    gridData.icon != null ? Icon(gridData.icon, size: 32, color: gridData.iconBackgroundColor,) : Image.asset(gridData.imagePath, width: 48,),
                    MySpacing.height(2),
                    MyText.labelSmall(gridData.title,),
                    //MyText.labelSmall(gridData.titleLong.toString(), style: theme.textTheme.labelSmall?.copyWith(fontSize: 8),),
                  ],
                ),
              ),
            )
          );
      },
    );
  }
}
