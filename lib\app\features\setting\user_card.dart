import 'package:avatar_glow/avatar_glow.dart';
import 'package:flutter/material.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/shared_components/circular_cached_network_image.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/localizations/strings_enum.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class UserCard extends StatelessWidget {
  final Color? backgroundColor;
  final Color? settingColor;
  final double? cardRadius;
  final Color? backgroundMotifColor;
  final Widget? cardActionWidget;
  final String? userName;
  final Widget? userMoreInfo;
  final ImageProvider userProfilePic;

  const UserCard({super.key, 
    this.backgroundColor,
    this.settingColor,
    this.cardRadius = 30,
    required this.userName,
    this.backgroundMotifColor = Colors.white,
    this.cardActionWidget,
    this.userMoreInfo,
    required this.userProfilePic,
  });

  Widget getProfileImage() {
    UserInfo? userInfo = LocalStorageServices.getUserInfo();
    if(userInfo?.picture == null) {
      return Image.asset('assets/images/app_icon_150.png', height: 100, width: 100,opacity: const AlwaysStoppedAnimation(.89),);
    }
    return CircularCachedNetworkImage(
      imageURL: userInfo!.picture.toString(),
      size: 100,
      borderColor: Colors.transparent,
      fit: BoxFit.cover,
      alignment: Alignment.topCenter,
    );
  }
  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Container(
      height: 100,
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(double.parse(cardRadius!.toString())), bottomRight: Radius.circular(double.parse(cardRadius!.toString()))),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------Data row----------------//
          Positioned(
            top: 10,
            right: 16,
            left: 16,
            child:
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                AvatarGlow(
                  //startDelay: const Duration(milliseconds: 1000),
                  glowColor: Colors.white,
                  glowShape: BoxShape.circle,
                  animate: true,
                  curve: Curves.fastOutSlowIn,
                  glowRadiusFactor: 0.16,
                  child: Material(
                    elevation: 8.0,
                    shape: const CircleBorder(),
                    color: Colors.transparent,
                    child: CircleAvatar(
                      //backgroundImage: NetworkImage(homeController.userInfo!.picture.toString()),
                      radius: 35.0,
                      child: getProfileImage(),
                    ),
                  ),
                ),
                MySpacing.width(12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    MyText.titleSmall(
                      Strings.goodMorning,
                      color: theme.colorScheme.onPrimary.withOpacity(0.85),
                    ),
                    MyText.titleMedium(
                      userName.toString(),
                      color: theme.colorScheme.onPrimary,
                    ),
                  ],
                ),
                const Spacer(),

              ],
            ),
          )
        ],
      ),
    );
  }
}
