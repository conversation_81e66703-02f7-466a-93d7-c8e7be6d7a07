import 'dart:typed_data';

class SignRequestDocument {
  final String id;
  final String name;
  final String path;
  final String signedPath;
  final DateTime createdAt;
  final DateTime? signedDate;
  final DateTime? expiryDate;
  final Uint8List pdfBytes;

  SignRequestDocument({
    required this.id,
    required this.name,
    required this.path,
    required this.signedPath,
    required this.createdAt,
    this.signedDate,
    this.expiryDate,
    required this.pdfBytes,
  });

  factory SignRequestDocument.fromJson(Map<String, dynamic> json) {
    return SignRequestDocument(
      id: json['id'],
      name: json['name'],
      path: json['path'],
      signedPath: json['signedPath'] ?? json['path'],
      createdAt: DateTime.parse(json['createdAt']),
      signedDate: json['signedDate'] != null
          ? DateTime.parse(json['signedDate'])
          : null,
      expiryDate: json['expiryDate'] != null
          ? DateTime.parse(json['expiryDate'])
          : null,
      pdfBytes: Uint8List(0), // Provide a default value or fetch from JSON
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'path': path,
      'signedPath': signedPath,
      'createdAt': createdAt.toIso8601String(),
      'signedDate': signedDate?.toIso8601String(),
      'expiryDate': expiryDate?.toIso8601String(),
      'pdfBytes': pdfBytes, // Convert to a suitable format if needed
    };
  }
}
