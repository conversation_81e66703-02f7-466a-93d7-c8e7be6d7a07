import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_home_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

// ---------------------- WIDGET CHỌN TRẠNG THÁI ------------------------ //
// Widget chọn trạng thái
Widget DieuXeStatusFilter(ThemeData theme, DieuXeHomeController controller,
    {required RxList<String> statusList}) {
  return SizedBox(
    height: 50,
    child: ListView.separated(
      scrollDirection: Axis.horizontal,
      itemBuilder: (context, index) {
        final status = statusList[index];
        return DieuXeStatusItem(theme, status, controller);
      },
      separatorBuilder: (context, index) => const SizedBox(width: 2),
      itemCount: statusList.length,
    ),
  );
}

Widget DieuXeStatusItem(
    ThemeData theme, String status, DieuXeHomeController controller) {
  return GestureDetector(
    onTap: () {
      controller.currentStatus.value = status;
      controller.loadDieuXe(); // Gọi loadDieuXe khi chọn trạng thái
    },
    child: Container(
      width: 130,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        color: controller.currentStatus.value == status
            ? theme.primaryColor
            : theme.primaryColor.withOpacity(0.5),
        border: Border.all(
          color: theme.scaffoldBackgroundColor,
        ),
      ),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      child: MyText.labelMedium(
        status, // Hiển thị tên tiếng Việt
        textAlign: TextAlign.center,
        color: theme.colorScheme.onPrimary,
      ),
    ),
  );
}
