// ignore_for_file: no_leading_underscores_for_local_identifiers, must_be_immutable

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/features/navbar/navbar_controller.dart';
import 'package:tvumobile/app/features/navbar/widgets/gv_bottom_bar.dart';
import 'package:tvumobile/app/features/navbar/widgets/sv_bottom_bar.dart';
import 'package:tvumobile/connection_fail.dart';

class BottomNavbar extends GetView<NavbarController> {
  const BottomNavbar({super.key});

  @override
  Widget build(BuildContext context) {
    Theme.of(context);
    return Scaffold(
      resizeToAvoidBottomInset: true,
      extendBody: true,
      extendBodyBehindAppBar: true,
      body: Obx(() {
        if (controller.connectionType == MConnectivityResult.waiting) {
          return loading();
        } else if (controller.connectionType == MConnectivityResult.none) {
          return const ConnectionFaildScreen();
        } else {
          return PopScope(
            canPop: false,
            onPopInvokedWithResult: (bool didPop, Object? result) async {
              if (didPop) {
                return;
              }
              if (controller.selectedIndex.value > 0) {
                controller.onTap(0);
                //didPop = false;
                //print("cant");
              } else {
                final bool shouldPop = await _showBackDialog(context) ?? false;
                if (context.mounted && shouldPop) {
                  Navigator.pop(context);
                  SystemNavigator.pop();
                }
              }
            },
            // onPopInvokedWithResult: (didPop, result) {
            //   if (controller.selectedIndex.value > 0) {
            //     controller.onTap(0);
            //     didPop = false;
            //     //print("cant");
            //   } else {
            //     didPop = true;
            //     Navigator.pop(context, true);
            //     // if (Platform.isAndroid) {
            //     //   SystemNavigator.pop();
            //     // } else if (Platform.isIOS) {
            //     //   exit(0);
            //     // }
            //     //print("can");
            //   }
            // },
            child: Obx(() => controller.isTeacher
                ? controller.navigation[controller.selectedIndex.value]
                : controller.svNavigation[controller.selectedIndex.value]),
          );
        }
      }),
      //appBar: appBar1(context),
      /*bottomNavigationBar: Obx(() => FlashyTabBar(
              height: 55,
              iconSize: 20,
              selectedIndex: controller.selectedIndex.value,
              animationDuration: const Duration(milliseconds: 500),
              showElevation: true,
              backgroundColor: theme.colorScheme.background,
              items: controller.getBottom(context),
              onItemSelected: (index) {
                controller.onTap(index);
              },
            )),*/

      floatingActionButtonLocation: FloatingActionButtonLocation.miniEndDocked,
      bottomNavigationBar: Obx(() {
        if (controller.userInfo!.isActualTeacher()) {
          return GvBottomBar(controller, context);
        }
        return SvBottomBar(controller);
      }),

      /*floatingActionButton:Obx(() => GestureDetector(
          onLongPress: () {
            Get.snackbar("title", "Long press");
          },
          onTap: () {
            Get.toNamed(Routes.QRSCANER, preventDuplicates: true);
          },
          child: Container(
            color: theme.colorScheme.background,
            padding: EdgeInsets.only(top: 6.h),
            width: 48,
            height: 48,
            child: Icon(
              controller.heart.value ? CupertinoIcons.qrcode : CupertinoIcons.qrcode,
              color: mlPrimaryTextColor, size: 42.w,
            ),
          ),
        )),*/

      floatingActionButton: FloatingActionButton(
        onPressed: () {
          if (kDebugMode) {
            print("clicked");
          }
          Get.toNamed(Routes.QRHOME, preventDuplicates: true);
        },
        backgroundColor: Colors.white.withOpacity(0.9),
        child: Obx(() => Icon(
              controller.heart.value
                  ? CupertinoIcons.qrcode
                  : CupertinoIcons.qrcode,
              color: mlPrimaryTextColor,
              size: 32,
            )),
      ),
    );
  }

  Widget loading() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
      ),
    );
  }

  Future<bool?> _showBackDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Thoát ứng dụng?'),
          content: const Text(
            'Bạn có chắc chắn muốn thoát ứng dụng này?',
          ),
          actions: <Widget>[
            TextButton(
              style: TextButton.styleFrom(
                textStyle: Theme.of(context).textTheme.labelLarge,
              ),
              child: const Text('Không'),
              onPressed: () {
                Navigator.pop(context, false);
              },
            ),
            TextButton(
              style: TextButton.styleFrom(
                textStyle: Theme.of(context).textTheme.labelLarge,
              ),
              child: const Text('Thoát'),
              onPressed: () {
                Navigator.pop(context, true);
              },
            ),
          ],
        );
      },
    );
  }
}
