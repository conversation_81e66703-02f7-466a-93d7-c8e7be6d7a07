import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepvienchuc.dart';
import 'package:tvumobile/app/services/api_provider.dart';

class LichNghiPhepController extends GetxController {
  ApiProvider apiProvider = Get.find();
  final Rx<DateTime> _focusedDay = DateTime.now().obs;
  final Rx<DateTime?> _selectedDay = Rx<DateTime?>(null);
  final donvi = ''.obs;
  final loadedds = false.obs;
  List<DonNghiPhepVienChuc> dsDNPDonVi = <DonNghiPhepVienChuc>[].obs;

  // Map để lưu trữ sự kiện: key là ngày, value là danh sách đơn nghỉ phép
  final RxMap<DateTime, List<DonNghiPhepVienChuc>> _events =
      <DateTime, List<DonNghiPhepVienChuc>>{}.obs;

  // <PERSON>õi tháng và năm hiện tại
  final RxInt _currentMonth = DateTime.now().month.obs;
  final RxInt _currentYear = DateTime.now().year.obs;

  DateTime get focusedDay => _focusedDay.value;
  DateTime? get selectedDay => _selectedDay.value;

  // ---------------------- SET GIÁ TRỊ NGÀY HIỆN TẠI VÀ NGÀY ĐƯỢC CHỌN -------------------------
  set focusedDay(DateTime day) {
    _focusedDay.value = day;
    // Gọi API khi tháng thay đổi
    // Chỉ gọi API nếu tháng hoặc năm thay đổi
    if (_currentMonth.value != day.month || _currentYear.value != day.year) {
      _currentMonth.value = day.month;
      _currentYear.value = day.year;
      getDsDNPDonVi(day.month, day.year);
    }
    update();
  }

  set selectedDay(DateTime? day) {
    _selectedDay.value = day;
    update();
  }
  // --------------------------------------------------------------------------------------------

  @override
  void onInit() {
    super.onInit();
    if (kDebugMode) {
      print("LichNghiPhepController onInit");
    }
    // Gọi API với tháng/năm hiện tại
    getDsDNPDonVi(DateTime.now().month, DateTime.now().year);
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    if (kDebugMode) {
      print("LichNghiPhepController onReady");
    }
  }

  Future<void> getDsDNPDonVi(int month, int year) async {
    dsDNPDonVi.clear();
    loadedds.value = false;
    List<DonNghiPhepVienChuc>? response = await apiProvider.getDsNghiPhepDonVi(
        dnpDuyetTai: "lnp", month: month, year: year);
    if (response != null) {
      dsDNPDonVi.assignAll(response); // Sử dụng assignAll để cập nhật RxList
      loadedds.value = true;

      // Xử lý dữ liệu thành Map<DateTime, List<DonNghiPhepVienChuc>>
      _events.clear();
      for (var doc in dsDNPDonVi) {
        if (doc.tuNgay != null && doc.denNgay != null) {
          try {
            DateTime start = doc.tuNgay!.toLocal();
            DateTime end = doc.denNgay!.toLocal();
            // Đảm bảo ít nhất ngày start được thêm vào
            DateTime normalizedStart =
                DateTime(start.year, start.month, start.day);
            _events.value.putIfAbsent(normalizedStart, () => []);
            _events.value[normalizedStart]!.add(doc);

            // Thêm các ngày tiếp theo nếu start và end khác nhau
            for (DateTime day = start.add(const Duration(days: 1));
                day.isBefore(end.add(const Duration(days: 1)));
                day = day.add(const Duration(days: 1))) {
              DateTime normalizedDay = DateTime(day.year, day.month, day.day);
              _events.value.putIfAbsent(normalizedDay, () => []);
              _events.value[normalizedDay]!.add(doc);
            }
          } catch (e) {
            print("Error parsing date for doc $doc: $e");
          }
        } else {
          print("Null tuNgay or denNgay for doc: $doc");
        }
      }
      print("Events map: $_events");
    } else {
      loadedds.value = true;
      print("API response is null");
    }
    update();
  }

  // Getter để truy cập events
  Map<DateTime, List<DonNghiPhepVienChuc>> get events => _events.value;
}
