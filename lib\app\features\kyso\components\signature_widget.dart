// signature_widget.dart
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tvumobile/app/features/kyso/enums/resize_mode.dart';
import 'package:tvumobile/app/features/kyso/models/signature_data.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class SignatureWidget extends StatelessWidget {
  const SignatureWidget({
    super.key,
    required this.signature,
    required this.imageBytesPlaceholder,
    required this.updateSignaturePosition,
    required this.updateSignatureSize,
    required this.fullName,
    required this.department,
    required this.personposition,
  });

  final SignatureData signature;
  final Uint8List? imageBytesPlaceholder;
  final Function(int, Offset) updateSignaturePosition;
  final Function(int, Offset, ResizeMode) updateSignatureSize;
  final String? fullName;
  final String? department;
  final String? personposition;

  @override
  Widget build(BuildContext context) {
    List<Widget> textWidgets = _buildTextWidgets();
    double textHeight =
        textWidgets.length * 25.0; // approximate height per line of text

    return Positioned(
      left: signature.position.dx,
      top: signature.position.dy,
      width: signature.stampStyle
          ? (fullName != null
              ? (('Người ký: $fullName'.length.toDouble() + 2) *
                  signature.width)
              : signature.width * 2.5)
          : signature.width, // Widget width is determined by signature.width
      height: signature.stampStyle
          ? textHeight
          : signature.displayFullName
              ? signature.height + textHeight
              : signature.height,
      child: GestureDetector(
        onPanUpdate: (details) => updateSignaturePosition(
            0, details.delta), // Assuming only one signature at index 0
        onPanEnd: (_) => ResizeMode.none, // Reset resize mode on pan end
        child: Stack(
          children: [
            _buildSignatureContent(textWidgets),
            // Resize Handlers - always rendered for both styles
            Positioned(
              top: -10,
              left: -10,
              child:
                  _buildResizeHandler(ResizeMode.topLeft, updateSignatureSize),
            ),
            Positioned(
              top: -10,
              right: -10,
              child:
                  _buildResizeHandler(ResizeMode.topRight, updateSignatureSize),
            ),
            Positioned(
              bottom: -10,
              left: -10,
              child: _buildResizeHandler(
                  ResizeMode.bottomLeft, updateSignatureSize),
            ),
            Positioned(
              bottom: -10,
              right: -10,
              child: _buildResizeHandler(
                  ResizeMode.bottomRight, updateSignatureSize),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSignatureContent(List<Widget> textWidgets) {
    if (signature.stampStyle) {
      return Container(
        width: signature.stampStyle
            ? (fullName != null
                ? ('Người ký: $fullName'.length.toDouble() * signature.width)
                : signature.width * 2.5)
            : signature.width, // Container width is also signature.width
        padding: const EdgeInsets.all(8), // Add padding inside the border
        decoration: BoxDecoration(
          border: Border.all(color: Colors.red, width: 2), // Red border
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment:
              CrossAxisAlignment.start, // Left align column content
          children: [
            ...textWidgets, // Text widgets will be rendered below the image
          ],
        ),
      );
    } else {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(0),
            width: signature.width,
            child: Image.memory(
              signature.signatureBytes ?? imageBytesPlaceholder!,
              fit: BoxFit.contain,
              width: signature.width,
              height: signature.height, // set image height
            ),
          ),
          ...textWidgets,
        ],
      );
    }
  }

  List<Widget> _buildTextWidgets() {
    List<Widget> textWidgets = [];

    if (signature.stampStyle) {
      textWidgets = [
        MyText.bodyMedium(
          'Người ký: ${fullName ?? ''}', // Always display, fallback to empty string
          color: Colors.red,
          maxLines: 1,
          textAlign: TextAlign.left,
          overflow: TextOverflow.clip,
        ),
        MyText.bodyMedium(
          'Ngày ký: ${signature.datetime ?? DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}', // Always display, default datetime
          color: Colors.red,
          maxLines: 1,
          textAlign: TextAlign.left,
          overflow: TextOverflow.clip,
        ),
        MyText.bodyMedium(
          'Chức danh: ${personposition ?? ''}', // Always display, fallback to empty string
          color: Colors.red,
          maxLines: 1,
          textAlign: TextAlign.left,
          overflow: TextOverflow.clip,
        ),
        MyText.bodyMedium(
          'Đơn vị: ${department ?? ''}', // Always display, fallback to empty string
          color: Colors.red,
          maxLines: 1,
          textAlign: TextAlign.left,
          overflow: TextOverflow.clip,
        ),
      ];
    } else {
      // Default signature text logic - only display if flags are set
      if (signature.displayFullName == true &&
          signature.fullName != null &&
          signature.fullName!.isNotEmpty) {
        textWidgets.add(MyText.bodyMedium(
          'Người ký: ${signature.fullName!}',
          color: Colors.black,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.clip,
        ));
      }
      if (signature.displayDatetime == true &&
          signature.datetime != null &&
          signature.datetime!.isNotEmpty) {
        textWidgets.add(MyText.bodyMedium(
          'Ngày ký: ${signature.datetime!}',
          color: Colors.black,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.clip,
        ));
      }
      if (signature.displayPosition == true &&
          signature.personposition != null &&
          signature.personposition!.isNotEmpty) {
        textWidgets.add(MyText.bodyMedium(
          'Chức danh: ${signature.personposition!}',
          color: Colors.black,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.clip,
        ));
      }
      if (signature.displayDepartment == true &&
          signature.department != null &&
          signature.department!.isNotEmpty) {
        textWidgets.add(MyText.bodyMedium(
          'Đơn vị: ${signature.department!}',
          color: Colors.black,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.clip,
        ));
      }
    }
    return textWidgets;
  }

  // Build resize handler widget
  Widget _buildResizeHandler(ResizeMode resizeMode,
      Function(int, Offset, ResizeMode) updateSignatureSize) {
    return GestureDetector(
      onPanStart: (details) =>
          ResizeMode.none, // No need to set _resizeMode here, managed in parent
      onPanUpdate: (details) => updateSignatureSize(0, details.delta,
          resizeMode), // Assuming only one signature at index 0
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color: Colors.blue,
          shape: BoxShape.circle,
          border: Border.all(
              color: Colors.white,
              width: 1), // Added white border for better visibility
        ),
      ),
    );
  }
}
