

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';
import 'package:tvumobile/app/data/models/sch/tkb/tbk_datasource.dart';
import 'package:tvumobile/app/data/models/sch/tkb/tkb_tuan_giangvien.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/extensions/string.dart';

final List<CalendarView> allowedViews = <CalendarView>[
  //CalendarView.week,
  //CalendarView.month,
  CalendarView.schedule,
];

SfCalendar getScheduleViewCalendar(CalendarController calendarController,{TkbGVDataSource? events, dynamic scheduleViewBuilder,BuildContext? context}) {
  ThemeData theme = Theme.of(context!);
  return SfCalendar(
      timeZone: "SE Asia Standard Time",
      allowedViews: allowedViews,
      controller: calendarController,
      showDatePickerButton: true,
      //showTodayButton: true,
      scheduleViewMonthHeaderBuilder: scheduleViewBuilder,
      appointmentBuilder: (context, calendarAppointmentDetails) => _getAppointmentUI(calendarController, context, calendarAppointmentDetails),
      view: CalendarView.schedule,
      dataSource: events,
      showNavigationArrow: true,
      firstDayOfWeek: DateTime.monday,
      headerStyle: CalendarHeaderStyle(textAlign: TextAlign.left,textStyle: theme.textTheme.titleMedium, backgroundColor: theme.cardColor),
      headerDateFormat: 'MMMM/yyyy',
      cellEndPadding: 8,
      scheduleViewSettings: ScheduleViewSettings(
          appointmentItemHeight: 80,
          weekHeaderSettings: WeekHeaderSettings(startDateFormat: '  Từ dd/MM', endDateFormat: 'đến dd/MM', backgroundColor: theme.colorScheme.primary.withOpacity(0.1), height: 45, weekTextStyle: theme.textTheme.bodyMedium),
          dayHeaderSettings: DayHeaderSettings(dayFormat: 'EEEE', width: 70,
            dayTextStyle: theme.textTheme.labelSmall,
            dateTextStyle: theme.textTheme.labelMedium,)
      ),
      monthViewSettings: const MonthViewSettings(
          appointmentDisplayMode: MonthAppointmentDisplayMode.appointment),
      timeSlotViewSettings: const TimeSlotViewSettings(
          timelineAppointmentHeight: 50,
          timeIntervalWidth: 100,
          minimumAppointmentDuration: Duration(minutes: 60))
  );
}

/// Returns the builder for schedule view.
Widget scheduleViewBuilder(
    BuildContext buildContext, ScheduleViewMonthHeaderDetails details) {
  var theme = Theme.of(buildContext);
  final String monthName = _getMonthDate(details.date.month);
  return Stack(
    children: <Widget>[
      Image(
          image: ExactAssetImage('assets/images/month_background/month_${details.date.month}.png'),
          fit: BoxFit.cover,
          width: details.bounds.width,
          height: details.bounds.height),
      Positioned(
        left: 55,
        right: 0,
        top: 20,
        bottom: 0,
        child: Text(
          '$monthName - ${details.date.year}',
          style: theme.textTheme.titleMedium?.copyWith(color: theme.primaryColor, fontWeight: FontWeight.bold),
        ),
      )
    ],
  );
}

/// Returns the month name based on the month value passed from date.
String _getMonthDate(int month) {
  if (month == 01) {
    return 'Tháng một';
  } else if (month == 02) {
    return 'Tháng hai';
  } else if (month == 03) {
    return 'Tháng ba';
  } else if (month == 04) {
    return 'Tháng tư';
  } else if (month == 05) {
    return 'Tháng năm';
  } else if (month == 06) {
    return 'Tháng sáu';
  } else if (month == 07) {
    return 'Tháng bảy';
  } else if (month == 08) {
    return 'Tháng tám';
  } else if (month == 09) {
    return 'Tháng chín';
  } else if (month == 10) {
    return 'Tháng mười';
  } else if (month == 11) {
    return 'Tháng mười một';
  } else {
    return 'Tháng mười hai';
  }
}


Widget _getAppointmentUI(CalendarController calendarController, BuildContext context, CalendarAppointmentDetails details) {
  var theme = Theme.of(context);
  //List colors = [getColorFromHex('#5E97F6'), getColorFromHex('#4DD0E1'), getColorFromHex('#F06292'), getColorFromHex('#F6BF26')];
  // ignore: unused_local_variable
  bool isMobileResolution = (MediaQuery.of(context).size.width) < 768;
  //bool isWeb = false;

  final dynamic meetingData = details.appointments.first;
  late final TkbTuanGiangVien meeting;
  if (meetingData is TkbTuanGiangVien) {
    meeting = meetingData;
  }
  // ignore: unused_local_variable
  final Color textColor = theme.colorScheme.brightness == Brightness.light
      ? Colors.black
      : Colors.white;

  //final String format2 =
  //(meeting.ketThuc!.difference(meeting.batDau!).inHours < 24) ? 'HH:mm' : 'dd MMM';
  // final Color iconColor = theme.colorScheme.brightness == Brightness.light
  //     ? Colors.black87
  //     : Colors.white;
  //var color2 = colors[meeting.batDau!.month % colors.length];
  const String format = 'HH:mm';
  const String formatD = 'dd/MM/yyyy';
  var color2 = colorFor(meeting.tenMon!.toString());
  return Container(
      padding: const EdgeInsets.fromLTRB(8, 0, 8, 0),
      color: theme.colorScheme.brightness == Brightness.light
          ? theme.scaffoldBackgroundColor
          : theme.cardColor,
      //color: color2,
      child: Row(
        children: <Widget>[
          Container(
            width: 8,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(3),
                  bottomLeft: Radius.circular(3)),
              color: color2,//meeting.background,
            ),
          ),
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                    topRight: Radius.circular(3),
                    bottomRight: Radius.circular(3)),
              ),
              padding: const EdgeInsets.only(left: 7),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      MyText.bodyMedium(
                        meeting.tenMon!,
                        fontWeight: 600,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      MyText.labelMedium(
                        '(${meeting.maMon!})',
                        //style: theme.textTheme.bodyMedium?.copyWith(fontSize: 12),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: <Widget>[
                        MyText.labelMedium(
                          'Thời gian: ngày ${DateFormat(formatD).format(meeting.batDau!)} - Bắt đầu từ ',
                          fontWeight: 600,
                          maxLines: 1,
                          softWrap: false,
                          overflow: TextOverflow.ellipsis,
                        ),
                        MyText.labelMedium(
                          '${DateFormat(format).format(meeting.batDau!)} - ${DateFormat(format).format(meeting.ketThuc!)}',
                          fontWeight: 900,
                          maxLines: 1,
                          softWrap: false,
                          overflow: TextOverflow.ellipsis,
                          color: theme.colorScheme.primary,
                        ),
                      ]
                  ),
                  Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        MyText.labelMedium(
                          "Lớp: ${meeting.maLop!}",
                          //style: theme.textTheme.bodyMedium?.copyWith(fontSize: 10.sp),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        MyText.labelMedium(
                          "Nhóm: ${meeting.nhomHoc!}",
                          //style: theme.textTheme.bodyMedium?.copyWith(fontSize: 10.sp),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (meeting.nhomTH.toString().trim().isNotEmpty)
                          MyText.labelMedium(
                            "Tổ TH: ${meeting.nhomTH!}",
                            //style: theme.textTheme.bodyMedium?.copyWith(fontSize: 10.sp),
                            maxLines: 1,
                            color: theme.colorScheme.primary,
                            fontWeight: 900,
                            overflow: TextOverflow.ellipsis,
                          ),
                        if (meeting.nhomTH.toString().trim().isEmpty)
                          MyText.labelMedium(
                            "Lý thuyết",
                            //style: theme.textTheme.bodyMedium?.copyWith(fontSize: 10.sp),
                            maxLines: 1,
                            color: theme.colorScheme.secondary,
                            fontWeight: 900,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ]
                  ),
                  MyText.labelMedium(
                    "Phòng: ${meeting.tenPhong!}",
                    //style: theme.textTheme.bodyMedium?.copyWith(fontSize: 12),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
          /* Container(
              margin: EdgeInsets.zero,
              width: 20,
              alignment: Alignment.center,
              child: Icon(
                meeting.icon!,
                size: 20,
                color: iconColor,
              ),
            ),*/
        ],
      ));
}

/*
* MONTH
* if (calendarController.view == CalendarView.timelineDay ||
      calendarController.view == CalendarView.timelineWeek ||
      calendarController.view == CalendarView.timelineWorkWeek ||
      calendarController.view == CalendarView.timelineMonth) {
    final double horizontalHighlight =
    calendarController.view == CalendarView.timelineMonth ? 10 : 20;
    final double cornerRadius = horizontalHighlight / 4;
    return Row(
      children: <Widget>[
        Container(
          width: horizontalHighlight,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(cornerRadius),
                bottomLeft: Radius.circular(cornerRadius)),
            color: meeting.background,
          ),
        ),
        Expanded(
            child: Container(
                alignment: Alignment.center,
                color: meeting.background!.withOpacity(0.8),
                padding: const EdgeInsets.only(left: 2),
                child: Text(
                  meeting.tenMon!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                  maxLines: 1,
                  softWrap: false,
                  overflow: TextOverflow.ellipsis,
                ))),
        Container(
          width: horizontalHighlight,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(cornerRadius),
                bottomRight: Radius.circular(cornerRadius)),
            color: meeting.background,
          ),
        ),
      ],
    );
  }
  else if (calendarController.view != CalendarView.month &&
      calendarController.view != CalendarView.schedule) {
    return Column(
      children: <Widget>[
        Container(
          padding: const EdgeInsets.all(3),
          height: 50,
          alignment: isMobileResolution
              ? Alignment.topLeft
              : Alignment.centerLeft,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(5), topRight: Radius.circular(5)),
            color: meeting.background,
          ),
          child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    meeting.tenMon!,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: isMobileResolution ? 3 : 1,
                    softWrap: false,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (isMobileResolution)
                    Container()
                  else
                    Text(
                      'Time: ${DateFormat('hh:mm a').format(meeting.batDau!)} - '
                          '${DateFormat('hh:mm a').format(meeting.ketThuc!)}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                      ),
                    )
                ],
              )),
        ),
        Container(
          height: details.bounds.height - 70,
          padding: const EdgeInsets.fromLTRB(3, 5, 3, 2),
          color: meeting.background!.withOpacity(0.8),
          alignment: Alignment.topLeft,
          child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    meeting.maLop!,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                  )
                ],
              )),
        ),
        Container(
          height: 20,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(5),
                bottomRight: Radius.circular(5)),
            color: meeting.background,
          ),
        ),
      ],
    );
  }
  else if (calendarController.view == CalendarView.month) {
    final double fontSize =
    details.bounds.height > 11 ? 10 : details.bounds.height - 1;
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 2),
      child: details.isMoreAppointmentRegion
          ? Padding(
          padding: const EdgeInsets.only(left: 2),
          child: Text(
            '+ More',
            style: TextStyle(
              color: textColor,
              fontSize: fontSize,
            ),
            maxLines: 1,
            softWrap: false,
            overflow: TextOverflow.ellipsis,
          ))
          : isMobileResolution
          ? Row(
        children: <Widget>[
          Icon(
            Icons.circle,
            color: meeting.background,
            size: fontSize,
          ),
          Expanded(
              child: Padding(
                  padding: const EdgeInsets.only(left: 2),
                  child: Text(
                    meeting.tenMon!,
                    style: TextStyle(
                      color: textColor,
                      fontSize: fontSize,
                    ),
                    maxLines: 1,
                    softWrap: false,
                    overflow: TextOverflow.ellipsis,
                  ))),
        ],
      )
          : Row(
        children: <Widget>[
          Icon(
            Icons.circle,
            color: meeting.background,
            size: fontSize,
          ),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 2),
              child: Text(
                meeting.isAllDay!
                    ? 'All'
                    : DateFormat('h a').format(meeting.batDau!),
                style: TextStyle(
                  color: textColor,
                  fontSize: fontSize,
                ),
                maxLines: 1,
                softWrap: false,
                overflow: TextOverflow.ellipsis,
              )),
          Expanded(
              child: Text(
                meeting.tenMon!,
                style: TextStyle(
                  color: textColor,
                  fontSize: fontSize,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )),
        ],
      ),
    );
  }
* */