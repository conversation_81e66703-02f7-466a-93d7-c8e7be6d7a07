import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/config/themes/custom_theme.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/duyetdnp_controller.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

Widget buildDNPDrawer(BuildContext context, DuyetDNPController controller) {
  var theme = Theme.of(context);
  return Drawer(
    backgroundColor: Colors.transparent, // Để nền của Drawer gốc trong suốt.
    child: Align(
      alignment: Alignment.topRight,
      child: MyContainer.none(
        margin:
            MySpacing.only(right: 16, top: MySpacing.safeAreaTop(context) + 16),
        borderRadiusAll: 4,
        // height: 500,
        clipBehavior: Clip.antiAliasWithSaveLayer,
        color: theme.scaffoldBackgroundColor,
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                padding: MySpacing.only(left: 20, bottom: 0, top: 8, right: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    MySpacing.height(16),
                    MyContainer(
                      padding: MySpacing.fromLTRB(12, 4, 12, 4),
                      borderRadiusAll: 4,
                      color: theme.primaryColor.withOpacity(0.9),
                      borderColor: theme.primaryColor,
                      bordered: true,
                      child: MyText.bodyLarge("DUYỆT ĐƠN NGHỈ PHÉP",
                          color: theme.colorScheme.onPrimary,
                          fontWeight: 700,
                          letterSpacing: 0.2),
                    ),
                  ],
                ),
              ),
              MySpacing.height(8),
              const Divider(thickness: 2),
              Container(
                margin: MySpacing.x(20),
                child: Column(
                  children: [
                    // ------------------ duyệt của Phòng TCNS ------------------
                    if (controller.isLanhDaoOrBGH.value.contains("ns")) ...[
                      MySpacing.height(10),
                      InkWell(
                        onTap: () {
                          controller.drawerClick("ns");
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Container(
                          padding:
                              MySpacing.all(8), // Khoảng cách bên trong Row
                          decoration: BoxDecoration(
                            color: controller.currentView.value == "ns"
                                ? CustomTheme.skyBlue
                                    .withAlpha(30) // Màu nền khi chọn
                                : Colors
                                    .transparent, // Không màu nền khi không chọn
                            borderRadius: BorderRadius.circular(8), // Bo góc
                          ),
                          child: Row(
                            children: [
                              MyContainer(
                                paddingAll: 12,
                                borderRadiusAll: 4,
                                color: CustomTheme.skyBlue.withAlpha(20),
                                child: Icon(
                                  LucideIcons.building2,
                                  size: 20,
                                  color: CustomTheme.skyBlue,
                                ),
                              ),
                              MySpacing.width(16),
                              Expanded(
                                child: MyText.titleMedium(
                                  'Duyệt của Phòng TCNS',
                                  color: CustomTheme.skyBlue,
                                  fontWeight: 700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],

                    // -------------------- duyệt của ĐƠN VỊ --------------------
                    if (controller.isLanhDaoOrBGH.value.contains("dv") ||
                        controller.isLanhDaoOrBGH.value.contains("ns")) ...[
                      MySpacing.height(10),
                      InkWell(
                        onTap: () {
                          controller.drawerClick("dv");
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Container(
                          padding:
                              MySpacing.all(8), // Khoảng cách bên trong Row
                          decoration: BoxDecoration(
                            color: controller.currentView.value == "dv"
                                ? CustomTheme.red
                                    .withAlpha(30) // Màu nền khi chọn
                                : Colors
                                    .transparent, // Không màu nền khi không chọn
                            borderRadius: BorderRadius.circular(8), // Bo góc
                          ),
                          child: Row(
                            children: [
                              MyContainer(
                                paddingAll: 12,
                                borderRadiusAll: 4,
                                color: CustomTheme.red.withAlpha(20),
                                child: Icon(
                                  LucideIcons.home,
                                  size: 20,
                                  color: CustomTheme.red,
                                ),
                              ),
                              MySpacing.width(16),
                              Expanded(
                                child: MyText.titleMedium(
                                  'Duyệt của đơn vị',
                                  color: CustomTheme.red,
                                  fontWeight: 700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],

                    // ------------------ duyệt của CÔNG ĐOÀN -------------------
                    if (controller.isLanhDaoOrBGH.value.contains("cd")) ...[
                      MySpacing.height(10),
                      InkWell(
                        onTap: () {
                          controller.drawerClick("cd");
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Container(
                          padding:
                              MySpacing.all(8), // Khoảng cách bên trong Row
                          decoration: BoxDecoration(
                            color: controller.currentView.value == "cd"
                                ? CustomTheme.peach
                                    .withAlpha(30) // Màu nền khi chọn
                                : Colors
                                    .transparent, // Không màu nền khi không chọn
                            borderRadius: BorderRadius.circular(8), // Bo góc
                          ),
                          child: Row(
                            children: [
                              MyContainer(
                                paddingAll: 12,
                                borderRadiusAll: 4,
                                color: CustomTheme.peach.withAlpha(20),
                                child: Icon(
                                  LucideIcons.hotel,
                                  size: 20,
                                  color: CustomTheme.peach,
                                ),
                              ),
                              MySpacing.width(16),
                              Expanded(
                                child: MyText.titleMedium(
                                  'Duyệt của Công đoàn',
                                  color: CustomTheme.peach,
                                  fontWeight: 700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],

                    // ---------------------- duyệt của BGH ---------------------
                    if (controller.isLanhDaoOrBGH.value.contains("bgh")) ...[
                      MySpacing.height(10),
                      InkWell(
                        onTap: () {
                          controller.drawerClick("bgh");
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Container(
                          padding:
                              MySpacing.all(8), // Khoảng cách bên trong Row
                          decoration: BoxDecoration(
                            color: controller.currentView.value == "bgh"
                                ? CustomTheme.darkGreen
                                    .withAlpha(30) // Màu nền khi chọn
                                : Colors
                                    .transparent, // Không màu nền khi không chọn
                            borderRadius: BorderRadius.circular(8), // Bo góc
                          ),
                          child: Row(
                            children: [
                              MyContainer(
                                paddingAll: 12,
                                borderRadiusAll: 4,
                                color: CustomTheme.darkGreen.withAlpha(20),
                                child: Icon(
                                  LucideIcons.school,
                                  size: 20,
                                  color: CustomTheme.darkGreen,
                                ),
                              ),
                              MySpacing.width(16),
                              Expanded(
                                child: MyText.titleMedium(
                                  'Duyệt của BGH',
                                  color: CustomTheme.darkGreen,
                                  fontWeight: 700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                    MySpacing.height(16),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
