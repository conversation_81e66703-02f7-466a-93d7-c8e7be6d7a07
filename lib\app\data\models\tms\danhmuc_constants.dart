abstract final class MaDanhMuc {
  static const int ChamCong = 10;
  static const int LoaiCongViec = 4;
  static const int MucDo = 6;
  static const int DoUuTien = 7;
}

enum MaDanhMuc2 { ChamCong, LoaiCongViec;
  String getString() {
    switch (this) {
      case MaDanhMuc2.ChamCong: return "first string";
      case MaDanhMuc2.LoaiCongViec: return "second string";
    }
  }

  int toInt() {
    switch (this) {
      case MaDanhMuc2.ChamCong: return 1;
      case MaDanhMuc2.LoaiCongViec: return 2;
    }
  }
}