/*
* File : App Theme Notifier (Listener)
* Version : 1.0.0
* */

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tvumobile/app/utils/extensions/extensions.dart';
import 'package:tvumobile/app/utils/localizations/language.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:tvumobile/app/utils/theme/theme_type.dart';

class AppNotifier extends ChangeNotifier {
  AppNotifier() {
    init();
  }

  init() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    ThemeType themeType =
        sharedPreferences.getString("theme_mode").toString().toThemeType;
    _changeTheme(themeType);
    notifyListeners();
  }

  updateTheme(ThemeType themeType) {
    _changeTheme(themeType);

    notifyListeners();

    updateInStorage(themeType);
  }

  Future<void> updateInStorage(ThemeType themeType) async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    sharedPreferences.setString("theme_mode", themeType.toText);
  }


  Future<void> changeLanguage(Language language,
      [bool notify = true, bool changeDirection = true]) async {

    await Language.changeLanguage(language);

    if (notify) notifyListeners();
  }

  void _changeTheme(ThemeType themeType) {
    LocalStorageServices.setThemeIsLight(themeType == ThemeType.dark ? false : true);
    //AppTheme.themeType = themeType;
    //AppTheme.customTheme = AppTheme.getCustomTheme(themeType);
    //AppTheme.theme = AppTheme.getTheme(themeType);
    //AppTheme.resetThemeData();
  }
}
