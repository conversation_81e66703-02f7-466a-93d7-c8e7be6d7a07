import 'dart:io';

import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepinfo.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/thongtinnghi_sauomdau.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/thongtinvo_dasinh.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/thongtinvo_sinh.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/thongtinxinhuongtrocap.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/donnghiphep_controller.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/nghiphep_home_appbar.dart';
import 'package:tvumobile/app/shared_components/form_builder_segmented_control.dart';
import 'package:tvumobile/app/shared_components/input_decoration_theme.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:tvumobile/app/utils/helpers/app_helpers.dart';

class DonNghiPhepView extends GetView<DonNghiPhepController> {
  const DonNghiPhepView({super.key});

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    final myDecoration =
        const InputDecoration().applyDefaults(GlobalInputDecoration(context));
    final width = Get.width - 32;
    final isEditing = Get.arguments != null && Get.arguments['id'] != null;

    return Scaffold(
      appBar: DonNghiPhepAppBar(
          context, isEditing ? "Chỉnh sửa đơn nghỉ phép" : "Tạo đơn nghỉ phép"),
      body: GetBuilder<DonNghiPhepController>(
        builder: (controller) => controller.formData['id'] != null &&
                controller.formData['loai'] == null
            ? Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: 16.0, right: 16.0, bottom: 16.0, top: 2),
                      child: FormBuilder(
                        key: controller.formKey,
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            // ------------------------- THÔNG TIN CÁ NHÂN -------------------------
                            const Align(
                              alignment: Alignment.center,
                              child: Padding(
                                padding: EdgeInsets.all(8.0),
                                child: MyText.titleLarge("Thông tin của bạn",
                                    decoration: TextDecoration.underline),
                              ),
                            ),

                            // ----------------------- SỐ NGÀY PHÉP ĐÃ NGHỈ ------------------------
                            Padding(
                              padding:
                                  const EdgeInsets.only(bottom: 4.0, top: 4.0),
                              child: Row(
                                children: [
                                  MyText.labelLarge("Số ngày phép đã nghỉ: ",
                                      color: theme.colorScheme.onSurface),
                                  controller.danghi.value.isNegative
                                      ? MyText.labelLarge("...",
                                          color: theme.colorScheme.onSurface)
                                      : MyText.labelLarge(
                                          controller.danghi.toString(),
                                          fontWeight: 900,
                                          color: theme.colorScheme.onSurface)
                                ],
                              ),
                            ),

                            // ----------------------- SỐ NGÀY PHÉP CÒN LẠI ------------------------
                            Padding(
                              padding: const EdgeInsets.only(bottom: 4.0),
                              child: Row(
                                children: [
                                  MyText.labelLarge("Số ngày phép còn lại: ",
                                      color: theme.colorScheme.onSurface),
                                  controller.danghi.value.isNegative
                                      ? MyText.labelLarge(" ...",
                                          color: theme.colorScheme.onSurface)
                                      : MyText.labelLarge(
                                          controller.conlai.toString(),
                                          fontWeight: 900,
                                          color: theme.colorScheme.onSurface)
                                ],
                              ),
                            ),

                            // ------------------------ THÔNG TIN NGHỈ PHÉP ------------------------
                            const Align(
                              alignment: Alignment.center,
                              child: Padding(
                                padding: EdgeInsets.all(8.0),
                                child: MyText.titleLarge(
                                  "Thông tin nghỉ phép",
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),

                            // ------------------------------ ĐƠN VỊ -------------------------------
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Padding(
                                padding: const EdgeInsets.only(
                                    bottom: 8.0, top: 8.0),
                                child: MyText.titleMedium(
                                  "Đơn vị: ${controller.donvi.value}",
                                  color: theme.colorScheme.onSurface,
                                  fontWeight: 700,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                            const SizedBox(height: 14),

                            // ----------------------------- NGƯỜI GỬI -----------------------------
                            FormBuilderDropdown<UserInfo>(
                              name: 'nguoigui',
                              decoration: myDecoration.copyWith(
                                labelText: 'Người gửi',
                                hintText: 'Người gửi',
                              ),
                              // initialValue: controller.userInfo,
                              initialValue: controller.formData['nguoigui'] ??
                                  controller.userInfo,
                              validator: FormBuilderValidators.compose(
                                  [FormBuilderValidators.required()]),
                              items: [controller.userInfo!]
                                  .map((ele) => DropdownMenuItem(
                                        alignment: AlignmentDirectional.center,
                                        value: ele,
                                        child: Text(ele.fullname.toString()),
                                      ))
                                  .toList(),
                              onChanged: (value) {
                                controller.formData['nguoigui'] =
                                    value ?? controller.userInfo;
                              },
                              onSaved: (newValue) {
                                controller.formData['nguoigui'] = newValue!;
                              },
                            ),
                            const SizedBox(height: 14),

                            // --------------------------- LOẠI NGHỈ PHÉP --------------------------
                            controller.leaveTypes.isNotEmpty
                                ? FormBuilderDropdown<LoaiNghiPhep>(
                                    name: 'loai',
                                    decoration: myDecoration.copyWith(
                                      labelText: 'Loại nghỉ phép',
                                    ),
                                    validator: FormBuilderValidators.compose(
                                        [FormBuilderValidators.required()]),
                                    initialValue: controller.formData['loai'],
                                    items: controller.leaveTypes
                                        .map((ele) => DropdownMenuItem(
                                              alignment:
                                                  AlignmentDirectional.center,
                                              value: ele,
                                              child: Text(ele.tenLoaiNghiPhep
                                                  .toString()),
                                            ))
                                        .toList(),
                                    // onChanged: (value) async {
                                    //   //print(value);
                                    //   controller.formData['loai'] = value ?? '';
                                    //   controller.leaveType = value!.id!;
                                    //   var dlg = checkLoainghiphep(
                                    //       controller.leaveType, controller);
                                    //   if (dlg != null) {
                                    //     if (Platform.isIOS) {
                                    //       await showCupertinoModalBottomSheet(
                                    //         context: context,
                                    //         builder: (context) {
                                    //           return dlg;
                                    //         },
                                    //       );
                                    //     } else {
                                    //       await showMaterialModalBottomSheet(
                                    //         context: context,
                                    //         builder: (context) {
                                    //           return dlg;
                                    //         },
                                    //       );
                                    //     }
                                    //   }
                                    //   controller.update();
                                    // },
                                    onChanged: (value) async {
                                      if (value != null && value.id != null) {
                                        controller.formData['loai'] = value;
                                        controller.leaveType = value
                                            .id!; // Loại bỏ !, vì đã kiểm tra null
                                        var dlg = checkLoainghiphep(
                                            controller.leaveType, controller);
                                        if (dlg != null) {
                                          if (Platform.isIOS) {
                                            await showCupertinoModalBottomSheet(
                                                context: context,
                                                builder: (context) => dlg);
                                          } else {
                                            await showMaterialModalBottomSheet(
                                                context: context,
                                                elevation: 8,
                                                builder: (context) => dlg);
                                          }
                                        }
                                        controller.update();
                                      } else {
                                        controller.formData['loai'] =
                                            controller.leaveTypes.firstOrNull;
                                        controller.leaveType = controller
                                                .leaveTypes.firstOrNull?.id ??
                                            2;
                                        controller.update();
                                      }
                                    },
                                    onSaved: (newValue) {
                                      controller.formData['loai'] = newValue!;
                                    },
                                  )
                                : const SkeletonLine(),
                            const SizedBox(height: 14),

                            // ------------------------- THÔNG TIN BỔ SUNG -------------------------
                            // --- DÀNH CHO CÁC LOẠI NGHỈ PHÉP ĐẶC BIỆT (VỢ SINH, DƯỠNG SỨC,...) ---
                            Visibility(
                              visible: controller.extraThongtin.value,
                              child: Align(
                                child: Container(
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                          width: 1, color: theme.dividerColor),
                                      borderRadius: BorderRadius.circular(5)),
                                  padding: const EdgeInsets.all(8),
                                  child: InkWell(
                                    onTap: () async {
                                      var dlg = checkLoainghiphep(
                                          controller.leaveType, controller);
                                      if (dlg != null) {
                                        if (Platform.isIOS) {
                                          await showCupertinoModalBottomSheet(
                                            context: context,
                                            builder: (context) {
                                              return dlg;
                                            },
                                          );
                                        } else {
                                          await showMaterialModalBottomSheet(
                                            context: context,
                                            elevation: 8,
                                            builder: (context) {
                                              return dlg;
                                            },
                                          );
                                        }
                                      }
                                      controller.update();
                                    },
                                    child: MyText.labelLarge(
                                      controller.buildthongtin != ""
                                          ? "${controller.buildthongtin}\n(Nhấp vào để sửa)"
                                          : "Chưa điền thông tin (nhấp vào để điền)",
                                      color: controller.buildthongtin == ""
                                          ? theme.colorScheme.error
                                          : theme.colorScheme.onSurface,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Visibility(
                              visible: controller.extraThongtin.value,
                              child: const SizedBox(height: 14),
                            ),

                            // -------------------------------- LÝ DO ------------------------------
                            FormBuilderTextField(
                              name: 'lydo',
                              validator: FormBuilderValidators.required(
                                  errorText: 'Vui lòng nhập Lý do chi tiết'),
                              decoration: myDecoration.copyWith(
                                labelText: 'Lý do chi tiết',
                              ),
                              initialValue:
                                  controller.formData['lydo']?.toString(),
                              maxLines: 2,
                              onChanged: (value) {
                                controller.formData['lydo'] = value ?? '';
                              },
                              onSaved: (newValue) {
                                controller.formData['lydo'] = newValue ?? '';
                              },
                            ),
                            const SizedBox(height: 14),

                            // ----------------------------- HÌNH THỨC -----------------------------
                            FormBuilderSegmentedControl<String>(
                              name: 'buoi',
                              // initialValue: 'CaNgay',
                              initialValue:
                                  controller.formData['buoi']?.toString() ??
                                      'CaNgay',
                              padding: const EdgeInsets.all(8),
                              segmentPadding: const EdgeInsets.only(top: 8),
                              widgetPadding: const EdgeInsets.all(8),
                              options: [
                                ['CaNgay', 'Cả ngày'],
                                ['Sang', 'Sáng'],
                                ['Chieu', 'Chiều']
                              ]
                                  .map((ele) => FormBuilderFieldOption(
                                        value: ele[0],
                                        child: Text(ele[1].toString()),
                                      ))
                                  .toList(growable: true),
                              onChanged: (value) {
                                controller.buoi.value = value ?? '';
                                controller.formData['buoi'] = value;
                                controller.tinhSoNgayThuc();
                                controller.update();
                              },
                              onSaved: (newValue) {
                                controller.formData['buoi'] = newValue!;
                              },
                              decoration: myDecoration.copyWith(
                                labelText: 'Hình thức',
                                // contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            const SizedBox(height: 14),

                            // --------------------- HÌNH THỨC NGHỈ - NỬA NGÀY ---------------------
                            Visibility(
                              visible: controller.buoi.value != 'CaNgay',
                              child: Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: FormBuilderDateTimePicker(
                                  name: 'ngaynghi1',
                                  initialEntryMode:
                                      DatePickerEntryMode.calendar,
                                  initialValue:
                                      controller.formData["ngaynghi1"],
                                  inputType: InputType.date,
                                  format: DateFormat('dd.MM.yyyy'),
                                  decoration: myDecoration.copyWith(
                                    labelText: 'Ngày nghỉ',
                                    suffixIcon: IconButton(
                                      icon: const Icon(Icons.close),
                                      onPressed: () {
                                        controller.formKey.currentState!
                                            .fields['ngaynghi1']
                                            ?.didChange(null);
                                      },
                                    ),
                                    contentPadding: const EdgeInsets.all(8),
                                  ),
                                  initialTime:
                                      const TimeOfDay(hour: 8, minute: 0),
                                  onSaved: (newValue) {
                                    controller.formData["ngaynghi1"] = newValue;
                                  },
                                  onChanged: (value) {
                                    if (value != null) {
                                      controller.formData["ngaynghi1"] = value;
                                      controller.tinhSoNgayThuc();
                                    }
                                  },
                                  // locale: const Locale.fromSubtags(languageCode: 'fr'),
                                ),
                              ),
                            ),

                            // -------------------- HÌNH THỨC NGHỈ - NGUYÊN NGÀY -------------------
                            Visibility(
                              visible: controller.buoi.value == 'CaNgay',
                              child: Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: FormBuilderDateRangePicker(
                                  name: 'ngaynghi',
                                  firstDate: DateTime(DateTime.now().year - 1),
                                  lastDate: DateTime(DateTime.now().year + 1),
                                  format: DateFormat('dd.MM.yyyy'),
                                  initialValue: controller.formData["ngaynghi"],
                                  onChanged: (value) {
                                    if (value != null) {
                                      controller.formData["ngaynghi"] = value;
                                      controller.tinhSoNgayThuc();
                                    }
                                  },
                                  onSaved: (newValue) {
                                    controller.formData["ngaynghi"] = newValue;
                                  },
                                  decoration: myDecoration.copyWith(
                                    labelText: 'Thời gian nghỉ',
                                    suffixIcon: IconButton(
                                      icon: const Icon(Icons.close),
                                      onPressed: () {
                                        controller.formKey.currentState!
                                            .fields['ngaynghi']
                                            ?.didChange(null);
                                      },
                                    ),
                                  ),
                                  locale: const Locale('vi'),
                                  cancelText: "Hủy bỏ",
                                  confirmText: "Xác nhận",
                                  style: theme.textTheme.titleMedium,
                                ),
                              ),
                            ),
                            const SizedBox(height: 14),

                            // --------------------- SỐ NGÀY NGHỈ - THỰC NGHỈ ----------------------
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                SizedBox(
                                  width: (width / 2) - 4,
                                  child: FormBuilderTextField(
                                    name: 'songaynghi',
                                    decoration: myDecoration.copyWith(
                                      labelText: 'Số ngày nghỉ',
                                    ),
                                    initialValue: controller
                                        .formData['songaynghi']
                                        ?.toString(),
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            decimal: true),
                                    maxLines: 1,
                                    onChanged: (value) {
                                      controller.formData['songaynghi'] = value;
                                    },
                                    onSaved: (newValue) {
                                      controller.formData['songaynghi'] =
                                          newValue ?? '';
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                SizedBox(
                                  width: (width / 2) - 4,
                                  child: FormBuilderTextField(
                                    name: 'songaythuc',
                                    decoration: myDecoration.copyWith(
                                      labelText: 'Số ngày thực',
                                    ),
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            decimal: true),
                                    initialValue: controller
                                        .formData['songaythuc']
                                        ?.toString(),
                                    maxLines: 1,
                                    onChanged: (value) {
                                      controller.formData['songaythuc'] = value;
                                    },
                                    onSaved: (newValue) {
                                      controller.formData['songaythuc'] =
                                          newValue ?? '';
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 14),

                            // ------- LÃNH ĐẠO ĐƠN VỊ DUYỆT (KHI CHỌN CHUYỂN ĐƠN CHỜ DUYỆT) -------
                            FormBuilderDropdown<LanhDaoDonviList>(
                              name: 'lanhdao',
                              decoration: myDecoration.copyWith(
                                labelText: 'Lãnh đạo đơn vị duyệt',
                              ),
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(
                                  errorText:
                                      'Vui lòng chọn lãnh đạo đơn vị duyệt',
                                )
                              ]),
                              items: controller.supervisors
                                  .map((ele) => DropdownMenuItem(
                                        alignment: AlignmentDirectional.center,
                                        value: ele,
                                        child: Text(
                                            "${ele.getHoTen()} ${ele.tenChucVu!.isEmpty ? "" : "(${ele.tenChucVu})"}"),
                                      ))
                                  .toList(),
                              onChanged: (value) {
                                controller.formData['lanhdao'] = value!;
                              },
                              onSaved: (newValue) {
                                controller.formData['lanhdao'] = newValue!;
                              },
                            ),
                            const SizedBox(height: 14),

                            // ------------------------------- GHI CHÚ -----------------------------
                            FormBuilderTextField(
                              name: 'ghichu',
                              decoration: myDecoration.copyWith(
                                labelText: 'Ghi chú',
                              ),
                              initialValue: controller.formData['ghichu'],
                              maxLines: 2,
                              minLines: 1,
                              onChanged: (value) {
                                controller.formData['ghichu'] = value;
                              },
                              onSaved: (newValue) {
                                controller.formData['ghichu'] = newValue ?? '';
                              },
                            ),

                            // ------------------------ CHUYỂN ĐƠN CHỜ DUYỆT -----------------------
                            if (!isEditing) ...[
                              const SizedBox(height: 14),
                              FormBuilderSwitch(
                                title: const MyText.bodyLarge(
                                    'Chuyển đơn chờ duyệt'),
                                name: 'chuyen',
                                initialValue: false,
                                onChanged: (value) {
                                  controller.chuyendon.value = value!;
                                  controller.formData["chuyen"] = value;
                                  controller.update();
                                },
                                onSaved: (newValue) {
                                  controller.formData["chuyen"] = newValue;
                                },
                                decoration: myDecoration,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),

                  // --------------------- NÚT TẠO VÀ GỬI ĐƠN NGHỈ PHÉP ---------------------
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: MyButton.block(
                            onPressed: () async {
                              bool isValid =
                                  controller.formKey.currentState!.validate();
                              if (isValid) {
                                controller.formKey.currentState!.save();
                                await controller.submitForm();
                              }

                              //final map = controller.formData;
                              //debugPrint(map.toString());
                              //debugPrint(isValid.toString());
                            },
                            //backgroundColor: Get.isDarkMode ? theme.colorScheme.tertiary.withAlpha(120) : theme.colorScheme.primaryContainer,
                            child: isEditing
                                ? MyText.labelLarge(
                                    'Cập nhật đơn',
                                    fontWeight: 900,
                                    color: theme.colorScheme.onPrimary,
                                  )
                                : controller.chuyendon.value
                                    ? MyText.labelLarge(
                                        'Gửi đơn & Chờ duyệt',
                                        fontWeight: 900,
                                        color: theme.colorScheme.onPrimary,
                                      )
                                    : MyText.labelLarge(
                                        'Tạo đơn',
                                        fontWeight: 900,
                                        color: theme.colorScheme.onPrimary,
                                      ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  // ------------------------- CHƯA ĐƯỢC SỬ DỤNG -------------------------
  Widget loading() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: const BoxDecoration(color: Colors.white),
      child: SkeletonItem(
          child: Column(
        children: [
          SkeletonLine(
            style: SkeletonLineStyle(
                height: 16, borderRadius: BorderRadius.circular(8)),
          ),
          const SizedBox(height: 8),
          SkeletonLine(
            style: SkeletonLineStyle(
                height: 32, borderRadius: BorderRadius.circular(8)),
          ),
          const SizedBox(height: 16),
          SkeletonLine(
            style: SkeletonLineStyle(
                height: 16, borderRadius: BorderRadius.circular(8)),
          ),
          const SizedBox(height: 8),
          SkeletonLine(
            style: SkeletonLineStyle(
                height: 32, borderRadius: BorderRadius.circular(8)),
          ),
          const SizedBox(height: 16),
          SkeletonLine(
            style: SkeletonLineStyle(
                height: 16, borderRadius: BorderRadius.circular(8)),
          ),
          const SizedBox(height: 8),
          SkeletonLine(
            style: SkeletonLineStyle(
                height: 32, borderRadius: BorderRadius.circular(8)),
          ),
          const SizedBox(height: 16),
          SkeletonLine(
            style: SkeletonLineStyle(
                height: 16, borderRadius: BorderRadius.circular(8)),
          ),
          const SizedBox(height: 8),
          SkeletonLine(
            style: SkeletonLineStyle(
                height: 32, borderRadius: BorderRadius.circular(8)),
          ),
          const SizedBox(height: 16),
        ],
      )),
    );
  }

  // ---------------------- KIỂM TRA LOẠI NGHỈ PHÉP ----------------------
  checkLoainghiphep(int loaiid, DonNghiPhepController controller) {
    //controller.resetThongtin();
    switch (loaiid) {
      case 7:
        controller.extraThongtin.value = true;
        return ThongTinVoSinhWidget(dnpManager: controller);
      case 8:
        controller.extraThongtin.value = true;
        return ThongTinVoDaSinhWidget(dnpManager: controller);
      case 9:
        controller.extraThongtin.value = true;
        return ThongTinSauOmDauWidget(dnpManager: controller);
      case 10:
        controller.extraThongtin.value = true;
        return ThongTinXinHuongTroCapWidget(dnpManager: controller);
      default:
        controller.extraThongtin.value = false;
        controller.update();
        return null;
    }
  }
}
