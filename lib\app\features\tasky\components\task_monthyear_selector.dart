import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lazy_load_scrollview/lazy_load_scrollview.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/duyetdnp_controller.dart';
import 'package:tvumobile/app/features/tasky/components/build_tasklist.dart';
import 'package:tvumobile/app/features/tasky/components/empty_widget.dart';
import 'package:tvumobile/app/features/tasky/components/tasky_drawer.dart';
import 'package:tvumobile/app/features/tasky/components/taskyappbar.dart';
import 'package:tvumobile/app/features/tasky/controllers/taskytaskview_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

// ---------------------- WIDGET CHỌN TRẠNG THÁI ------------------------ //
// Widget chọn trạng thái
Widget buildStatusSelector(
    ThemeData theme, TaskyTaskViewController controller) {
  return SizedBox(
    height: 30,
    child: ListView.separated(
      scrollDirection: Axis.horizontal,
      itemBuilder: (context, index) {
        final status = controller.statusList[index];
        return _buildStatusItem(theme, status, controller);
      },
      separatorBuilder: (context, index) => const SizedBox(width: 2),
      itemCount: controller.statusList.length,
    ),
  );
}

Widget _buildStatusItem(
    ThemeData theme, String status, TaskyTaskViewController controller) {
  return GestureDetector(
    onTap: () {
      controller.currentStatus.value = status;
    },
    child: Container(
      width: 110,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        color: controller.currentStatus.value == status
            ? theme.primaryColorDark
            : theme.primaryColorDark.withOpacity(0.5),
        border: Border.all(
          color: theme.scaffoldBackgroundColor,
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: MyText.labelMedium(
        controller.getStatusDisplayName(status), // Hiển thị tên tiếng Việt
        textAlign: TextAlign.center,
        color: theme.colorScheme.onPrimary,
      ),
    ),
  );
}

// -------------------------- WIDGET CHỌN NĂM --------------------------- //
// ---------------- Hiển thị một hàng (Row) để chọn năm ----------------- //
Widget buildYearSelector(ThemeData theme, TaskyTaskViewController controller) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.center,
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      _buildYearNavigationButton(
        theme,
        onTap: controller.currentYearMinus,
        icon: Icons.arrow_back_ios_new,
        label: "Trước".tr,
        isEnabled: controller.currentYear.value > 2006,
        islast: false,
      ),
      MyText.titleLarge(
        "Năm ${controller.currentYear.value}",
        style: theme.textTheme.titleMedium!.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.onSurface,
        ),
      ),
      _buildYearNavigationButton(
        theme,
        onTap: controller.currentYearPlus,
        icon: Icons.arrow_forward_ios,
        label: "Sau".tr,
        isEnabled: controller.currentYear.value < controller.maxYear.value,
        islast: true,
      ),
    ],
  );
}

// --------- Tạo một nút điều hướng (trước/sau) để thay đổi năm --------- //
Widget _buildYearNavigationButton(
  ThemeData theme, {
  required VoidCallback onTap,
  required IconData icon,
  required String label,
  required bool isEnabled,
  required bool islast,
}) {
  return GestureDetector(
    onTap: isEnabled ? onTap : null,
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (!islast)
          Icon(
            icon,
            size: 17,
            color:
                isEnabled ? theme.colorScheme.onSurface : theme.disabledColor,
          ),
        if (!islast) const SizedBox(width: 4),
        MyText.labelLarge(
          label,
          color: isEnabled ? theme.colorScheme.onSurface : theme.disabledColor,
          fontWeight: 700,
        ),
        if (islast) const SizedBox(width: 4),
        if (islast)
          Icon(
            icon,
            size: 17,
            color:
                isEnabled ? theme.colorScheme.onSurface : theme.disabledColor,
          ),
      ],
    ),
  );
}

// -------------------------- WIGET CHỌN THÁNG -------------------------- //
// ----- Hiển thị ds 12 tháng dạng ListView ngang để người dùng chọn ---- //
Widget buildMonthSelector(ThemeData theme, TaskyTaskViewController controller) {
  return SizedBox(
    height: 70,
    child: ListView.separated(
      controller: controller.monthScroll,
      scrollDirection: Axis.horizontal,
      itemBuilder: (context, index) {
        final month = index + 1;
        return _buildMonthItem(theme, month, controller);
      },
      separatorBuilder: (context, index) => const SizedBox(width: 2),
      itemCount: 12,
    ),
  );
}

// ----------------- Tạo một item đại diện cho một tháng ---------------- //
Widget _buildMonthItem(
    ThemeData theme, int month, TaskyTaskViewController controller) {
  return GestureDetector(
    onTap: () => controller.onMonthTap(month),
    child: Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(7),
        color: _getMonthColor(theme, month, controller),
        border: Border.all(
          color: theme.scaffoldBackgroundColor,
        ),
      ),
      padding: const EdgeInsets.all(8),
      child: SizedBox(
        child: Column(
          children: [
            MyText.labelSmall(
              "tháng",
              textAlign: TextAlign.center,
              color: theme.colorScheme.onPrimary,
            ),
            MyText.titleLarge(
              "$month",
              textAlign: TextAlign.center,
              color: theme.colorScheme.onPrimary,
            ),
          ],
        ),
      ),
    ),
  );
}

// --------- Xác định màu nền cho item tháng dựa trên trạng thái -------- //
Color _getMonthColor(
    ThemeData theme, int month, TaskyTaskViewController controller) {
  if (controller.currentYear.value == controller.maxYear.value) {
    if (month > controller.maxMonth) {
      return theme.primaryColor.withOpacity(0.25);
    }
    if (month == controller.currentMonth.value) {
      return theme.primaryColor;
    }
    return theme.primaryColor.withOpacity(0.7);
  } else {
    if (month == controller.currentMonth.value) {
      return theme.primaryColor;
    }
    return theme.primaryColor.withOpacity(0.7);
  }
}
