import 'package:flutter/material.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

Widget emptyCard(BuildContext context) {
  var theme = Theme.of(context);
  return Column(
    mainAxisAlignment: MainAxisAlignment.start,
    children: [
      ClipPath(
        clipper: WaveClipper(),
        child: <PERSON><PERSON>(
          alignment: Alignment.center,
          children: [
            Container(
              height: 300,
              decoration: BoxDecoration(color: theme.secondaryHeaderColor),
            ),
            Image.asset('assets/images/noEvent1.png', height: 250),
          ],
        ),
      ),
      Container(
        color: theme.cardColor,
        child: Column(
          children: [
            MySpacing.height(32),
            const MyText.titleLarge('Danh sách của bạn trống'),
          ],
        ),
      ),
    ],
  );
}

class WaveClipper extends CustomClipper<Path> {
  @override
  getClip(Size size) {
    Path path = Path();
    path.lineTo(0, size.height);
    path.quadraticBezierTo(size.width / 4, size.height - 50, size.width / 2, size.height - 30);
    path.quadraticBezierTo(3 / 4 * size.width, size.height, size.width, size.height - 30);
    path.lineTo(size.width, 0);
    return path;
  }

  @override
  bool shouldReclip(CustomClipper oldClipper) {
    return false;
  }
}

class EmptyWidget extends StatelessWidget {
  final String message;
  final String imageAsset;

  const EmptyWidget({super.key,required this.message,required this.imageAsset});
  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return SizedBox(
      width: size.width,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/$imageAsset',
              width: size.width / 2,
              height: size.width / 2,
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 50),
              child: Text(
                message,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            )
          ],
        ),
      ),
    );
  }
}