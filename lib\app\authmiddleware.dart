import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class AuthMiddleWare extends GetMiddleware {
  @override
  int? get priority => 2;

  @override
  RouteSettings? redirect(String? route) {
    if (kDebugMode) {
      Logger().i(route);
    }
    var user = LocalStorageServices.getUserInfo();
    if (user != null && route != null && !user.isActualTeacher()) {
      if (route.contains("nghiphep") ||
          route.contains("luongthang") ||
          route.contains("cv_detail")) {
        Get.snackbar(
          "Lỗi",
          "Bạn không có quyền truy cập nội dung này!",
          colorText: Colors.white,
          backgroundColor: Colors.lightBlue,
          icon: const Icon(Icons.add_alert),
        );
        route = AppPages.NAV;
      }
    }
    return super.redirect(route);
  }
}
