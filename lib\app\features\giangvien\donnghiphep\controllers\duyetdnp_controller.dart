import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_toggle_tab/flutter_toggle_tab.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepvienchuc.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/dnp_tab_toggle.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:tvumobile/app/utils/helpers/pagination_filter.dart';

class DuyetDNPController extends GetxController {
  ApiProvider apiProvider = Get.find();
  final donvi = ''.obs;
  final loadedds = false.obs;
  List<DonNghiPhepVienChuc> dsDNPDonVi = <DonNghiPhepVienChuc>[].obs;
  final appbarTitle = "".obs;
  var isLanhDaoOrBGH = "".obs;
  UserInfo? userinfo = LocalStorageServices.getUserInfo();

  // Khai báo biến sử dụng cho lazyloadview
  final _paginationFilter = PaginationFilter().obs;
  final _lastPage = false.obs;
  int get limit => _paginationFilter.value.limit;
  int get page => _paginationFilter.value.page;
  bool get lastPage => _lastPage.value;
  List<DonNghiPhepVienChuc> allDNPs = <DonNghiPhepVienChuc>[].obs;

  // Khai báo drawer
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final RxString currentView = "".obs;
  final RxString duyetai = "".obs; // Lưu duyetai trong controller
  final currentDataAvaliable = true.obs;

  // Khai báo widget month year
  final currentYear = DateTime.now().year.obs;
  final maxYear = DateTime.now().year.obs;
  final maxMonth = DateTime.now().month;
  final currentMonth = "Tất cả".obs;
  ScrollController monthScroll = ScrollController();
  final viFormat = NumberFormat.currency(locale: "vi");

  // Khai báo Toggle
  var tabTextIndexSelected = 0.obs;
  List<DataTab> get listTextTabToggle => [
        DataTab(title: "Chờ duyệt"),
        DataTab(title: "Đã duyệt"),
      ];

  List<Widget Function(DuyetDNPController)> navigation = [];

  // ----------------------------------------------------------

  @override
  Future<void> onInit() async {
    super.onInit();
    if (kDebugMode) {
      print("DuyetDNPController onInit");
    }
    navigation = [
      tabChoDuyet,
      tabDaDuyet,
    ];

    await checkLanhDaoOrBGH();
    _setInitialView();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    if (kDebugMode) {
      print("DuyetDNPController onReady");
    }
    currentView.listen(changeView);
  }

  // ------------------------- TOGGLE -------------------------
  // Cập nhật chỉ số tab được chọn
  void updateTabIndex(int index) {
    tabTextIndexSelected.value = index;
    if (index == 0) {
      currentMonth.value = "Tất cả"; // Luôn đặt "Tất cả" cho tab "Chờ duyệt"
      scrolltoFocus();
      loadDNP();
    }
    update();
  }

  // ------------------------- DRAWER -------------------------
  // Mở drawer
  void openDrawer() {
    scaffoldKey.currentState?.openDrawer();
  }

  // Đóng drawer
  void closeDrawer() {
    if (scaffoldKey.currentState!.isEndDrawerOpen) {
      scaffoldKey.currentState!.closeEndDrawer();
    } else {
      scaffoldKey.currentState!.closeDrawer();
    }
  }

  // Xử lý sự kiện khi người dùng chọn 1 mục trong drawer
  void drawerClick(String view) {
    currentView.value = view;
    closeDrawer();
    update();
  }
  // ----------------------------------------------------------

  // ---------------------- MONTH YEAR ------------------------
  // Cuộn danh sách tháng đến vị trí của tháng hiện tại
  void scrolltoFocus() {
    double ti = 1.0;
    if (currentMonth.value != "Tất cả") {
      int monthIndex = int.parse(currentMonth.value) - 1;
      ti = monthIndex <= 4 ? 1.0 : ((16 + 16 + 6) * monthIndex) * 1.0;
    }
    monthScroll.animateTo(ti,
        duration: Duration(milliseconds: 200), curve: Curves.linear);
  }

  // Tăng năm hiện tại lên 1
  Future<void> currentYearPlus() async {
    if (currentYear.value >= maxYear.value) return;
    currentYear.value += 1;

    await changeView(null); // Chờ dữ liệu tải xong
  }

  // Giảm năm hiện tại xuống 1
  Future<void> currentYearMinus() async {
    if (currentYear.value <= 2006) return;
    currentYear.value -= 1;
    await changeView(null); // Chờ dữ liệu tải xong
  }

  // Xử lý khi người dùng chọn 1 tháng
  Future<void> onMonthTap(String month) async {
    currentMonth.value = month;
    tabTextIndexSelected.value = 1;
    await changeView(null); // Chờ dữ liệu tải xong
  }
  // ----------------------------------------------------------

  // Gán currentView dựa trên vai trò từ isLanhDaoOrBGH
  void _setInitialView() {
    switch (isLanhDaoOrBGH.value) {
      case "bgh":
        currentView.value = "bgh";
        break;
      case "cd":
        currentView.value = "cd";
        break;
      case "ns":
        currentView.value = "ns";
        break;
      case "dv":
        currentView.value = "dv";
        break;
    }
    // Gọi changeView để tải dữ liệu ban đầu
    // changeView(null);
  }

  // Thay đổi chế độ xem và cập nhật dữ liệu tương ứng
  Future<void> changeView(data) async {
    currentDataAvaliable.value = true;
    dsDNPDonVi.clear();

    switch (currentView.value) {
      case "dv":
        loadedds.value = true; // Bắt đầu loading
        duyetai.value = "dv";
        appbarTitle.value = "Duyệt của đơn vị";
        loadDNP();
        loadedds.value = false; // Kết thúc loading
        break;
      case "cd":
        loadedds.value = true; // Bắt đầu loading
        duyetai.value = "cd";
        appbarTitle.value = "Duyệt của Công đoàn";
        await loadDNP();
        loadedds.value = false; // Kết thúc loading
        break;
      case "ns":
        loadedds.value = true; // Bắt đầu loading
        duyetai.value = "ns";
        appbarTitle.value = "Duyệt của Phòng TCNS";
        await loadDNP();
        loadedds.value = false; // Kết thúc loading
        break;
      case "bgh":
        loadedds.value = true; // Bắt đầu loading
        duyetai.value = "bgh";
        appbarTitle.value = "Duyệt của Ban giám hiệu";
        await loadDNP();
        loadedds.value = false; // Kết thúc loading
        break;
    }
    update();
  }

  // Kiểm tra viên chức
  Future<void> checkLanhDaoOrBGH() async {
    try {
      final result = await apiProvider.checkLanhDaoOrBGH();
      isLanhDaoOrBGH.value = result;
    } catch (e) {
      if (kDebugMode) {
        print("Error checking LanhDao or BGH: $e");
      }
    }
    update();
  }

  // ---------- Làm mới danh sách đơn nghỉ phép (reset về trang đầu). ----------
  Future<void> loadDNP() async {
    _paginationFilter.value.page = 1;
    _lastPage.value = false;
    dsDNPDonVi.clear();

    var tmp = await apiProvider.getDsNghiPhepDonVi(
        page: _paginationFilter.value.page,
        pageSize: _paginationFilter.value.limit,
        dnpDuyetTai: duyetai.value,
        month:
            currentMonth.value == "Tất cả" ? 0 : int.parse(currentMonth.value),
        year: currentYear.value);

    if (tmp!.isEmpty) {
      _lastPage.value = true;
      // showLastPage();
      update();
    } else {
      dsDNPDonVi.addAll(tmp);
      update();
    }
  }

  // Tải thêm đơn nghỉ phép khi người dùng cuộn đến cuối danh sách (load more).
  Future<void> loadMoreDNP() async {
    if (_lastPage.value) {
      return;
    }
    _paginationFilter.value.page += 1;

    var tmp = await apiProvider.getDsNghiPhepDonVi(
        page: _paginationFilter.value.page,
        pageSize: _paginationFilter.value.limit,
        dnpDuyetTai: duyetai.value,
        month:
            currentMonth.value == "Tất cả" ? 0 : int.parse(currentMonth.value),
        year: currentYear.value);

    if (tmp!.isEmpty) {
      _lastPage.value = true;
    } else {
      dsDNPDonVi.addAll(tmp);
      // await _filterTasksByStatus();
      update();
    }
  }

  // Thêm phương thức làm mới khi quay lại
  void refreshOnResume() async {
    if (kDebugMode) {
      print("DuyetDNPController refreshOnResume");
    }
    await loadDNP(); // Làm mới danh sách
    update();
  }
}
