library;


import '../../../../shared_components/header_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// binding
part '../../bindings/dashboard_binding.dart';

// controller
part '../../controllers/dashboard_controller.dart';

// model

// component
part '../components/login_button.dart';

class DashboardScreen extends GetView<DashboardController> {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Safe<PERSON><PERSON>(
        child: Center(
          child: Column(
            children: [
              const HeaderText("dashboard"),
              const SizedBox(height: 10),
              _LoginButton(onPressed: () {
                controller.changeTheme();
              }),
            ],
          ),
        ),
      ),
    );
  }
}
