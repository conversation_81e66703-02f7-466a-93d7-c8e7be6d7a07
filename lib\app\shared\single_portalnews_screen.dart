import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/extensions/extensions.dart';
import 'package:tvumobile/app/utils/localizations/strings_enum.dart';
import '../data/models/portal/portal_news_model.dart';

class SinglePortalNewsScreen extends StatelessWidget {

  SinglePortalNewsScreen(this.product, this.heroKey, this.portalNews, {super.key});
  final String heroKey;
  final PortalNewsModel product;
  final List<PortalNewsModel?> portalNews;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Get.theme;
    //final HomeController homeController = Get.find();
    //List<PortalNewsModel?> portalNews = homeController.homeState.state1!;

    //var employeeItemTheme = theme.extension<NewsListItemThemeData>();
    //portalNews = await MyHive.fetchAllLocalPortalNews();
    //portalNews = _cacheService.portalNews!;
    // ignore: unused_local_variable
    List<String> paths = <String>["Home",'Tin tức', 'Xem chi tiết tin'];
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.primaryColor.withOpacity(0.95),
        centerTitle: true,
        toolbarHeight: 42,
        leading: IconButton(onPressed: () { Get.back(); }, icon: Icon(LucideIcons.arrowLeft, color: theme.colorScheme.onPrimary,),),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            MyText.titleMedium(Strings.portal_chitiet.lang().toUpperCase(), color: theme.colorScheme.onPrimary,),
          ],
        ),
        actions: [
          Icon(LucideIcons.fileMinus, size: 16, color: theme.colorScheme.onPrimary,),
          MySpacing.width(20),
        ],
      ),
        body: ListView(
          padding: const EdgeInsets.only(left: 0.0),
          children: <Widget>[
            Column(
              children: [
                Hero(
                  tag: heroKey,
                  child: FadeInImage.assetNetwork(
                      placeholder: 'assets/images/app_icon.png',
                      image: product.image.toString(),
                      imageErrorBuilder:(context, error, stackTrace) {
                        return Image.asset('assets/images/app_icon.png');
                      }
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: Column(
                    children: [
                      MySpacing.height(8),
                      MyText.titleMedium(product.title.toString(),),
                      MySpacing.height(12),
                      HtmlWidget(product.summary.toString(), textStyle: theme.textTheme.bodyMedium,),
                      MySpacing.height(8),
                      Divider(height: 8, color: theme.dividerTheme.color,),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          MyText.titleMedium("Tin khác".tr, decoration: TextDecoration.underline,),
                          //MyText.titleMedium("See All".tr, xMuted: true,),
                        ],
                      ),
                      Divider(height: 8, color: theme.dividerTheme.color,),
                      Column(
                        //children: buildProducts(),
                        children: [
                          ListView.builder(
                              padding: EdgeInsets.zero,
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: portalNews.length,
                              itemBuilder: (ctx, index) {
                                String heroKey = "${portalNews[index]!.urlSlug}home$index";
                                return InkWell(
                                    onTap: () {
                                      print("SinglePortalNewsScreen");
                                      Get.to(() => SinglePortalNewsScreen(portalNews[index]!, heroKey, portalNews),  transition: Transition.fadeIn);
                                    },
                                    child: Container(
                                      margin: EdgeInsets.zero,
                                      padding: const EdgeInsets.symmetric(horizontal: 20,vertical: 13),
                                      // border only from top and bottom
                                      decoration: BoxDecoration(
                                          border: Border(
                                            // bottom: BorderSide(
                                            //   color: theme.dividerColor
                                            // ),
                                            top: BorderSide(
                                              color: Get.isDarkMode ? const Color(0xFF414141) : const Color(0xFFF6F6F6),
                                            ),
                                          )
                                      ),
                                      child: Row(
                                        children: [
                                          Container(
                                            height: 65,
                                            width: 65,
                                            decoration: BoxDecoration(
                                              color: bgColor[_random.nextInt(bgColor.length)],
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                            child: FadeInImage.assetNetwork(placeholder: 'assets/images/app_icon.png', image:
                                            (portalNews[index]!.imageSet.length > 3) ?
                                            portalNews[index]!.imageSet[3].url.toString() : portalNews[index]!.image.toString(),
                                                imageErrorBuilder:(context, error, stackTrace) {
                                                  return Image.asset('assets/images/app_icon.png',
                                                      fit: BoxFit.contain
                                                  );
                                                }
                                            ),
                                          ),
                                          MySpacing.width(17),
                                          Expanded(
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  MyText.titleMedium(portalNews[index]!.title.toString(), maxLines: 2, overflow: TextOverflow.ellipsis,),
                                                  MySpacing.height(4),
                                                  Row(
                                                    children: [
                                                      const Icon(LucideIcons.calendar),
                                                      MySpacing.width(4),
                                                      MyText.labelSmall(portalNews[index]!.publishDate.toString(), xMuted: true,),
                                                    ],
                                                  ),
                                                  MySpacing.height(6),
                                                ],
                                              )
                                          )
                                        ],
                                      ),
                                    )
                                );
                              })
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
          ]
        ),
    );
  }

  final List<Color> bgColor = [const Color(0xFFFFE2C2),const Color(0xFFD9839F),const Color(0xFFFFE2C2)];
  final _random = Random();
}


