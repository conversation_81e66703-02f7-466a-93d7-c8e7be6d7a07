class PaginationFilter {
  late int page;
  late int limit;
  late int docType;
  late String searchQuery;


  PaginationFilter({this.page = 1, this.limit = 15, this.docType = 0, this.searchQuery = ''});

  @override
  String toString() => 'PaginationFilter(page: $page, limit: $limit, docType: $docType, searchQuery: $searchQuery)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is PaginationFilter && other.page == page && other.limit == limit && other.docType == docType && other.searchQuery == searchQuery;
  }

  @override
  int get hashCode => page.hashCode ^ limit.hashCode;
}