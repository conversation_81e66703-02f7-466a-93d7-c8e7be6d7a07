import 'dart:convert';

String dateToWeekday(DateTime date) {
  dynamic dayData =
      '{ "1" : "Thứ hai", "2" : "Thứ ba", "3" : "<PERSON><PERSON><PERSON> tư", "4" : "<PERSON><PERSON><PERSON> năm", "5" : "<PERSON><PERSON><PERSON> sáu", "6" : "<PERSON><PERSON><PERSON> bảy", "7" : "Chủ nhật" }';

  // ignore: unused_local_variable
  dynamic monthData =
      '{ "1" : "Jan", "2" : "Feb", "3" : "Mar", "4" : "Apr", "5" : "May", "6" : "June", "7" : "Jul", "8" : "Aug", "9" : "Sep", "10" : "Oct", "11" : "Nov", "12" : "Dec" }';
  return json.decode(dayData)['${date.weekday}'];
  // return json.decode(dayData)['${date.weekday}'] +
  //     ", " +
  //     date.day.toString() +
  //     " " +
  //     json.decode(monthData)['${date.month}'] +
  //     " " +
  //     date.year.toString();
}

DateTime getTietBD(DateTime ngay, num tietbd) {
  switch(tietbd.toInt()) {
    case 1:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 7, 0, 0, 0, 0);
      break;
    case 2:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 7, 50, 0, 0, 0);
      break;
    case 3:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 9, 00, 0, 0, 0);
      break;
    case 4:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 9, 50, 0, 0, 0);
      break;
    case 5:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 10, 40, 0, 0, 0);
      break;
    case 6:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 13, 0, 0, 0, 0);
      break;
    case 7:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 13, 50, 0, 0, 0);
      break;
    case 8:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 15, 00, 0, 0, 0);
      break;
    case 9:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 15, 50, 0, 0, 0);
      break;
    case 10:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 16, 40, 0, 0, 0);
      break;
    case 11:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 18, 00, 0, 0, 0);
      break;
    case 12:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 18, 50, 0, 0, 0);
      break;
    case 13:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 19, 40, 0, 0, 0);
      break;
    case 14:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 20, 30, 0, 0, 0);
      break;
    case 15:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 21, 20, 0, 0, 0);
      break;
    case 16:
      ngay = DateTime(ngay.year, ngay.month, ngay.day, 22, 10, 0, 0, 0);
      break;
  }
  return ngay;
}