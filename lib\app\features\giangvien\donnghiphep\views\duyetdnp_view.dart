import 'package:flutter/material.dart';
import 'package:flutter_toggle_tab/flutter_toggle_tab.dart';
import 'package:get/get.dart';
import 'package:lazy_load_scrollview/lazy_load_scrollview.dart';
import 'package:tvumobile/app/config/themes/colors.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/dnp_drawer.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/dnp_monthyear_selector.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/duyetdnp_controller.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/nghiphep_home_appbar.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/empty_widget.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class DuyetDNPView extends GetView<DuyetDNPController> {
  const DuyetDNPView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    Color? customColor = const Color(0xFF21409A);
    return Scaffold(
      key: controller.scaffoldKey,
      appBar: DonNghiPhepAppBar(context, "Danh sách đơn nghỉ phép"),
      endDrawer: buildDNPDrawer(context, controller),
      body: Obx(() {
        return Column(children: [
          Padding(
            padding: EdgeInsets.only(top: 8),
            child: Align(
              child: MyText.titleLarge(
                controller.appbarTitle.value.toUpperCase(),
                decoration: TextDecoration.underline,
                fontWeight: 700,
                color: theme.primaryColor,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 5, 16, 0),
            child: Column(
              children: [
                buildYearSelector(theme, controller),
                buildMonthSelector(theme, controller),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
            child: FlutterToggleTab(
              width: 90, // width in percent
              borderRadius: 30,
              height: 35,
              selectedIndex: controller.tabTextIndexSelected.value,
              selectedBackgroundColors: [customColor, customColor],
              selectedTextStyle: TextStyle(
                color: Colors.white,
                fontSize: 15,
                fontWeight: FontWeight.w700,
              ),
              unSelectedTextStyle: TextStyle(
                color: Colors.black87,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              dataTabs: controller.listTextTabToggle,
              selectedLabelIndex: (index) {
                controller.updateTabIndex(index);
              },
              isScroll: false,
            ),
          ),
          Expanded(
            child: controller.loadedds.value
                ? const Center(
                    child:
                        CircularProgressIndicator(), // Hiển thị spinner khi đang tải
                  )
                : LazyLoadScrollView(
                    onEndOfPage: controller.loadMoreDNP,
                    isLoading: controller.lastPage,
                    child: RefreshIndicator(
                      onRefresh: () => Future.sync(() => controller.loadDNP()),
                      child: SingleChildScrollView(
                        physics: AlwaysScrollableScrollPhysics(),
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.only(
                                  left: 16.0, right: 16.0, top: 0.0),
                              child: controller.navigation[controller
                                  .tabTextIndexSelected.value](controller),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
        ]);
      }),
    );
  }
}
