// ignore_for_file: library_prefixes

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tvumobile/app/features/kyso/models/pdf_viewer_state.dart';
import 'package:tvumobile/app/features/kyso/models/signature_model.dart';
import 'package:tvumobile/app/features/kyso/utils/helpers.dart';
import 'package:pdf_render/pdf_render_widgets.dart' as pdfRender;
import 'package:tvumobile/app/features/kyso/utils/pdf_helpers.dart'
    as pdfHelpers;
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class PdfViewerScreen extends StatefulWidget {
  const PdfViewerScreen({
    super.key,
    required this.pdfUrl,
    required this.signatureUrl,
    required this.availableSignatures,
    this.fullName, // Add optional fullname parameter
    this.department, // Add optional department parameter
    this.personposition, // Add optional position parameter
  });
  final String pdfUrl;
  final String signatureUrl;
  final List<String> availableSignatures;
  final String? fullName; // Optional fullname parameter
  final String? department; // Optional department parameter
  final String? personposition; // Optional position parameter

  @override
  _PdfViewerScreenState createState() => _PdfViewerScreenState();
}

class _PdfViewerScreenState extends State<PdfViewerScreen> {
  Uint8List? pdfBytes;
  Uint8List? signatureImageBytes;
  late String pdfUrl;
  late String signatureUrl;
  PdfViewerState pdfViewerState = PdfViewerState();
  List<SignatureModel> signatures = [];

  @override
  void initState() {
    super.initState();
    if (widget.pdfUrl.isNotEmpty) {
      pdfUrl = widget.pdfUrl;
    } else {
      pdfUrl = "";
    }
    if (widget.signatureUrl.isNotEmpty) {
      signatureUrl = widget.signatureUrl;
    } else {
      signatureUrl = "";
    }
    initLoading();
  }

  Future<void> initLoading() async {
    pdfBytes = await loadPdfFromURL(pdfUrl);
    signatureImageBytes = await loadSignatureImageFromURL(signatureUrl);
    setState(() {});
  }

  void _addSignature() {
    if (pdfBytes == null) return;
    if (signatureImageBytes == null) return;
    if (pdfViewerState.pageSize == null) return;
    if (pdfViewerState.rect == null) return;
    final SignatureModel newSignature = SignatureModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      currentPage: pdfViewerState.currentPage,
      signature: signatureImageBytes,
      fixedWidth: 100,
      fixedHeight: 50,
      fixedTop: 100,
      fixedLeft: 100,
      documentSize: Size(
        pdfViewerState.pageSize!.width,
        pdfViewerState.pageSize!.height,
      ),
      documentViewSize: Size(
        pdfViewerState.rect!.width,
        pdfViewerState.rect!.height,
      ),
    );
    setState(() {
      signatures.add(newSignature);
    });
  }

  VoidCallback? _removeSignature(String signatureId) {
    setState(() {
      signatures.removeWhere((signature) => signature.id == signatureId);
    });
    return null;
  }

  bool pdfVieweCtrlInited = false;
  Widget buildHeader() {
    if (!pdfVieweCtrlInited) return SizedBox();
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('Chữ ký hiện tại: '),
              signatureImageBytes == null
                  ? SizedBox(
                      height: 50,
                    )
                  : Image.memory(
                      signatureImageBytes!,
                      height: 50,
                      width: 100,
                    ),
              MyButton.small(
                onPressed: () {
                  //showSignatureDialogue();
                },
                borderRadiusAll: 5,
                child:
                    MyText.labelLarge('Chọn chữ ký khác', color: Colors.white),
              ),
            ],
          ),
          SizedBox(
            height: 5,
          ),
          ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            child: Container(
              margin: const EdgeInsets.only(bottom: 8.0),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey,
                    blurRadius: 8,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              height: 30,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Trang: $currentPage/${_pdfVieweCtrl.pageCount}'),
                  SizedBox(width: 10),
                  IconButton(
                    iconSize: 20,
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          currentPage <= 1 ? Colors.grey : Colors.blue),
                      maximumSize: WidgetStatePropertyAll(Size(20, 20)),
                    ),
                    icon: Icon(
                      Icons.first_page,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      _pdfVieweCtrl.goToPage(pageNumber: 1);
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          currentPage <= 1 ? Colors.grey : Colors.blue),
                      maximumSize: WidgetStatePropertyAll(Size(20, 20)),
                    ),
                    icon: Icon(
                      Icons.navigate_before,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      _pdfVieweCtrl.goToPage(pageNumber: currentPage - 1);
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          _pdfVieweCtrl.pageCount == currentPage
                              ? Colors.grey
                              : Colors.blue),
                    ),
                    icon: Icon(
                      Icons.navigate_next,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      _pdfVieweCtrl.goToPage(pageNumber: currentPage + 1);
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          _pdfVieweCtrl.pageCount == currentPage
                              ? Colors.grey
                              : Colors.blue),
                    ),
                    icon: Icon(
                      Icons.last_page,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      //_pdfVieweCtrl.lastPage();
                    },
                  ),
                  SizedBox(width: 20),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Colors.lightBlue),
                    ),
                    icon: Icon(
                      Icons.zoom_out_map,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      //_pdfVieweCtrl.zoomLevel = 1.0;
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Colors.lightBlue),
                    ),
                    icon: Icon(
                      Icons.zoom_in,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      //_pdfVieweCtrl.zoomLevel = 2.0;
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Colors.lightBlue),
                    ),
                    icon: Icon(
                      Icons.zoom_out,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      //_pdfVieweCtrl.zoomLevel = 0.5;
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  int currentPage = 1;
  final pdfRender.PdfViewerController _pdfVieweCtrl =
      pdfRender.PdfViewerController();

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('PDF Viewer'),
      ),
      body: Column(
        children: [
          SizedBox(
            height: 8,
            //child: buildHeader(),
          ),
          Expanded(
            child: GestureDetector(
              onTap: _addSignature,
              child: pdfRender.PdfViewer.openData(
                pdfBytes ?? Uint8List(0),
                viewerController: _pdfVieweCtrl,
                params: pdfRender.PdfViewerParams(
                  scrollByMouseWheel: 1,
                  scaleEnabled: true,
                  layoutPages: (contentViewSize, pageSizes) {
                    return pdfHelpers.changePdfPageLayout(
                        contentViewSize: contentViewSize, pageSizes: pageSizes);
                  },
                  onInteractionEnd: (ScaleEndDetails details) {},
                  onViewerControllerInitialized: (p0) {
                    debugPrint("onViewerControllerInitialized pageNumber: $p0");
                    Rect? pageRect = _pdfVieweCtrl.ready?.getPageRect(1);
                    if (pageRect != null) {
                      pdfViewerState = pdfViewerState.copyWith(
                        pdfDocument: pdfBytes,
                        rect: pageRect,
                        currentPage: p0.currentPageNumber,
                        pageSize: Size(pageRect.width, pageRect.height),
                        totalPage: _pdfVieweCtrl.ready?.pageCount ?? 0,
                      );
                    }
                    p0.addListener(() {
                      // Listen for page document changes
                      final currentPagePdf = p0.ready?.currentPageNumber;
                      if (currentPagePdf == null) return;
                      pageRect =
                          _pdfVieweCtrl.ready?.getPageRect(currentPagePdf);
                      if (pageRect != null && currentPagePdf != currentPage) {
                        debugPrint(
                            "p0.addListener pageNumber: $currentPagePdf");
                        pdfViewerState = pdfViewerState.copyWith(
                          pdfDocument: pdfBytes,
                          rect: pageRect,
                          currentPage: currentPagePdf,
                          pageSize: Size(pageRect!.width, pageRect!.height),
                          totalPage: _pdfVieweCtrl.ready?.pageCount ?? 0,
                        );
                      }
                    });
                  },
                  buildPageOverlay: (context, pageNumber, pageRect) {
                    debugPrint("buildPageOverlay pageNumber: $pageNumber");
                    if (currentPage != pageNumber) {
                      return Container();
                    }
                    return pdfHelpers.buildPageOverlay(
                      context: context,
                      pageNumber: pageNumber,
                      pageRect: pageRect,
                      pdfViewerState: pdfViewerState,
                      signatures: signatures,
                      availableSignatures: widget.availableSignatures,
                      fullName: widget.fullName, // Pass fullname
                      department: widget.department, // Pass department
                      personposition: widget.personposition, // Pass position
                      onPressDelete: _removeSignature,
                    );
                  },
                  buildPagePlaceholder: (context, pageNumber, pageRect) =>
                      Center(
                    child: CircularProgressIndicator(
                      color: theme.colorScheme.secondary,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
