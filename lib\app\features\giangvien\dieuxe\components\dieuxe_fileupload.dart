import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
// ignore: depend_on_referenced_packages
import 'package:path/path.dart' as path;
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class FileUploadTheme {
  final Color primaryColor;
  final Color backgroundColor;
  final Color borderColor;
  final Color textColor;
  final Color errorColor;
  final double borderRadius;
  final EdgeInsets contentPadding;
  final TextStyle titleStyle;
  final TextStyle subtitleStyle;

  const FileUploadTheme({
    this.primaryColor = Colors.blue,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFE0E0E0),
    this.textColor = Colors.black87,
    this.errorColor = Colors.red,
    this.borderRadius = 8.0,
    this.contentPadding = const EdgeInsets.all(8.0),
    this.titleStyle = const TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 14,
    ),
    this.subtitleStyle = const TextStyle(color: Colors.grey, fontSize: 12),
  });
}

class DieuXeFileUploadState extends State<DieuXeFileUpload> {
  bool _isLoading = false;
  List<FileItem> _files = [];
  String? _errorMessage;
  double _uploadProgress = 0;

  // Public getters
  List<PlatformFile> get selectedFiles => _files.map((f) => f.file).toList();
  bool get hasFiles => _files.isNotEmpty;
  String? get errorMessage => _errorMessage;

  // Method to update upload progress
  void updateProgress(double progress) {
    setState(() {
      _uploadProgress = progress;
    });
    if (widget.onUploadProgress != null) {
      widget.onUploadProgress!(progress);
    }
  }

  // Method to set error message
  void setError(String message) {
    setState(() {
      _errorMessage = message;
    });
  }

  // Method to clear files
  void clearFiles() {
    setState(() {
      _files.clear();
      _errorMessage = null;
      _uploadProgress = 0;
    });
  }

  Future<void> _pickFiles() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: widget.allowedExtensions != null ? FileType.custom : FileType.any,
        allowedExtensions: widget.allowedExtensions,
        allowMultiple: widget.allowMultiple,
      );

      if (result != null) {
        List<FileItem> newFiles = [];

        for (var file in result.files) {
          if (file.size > widget.maxFileSizeMB * 1024 * 1024) {
            setState(() {
              _errorMessage =
                  '${file.name} quá lớn. Kích thước tối đa là ${widget.maxFileSizeMB}MB';
            });
            continue;
          }
          if (_files.any((f) => f.file.name == file.name)) {
            setState(() {
              _errorMessage = '${file.name} đã được chọn';
            });
            continue;
          } else {
            newFiles.add(FileItem(file: file));
          }
        }

        setState(() {
          if (widget.allowMultiple) {
            _files.addAll(newFiles);
          } else {
            _files = newFiles;
          }
        });

        if (widget.onFilesSelected != null) {
          widget.onFilesSelected!(result.files);
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Có lỗi xảy ra khi chọn file';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  IconData _getFileIcon(String fileName) {
    String ext = path.extension(fileName).toLowerCase();
    switch (ext) {
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
        return Icons.image;
      case '.pdf':
        return Icons.picture_as_pdf;
      case '.doc':
      case '.docx':
        return Icons.description;
      case '.xls':
      case '.xlsx':
        return Icons.table_chart;
      case '.zip':
      case '.rar':
        return Icons.folder_zip;
      default:
        return Icons.insert_drive_file;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        primaryColor: widget.theme.primaryColor,
        colorScheme: Theme.of(
          context,
        ).colorScheme.copyWith(primary: widget.theme.primaryColor),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              MyText.bodyMedium(widget.attachTitleText, fontWeight: 700),
              Spacer(),
              MyButton.small(
                onPressed: _isLoading ? null : _pickFiles,
                borderRadiusAll: widget.theme.borderRadius,
                borderColor: widget.theme.borderColor,
                backgroundColor: widget.theme.primaryColor,
                elevation: 2,
                child: Row(
                  children: [
                    _isLoading
                        ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                widget.theme.textColor,
                              ),
                            ),
                          )
                        : Icon(
                            Icons.upload_file,
                            color: widget.theme.textColor,
                            size: 20,
                          ),
                    SizedBox(width: 6),
                    Text(
                      widget.buttonText,
                      style: widget.theme.titleStyle.copyWith(
                        color: widget.theme.textColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (_files.isEmpty && 1 == 2)
            DottedBorder(
              options: RoundedRectDottedBorderOptions(
                radius: Radius.circular(10),
                dashPattern: [10, 4],
                strokeCap: StrokeCap.round,
                color: Colors.blue.shade400,
              ),
              child: Container(
                width: double.infinity,
                height: 35,
                decoration: BoxDecoration(
                  color: Colors.blue.shade50.withOpacity(.3),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.folder_open, color: Colors.blue, size: 20),
                    SizedBox(width: 4),
                    Text(
                      widget.emptyText,
                      style: TextStyle(
                        fontSize: 15,
                        color: Colors.grey.shade400,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          if (_uploadProgress > 0 && _uploadProgress < 1)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: LinearProgressIndicator(
                value: _uploadProgress,
                backgroundColor: widget.theme.borderColor,
                valueColor: AlwaysStoppedAnimation<Color>(
                  widget.theme.primaryColor,
                ),
              ),
            ),
          if (_files.isNotEmpty)
            Container(
              margin: EdgeInsets.only(top: 8),
              padding: EdgeInsets.zero,
              decoration: BoxDecoration(
                color: widget.theme.backgroundColor,
                border: Border.all(color: widget.theme.borderColor),
                borderRadius: BorderRadius.circular(widget.theme.borderRadius),
              ),
              child: Column(
                children: _files.asMap().entries.map((entry) {
                  return _buildFileItem(entry.value, entry.key);
                }).toList(),
              ),
            ),
          if (_errorMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _errorMessage!,
                style: TextStyle(color: widget.theme.errorColor),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFileItem(FileItem fileItem, int index) {
    return Container(
      padding: widget.theme.contentPadding,
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: widget.theme.borderColor)),
      ),
      child: Row(
        children: [
          Container(
            width: 30,
            height: 30,
            margin: EdgeInsets.only(right: 8, left: 8),
            decoration: BoxDecoration(
              color: widget.theme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(
                widget.theme.borderRadius / 2,
              ),
            ),
            child: Icon(
              _getFileIcon(fileItem.file.name),
              color: widget.theme.primaryColor,
              size: 30,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fileItem.file.name,
                  style: widget.theme.titleStyle,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '${(fileItem.file.size / 1024).toStringAsFixed(1)} KB',
                  style: widget.theme.subtitleStyle,
                ),
              ],
            ),
          ),
          IconButton(
            icon: Icon(Icons.close, color: widget.theme.errorColor),
            onPressed: () {
              setState(() {
                _files.removeAt(index);
              });
            },
          ),
        ],
      ),
    );
  }
}

class DieuXeFileUpload extends StatefulWidget {
  final Function(List<PlatformFile>)? onFilesSelected;
  final Function(double)? onUploadProgress;
  final String attachTitleText;
  final String buttonText;
  final String emptyText;
  final double maxFileSizeMB;
  final bool allowMultiple;
  final List<String>? allowedExtensions;
  final FileUploadTheme theme;

  const DieuXeFileUpload({
    super.key,
    this.onFilesSelected,
    this.onUploadProgress,
    this.buttonText = 'Chọn file để tải lên',
    this.emptyText = 'Chưa chọn file',
    this.attachTitleText = 'Có file đính kèm không?',
    this.maxFileSizeMB = 10,
    this.allowMultiple = true,
    this.allowedExtensions,
    this.theme = const FileUploadTheme(),
  });

  @override
  DieuXeFileUploadState createState() => DieuXeFileUploadState();
}

class FileItem {
  final PlatformFile file;
  FileItem({required this.file});
}
