import 'package:get/get.dart';
import 'package:tvumobile/app/features/danhgia/controllers/qr_canhan_controller.dart';
import 'package:tvumobile/app/features/danhgia/controllers/qr_home_controller.dart';
import 'package:tvumobile/app/features/danhgia/controllers/qr_llkh_controller.dart';
import 'package:tvumobile/app/features/danhgia/qr_scanner_controller.dart';

class QrHomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<QrHomeController>(
      () => QrHomeController(),
    );
    Get.lazyPut<QrCaNhanController>(
      () => QrCaNhanController(),
    );
    Get.lazyPut<QrLLKHController>(
      () => QrLLKHController(),
    );
    Get.lazyPut<QrScannerController>(() => QrScannerController());
  }
}
