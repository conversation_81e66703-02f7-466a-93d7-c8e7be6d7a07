import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxeinfo.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/giaydieuxe.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepinfo.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_fileupload.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_detail_controller.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_home_controller.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class DieuXeCreateController extends GetxController {
  ApiProvider apiProvider = Get.find();
  late DieuXeInfo dieuXeInfo;
  final formKey = GlobalKey<FormBuilderState>();
  final formKeyVCCT = GlobalKey<FormBuilderState>();

  Rx<DateTime> startDate = DateTime.now().obs;
  Rx<DateTime> endDate = DateTime.now().obs;
  // LanhDaoDonviList? supervisor;
  // String supervisorName = "";
  final lanhDaoDV = <LanhDaoDonviList>[].obs;
  final donviList = <DonViList>[].obs;
  var vienChucList = <dynamic>[].obs; // Danh sách viên chức động
  var selectedVienChuc = <dynamic>[].obs; // Danh sách viên chức đã chọn
  var selectedDonVi = Rx<DonViList?>(null); // Đơn vị được chọn
  final donvi = ''.obs;
  int donviID = 0;
  bool isEditMode = false; // Biến xác định chế độ (tạo mới hay sửa)
  bool isMergeMode = false; // Biến xác định chế độ ghép phiếu
  final List<DonVi> listDVQuyetToan = [];
  final List<int> listPDXGhep = [];
  RxString trangthai = ''.obs;
  UserInfo? userInfo = LocalStorageServices.getUserInfo();

  // HIỂN THỊ CÁC WIDGET ẨN
  final choduyet = false.obs;
  final hienvcngoaitruong = false.obs;
  var isLoadingVienChuc = false.obs; // Trạng thái loading

  // KHAI BÁO FILE UPLOAD
  List<PlatformFile> selectedFiles = [];
  final uploadWidgetKey = GlobalKey<DieuXeFileUploadState>();

  RxBool isLoading = false.obs;

  Map<String, dynamic> formData = {
    'startendday': DateTimeRange(start: DateTime.now(), end: DateTime.now()),
    'noicongtac': '',
    'noidung': '',
    'diadiemdi': '',
    'diadiemve': '',
    'vienchuc': [],
    'document': null
  };

  @override
  Future<void> onInit() async {
    super.onInit();
    // // Kiểm tra nếu đang chỉnh sửa đơn nghỉ phép
    if (Get.arguments != null && Get.arguments['id'] != null) {
      final int phieuDieuXeId = Get.arguments['id'];
      await loadPhieuDieuXe(phieuDieuXeId);
      await getDieuXeInfo();
    } else if (Get.arguments != null && Get.arguments['listID'] != null) {
      final List<String> listPhieuDieuXeId = Get.arguments['listID'];
      await loadMultiplePhieuDieuXe(listPhieuDieuXeId);
    } else {
      getDieuXeInfo();
    }
  }

  @override
  void onReady() {
    // if (formData['id'] == null) {
    //   getDNPInfo(2); // Chỉ gọi getDNPInfo nếu không ở chế độ chỉnh sửa
    // }
  }

  // Lấy thông tin chung của phiếu điều xe
  getDieuXeInfo() async {
    var response = await apiProvider.getDieuXeInfo();
    lanhDaoDV.addAll(response.lanhDaoDonviList);
    donviList.addAll(response.donviList);
    if (response.donVi != null) {
      donvi.value = response.donVi!.tenDonVi!;
    }
    update();
  }

  // Lấy danh sách viên chức đơn vị theo đơn vị ID
  getDsVienChuc(int dvID) async {
    isLoadingVienChuc.value = true;
    var response = await apiProvider.getDsVienChuc(donviID: dvID);
    if (response.isNotEmpty) {
      //vienChucList.assignAll(response); // Cập nhật danh sách viên chức

      // Lấy danh sách từ API
      var newVienChucList = response;

      // Kết hợp với selectedVienChuc để đánh dấu trạng thái
      vienChucList.assignAll(newVienChucList.map((newVc) {
        // Kiểm tra xem viên chức này có trong selectedVienChuc không
        var selectedVc = selectedVienChuc
            .firstWhere((vc) => vc['id'] == newVc['id'], orElse: () => null);
        if (selectedVc != null) {
          return selectedVc; // Sử dụng bản ghi từ selectedVienChuc để giữ trạng thái
        }
        return newVc; // Sử dụng bản ghi mới từ API
      }).toList());

      isLoadingVienChuc.value = false;
    } else {
      Get.snackbar('Lỗi', 'Không thể tải danh sách viên chức');
    }
  }

  @override
  void onClose() {
    // resetThongtin();
    formData.clear();
    super.onClose();
  }

  // ---------- CÁC HÀM DÙNG CHO CHỨC NĂNG CHỌN DANH SÁCH THÀNH PHẦN ĐI CÔNG TÁC ----------
  // Gọi hàm lấy danh sách viên chức khi thay đổi đơn vị
  void updateSelectedDonVi(DonViList? value) {
    selectedDonVi.value = value;
    if (value != null) {
      getDsVienChuc(value.id!); // Gọi API khi chọn đơn vị
    } else {
      vienChucList.clear(); // Xóa danh sách nếu không có đơn vị
    }

    // Lọc selectedVienChuc để chỉ giữ các viên chức thuộc đơn vị mới
    if (value != null) {
      selectedVienChuc.value = selectedVienChuc.where((vienChuc) {
        return vienChuc['donVi'] == value.tenDonVi || vienChuc['id'] == null;
      }).toList();
    } else {
      selectedVienChuc.clear(); // Xóa hết nếu không có đơn vị
    }
    update();
  }

  // Thêm viên chức vào danh sách thành phần đi công tác
  void addSelectedVienChuc(dynamic vienChuc) {
    if (!selectedVienChuc.any((vc) => vc['id'] == vienChuc['id'])) {
      selectedVienChuc.add(vienChuc);
    }
    update();
  }

  // Xóa viên chức khoải danh sách thành phần đi công tác
  void removeSelectedVienChuc(dynamic vienChuc) {
    selectedVienChuc.remove(vienChuc);
    update();
  }
  // --------------------------------------------------------------------------------------

  // Tải dữ liệu phiếu điều xe theo ID
  Future<void> loadPhieuDieuXe(int id) async {
    String pdxId = id.toString();
    final response = await apiProvider.getChiTietPhieuDX(pdxId: pdxId);
    if (response != null && response.isNotEmpty) {
      print("response: $response");

      // Lấy dữ liệu đầu tiên từ response
      final phieu = response[0];

      // Chuyển đổi thời gian từ chuỗi sang DateTime
      DateTime? thoigiandi =
          parseSimpleTimeString(phieu.thoiGianKhoiHanh ?? "");
      DateTime? thoigianve = parseSimpleTimeString(phieu.thoiGianVe ?? "");

      // Cập nhật formData với dữ liệu
      formData.assignAll({
        'id': phieu.id,
        'donvi': phieu.donVi!.id ?? '', // Đơn vị
        'noidung': phieu.noiDung ?? '', // Nội dung
        'noicongtac': phieu.noiCongTac ?? '', // Nơi công tác
        'diadiemdi': phieu.diaDiemKhoiHanh ?? '', // Địa điểm đi
        'diadiemve': phieu.diaDiemVe ?? '', // Địa điểm về
        'startendday': phieu.tuNgay != null && phieu.denNgay != null
            ? DateTimeRange(start: phieu.tuNgay!, end: phieu.denNgay!)
            : DateTimeRange(
                start: DateTime.now(), end: DateTime.now()), // Ngày đi & về
        'thoigiandi': thoigiandi, // Sử dụng DateTime đã chuyển đổi
        'thoigianve': thoigianve, // Sử dụng DateTime đã chuyển đổi
        'vienchuc': phieu.dsDiCongTac ?? [],
        'vcngoaitruong': (phieu.vienChucNgoaiTruong != null &&
                phieu.vienChucNgoaiTruong!.isNotEmpty)
            ? true
            : false,
        'vienchucngoaitruong': phieu.vienChucNgoaiTruong ?? '',
        'donvivienchucngoaitruong': phieu.donViVienChucNgoaiTruong ?? '',
        'chuyendon': false,
        'document': phieu.fileDinhKem ?? '',
      });

      // Cập nhật selectedVienChuc từ dsDiCongTac
      if (phieu.dsDiCongTac != null && phieu.dsDiCongTac!.isNotEmpty) {
        selectedVienChuc.assignAll(phieu.dsDiCongTac!
            .map((vc) => {
                  'id': vc.id,
                  'ho': vc.ho,
                  'tenDem': vc.tenDem,
                  'ten': vc.ten,
                  'donVi': vc.donVi ?? '',
                  'chucVu': vc.chucVu ?? '',
                  'hinhAnh': vc.hinhAnh ?? '',
                })
            .toList());
      }

      trangthai.value = phieu.trangThai!;
      donvi.value = phieu.donVi?.tenDonVi ?? ''; // Đơn vị
      donviID = phieu.donVi!.id!;
      hienvcngoaitruong.value = (phieu.vienChucNgoaiTruong != null &&
              phieu.vienChucNgoaiTruong!.isNotEmpty)
          ? true
          : false; // Hiển thị viên chức ngoài trường

      // Đồng bộ form với dữ liệu
      if (formKey.currentState != null) {
        formKey.currentState!.patchValue({
          'noicongtac': formData['noicongtac'],
          'noidung': formData['noidung'],
          'diadiemdi': formData['diadiemdi'],
          'diadiemve': formData['diadiemve'],
          'startendday': formData['startendday'],
          'thoigiandi': formData['thoigiandi'],
          'thoigianve': formData['thoigianve'],
          'vcngoaitruong': formData['vcngoaitruong'],
          'vienchucngoaitruong': formData['vienchucngoaitruong'],
          'donvivienchucngoaitruong': formData['donvivienchucngoaitruong'],
        });
      }

      // Cập nhật trạng thái isEditMode
      isEditMode = true;

      update();
    } else {
      Get.showSnackbar(GetSnackBar(
        title: 'Lỗi',
        message: 'Không thể tải chi tiết phiếu điều xe',
        duration: Duration(seconds: 3),
        snackPosition: SnackPosition.TOP,
      ));
    }
  }

  // Tải dữ liệu nhiều phiếu điều xe
  Future<void> loadMultiplePhieuDieuXe(List<String> ids) async {
    try {
      // Lưu trữ danh sách phiếu được tải
      final List<GiayDieuXe> phieuList = [];
      for (String pdxId in ids) {
        final response = await apiProvider.getChiTietPhieuDX(pdxId: pdxId);
        if (response != null && response.isNotEmpty) {
          phieuList.add(response[0]);
          listDVQuyetToan.add(response[0].donVi!);
          listPDXGhep.add(response[0].id!);
        } else {
          Get.showSnackbar(GetSnackBar(
            title: 'Lỗi',
            message: 'Không thể tải chi tiết phiếu điều xe $pdxId',
            duration: Duration(seconds: 3),
            snackPosition: SnackPosition.TOP,
          ));
        }
      }

      if (phieuList.isEmpty) {
        Get.showSnackbar(GetSnackBar(
          title: 'Lỗi',
          message: 'Không tải được bất kỳ phiếu điều xe nào',
          duration: Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        ));
        return;
      }

      // Gộp dữ liệu từ các phiếu
      // Lấy tuNgay sớm nhất và denNgay muộn nhất
      DateTime? earliestTuNgay;
      DateTime? latestDenNgay;
      for (var phieu in phieuList) {
        if (earliestTuNgay == null ||
            (phieu.tuNgay != null && phieu.tuNgay!.isBefore(earliestTuNgay))) {
          earliestTuNgay = phieu.tuNgay;
        }
        if (latestDenNgay == null ||
            (phieu.denNgay != null && phieu.denNgay!.isAfter(latestDenNgay))) {
          latestDenNgay = phieu.denNgay;
        }
      }

      final donVi = phieuList
          .map((phieu) => '- ${phieu.donVi!.tenDonVi}')
          .where((s) => s.isNotEmpty)
          .join('\n');
      final noiDung = phieuList
          .map((phieu) => '- ${phieu.noiDung}')
          .where((s) => s.isNotEmpty)
          .join('\n');
      final noiCongTac = phieuList
          .map((phieu) => '- ${phieu.noiCongTac}')
          .where((s) => s.isNotEmpty)
          .join('\n');
      final diaDiemDi = phieuList
          .map((phieu) => '- ${phieu.diaDiemKhoiHanh}')
          .where((s) => s.isNotEmpty)
          .join('\n');
      final diaDiemVe = phieuList
          .map((phieu) => '- ${phieu.diaDiemVe}')
          .where((s) => s.isNotEmpty)
          .join('\n');
      final thoiGianDi = phieuList
          .map((phieu) => '- ${phieu.thoiGianKhoiHanh}')
          .where((s) => s.isNotEmpty)
          .join('\n');
      final thoiGianVe = phieuList
          .map((phieu) => '- ${phieu.thoiGianVe}')
          .where((s) => s.isNotEmpty)
          .join('\n');

      // Gộp dsDiCongTac
      // final dsDiCongTac = phieuList
      //     .expand((phieu) => phieu.dsDiCongTac ?? [])
      //     .toSet() // Loại bỏ trùng lặp dựa trên id
      //     .toList();

      // Gộp dsDiCongTac, loại bỏ trùng lặp dựa trên id
      final Map<String, dynamic> vienChucMap = {};
      for (var phieu in phieuList) {
        if (phieu.dsDiCongTac != null && phieu.dsDiCongTac!.isNotEmpty) {
          for (var vc in phieu.dsDiCongTac!) {
            vienChucMap[vc.id.toString()] = {
              'id': vc.id,
              'ho': vc.ho,
              'tenDem': vc.tenDem,
              'ten': vc.ten,
              'donVi': vc.donVi ?? '',
              'chucVu': vc.chucVu ?? '',
              'hinhAnh': vc.hinhAnh ?? '',
            };
          }
        }
      }
      final dsDiCongTac = vienChucMap.values.toList();

      // Gộp vienChucNgoaiTruong và donViVienChucNgoaiTruong
      final vienChucNgoaiTruong = phieuList
          .map((phieu) => phieu.vienChucNgoaiTruong ?? '')
          .where((s) => s.isNotEmpty)
          .join('\n');
      final donViVienChucNgoaiTruong = phieuList
          .map((phieu) => phieu.donViVienChucNgoaiTruong ?? '')
          .where((s) => s.isNotEmpty)
          .join('\n');

      // Cập nhật formData với dữ liệu gộp
      formData.assignAll({
        'id': null, // Phiếu ghép mới không có id
        'donvighep': donVi,
        'noidung': noiDung,
        'noicongtac': noiCongTac,
        'diadiemdi': diaDiemDi,
        'diadiemve': diaDiemVe,
        'startendday': earliestTuNgay != null && latestDenNgay != null
            ? DateTimeRange(start: earliestTuNgay, end: latestDenNgay)
            : DateTimeRange(start: DateTime.now(), end: DateTime.now()),
        'thoigiandighep': thoiGianDi,
        'thoigianveghep': thoiGianVe,
        'thoigiandi': null,
        'thoigianve': null,
        'vienchuc': dsDiCongTac,
        'vcngoaitruong': vienChucNgoaiTruong.isNotEmpty,
        'vienchucngoaitruong': vienChucNgoaiTruong,
        'donvivienchucngoaitruong': donViVienChucNgoaiTruong,
        'chuyendon': false,
        'document': '', // File đính kèm để trống cho phiếu mới
      });

      // Cập nhật selectedVienChuc từ dsDiCongTac
      // if (dsDiCongTac.isNotEmpty) {
      //   selectedVienChuc.assignAll(dsDiCongTac
      //       .map((vc) => {
      //             'id': vc.id,
      //             'ho': vc.ho,
      //             'tenDem': vc.tenDem,
      //             'ten': vc.ten,
      //             'donVi': vc.donVi ?? '',
      //             'chucVu': vc.chucVu ?? '',
      //             'hinhAnh': vc.hinhAnh ?? '',
      //           })
      //       .toSet() // Loại bỏ trùng lặp
      //       .toList());
      // }

      // Cập nhật selectedVienChuc từ dsDiCongTac
      if (dsDiCongTac.isNotEmpty) {
        selectedVienChuc.assignAll(dsDiCongTac);
      } else {
        selectedVienChuc.clear();
      }

      // Cập nhật các biến trạng thái
      hienvcngoaitruong.value = vienChucNgoaiTruong.isNotEmpty;

      // Đồng bộ form với dữ liệu
      if (formKey.currentState != null) {
        formKey.currentState!.patchValue({
          'donvighep': formData['donvighep'],
          'noicongtac': formData['noicongtac'],
          'noidung': formData['noidung'],
          'diadiemdi': formData['diadiemdi'],
          'diadiemve': formData['diadiemve'],
          'startendday': formData['startendday'],
          'thoigiandi': null,
          'thoigianve': null,
          'thoigiandighep': formData['thoigiandighep'],
          'thoigianveghep': formData['thoigianveghep'],
          'vcngoaitruong': formData['vcngoaitruong'],
          'vienchucngoaitruong': formData['vienchucngoaitruong'],
          'donvivienchucngoaitruong': formData['donvivienchucngoaitruong'],
        });
      }

      // Cập nhật trạng thái isMergeMode
      isMergeMode = true;

      update();
    } catch (e) {
      Get.showSnackbar(GetSnackBar(
        title: 'Lỗi',
        message: 'Lỗi khi tải danh sách phiếu điều xe: $e',
        duration: Duration(seconds: 3),
        snackPosition: SnackPosition.TOP,
      ));
    }
  }

  // ---------- Gửi đề nghị điều xe lên server và hiển thị thông báo kết quả. -------------
  Future<void> taoSuaPhieuDieuXe() async {
    Map<String, dynamic> postData = {};

    // Lấy thông tin từ formData
    // postData["VienChucId"] = userInfo!.vienchucid!;
    postData["DonViId"] = donviID;
    postData["NoiCongTac"] = formData["noicongtac"]?.toString() ?? '';
    postData["NoiDung"] = formData["noidung"]?.toString() ?? '';
    postData["DiaDiemKhoiHanh"] = formData["diadiemdi"]?.toString() ?? '';
    postData["DiaDiemVe"] = formData["diadiemve"]?.toString() ?? '';

    postData["TuNgay"] =
        (formData["startendday"] as DateTimeRange?)?.start.toIso8601String() ??
            '';

    postData["DenNgay"] =
        (formData["startendday"] as DateTimeRange?)?.end.toIso8601String() ??
            '';

    postData["ThoiGianKhoiHanh"] = formData["thoigiandi"] != null
        ? DateTime.fromMillisecondsSinceEpoch(formData["thoigiandi"] as int)
            .toIso8601String()
        : '';

    postData["ThoiGianVe"] = formData["thoigianve"] != null
        ? DateTime.fromMillisecondsSinceEpoch(formData["thoigianve"] as int)
            .toIso8601String()
        : '';

    postData["ThanhPhanDiCongTac"] =
        formData["vienchuc"] ?? []; // Danh sách viên chức đã chọn

    postData["ChoDuyet"] = formData["chuyendon"] ?? false;

    postData["DonViDuyet"] = (formData["donviduyet"] as LanhDaoDonviList?) ?? 0;

    if (formData['vcngoaitruong'] != null &&
        formData['vcngoaitruong'] == true) {
      postData["VienChucNgoaiTruong"] =
          formData["vienchucngoaitruong"]?.toString() ?? '';

      postData["DonViVienChucNgoaiTruong"] =
          formData["donvivienchucngoaitruong"]?.toString() ?? '';
    }

    // Lấy danh sách file từ upload widget
    final uploadState = uploadWidgetKey.currentState;
    List<PlatformFile>? selectedFiles = uploadState?.selectedFiles;

    // Log dữ liệu để debug, $selectedFiles
    print("Dieu Xe Post Data: $postData");

    String? success;

    if (isEditMode) {
      // Cập nhật phiếu điều xe hiện có
      int idDieuXe = formData['id'] as int? ?? 0;
      success =
          await apiProvider.putDieuXe(pdxId: idDieuXe, formValue: postData);
    } else {
      // Tạo phiếu điều xe mới
      success = await apiProvider.postDieuXe(
          formValue: postData, selectedFiles: selectedFiles);
    }
    print("Success: $success");

    if (success != null) {
      Map<String, dynamic> response = jsonDecode(success);

      if (response['status'] == 200) {
        bool? val = await Get.dialog(
          AlertDialog(
            title: const MyText.titleMedium('Thông báo'),
            content: MyText.bodyLarge(isEditMode
                ? 'Phiếu điều xe đã được cập nhật!'
                : 'Phiếu điều xe đã được tạo!'),
            actions: [
              TextButton(
                onPressed: () async {
                  // Làm mới danh sách hoặc dữ liệu liên quan
                  Get.find<DieuXeHomeController>().refreshData();
                  Get.back(result: true);
                },
                child: const Text('OK'),
              ),
            ],
          ),
        );

        if (val != null && val) {
          if (isEditMode && formData['id'] != null) {
            // Làm mới dữ liệu nếu đang ở chế độ chỉnh sửa
            final chiTietController =
                Get.find<DieuXeDetailController>(); // Giả định controller
            if (chiTietController.pdxId == formData['id'].toString()) {
              chiTietController.refreshData();
            }
          }
          Get.back(); // Quay lại màn hình trước
        }
      }
    }
  }

  // -------------------------------- Ghép phiếu điều xe. ---------------------------------
  Future<void> ghepPhieuDieuXe() async {
    Map<String, dynamic> postData = {};

    // Lấy thông tin từ formData
    // postData["VienChucId"] = userInfo!.vienchucid!;
    postData["DonViId"] = null;
    postData["DonViGhep"] = formData["donvighep"]?.toString() ?? '';
    postData["NoiCongTac"] = formData["noicongtac"]?.toString() ?? '';
    postData["NoiDung"] = formData["noidung"]?.toString() ?? '';
    postData["DiaDiemKhoiHanh"] = formData["diadiemdi"]?.toString() ?? '';
    postData["DiaDiemVe"] = formData["diadiemve"]?.toString() ?? '';

    postData["TuNgay"] =
        (formData["startendday"] as DateTimeRange?)?.start.toIso8601String() ??
            '';

    postData["DenNgay"] =
        (formData["startendday"] as DateTimeRange?)?.end.toIso8601String() ??
            '';

    postData["ThoiGianKhoiHanh"] = formData["thoigiandighep"]?.toString() ?? '';
    postData["ThoiGianVe"] = formData["thoigianveghep"]?.toString() ?? '';

    postData["ThanhPhanDiCongTac"] =
        formData["vienchuc"] ?? []; // Danh sách viên chức đã chọn

    postData["DonViQuyetToanId"] = formData["donviquyettoan"]?.toString() ?? '';

    if (formData['vcngoaitruong'] != null &&
        formData['vcngoaitruong'] == true) {
      postData["VienChucNgoaiTruong"] =
          formData["vienchucngoaitruong"]?.toString() ?? '';

      postData["DonViVienChucNgoaiTruong"] =
          formData["donvivienchucngoaitruong"]?.toString() ?? '';
    }

    // Log dữ liệu để debug, $selectedFiles
    print("listPDXid: $listPDXGhep");
    print("Donviquyettoan: ${postData["DonViQuyetToan"]}");
    print("Dieu Xe Post Data: $postData");

    // Ghép phiếu điều xe
    String? success = await apiProvider.mergeDieuXe(
        listPDXid: listPDXGhep, formValue: postData);

    print("Success: $success");

    if (success != null) {
      Map<String, dynamic> response = jsonDecode(success);

      if (response['status'] == 200) {
        Get.back();
        Get.snackbar(
          'Thành công',
          'Đã ghép phiếu xe thành công.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );
        // Làm mới danh sách hoặc dữ liệu liên quan
        Get.find<DieuXeHomeController>().refreshData();
      }
    }
  }

  // LẤY TÊN FILE
  String getFileName(String path) {
    if (path.isEmpty || path == 'Không có file đính kèm') return path;
    // Tách đường dẫn và lấy phần cuối
    return path.split('\\').last;
  }

  // HÀM ĐỔI CHUỖI THỜI GIAN KHỞI HÀNH VÀ THỜI GIAN VỀ THÀNH TIME
  DateTime? parseSimpleTimeString(String timeString) {
    try {
      // Tách phần thời gian và ngày
      final parts = timeString.split(', ngày ');
      if (parts.length != 2) return null;

      final timePart =
          parts[0].replaceAll('Lúc ', '').trim(); // ".... giờ ...."
      final datePart = parts[1].trim(); // "17/7/2025"

      // Tách ngày, tháng, năm
      final dateComponents = datePart.split('/');
      if (dateComponents.length != 3) return null;
      final day = int.parse(dateComponents[0]);
      final month = int.parse(dateComponents[1]);
      final year = int.parse(dateComponents[2]);

      // Kiểm tra và phân tích giờ/phút
      int hour = 0;
      int minute = 0;
      if (timePart.contains('....')) {
        // Trường hợp thiếu giờ/phút, đặt mặc định 00:00
        hour = 0;
        minute = 0;
      } else {
        final timeComponents = timePart.split(' giờ ');
        if (timeComponents.length == 2) {
          hour = int.parse(timeComponents[0].trim());
          minute = int.parse(timeComponents[1].replaceAll(' phút', '').trim());
        } else {
          return null; // Không đúng định dạng
        }
      }

      // Tạo DateTime
      return DateTime(year, month, day, hour, minute);
    } catch (e) {
      print("Lỗi phân tích chuỗi thời gian: $e");
      return null;
    }
    // ------------------------------------------------------------
  }

  String getHoTen(NguoiTao? nguoi) {
    if (nguoi == null) return "";
    var ht = "";
    ht = nguoi.ho!.isEmpty ? "" : "${nguoi.ho} ";
    ht += nguoi.tenDem!.isEmpty ? "" : "${nguoi.tenDem} ";
    ht += nguoi.ten!.isEmpty ? "" : nguoi.ten.toString();
    return ht;
  }
}
