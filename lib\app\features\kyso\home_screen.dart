import 'dart:convert';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_signaturepad/signaturepad.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'home_controller.dart';
import 'waiting_list_screen.dart';
import 'signed_list_screen.dart';
import 'models/sign_request_document.dart';
import 'document_view_screen.dart';
//import 'document_sign_screen.dart';

class HomeScreen extends GetView<HomeController> {
  const HomeScreen({super.key});

  // ignore: unused_element
  void _showDocumentOptions(SignRequestDocument document) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('Tải xuống'),
              onTap: () {
                Get.back();
                // Implement download functionality
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share'),
              onTap: () {
                Get.back();
                // Implement share functionality
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('Delete'),
              onTap: () {
                Get.back();
                // Implement delete functionality
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    //final HomeController controller = Get.put(HomeController());
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bảng Điều Khiển Chữ Ký Số'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Implement search functionality
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Statistics Cards
            Row(
              children: [
                _buildStatCard(
                  title: 'Đang chờ',
                  count: controller.pendingDocuments.length,
                  color: Colors.orange,
                ),
                const SizedBox(width: 16),
                _buildStatCard(
                  title: 'Đã ký',
                  count: controller.signedDocuments.length,
                  color: Colors.green,
                ),
                const SizedBox(width: 16),
                _buildStatCard(
                  title: 'Đến hạn',
                  count: controller.expiringDocuments.length,
                  color: Colors.red,
                ),
              ],
            ),
            const SizedBox(height: 18),

            // Signature Section
            const Text(
              'My Signature',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 80,
              width: double.infinity,
              child: ListView(
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                children: [
                  Obx(() {
                    if (controller.imageChuKy.value.isNotEmpty) {
                      return Container(
                          height: 70,
                          width: 110,
                          padding: const EdgeInsets.all(0),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Stack(
                            children: [
                              Image(
                                  height: 65,
                                  image: controller.imageChuKy.value.isNotEmpty
                                      ? NetworkImage(
                                          controller.imageChuKy.value)
                                      : AssetImage('assets/tasky/no_task.png')),
                              Positioned(
                                  right: -15,
                                  bottom: -15,
                                  child: IconButton(
                                    iconSize: 20,
                                    icon: const Icon(Icons.edit),
                                    tooltip: 'Draw Signature',
                                    onPressed: () {
                                      _showSignatureDialog(context);
                                    },
                                  )),
                              Positioned(
                                  left: 4,
                                  bottom: 0,
                                  child: MyText.labelMedium("Chữ ký")),
                            ],
                          ));
                    }
                    return SizedBox.shrink();
                  }),
                  SizedBox(
                    width: 8,
                  ),
                  //controller.imageChuKyTat.value,
                  Obx(() {
                    if (controller.imageChuKyTat.value.isNotEmpty) {
                      return Container(
                        height: 70,
                        width: 110,
                        padding: const EdgeInsets.all(0),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Stack(
                          children: [
                            Image(
                                height: 65,
                                image: NetworkImage(
                                    controller.imageChuKyTat.value)),
                            Positioned(
                                right: -15,
                                bottom: -15,
                                child: IconButton(
                                  iconSize: 20,
                                  icon: const Icon(Icons.edit),
                                  tooltip: 'Draw Signature',
                                  onPressed: () {
                                    _showSignatureDialog(context);
                                  },
                                )),
                            Positioned(
                                left: 4,
                                bottom: 0,
                                child: MyText.labelMedium("Chữ ký tắt")),
                          ],
                        ),
                      );
                    }
                    return SizedBox.shrink();
                  }),
                  SizedBox(
                    width: 8,
                  ),
                  //controller.imageMoc.value
                  Obx(() {
                    if (controller.imageMoc.value.isNotEmpty) {
                      return Container(
                        height: 70,
                        width: 110,
                        padding: const EdgeInsets.all(0),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Stack(
                          children: [
                            Center(
                              child: Image(
                                  height: 65,
                                  image:
                                      NetworkImage(controller.imageMoc.value)),
                            ),
                            Positioned(
                                right: -15,
                                bottom: -15,
                                child: IconButton(
                                  iconSize: 20,
                                  icon: const Icon(Icons.edit),
                                  tooltip: 'Draw Signature',
                                  onPressed: () {
                                    _showSignatureDialog(context);
                                  },
                                )),
                            Positioned(
                                left: 4,
                                bottom: 0,
                                child: MyText.labelMedium("Con dấu")),
                          ],
                        ),
                      );
                    }
                    return SizedBox.shrink();
                  }),
                ],
              ),
            ),

            const SizedBox(height: 18),

            // Pending Documents Section
            _buildSectionHeader(
              title: 'Đang Chờ Ký',
              onViewAll: () {
                Get.to(() => const WaitingListScreen());
              },
              controller: controller,
            ),
            Obx(() => _buildDocumentList(
                  documents: controller.pendingDocuments,
                  isSigned: false,
                )),

            const SizedBox(height: 32),

            // Signed Documents Section
            _buildSectionHeader(
              title: 'Tài Liệu Đã Ký',
              onViewAll: () {
                Get.to(() => const SignedListScreen());
              },
              controller: controller,
            ),
            Obx(() => _buildDocumentList(
                  documents: controller.signedDocuments,
                  isSigned: true,
                )),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Navigate to add new document screen
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required int count,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MyText.labelLarge(
              title,
              color: color,
              fontWeight: 900,
            ),
            const SizedBox(height: 4),
            Center(
              child: MyText.labelLarge(
                count.toString(),
                color: color,
                fontWeight: 900,
                fontSize: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader({
    required String title,
    required VoidCallback onViewAll,
    required HomeController controller,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: onViewAll,
              child: MyText.labelMedium('Xem thêm'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Obx(() => Row(
              children: [
                FilterChip(
                  label: const Text('Tất cả'),
                  selected: controller.currentFilter.value == 'all',
                  onSelected: (_) => controller.setFilter('all'),
                ),
                const SizedBox(width: 8),
                FilterChip(
                  label: const Text('Tuần Này'),
                  selected: controller.currentFilter.value == 'week',
                  onSelected: (_) => controller.setFilter('week'),
                ),
                const SizedBox(width: 8),
                FilterChip(
                  label: const Text('Tháng Này'),
                  selected: controller.currentFilter.value == 'month',
                  onSelected: (_) => controller.setFilter('month'),
                ),
              ],
            )),
      ],
    );
  }

  Widget _buildDocumentList({
    required List<SignRequestDocument> documents,
    required bool isSigned,
  }) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: documents.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final document = documents[index];
        return ListTile(
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isSigned
                  ? Colors.green.withOpacity(0.1)
                  : Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              isSigned ? Icons.verified : Icons.pending,
              color: isSigned ? Colors.green : Colors.orange,
              size: 24,
            ),
          ),
          title: MyText.labelLarge(
            document.name,
            fontWeight: 700,
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isSigned ? 'Ký vào ${document.signedDate}' : 'Đang chờ ký',
                style: TextStyle(
                  color: isSigned ? Colors.green : Colors.orange,
                ),
              ),
              if (document.expiryDate != null)
                Text(
                  document.expiryDate!.isBefore(DateTime.now())
                      ? 'Expired on ${document.expiryDate}'
                      : 'Expires on ${document.expiryDate}',
                  style: TextStyle(
                    fontSize: 12,
                    color: document.expiryDate!.isBefore(DateTime.now())
                        ? Colors.red
                        : Colors.grey,
                  ),
                ),
            ],
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: Icon(
                  isSigned ? Icons.visibility : Icons.edit,
                  color: Colors.blue,
                ),
                onPressed: () {
                  if (isSigned) {
                    Get.to(() => DocumentViewScreen(document: document),
                        transition: Transition.rightToLeft);
                  } else {
                    // Get.to(
                    //     () => DocumentSignScreen(
                    //           pdfUrl: document.path,
                    //           signatureUrl: controller.imageChuKy.value,
                    //           availableSignatures: [
                    //             controller.imageChuKy.value,
                    //             controller.imageChuKyTat.value,
                    //             controller.imageMoc.value,
                    //           ],
                    //           fullName: "Nguyễn Văn Vũ Linh",
                    //         ),
                    //     transition: Transition.rightToLeft);
                    // Get.to(
                    //     () => PdfViewerScreen(
                    //           pdfUrl: document.path,
                    //           signatureUrl: controller.imageChuKy.value,
                    //           // availableSignatures: [
                    //           //   controller.imageChuKy.value,
                    //           //   controller.imageChuKyTat.value,
                    //           //   controller.imageMoc.value,
                    //           // ],
                    //           // fullName: "Nguyễn Văn Vũ Linh",
                    //         ),
                    //     transition: Transition.rightToLeft);
                    Get.toNamed(AppPages.KYSO2, arguments: {
                      "pdfUrl": document.path,
                      "signatureUrl": controller.imageChuKy.value,
                      "availableSignatures": [
                        {"imageChuKy": controller.imageChuKy.value},
                        {"imageChuKyTat": controller.imageChuKyTat.value},
                        {"imageMoc": controller.imageMoc.value},
                      ],
                      "signerInfo": {
                        "fullName": "Nguyễn Văn Vũ Linh",
                        "department": "Kế Toán",
                        "position": "Kế Toán",
                        "datetime": "2023-08-01 10:00:00",
                      },
                    });
                  }
                },
              ),
              // IconButton(
              //   icon: const Icon(Icons.more_vert),
              //   onPressed: () {
              //     _showDocumentOptions(document);
              //   },
              // ),
            ],
          ),
        );
      },
    );
  }

  void _showSignatureDialog(BuildContext context) {
    final GlobalKey<SfSignaturePadState> signaturePadKey2 = GlobalKey();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Draw your signature',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Container(
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SfSignaturePad(
                  key: signaturePadKey2,
                  backgroundColor: Colors.transparent,
                  strokeColor: Colors.black,
                  minimumStrokeWidth: 2.0,
                  maximumStrokeWidth: 4.0,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton(
                    onPressed: () {
                      signaturePadKey2.currentState?.clear();
                    },
                    child: const Text('Clear'),
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      final data =
                          await signaturePadKey2.currentState?.toImage();
                      if (data != null) {
                        final bytes =
                            await data.toByteData(format: ImageByteFormat.png);
                        if (bytes != null) {
                          final base64String =
                              base64Encode(bytes.buffer.asUint8List());
                          controller.uploadSignature(base64String);
                          Get.back(result: base64String);
                        }
                      }
                    },
                    child: const Text('Save'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
