import 'package:get/get.dart';
import 'package:tvumobile/app/features/danhgia/danhgia_controller.dart';
import 'package:tvumobile/app/features/giangvien/congvan/chitiet_congvan_controller.dart';
import 'package:tvumobile/app/features/kyso/kyso_controller.dart';
import 'package:tvumobile/app/features/kyso/v2/pdf_viewer_controller.dart';
import 'package:tvumobile/app/services/api_provider.dart';

class AppBinding extends Bindings {
  @override
  void dependencies() async {
    //Get.lazyPut<ConnectivityController>(() => ConnectivityController());
    //Get.put(ConnectivityController(), permanent: true);
    Get.put(ApiProvider(), permanent: true);
    //Get.put(UrlHandleService(), permanent: true);
    Get.lazyPut<ChiTietCongVanController>(
      () => ChiTietCongVanController(),
    );
    Get.lazyPut<DanhGiaController>(
      () => DanhGiaController(),
    );
    Get.lazyPut<KysoController>(
      () => KysoController(),
    );
    // Get.lazyPut<PdfViewerController>(
    //   () => PdfViewerController(),
    // );
  }
}
