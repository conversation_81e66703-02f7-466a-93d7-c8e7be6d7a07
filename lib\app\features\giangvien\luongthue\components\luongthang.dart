

import 'package:flutter/material.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/luongthuethang_controller.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

Widget buildLuongThangInfo(LuongThueController controller, ThemeData theme) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Center(
          child: Column(
            children: [
              const Divider(),
              MyText.titleMedium(
                "Thông tin lương tháng ${controller.currentMonth}/${controller
                    .currentYear}".toUpperCase(),
                fontWeight: 900,
              ),
              const Divider(),
            ],
          ),
        ),
      ),
      if (controller.luongThangInfo.isEmpty)
        controller.currentDataAvaliable.value ?
        SizedBox(
          height: 200,
          child: SkeletonListView(),
        ) : Center(child: MyText.titleLarge("KHÔNG CÓ DỮ LIỆU")),
      if (controller.luongThangInfo.isNotEmpty)
        _buildLuongThangDetails(controller, theme),
    ],
  );
}

Widget _buildLuongThangDetails(LuongThueController controller, ThemeData theme) {
  final luongThang = controller.luongThangInfo.first;
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Center(
        child: Column(
          children: [
            const MyText.titleMedium("THỰC LÃNH"),
            MyText.titleLarge(
              controller.viFormat.format(luongThang.tonglanh),
              fontWeight: 900,
              fontSize: 24,
            ),
          ],
        ),
      ),
      MySpacing.height(16),
      _buildSectionHeader(theme, "CÁC KHOẢN CỘNG"),
      _buildLuongItem(controller, theme, "Lương tháng (HSL*LCB):", luongThang.thanhtien!.toDouble()),
      _buildLuongItem(controller,
          theme, "PC thâm niên nhà giáo:", luongThang.thanhtienPctnng!.toDouble()),
      _buildLuongItem(controller, theme, "Phụ cấp ưu đãi:", luongThang.phucapuudai!.toDouble()),
      _buildLuongItem(controller,
          theme, "Truy lãnh lương mới:", luongThang.tlluongvienchucmoi!.toDouble()),
      _buildLuongItem(controller, theme, "Truy lãnh nâng lương:", luongThang.tlnangluong!.toDouble()),
      _buildLuongItem(controller, theme, "TL nâng % hợp đồng:",
          luongThang.truylanhnangphantramhopdong!.toDouble()),
      _buildLuongItem(controller, theme, "TL Phụ cấp chức vụ:", luongThang.truylanhPccv!.toDouble()),
      _buildLuongItem(controller,
          theme, "TL PC thâm niên nhà giáo:", luongThang.truylanhPctnng!.toDouble()),
      _buildLuongItem(controller, theme, "Truy lãnh khác:", luongThang.truylanhkhac!.toDouble()),
      MySpacing.height(16),
      _buildSectionHeader(theme, "CÁC KHOẢN TRỪ"),
      _buildLuongItem(controller, theme, "Trừ BHXH:", luongThang.bhxh!.toDouble()),
      _buildLuongItem(controller, theme, "Trừ BHYT:", luongThang.bhyt!.toDouble()),
      _buildLuongItem(controller, theme, "Trừ BHTN:", luongThang.bhtn!.toDouble()),
      _buildLuongItem(controller, theme, "Trừ KPCD:", luongThang.kpcd!.toDouble()),
      _buildLuongItem(controller, theme, "Trừ Tiền quỹ:", luongThang.tienquy!.toDouble()),
      _buildLuongItem(controller, theme, "Thuế Thu nhập cá nhân:", luongThang.thueTncn!.toDouble()),
      _buildLuongItem(controller, theme, "Trừ Truy thu khác:", luongThang.truythukhac!.toDouble()),
    ],
  );
}

Widget _buildSectionHeader(ThemeData theme, String title) {
  return Align(
    alignment: Alignment.centerLeft,
    child: MyContainer(
      paddingAll: 6,
      width: double.infinity,
      color: theme.secondaryHeaderColor,
      child: MyText.titleMedium(title),
    ),
  );
}

Widget _buildLuongItem(LuongThueController controller, ThemeData theme, String label, double value) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4.0),
    child: Row(
      children: [
        SizedBox(
          width: 200,
          child: MyText.titleMedium(label),
        ),
        MyText.titleMedium(controller.viFormat.format(value)),
      ],
    ),
  );
}