import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/features/tasky/views/taskyinboxview.dart';
import 'package:tvumobile/app/features/tasky/views/taskyoverview.dart';
import 'package:tvumobile/app/features/tasky/views/taskytasksview.dart';

class TaskyDashboardController extends GetxController {
  // RxInt currentIndex = 0.obs;

  // // final List<Widget> pages = [
  // //   TaskyOverview(),
  // //   TaskyTaskView(),
  // //   TaskyInboxView(),
  // //   TaskyInboxView(), // Thay bằng () => AccountView() nếu có
  // //   //const AccountView()
  // // ];

  // final List<Widget Function()> pageBuilders = [
  //   () => TaskyOverview(),
  //   () => TaskyTaskView(),
  //   () => TaskyInboxView(),
  //   () => TaskyInboxView(),
  // ];

  // onChanged(int index) {
  //   currentIndex.value = index;
  //   // update();
  // }
}
