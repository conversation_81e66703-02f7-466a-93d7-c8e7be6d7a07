import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/features/danhgia/widgets/ds_taixe.dart';
import 'package:tvumobile/app/shared/appbar.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/extensions/extensions.dart';
import 'danhgia_controller.dart';

class DanhGiaChatLuong extends GetView<DanhGiaController> {
  const DanhGiaChatLuong({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return PopScope(
      canPop: false,
      // ignore: deprecated_member_use
      onPopInvoked: (bool didPop) {
        if (didPop) {
          return;
        }
        Get.offAllNamed(AppPages.HOME);
      },
      child: Scaffold(
        appBar: appBarTitle(context, "Đánh giá chất lượng".lang()),
        body: Container(
          padding: const EdgeInsets.all(16),
          child: ListView(
            children: [
              15.verticalSpace,
              Center(
                child: Obx(() => QrImageView(
                      data: controller.donvi.value,
                      size: 150.w,
                      version: QrVersions.auto,
                      backgroundColor: theme.colorScheme.onSurface,
                    )),
              ),
              Center(
                child: Obx(() => MyText.headlineSmall(
                      controller.donvi.value,
                      color: mlPrimaryColor,
                    )),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  15.verticalSpace,
                  MyText.titleMedium(
                    "Mức độ hài lòng của bạn:".lang(),
                    fontWeight: 600,
                  ),
                  15.verticalSpace,
                  Center(
                    child: RatingBar.builder(
                      initialRating: 0,
                      direction: Axis.horizontal,
                      itemCount: 5,
                      maxRating: 5,
                      minRating: 1,
                      itemSize: 52.w,
                      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                      itemBuilder: (context, _) => const Icon(
                        Icons.star,
                        color: Colors.amber,
                      ),
                      onRatingUpdate: (rating) {
                        controller.onRatingChanged(rating);
                      },
                      updateOnDrag: true,
                    ),
                  ),
                ],
              ),
              Column(
                children: [
                  Obx(() {
                    if (controller.loaidanggia.value == "dv") {
                      return 15.verticalSpace;
                    } else {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          15.verticalSpace,
                          MyText.titleMedium(
                            "Nhân viên lái xe:".lang(),
                            fontWeight: 600,
                          ),
                          if (controller.dstaixe.isNotEmpty)
                            DsTaiXeBox(
                              dstaixe: controller.dstaixe,
                              selected_changed: controller.onDstaixeChanged,
                            ),
                          if (controller.dstaixe.isEmpty)
                            SkeletonLine(
                              style: SkeletonLineStyle(
                                  height: 16,
                                  width: 64,
                                  borderRadius: BorderRadius.circular(8)),
                            )
                        ],
                      );
                    }
                  }),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      15.verticalSpace,
                      MyText.titleMedium(
                        "Ý kiến của bạn về công việc, thái độ,...:".lang(),
                      ),
                      15.verticalSpace,
                      TextFormField(
                        decoration: InputDecoration(
                          border: const OutlineInputBorder(),
                          labelText: 'Vui lòng cho ý kiến của bạn'.lang(),
                          labelStyle: theme.textTheme.labelLarge
                              ?.copyWith(color: Colors.grey),
                        ),
                        onChanged: (txt) {
                          controller.onYKienChanged(txt);
                        },
                        autofillHints: [
                          "Tuyệt vời!".lang(),
                          "Hài lòng với việc được phục vụ".lang()
                        ],
                      ),
                    ],
                  ),
                  20.verticalSpace,
                  MyButton.large(
                      child: MyText.titleMedium(
                        "Gủi phản hồi".lang().toUpperCase(),
                        color: theme.colorScheme.onPrimary,
                        fontWeight: 700,
                      ),
                      onPressed: () {
                        controller.guiDanhgia();
                      })
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
