import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxetaixe.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/giaydieuxe.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_home_controller.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:tvumobile/app/utils/extensions/extensions.dart';

class DieuXeDetailController extends GetxController {
  ApiProvider apiProvider = Get.find();

  // Khai báo form chọn nhân viên lái xe
  final formKeyXeNVLai = GlobalKey<FormBuilderState>();
  final isEditNVLai = false.obs;
  final phonghctcduyet = false.obs;
  final thuexengoai = false.obs;
  Map<String, dynamic> formData = {};
  // Danh sách xe và nhân viên lái xe
  final xeList = <Xe>[].obs;
  final nvLaiList = <NguoiTao>[].obs;
  // Giá trị hiện tại của dropdown
  Rx<Xe?> selectedXe = Rx<Xe?>(null);
  Rx<NguoiTao?> selectedNvLai = Rx<NguoiTao?>(null);

  final loadpdx = false.obs;
  final isEditHDThueXe = false.obs;
  final formKeyHDThueXe = GlobalKey<FormBuilderState>();
  Map<String, dynamic> formDataHDThueXe = {};

  late String? pdxId;
  late String? currentView;

  // Xác định số lượng tab tối đa dựa trên laPhieuGhep
  final RxBool phieughep = false.obs;
  late int maxTabs;

  final RxString phanQuyen = "".obs; // Lưu phanQuyen trong controller
  final RxString trangthai = "".obs; // Lưu trangthai trong controller
  final RxString xoa = "".obs; // Hiển thị nút xóa trên AppBar

  GiayDieuXe pdx = GiayDieuXe();
  RxList<GiayDieuXe> listpdxcon = <GiayDieuXe>[].obs;
  RxList<DieuXeTaiXe> listdxtx = <DieuXeTaiXe>[].obs;
  UserInfo? userinfo = LocalStorageServices.getUserInfo();
  final PageController pageController = PageController(); // Thêm PageController
  final PageController tabController = PageController(); // Thêm TabController
  final PageController tabHctcDuyetController = PageController();

  // Khai báo mở rộng nội dung
  // final RxBool isExpandedNoiDung = false.obs;
  // final RxBool isExpandedNoiCT = false.obs;
  // final RxBool isExpandedDiaDiemDi = false.obs;
  // final RxBool isExpandedDiaDiemVe = false.obs;
  // final RxBool isExpandedVienChuc = false.obs;
  // final RxBool isExpandedGhiChu = false.obs;
  // Theo dõi trang (0: thông tin, 1: danh sách)
  late RxInt currentPage = 0.obs;
  // Theo dõi tab trong tt (0: đi, 1: về)
  late RxInt currentTab = 0.obs;

  @override
  void onInit() {
    super.onInit();
    if (kDebugMode) {
      print("DieuXeDetailController onInit");
    }

    // Dùng cho toggle vuốt thông tin di chuyển
    tabController.addListener(() {
      int tab = tabController.page?.round() ?? 0;
      if (tab != currentTab.value) {
        currentTab.value = tab; // Cập nhật trang khi vuốt
      }
    });
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    if (kDebugMode) {
      print("DieuXeDetailController onReady");
    }
    pdxId = Get.parameters['pdxId'] ?? "";
    currentView = Get.parameters['currentview'] ?? "";
    trangthai.value = Get.parameters['trangthai'] ?? "";
    maxTabs = trangthai.toLowerCase().contains("phòng hc-tc đã duyệt") ? 2 : 1;

    // Sử dụng để xác định số tab
    phieughep.value = Get.parameters['phieughep']!.toBool();
    maxTabs = phieughep.value ? 2 : maxTabs;

    await checkPhanQuyen();
    await getChiTietDX();
    await getXeNvLai();
    // print("daidiemkhoihanh length: ${pdx.diaDiemKhoiHanh!.length}");
    // print("noidung length: ${pdx.noiDung!.length}");
    // getDNPInfo();
  }

  @override
  void onClose() {
    pageController.dispose(); // Giải phóng tài nguyên khi controller đóng
    super.onClose();
  }

  // Lấy chi tiết phiếu điều xe
  getChiTietDX() async {
    final chitietpdx = await apiProvider.getChiTietPhieuDX(pdxId: pdxId);
    if (chitietpdx != null) {
      pdx = chitietpdx[0];
      loadpdx.value = true;
      xoa.value = pdx.trangThai!;
      // Lọc các phiếu con (có ParentId == pdx.id)
      listpdxcon.value = chitietpdx.where((p) => p.parentId == pdx.id).toList();

      if (pdx.trangThai!.toLowerCase().contains("phòng hc-tc đã duyệt")) {
        phonghctcduyet.value = true;
        thuexengoai.value = false;
        selectedXe.value = pdx.ttDieuXe!.xe;
        selectedNvLai.value = pdx.ttDieuXe!.taiXe;
        isEditNVLai.value = true;
        phieughep.value ? maxTabs = 3 : maxTabs = 2;
      } else if (pdx.trangThai!.toLowerCase().contains("thuê xe ngoài")) {
        phieughep.value ? maxTabs = 3 : maxTabs = 2;
        if (pdx.hopDongThueXe != null) isEditHDThueXe.value = true;
      }

      if (pdx.tuNgay != null && pdx.denNgay != null) {
        final ngayDi = pdx.tuNgay!;
        final ngayVe = pdx.denNgay!;
        getDXTXTheoNgay(ngayDi);
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy phiếu điều xe với ID: ${pdx.id}',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
    update();
  }

  // Lấy điều xe tài xế theo ngày đi ngày về
  getDXTXTheoNgay(DateTime ngayDi) async {
    final dxtxtheongay = await apiProvider.getDXTXTheoNgay(ngayDi: ngayDi);
    if (dxtxtheongay != null) {
      listdxtx.value = dxtxtheongay;
      update();
    }
  }

  // Kiểm tra xem xe có bị chiếm dụng không
  bool isXeDisabled(Xe xe) {
    return listdxtx.any((dxtx) => dxtx.xe!.id == xe.id);
  }

  // Kiểm tra xem tài xế có bị chiếm dụng không
  bool isTaiXeDisabled(NguoiTao taiXe) {
    return listdxtx.any((dxtx) => dxtx.taiXe!.id == taiXe.id);
  }

  // Thêm phương thức để làm mới dữ liệu khi quay về
  void refreshData() async {
    if (kDebugMode) {
      print("Refreshing ChiTietDNPController data");
    }
    await getChiTietDX();
  }

  // Lấy thông tin xe và nhân viên lái
  Future<void> getXeNvLai() async {
    try {
      final response = await apiProvider.getDsXeNvLai();
      xeList.value = response.xe!;
      nvLaiList.value = response.nvLai!;
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể lấy danh sách xe và nhân viên lái xe: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
    update();
  }

  // Cập nhật nhân viên lái xe khi chọn xe
  // void updateNvLaiFromXe(Xe? xe) {
  //   if (xe != null && xe.nvLai != null) {
  //     // Tìm NguoiTao trong nvLaiList có id khớp với xe.nvLai.id
  //     final matchedNvLai =
  //         nvLaiList.firstWhereOrNull((nv) => nv.id == xe.nvLai!.id);
  //     if (matchedNvLai != null) {
  //       selectedNvLai.value = matchedNvLai;
  //       formData['nhanVienLaiXe'] = matchedNvLai.toJson();
  //       formKeyXeNVLai.currentState?.fields['nhanVienLaiXe']
  //           ?.didChange(matchedNvLai);
  //     } else {
  //       selectedNvLai.value = null;
  //       formData['nhanVienLaiXe'] = null;
  //       formKeyXeNVLai.currentState?.fields['nhanVienLaiXe']?.didChange(null);
  //     }
  //   } else {
  //     selectedNvLai.value = null;
  //     formData['nhanVienLaiXe'] = null;
  //     formKeyXeNVLai.currentState?.fields['nhanVienLaiXe']?.didChange(null);
  //   }
  //   update();
  // }
  void updateNvLaiFromXe(Xe? xe) {
    if (xe != null) {
      // Kiểm tra xem xe có tài xế mặc định không
      if (xe.nvLai != null) {
        // Tìm NguoiTao trong nvLaiList có id khớp với xe.nvLai.id
        final matchedNvLai =
            nvLaiList.firstWhereOrNull((nv) => nv.id == xe.nvLai!.id);
        if (matchedNvLai != null && !isTaiXeDisabled(matchedNvLai)) {
          // Chỉ gán tài xế nếu tài xế không bị chiếm dụng
          selectedNvLai.value = matchedNvLai;
          formData['nhanVienLaiXe'] = matchedNvLai.toJson();
          formKeyXeNVLai.currentState?.fields['nhanVienLaiXe']
              ?.didChange(matchedNvLai);
        } else {
          // Nếu tài xế bị chiếm dụng, để trống dropdown tài xế
          selectedNvLai.value = null;
          formData['nhanVienLaiXe'] = null;
          formKeyXeNVLai.currentState?.fields['nhanVienLaiXe']?.didChange(null);
        }
      } else {
        // Nếu xe không có tài xế mặc định, để trống
        selectedNvLai.value = null;
        formData['nhanVienLaiXe'] = null;
        formKeyXeNVLai.currentState?.fields['nhanVienLaiXe']?.didChange(null);
      }
    } else {
      // Nếu không chọn xe, để trống dropdown tài xế
      selectedNvLai.value = null;
      formData['nhanVienLaiXe'] = null;
      formKeyXeNVLai.currentState?.fields['nhanVienLaiXe']?.didChange(null);
    }

    update();
  }

  // Kiểm tra phân quyền
  Future<void> checkPhanQuyen() async {
    try {
      var result = await apiProvider.checkPhanQuyenDieuXe();
      if (result.contains("taichinh") || result.contains("bgh")) {
        phanQuyen.value = "truongdonvi";
      } else {
        phanQuyen.value = result;
      }
      // phanQuyen.value = "phonghcth";
    } catch (e) {
      if (kDebugMode) {
        print("Error checking LanhDao or BGH: $e");
      }
    }
    update();
  }

  // Chuyển đơn và thu hồi phiếu điều xe chờ duyệt
  Future<bool> chuyenDieuXe() async {
    if (pdxId != null && pdxId!.isNotEmpty) {
      String success =
          await apiProvider.chuyenPhieuDieuXe(pdxId: pdxId, waiting: true);

      // CHUYỂN PHIẾU ĐIỀU XE CHỜ DUYỆT
      if (success.toLowerCase().contains("đã chuyển")) {
        Get.snackbar(
          'Thành công',
          'Đã chuyển giấy đề nghị điều xe.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );
        refreshData(); // Đợi refreshData hoàn tất
        return true;
      }
      // THU HỒI PHIẾU ĐIỀU XE CHỜ DUYỆT ĐÃ CHUYỂN
      else if (success.toLowerCase().contains("đã thu hồi")) {
        Get.snackbar(
          'Thành công',
          'Đã thu hồi giấy đề nghị điều xe.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );
        refreshData(); // Đợi refreshData hoàn tất
        Get.find<DieuXeHomeController>().refreshData();
        return true;
      }
      // TRƯỞNG ĐƠN VỊ THU HỒI DUYỆT PHIẾU ĐIỀU XE ĐÃ DUYỆT
      else if (success.toLowerCase().contains("đã hủy")) {
        Get.snackbar(
          'Thành công',
          'Đã thu hồi duyệt giấy đề nghị điều xe.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );
        refreshData(); // Đợi refreshData hoàn tất
        return true;
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể chuyển giấy đề nghị điều xe. Vui lòng thử lại.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
        return false;
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy ID giấy đề nghị điều xe.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Thêm phương thức xóa phiếu đề nghị điều xe
  Future<bool> deleteDieuXe() async {
    if (pdxId != null && pdxId!.isNotEmpty) {
      try {
        final success = await apiProvider.deleteDieuXe(pdxId!.toInt());
        if (success != null) {
          Map<String, dynamic> response = jsonDecode(success);
          if (response["status"] == 200) {
            Get.find<DieuXeHomeController>().refreshData(); // Làm mới danh sách
            Get.back();
            Get.snackbar(
              'Thành công',
              'Đã xóa đề nghị điều xe.',
              snackPosition: SnackPosition.TOP,
              backgroundColor: Colors.green.withOpacity(0.8),
              colorText: Colors.white,
            );
            return true;
          } else {
            Get.snackbar(
              'Lỗi',
              'Không thể xóa phiếu điều xe. Vui lòng thử lại.',
              snackPosition: SnackPosition.TOP,
              backgroundColor: Colors.red.withOpacity(0.8),
              colorText: Colors.white,
            );
            return false;
          }
        } else {
          Get.snackbar(
            'Lỗi',
            'Không thể xóa phiếu điều xe. Vui lòng thử lại.',
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.red.withOpacity(0.8),
            colorText: Colors.white,
          );
          return false;
        }
      } catch (e) {
        Get.snackbar(
          'Lỗi',
          'Đã xảy ra lỗi khi xóa: $e',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
        return false;
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy ID phiếu điều xe.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Duyệt phiếu điều xe (phòng HCTC duyệt)
  Future<void> duyetDieuXeHCTC() async {
    Map<String, dynamic> postData = {};

    // Lấy thông tin từ formData
    if (formData["phonghctcduyet"]) {
      postData["Xe_ID"] = formData["xe"]?.toString() ?? '';
      postData["TaiXe_ID"] = formData["nhanVienLaiXe"]?.toString() ?? '';
      postData["SoLenhDieuXe"] = formData["solenhdieuxe"]?.toString() ?? '';
      postData["CanBoDiCung"] = formData["canbodicung"]?.toString() ?? '';
      postData["SoKmDuKien"] = formData["sokmdukien"]?.toString() ?? '';
      postData["GhiChu"] = formData["ghichu"]?.toString() ?? '';
      postData["NgayDi"] = formData["ngaydi"]?.toString() ?? ''; // Timestamp
      postData["NgayVe"] = formData["ngayve"]?.toString() ?? ''; // Timestamp
    } else if (formData["thuexengoai"]) {
      postData["GhiChu"] = formData["ghichuthuexe"]?.toString() ?? '';
    }

    if (kDebugMode) {
      print(
          "Dieu Xe Post Data: $postData, phonghctc ${formData["phonghctcduyet"]}, thuexengoai ${formData["thuexengoai"]}");
    }

    String? success =
        await apiProvider.duyetDieuXeHcTc(pdxId: pdxId!, formValue: postData);

    if (success != null) {
      Map<String, dynamic> response = jsonDecode(success);

      if (response['message'].toString().contains("Duyệt phiếu")) {
        Get.snackbar(
          'Thành công',
          'Đã duyệt giấy đề nghị điều xe.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );
        refreshData();
        Get.find<DieuXeHomeController>().refreshData();
        // Get.back();
      } else if (response['message'].toString().contains("Điều chỉnh")) {
        Get.snackbar(
          'Thành công',
          'Đã điều chỉnh giấy đề nghị điều xe.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );
        refreshData();
        // Get.back();
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể chuyển giấy đề nghị điều xe. Vui lòng thử lại.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy ID giấy đề nghị điều xe.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }

  // Duyệt phiếu điều xe (trưởng đơn vị duyệt)
  Future<bool> duyetDieuXeDV() async {
    if (pdxId != null && pdxId!.isNotEmpty) {
      String success =
          await apiProvider.duyetDieuXeDV(pdxId: pdxId, waiting: true);

      if (success.toLowerCase().contains("đã duyệt")) {
        refreshData(); // Đợi refreshData hoàn tất
        Get.find<DieuXeHomeController>().refreshData();
        Get.back();
        Get.snackbar(
          'Thành công',
          'Đã duyệt đề nghị điều xe.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );

        return true;
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể duyệt đề nghị điều xe. Vui lòng thử lại.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
        return false;
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy ID đề nghị điều xe.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Trả phiếu điều xe (Không duyệt)
  Future<bool> traDieuXe(String lyDo) async {
    if (pdxId != null && pdxId!.isNotEmpty) {
      String success = await apiProvider.traPhieuDieuXe(
        pdxId: pdxId,
        lydo: lyDo,
        waiting: true,
      );

      if (success.toLowerCase().contains("đã trả phiếu")) {
        refreshData(); // Đợi refreshData hoàn tất
        Get.find<DieuXeHomeController>().refreshData();
        Get.back();
        Get.snackbar(
          'Thành công',
          'Đã hủy đề nghị điều xe.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );

        return true;
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể hủy đề nghị điều xe. Vui lòng thử lại.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
        return false;
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy ID đề nghị điều xe.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Điều chỉnh phiếu điều xe
  Future<bool> dieuchinhDieuXe(String lyDo) async {
    if (pdxId != null && pdxId!.isNotEmpty) {
      String success = await apiProvider.dieuchinhPhieuDieuXe(
        pdxId: pdxId,
        lydo: lyDo,
        waiting: true,
      );

      if (success.toLowerCase().contains("đã yêu cầu")) {
        refreshData(); // Đợi refreshData hoàn tất
        // Get.find<DuyetDNPController>().refreshOnResume(); // Làm mới danh sách
        Get.back();
        Get.snackbar(
          'Thành công',
          'Đã yêu cầu điều chỉnh đề nghị điều xe.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );

        return true;
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể yêu cầu điều chỉnh đề nghị điều xe. Vui lòng thử lại.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
        return false;
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy ID đề nghị điều xe.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Tạo mới và chỉnh sửa hợp đồng thuê xe
  Future<void> themsuaHopDongThueXe() async {
    Map<String, dynamic> postData = {};

    // Lấy thông tin từ formData
    postData["DonViThueXe"] = pdx.donVi!.id.toString();
    postData["DonViChoThueXe"] = formDataHDThueXe["donvichothuexe"]?.toString();
    postData["LoaiXe"] = formDataHDThueXe["loaixe"]?.toString();
    postData["BienSo"] = formDataHDThueXe["bienso"]?.toString() ?? '';
    postData["NoiDung"] = formDataHDThueXe["noidunghopdong"]?.toString() ?? '';
    postData["GiaTriHopDong"] =
        formDataHDThueXe["giatrihopdong"]?.toString() ?? '';
    postData["TuNgay"] = (formDataHDThueXe["thoigianthuexe"] as DateTimeRange?)
            ?.start
            .toIso8601String() ??
        '';
    postData["DenNgay"] = (formDataHDThueXe["thoigianthuexe"] as DateTimeRange?)
            ?.end
            .toIso8601String() ??
        '';
    postData["GhiChu"] = formDataHDThueXe["ghichuthuexe"]?.toString() ?? '';

    if (kDebugMode) {
      print("Hop Dong Thue Xe Post Data: $postData");
    }

    String? success;

    if (isEditHDThueXe.value) {
      success =
          await apiProvider.updateHDThueXe(pdxId: pdx.id!, formValue: postData);
    } else {
      success =
          await apiProvider.createHDThueXe(pdxId: pdx.id!, formValue: postData);
    }

    if (success != null) {
      Map<String, dynamic> response = jsonDecode(success);

      if (response['message'].toString().contains("Tạo thành công")) {
        Get.snackbar(
          'Thành công',
          'Đã thêm hợp đồng thuê xe.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );
        refreshData();
        // Get.back();
      } else if (response['message'].toString().contains("Sửa thành công")) {
        Get.snackbar(
          'Thành công',
          'Đã điều chỉnh hợp đồng thuê xe.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );
        refreshData();
        // Get.back();
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể chuyển giấy đề nghị điều xe. Vui lòng thử lại.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy ID giấy đề nghị điều xe.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }

  // Xóa hợp đồng thuê xe
  Future<bool> deleteHopDongThueXe() async {
    final success = await apiProvider.deleteHDThueXe(pdxId!.toInt());
    if (success != null) {
      Map<String, dynamic> response = jsonDecode(success);
      if (response["message"].toString().contains("Xóa thành công")) {
        refreshData();
        Get.snackbar(
          'Thành công',
          'Đã xóa đề nghị điều xe.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );
        return true;
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể xóa phiếu điều xe. Vui lòng thử lại.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
        return false;
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không thể xóa phiếu điều xe. Vui lòng thử lại.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Hộp thoại xác nhận
  void showConfirmDialog(
      String action, String duyet, String? middleText, Function onConfirm) {
    Get.defaultDialog(
      title: "Xác nhận",
      middleText: middleText == null || middleText.isEmpty
          ? "Bạn có chắc chắn muốn $action đề nghị điều xe$duyet?"
          : middleText,
      confirm: TextButton(
        onPressed: () {
          onConfirm();
          Get.back(); // Đóng dialog sau khi xác nhận
        },
        style: TextButton.styleFrom(
          backgroundColor: Colors.green, // Màu xanh cho nút "OK"
        ),
        child: Text(
          "Có",
          style: TextStyle(color: Colors.white),
        ),
      ),
      cancel: TextButton(
        onPressed: () {
          Get.back(); // Đóng dialog khi nhấn "Hủy"
        },
        style: TextButton.styleFrom(
          backgroundColor: Colors.red, // Màu đỏ cho nút "Hủy"
        ),
        child: Text(
          "Không",
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  String getHoTen(NguoiTao? nguoi) {
    if (nguoi == null) return "";
    var ht = "";
    ht = nguoi.ho!.isEmpty ? "" : "${nguoi.ho} ";
    ht += nguoi.tenDem!.isEmpty ? "" : "${nguoi.tenDem} ";
    ht += nguoi.ten!.isEmpty ? "" : nguoi.ten.toString();
    return ht;
  }
}
