
import 'package:tvumobile/app/deeplinkparser.dart';

class MyConstantData {
  final double containerRadius;
  final double cardRadius;
  final double buttonRadius;

  MyConstantData(
      {this.containerRadius = 4, this.cardRadius = 4, this.buttonRadius = 4});
}

class MyConstant {
  static MyConstantData _constant = MyConstantData();

  static MyConstantData get constant => _constant;

  static setConstant(MyConstantData constantData) {
    _constant = constantData;
  }
}
Uri initialUri = Uri();
String cvCode = "";
String cvPath = "";
DeepLinkParser? deepLinkParser;
bool launchFromURL = false;
enum NotificationType
{
  CongVan,
  TaoMoiNghiPhep,
  DonNghiPhepCanDuyet,
  DonNghiPhepDaDuyet,
  DonNghiPhepDaThuHoi,
  DonNghiPhepTraVe,
  ThuTucHanhChinhGV,
  ThuTucHanhChinhSV,
  CongLenh,
  GiaoViec,
  DieuXe
}