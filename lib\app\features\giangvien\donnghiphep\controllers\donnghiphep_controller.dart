import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:logger/web.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepinfo.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/chitietdnp_controller.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/nghiphep_home_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/date_time_extension.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class DonNghiPhepController extends GetxController {
  ApiProvider apiProvider = Get.find();
  late DonNghiPhepInfo donNghiPhepInfo;
  final formKey = GlobalKey<FormBuilderState>();
  Rx<DateTime> startDate = DateTime.now().obs;
  Rx<DateTime> endDate = DateTime.now().obs;

  String reason = '';
  int leaveType = 0;
  LanhDaoDonviList? supervisor;
  String supervisorName = "";
  final leaveTypes = <LoaiNghiPhep>[].obs;
  final supervisors = <LanhDaoDonviList>[].obs;
  UserInfo? userInfo = LocalStorageServices.getUserInfo();
  final chuyendon = false.obs;
  Map<String, dynamic> thongtinVosinh = {};
  Map<String, dynamic> thongtinDasinh = {};
  Map<String, dynamic> thongtinSauOmdau = {};
  Map<String, dynamic> thongtinTrocap = {};
  final danghi = 0.0.obs;
  final conlai = 0.0.obs;
  final donvi = ''.obs;
  final buoi = 'CaNgay'.obs;
  final extraThongtin = false.obs;

  // Map<String, dynamic> formData = {
  //   'ngaynghi': DateTimeRange(start: DateTime.now(), end: DateTime.now()),
  //   'ngaynghi1': DateTime.now().add(const Duration(days: 1)),
  //   'nvs': DateTime.now(),
  //   'loai': null,
  //   'diem': 'a'
  // };

  Map<String, dynamic> formData = {
    'ngaynghi': DateTimeRange(start: DateTime.now(), end: DateTime.now()),
    'ngaynghi1': DateTime.now(),
    'nvs': DateTime.now(),
    'loai': '',
    'diem': 'a',
    'lydo': '',
    'ghichu': '',
    'songaynghi': '',
    'songaythuc': '',
    'buoi': 'CaNgay',
  };

  @override
  Future<void> onInit() async {
    super.onInit();
    danghi.value = -1;
    conlai.value = -1;
    // Kiểm tra nếu đang chỉnh sửa đơn nghỉ phép
    if (Get.arguments != null && Get.arguments['id'] != null) {
      final int donNghiPhepId = Get.arguments['id'];
      await loadDonNghiPhep(donNghiPhepId);
    } else {
      getDNPInfo(2);
    }
  }

  @override
  void onReady() {
    // if (formData['id'] == null) {
    //   getDNPInfo(2); // Chỉ gọi getDNPInfo nếu không ở chế độ chỉnh sửa
    // }
  }

  // Tải dữ liệu đơn nghỉ phép theo ID
  Future<void> loadDonNghiPhep(int id) async {
    String dnpId = id.toString();
    final response = await apiProvider.getChiTietDonNghiPhep(dnpId: dnpId);
    if (response != null && response.isNotEmpty) {
      // Đảm bảo getDNPInfo hoàn thành trước khi xử lý
      await getDNPInfo(response[0].loaiNghiPhep!.id!);

      // Kiểm tra nếu leaveTypes rỗng
      if (leaveTypes.isEmpty) {
        Get.showSnackbar(GetSnackBar(
          title: 'Lỗi',
          message: 'Không thể tải danh sách loại nghỉ phép',
          duration: Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        ));
        return;
      }

      // Tìm loại nghỉ phép hoặc sử dụng giá trị mặc định an toàn
      final selectedLeaveType = leaveTypes.firstWhereOrNull(
            (l) => l.id == response[0].loaiNghiPhep!.id,
          ) ??
          leaveTypes.firstWhereOrNull((l) => l.id == 2);

      // Cập nhật formData với dữ liệu đơn
      formData.assignAll({
        'id': response[0].id,
        'nguoigui': userInfo,
        'loai': selectedLeaveType,
        'lydo': response[0].lyDo,
        'buoi': response[0].buoi ?? 'CaNgay',
        'ngaynghi': response[0].tuNgay != null && response[0].denNgay != null
            ? DateTimeRange(
                start: response[0].tuNgay!, end: response[0].denNgay!)
            : DateTimeRange(start: DateTime.now(), end: DateTime.now()),
        'ngaynghi1': response[0].tuNgay ?? DateTime.now(),
        'songaynghi': response[0].soNgay?.toString(),
        'songaythuc': response[0].soNgay?.toString(),
        'ghichu': response[0].ghiChu,
        'chuyen': false,
        'lanhdao': null,
      });

      // Cập nhật loại nghỉ phép và thông tin bổ sung
      leaveType = response[0].loaiNghiPhep!.id ?? 2;
      buoi.value = response[0].buoi ?? 'CaNgay';
      extraThongtin.value =
          [7, 8, 9, 10].contains(response[0].loaiNghiPhep!.id);

      // Điền thông tin bổ sung cho các loại nghỉ phép đặc biệt
      if (extraThongtin.value) {
        switch (response[0].loaiNghiPhep!.id) {
          case 7: // Nghỉ vợ sinh
            formData.addAll({
              'nvs': response[0].ngaySinhCon,
              'stpt': response[0].sinhThuongPhauThuat ?? 'SinhThuong',
              'socon': int.parse(response[0].soCon?.toString() ?? '1'),
              'bv': response[0].taiBenhVien ?? '',
              'diem': response[0].diemLuatBhxh ?? 'a',
            });
            thongtinVosinh = {
              'nvs': response[0].ngaySinhCon,
              'stpt': response[0].sinhThuongPhauThuat ?? 'SinhThuong',
              'socon': int.parse(response[0].soCon?.toString() ?? '1'),
              'bv': response[0].taiBenhVien ?? '',
              'diem': response[0].diemLuatBhxh ?? 'a',
            };
            break;
          case 8: // Nghỉ dưỡng sức sau thai sản
            formData.addAll({
              'nvs': response[0].ngaySinhCon,
              'stpt': response[0].sinhThuongPhauThuat ?? 'SinhThuong',
              'conthu': response[0].conThu ?? '1',
              'tungay': response[0].thaiSanDauOmTuNgay,
              'denngay': response[0].thaiSanDauOmDenNgay,
            });
            thongtinDasinh = {
              'nvs': response[0].ngaySinhCon,
              'stpt': response[0].sinhThuongPhauThuat ?? 'SinhThuong',
              'conthu': response[0].conThu ?? '1',
              'tungay': response[0].thaiSanDauOmTuNgay,
              'denngay': response[0].thaiSanDauOmDenNgay,
            };
            break;
          case 9: // Nghỉ sau ốm đau
            formData.addAll({
              'tungay': response[0].tuNgay,
              'denngay': response[0].denNgay,
            });
            thongtinSauOmdau = {
              'tungay': response[0].thaiSanDauOmTuNgay,
              'denngay': response[0].thaiSanDauOmDenNgay,
            };
            break;
          case 10: // Xin hưởng trợ cấp khi vợ sinh
            formData.addAll({
              'hvtvo': response[0].hoTenVo ?? '',
              'ngaysinhvo': response[0].ngaySinhCuaVo,
              'cccdvo': response[0].cmndCanCuocCuaVo ?? '',
              'nvs': response[0].ngaySinhCon,
              'stpt': response[0].sinhThuongPhauThuat ?? 'SinhThuong',
              'socon': int.parse(response[0].soCon?.toString() ?? '1'),
              'cannang': response[0].canNang ?? '',
              'bv': response[0].taiBenhVien ?? '',
            });
            thongtinTrocap = {
              'hvtvo': response[0].hoTenVo ?? '',
              'ngaysinhvo': response[0].ngaySinhCuaVo,
              'cccdvo': response[0].cmndCanCuocCuaVo ?? '',
              'nvs': response[0].ngaySinhCon,
              'stpt': response[0].sinhThuongPhauThuat ?? 'SinhThuong',
              'socon': int.parse(response[0].soCon?.toString() ?? '1'),
              'cannang': response[0].canNang ?? '',
              'bv': response[0].taiBenhVien ?? '',
            };
            break;
        }
      }

      // Đồng bộ form với dữ liệu
      if (formKey.currentState != null) {
        formKey.currentState!.patchValue({
          'nguoigui': formData['nguoigui'],
          'loai': formData['loai'],
          'lydo': formData['lydo'],
          'buoi': formData['buoi'],
          'ngaynghi': formData['ngaynghi'],
          'ngaynghi1': formData['ngaynghi1'],
          'songaynghi': formData['songaynghi'],
          'songaythuc': formData['songaythuc'],
          'ghichu': formData['ghichu'],
          'nvs': formData['nvs'],
          'stpt': formData['stpt'],
          'socon': formData['socon'],
          'bv': formData['bv'],
          'diem': formData['diem'],
          'conthu': formData['conthu'],
          'tungay': formData['tungay'],
          'denngay': formData['denngay'],
          'hvtvo': formData['hvtvo'],
          'ngaysinhvo': formData['ngaysinhvo'],
          'cccdvo': formData['cccdvo'],
          'cannang': formData['cannang'],
        });
      }

      // tinhSoNgayThuc();
      update();
    } else {
      Get.showSnackbar(GetSnackBar(
        title: 'Lỗi',
        message: 'Không thể tải chi tiết đơn nghỉ phép',
        duration: Duration(seconds: 3),
        snackPosition: SnackPosition.TOP,
      ));
    }
  }

  // ------------------------------------ Lấy thông tin nghỉ phép từ API và cập nhật trạng thái giao diện. ------------------------------------
  getDNPInfo(int loaidnp) async {
    DonNghiPhepInfo? response = await apiProvider.getThongtinNghiPhep();
    donNghiPhepInfo = response;
    supervisors.addAll(donNghiPhepInfo.lanhDaoDonviList);
    leaveTypes.addAll(donNghiPhepInfo.loaiNghiPhep);
    danghi.value = donNghiPhepInfo.soNgayDaNghi!;
    conlai.value = donNghiPhepInfo.soNgayConLai!;
    if (donNghiPhepInfo.donVi != null) {
      donvi.value = donNghiPhepInfo.donVi!.tenDonVi!;
    }
    formData["loai"] = leaveTypes.where((w) => w.id == loaidnp).firstOrNull;
    tinhSoNgayThuc();
    update();
  }

  // -------------------------- Tải lại thông tin nghỉ phép từ API và cập nhật danh sách lãnh đạo, loại nghỉ phép. ----------------------------
  luuDNP() async {
    DonNghiPhepInfo? response = await apiProvider.getThongtinNghiPhep();
    donNghiPhepInfo = response;
    supervisors.addAll(donNghiPhepInfo.lanhDaoDonviList);
    leaveTypes.addAll(donNghiPhepInfo.loaiNghiPhep);
    update();
  }

  // ------------------------------------- Hàm tiện ích thực hiện điều kiện ba ngôi (ternary operator). ---------------------------------------
  ifThen(a, b, c) {
    return a == b ? c : a;
  }

  // ------------------------------------ Tính số ngày làm việc (loại trừ thứ Bảy, Chủ Nhật) giữa hai ngày. -----------------------------------
  String method(String start, String end) {
    int a = 0;
    DateTime startDate = DateTime.parse(start);
    DateTime endDate = DateTime.parse(end);

    while (startDate.isBefore(endDate)) {
      startDate = startDate.add(const Duration(days: 1));
      if (startDate.weekday != DateTime.saturday &&
          startDate.weekday != DateTime.sunday) {
        a++;
      }
    }
    if (kDebugMode) {
      print('COUNT: $start :: $end $a');
    }
    return a.toString();
  }
  // Map<String, dynamic> thongtinVosinh = {};
  // Map<String, dynamic> thongtinDasinh = {};
  // Map<String, dynamic> thongtinSauOmdau = {};
  // Map<String, dynamic> thongtinTrocap = {};

  // ------------------------------------------- Xóa thông tin bổ sung của các loại nghỉ phép đặc biệt. ---------------------------------------
  void resetThongtin() {
    thongtinVosinh = {};
    thongtinDasinh = {};
    thongtinSauOmdau = {};
    thongtinTrocap = {};
  }

  // ---------------------------------------------- Khai báo biến --------------------------------------------
  final bhxh = {
    'a': 'a) 05 ngày làm việc',
    'b':
        'b) 07 ngày làm việc khi vợ sinh con phải phẫu thuật, sinh con dưới 32 tuần tuổi',
    'c':
        'c) Nếu vợ sinh đôi thì được nghỉ 10 ngày, từ ba trở lên thì cứ thêm mỗi con được nghỉ thêm 03 ngày',
    'd': 'd) Nếu vợ sinh đôi trở lên mà phải phẫu thuật thì được nghỉ 14 ngày'
  };
  final hinhthucsinh = {'SinhThuong': 'Sinh thường', 'PhauThuat': 'Phẩu thuật'};
  var songaythuc = 0.0;
  var songaynghi = 0.0;

  // ------------------------------------- Tạo chuỗi mô tả thông tin nghỉ vợ sinh dựa trên thongtinVosinh. ------------------------------------
  String get buildthongtinVosinh {
    if (thongtinVosinh.isEmpty) {
      return "";
    }
    String tmp =
        "Sinh ${thongtinVosinh['socon']} con vào ngày ${(thongtinVosinh['nvs'] as DateTime).toFormat()}\n";
    tmp +=
        "với hình thức \"${hinhthucsinh[thongtinVosinh['stpt']]}\" tại ${thongtinVosinh['bv']}\n";
    tmp += "Tính theo BHXH: ${bhxh[thongtinVosinh['diem']]}";
    return tmp;
  }

  // ---------------------------------------- Tạo chuỗi mô tả thông tin nghỉ dưỡng sức sau sinh. ----------------------------------------------
  String get buildthongtinDasinh {
    if (thongtinDasinh.isEmpty) {
      return "";
    }
    String tmp =
        "Sinh con thứ ${thongtinDasinh['conthu']} vào ngày ${(thongtinDasinh['nvs'] as DateTime).toFormat()}\n";
    tmp += "với hình thức \"${hinhthucsinh[thongtinDasinh['stpt']]}\"\n";
    tmp +=
        "Đã nghỉ trước đó từ ${(thongtinDasinh['tungay'] as DateTime).toFormat()} đến ${(thongtinDasinh['denngay'] as DateTime).toFormat()}";
    return tmp;
  }

  // -------------------------------------------------- Tạo chuỗi mô tả thông tin nghỉ ốm đau. ------------------------------------------------
  String get buildthongtinSauOmdau {
    if (thongtinSauOmdau.isEmpty) {
      return "";
    }
    String tmp =
        "Đã nghĩ ốm từ ngày ${(thongtinSauOmdau['tungay'] as DateTime).toFormat()} đến ${(thongtinSauOmdau['denngay'] as DateTime).toFormat()}";
    return tmp;
  }

  // ---------------------------------------- Tạo chuỗi mô tả thông tin xin trợ cấp khi vợ sinh con. ------------------------------------------
  String get buildthongtinTrocap {
    if (thongtinTrocap.isEmpty) {
      return "";
    }
    String tmp =
        "Vợ: ${thongtinTrocap['hvtvo']}, sinh ngày ${(thongtinTrocap['ngaysinhvo'] as DateTime).toFormat()}, CCCD: ${thongtinTrocap['cccdvo']}\n";
    tmp +=
        "với hình thức \"${thongtinTrocap['stpt']}\" ngày ${(thongtinTrocap['nvs'] as DateTime).toFormat()} tại ${thongtinTrocap['bv']}\n";
    //tmp += "Tính theo BHXH: ${thongtinTrocap['diem'].name}";
    return tmp;
  }

  // ---------------------------------- Tính số ngày nghỉ cho nghỉ vợ sinh dựa trên số con và hình thức sinh. ---------------------------------
  void tinhNgayNghi() {
    int value = thongtinVosinh['socon'] ?? 1;
    int songaynghi = 0;
    if (thongtinVosinh['stpt'] == "SinhThuong") {
      if (value >= 2) {
        songaynghi = ((value - 2) * 3) + 10;
      } else {
        songaynghi = 5;
      }
    } else {
      if (value >= 2) {
        songaynghi = 14;
      } else {
        songaynghi = 7;
      }
    }
    if (leaveType >= 7) {
      var curdate = formData["ngaynghi"] as DateTimeRange;
      formData["ngaynghi"] = DateTimeRange(
          start: curdate.start,
          end: curdate.start.add(Duration(days: songaynghi)));
      formKey.currentState!.fields['ngaynghi']?.didChange(formData["ngaynghi"]);
      update();
    }
  }

  // ---------------------- Tính số ngày nghỉ thực tế (songaythuc) và tổng số ngày (songaynghi) dựa trên lựa chọn buổi nghỉ. ------------------
  void tinhSoNgayThuc() {
    if (kDebugMode) {
      print(buoi);
    }
    if (buoi.isNotEmpty) {
      var ngay1 = DateTime.now();
      var ngay2 = DateTime.now();
      if (buoi.value == 'CaNgay') {
        if (formData["ngaynghi"] == null) {
          formData["ngaynghi"] =
              DateTimeRange(start: DateTime.now(), end: DateTime.now());
        }
        ngay1 = (formData["ngaynghi"] as DateTimeRange).start;
        ngay2 = (formData["ngaynghi"] as DateTimeRange).end;
      } else {
        if (formData["ngaynghi1"] == null) {
          formData["ngaynghi1"] = DateTime.now();
        }
        ngay1 = formData["ngaynghi1"] as DateTime;
        ngay2 = formData["ngaynghi1"] as DateTime;
      }
      var tmp1 = calculateDaysexcludeWeekends(ngay1, ngay2);
      if (kDebugMode) {
        print(tmp1);
      }
      if (tmp1[1] == 0) {
        if (buoi.value != 'CaNgay') {
          songaythuc = 0.5;
        } else {
          songaythuc = 1;
        }
      } else {
        songaythuc = tmp1[0].toDouble();
      }
      songaynghi = (tmp1[1] + 1);

      formKey.currentState!.fields['songaythuc']
          ?.didChange(songaythuc.toString());

      formKey.currentState?.patchValue({
        'songaythuc': songaythuc.toString(),
        'songaynghi': songaynghi.toString(),
      });
    }
    update();
  }

  // ------------------------------- Tính số ngày làm việc (loại trừ cuối tuần) và tổng số ngày giữa hai ngày. --------------------------------
  List<int> calculateDaysexcludeWeekends(DateTime startDate, DateTime endDate) {
    int a = 1;
    if (kDebugMode) {
      print(startDate);
      print(endDate);
    }
    var b = endDate.difference(startDate).inDays;
    while (startDate.isBefore(endDate)) {
      startDate = startDate.add(const Duration(days: 1));
      if (startDate.weekday != DateTime.saturday &&
          startDate.weekday != DateTime.sunday) {
        a++;
      }
    }
    //print('COUNT: $startDate :: $endDate $a');
    return [a, b];
  }

  // ---------------------------------- Trả về chuỗi thông tin bổ sung dựa trên loại nghỉ phép (leaveType). -----------------------------------
  String get buildthongtin {
    String tmp = "";
    switch (leaveType) {
      case 7:
        return buildthongtinVosinh;
      case 8:
        return buildthongtinDasinh;
      case 9:
        return buildthongtinSauOmdau;
      case 10:
        return buildthongtinTrocap;
      default:
        extraThongtin.value = false;
        return tmp;
    }
  }

  @override
  void onClose() {
    resetThongtin();
    formData.clear();
    super.onClose();
  }

  // ------------------------------------- Gửi đơn nghỉ phép lên server và hiển thị thông báo kết quả. ----------------------------------------
  Future<void> submitForm() async {
    Map<String, dynamic> postData = {};
    //Logger().i(formData);
    postData["VienChucId"] = (formData["nguoigui"] as UserInfo).vienchucid;
    postData["DonViId"] =
        donNghiPhepInfo.donVi == null ? 0 : donNghiPhepInfo.donVi?.id;
    postData["LoaiNghiPhepId"] = (formData["loai"] as LoaiNghiPhep).id;
    if (buoi.value == 'CaNgay') {
      postData["TuNgay"] =
          (formData["ngaynghi"] as DateTimeRange).start.toString();
      postData["DenNgay"] =
          (formData["ngaynghi"] as DateTimeRange).end.toString();
    } else {
      postData["TuNgay"] = (formData["ngaynghi1"] as DateTime).toString();
      postData["DenNgay"] = (formData["ngaynghi1"] as DateTime).toString();
    }
    postData["SoNgay"] = songaythuc.toString();
    postData["GhiChu"] = formData["ghichu"];
    postData["LyDo"] = formData["lydo"];
    postData["NguoiTaoId"] = (formData["nguoigui"] as UserInfo).vienchucid;
    postData["Duyet"] = formData["chuyen"];
    postData["Buoi"] = formData["buoi"];
    postData["NgayNghiMotBuoi"] =
        (formData["ngaynghi1"] as DateTime).toString();
    postData["LanhDao"] = (formData["lanhdao"] as LanhDaoDonviList).id;

    //postData["NguoiTaoId"] = formData["nguoigui"];
    switch (leaveType) {
      case 7: //NGHỈ VỢ SINH
        postData["NgaySinhCon"] =
            (thongtinVosinh["nvs"] as DateTime).toString();
        postData["SinhThuongPhauThuat"] = thongtinVosinh["stpt"];
        postData["SoCon"] = thongtinVosinh["socon"];
        postData["TaiBenhVien"] = thongtinVosinh["bv"];
        postData["DiemLuatBhxh"] = thongtinVosinh["diem"];
        break;
      case 8: //NGHỈ DƯỠNG SỨC, PHỤC HỒI SỨC KHỎE SAU THAI SẢN
        postData["NgaySinhCon"] =
            (thongtinDasinh["nvs"] as DateTime).toString();
        postData["SinhThuongPhauThuat"] = thongtinDasinh["stpt"];
        postData["ConThu"] = thongtinDasinh["conthu"];
        postData["ThaiSanDauOmTuNgay"] =
            (thongtinDasinh["tungay"] as DateTime).toString();
        postData["ThaiSanDauOmDenNgay"] =
            (thongtinDasinh["denngay"] as DateTime).toString();
        break;
      case 9:
        postData["ThaiSanDauOmTuNgay"] =
            (thongtinSauOmdau["tungay"] as DateTime).toString();
        postData["ThaiSanDauOmDenNgay"] =
            (thongtinSauOmdau["denngay"] as DateTime).toString();
        break;
      case 10: //XIN HƯỞNG TRỢ CẤP MỘT LẦN KHI VỢ SINH CON
        postData["HoTenVo"] = thongtinTrocap["hvtvo"];
        postData["NgaySinhCuaVo"] =
            (thongtinTrocap["ngaysinhvo"] as DateTime).toString();
        postData["CmndcanCuocCuaVo"] = thongtinTrocap["cccdvo"];
        postData["SinhThuongPhauThuat"] = thongtinTrocap["stpt"];
        postData["SoCon"] = thongtinTrocap["socon"];
        postData["CanNang"] = thongtinTrocap["cannang"];
        //postData["CanNang"] = thongtinTrocap["ghichutt"];
        postData["NgaySinhCon"] =
            (thongtinTrocap["nvs"] as DateTime).toString();
        postData["TaiBenhVien"] = thongtinTrocap["bv"];
        break;
      default:
        break;
    }

    Logger().i(postData);
    bool success;
    if (formData['id'] != null) {
      // Cập nhật đơn hiện có
      int idDNP = formData['id'];
      success = await apiProvider.putDonNghiPhep(idDNP, postData);
    } else {
      // Tạo đơn mới
      success = await apiProvider.postDonNghiPhep(postData);
    }

    if (success) {
      bool? val = await Get.dialog(
        AlertDialog(
          title: const MyText.titleMedium('Thông báo'),
          content: MyText.bodyLarge(formData['id'] != null
              ? 'Đơn của bạn đã cập nhật!'
              : 'Đơn của bạn đã được tạo!'),
          actions: [
            TextButton(
              onPressed: () async {
                Get.find<NghiPhepHomeController>()
                    .refreshData(); // Làm mới danh sách
                Get.back(result: true);
              },
              child: const Text('OK'),
            ),
          ],
        ),
      );

      if (val != null && val) {
        if (formData['id'] != null) {
          // Làm mới dữ liệu khi quay về ChiTietDNPView
          final chiTietController = Get.find<ChiTietDNPController>();
          if (chiTietController.dnpId == formData['id'].toString()) {
            chiTietController.refreshData();
          }
        }

        Get.back();
      }
    }
  }
}
