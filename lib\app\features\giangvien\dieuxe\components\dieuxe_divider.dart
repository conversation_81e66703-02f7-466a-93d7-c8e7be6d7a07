import 'package:flutter/material.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

Widget DieuXeDivider(BuildContext context, String text) {
  ThemeData theme = Theme.of(context);
  return Row(
    children: [
      SizedBox(width: 35),
      Expanded(
        child: Divider(
          color: theme.dividerColor.withOpacity(0.3),
          thickness: 1,
          height: 20, // Chiều cao của divider, điều chỉnh theo nhu cầu
        ),
      ),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: MyText.bodySmall(
          text.toUpperCase(), // Văn bản bạn muốn hiển thị
          color: theme.dividerColor.withOpacity(0.3),
          fontWeight: 700, // Tùy chọn để làm nổi bật
        ),
      ),
      Expanded(
        child: Divider(
          color: theme.dividerColor.withOpacity(0.3),
          thickness: 1,
          height: 20, // Chi<PERSON>u cao của divider, điều chỉnh theo nhu cầu
        ),
      ),
      SizedBox(width: 35),
    ],
  );
}
