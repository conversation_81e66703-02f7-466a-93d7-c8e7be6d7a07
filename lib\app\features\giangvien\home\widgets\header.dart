import 'package:avatar_glow/avatar_glow.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/shared_components/circular_cached_network_image.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import '../gv_home_controller.dart';

class Header extends StatelessWidget {
  Header(this.controller, {super.key});

  final UserInfo userInfo = LocalStorageServices.getUserInfo()!;
  final GvHomeController controller;

  Widget getProfileImage() {
    if (userInfo.picture == null) {
      return Image.asset(
        'assets/images/app_icon_150.png',
        height: 100,
        width: 100,
        opacity: const AlwaysStoppedAnimation(.89),
      );
    }
    return CircularCachedNetworkImage(
      imageURL: userInfo.picture.toString(),
      size: 100,
      borderColor: Colors.transparent,
      fit: BoxFit.cover,
      alignment: Alignment.topCenter,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final topPadding = MediaQuery.of(context).padding.top;
    return Container(
      height: 170,
      width: double.infinity,
      color: theme.primaryColor,
      //bordered: true,
      padding: const EdgeInsets.only(top: 1),
      child: Stack(
        alignment: Alignment.topCenter,
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -85,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset(
                  'assets/images/app_icon.png',
                  height: 150,
                  width: 150,
                  opacity: const AlwaysStoppedAnimation(.15),
                )),
          ),
          Positioned(
            right: 0,
            top: -100,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: 0,
            top: -155,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),

          //----------------Data row----------------//
          Positioned(
            top: topPadding + 10,
            right: 16,
            left: 16,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                AvatarGlow(
                  //startDelay: const Duration(milliseconds: 1000),
                  glowColor: Colors.white,
                  glowShape: BoxShape.circle,
                  animate: true,
                  curve: Curves.fastOutSlowIn,
                  glowRadiusFactor: 0.2,
                  child: Material(
                    elevation: 8.0,
                    shape: const CircleBorder(),
                    color: Colors.transparent,
                    child: CircleAvatar(
                      //backgroundImage: NetworkImage(homeController.userInfo!.picture.toString()),
                      radius: 32.0,
                      child: getProfileImage(),
                    ),
                  ),
                ),
                MySpacing.height(9),
                Padding(
                  padding: const EdgeInsets.only(left: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      MyText.titleSmall("Xin chào", color: theme.colorScheme.onPrimary,),
                      MyText.titleMedium(userInfo.fullname.toString(), color: theme.colorScheme.onPrimary),
                    ],
                  ),
                ),
                const Spacer(),
                //----------------Theme Button----------------//
                InkWell(
                  onTap: () {
                    controller.changeTheme(context);
                      if (kDebugMode) {
                        print("changeTheme");
                      }
                  },
                  child: Ink(
                    child: SizedBox(
                      height: 39,
                      width: 39,
                      child: Icon(
                        Get.isDarkMode
                            ? LucideIcons.moon
                            : LucideIcons.sun,
                        size: 18,
                        color: theme.colorScheme.onPrimary,
                      ),
                    ),
                  ),
                ),

                MySpacing.width(10),
              ],
            ),
          ),
          MySpacing.height(80)
        ],
      ),
    );
  }
}
