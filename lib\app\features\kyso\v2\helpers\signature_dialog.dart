// bool _stampStyle = false;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:signature/signature.dart';
import 'package:tvumobile/app/features/kyso/utils/helpers.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

showSignatureDialogue(
    BuildContext context,
    Function(bool displayFullname, bool displayDepartment, bool displayPosition,
            bool displayDatetime)
        setTextDisplay,
    Function(bool stampStyle) setStampStyle,
    List<Map<String, String>> sampleSignatureUrls,
    Function(List<int>? signature, String signType) onSignatureSaved) {
  var theme = Theme.of(context);
  bool displayFullName = false;
  bool displayDepartment = false;
  bool displayPosition = false;
  bool displayDatetime = false;
  bool stampStyle = false; // Reset _stampStyle at the beginning of the dialog

  showDialog(
      context: context,
      builder: (c) {
        return StatefulBuilder(builder: (context, setStateDialog) {
          return Dialog(
            insetPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 80),
            child: Container(
              padding: EdgeInsets.all(20),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () {
                            stampStyle =
                                false; // Set _stampStyle to false for "Vẽ mới"
                            setStampStyle(stampStyle);
                            Navigator.pop(c);
                            showDrawSignatureDialog(context, onSignatureSaved);
                          },
                          child: Column(
                            children: [
                              Icon(LucideIcons.signature,
                                  size: 64, color: theme.primaryColor),
                              SizedBox(height: 5),
                              MyText.bodyLarge(
                                'Vẽ mới\nchữ ký',
                                color: theme.colorScheme.onSurface,
                                textAlign: TextAlign.center,
                              )
                            ],
                          ),
                        ),
                        SizedBox(height: 40),
                        InkWell(
                          onTap: () {
                            stampStyle =
                                false; // Set _stampStyle to false for "Chọn ảnh"
                            setStampStyle(stampStyle);
                            Get.back();
                            showSelectSignatureUrlDialog(
                                sampleSignatureUrls, onSignatureSaved);
                          },
                          child: Column(
                            children: [
                              Icon(LucideIcons.image,
                                  size: 64, color: theme.primaryColor),
                              SizedBox(height: 5),
                              MyText.bodyLarge(
                                'Chọn ảnh\nchữ ký',
                                color: theme.colorScheme.onSurface,
                                textAlign: TextAlign.center,
                              )
                            ],
                          ),
                        ),
                        SizedBox(height: 40),
                        InkWell(
                          onTap: () {
                            stampStyle =
                                true; // Set _stampStyle to true for "Thông tin chữ ký"
                            // Automatically check all info checkboxes for stamp style
                            displayFullName = true;
                            displayDepartment = true;
                            displayPosition = true;
                            displayDatetime = true;
                            setStampStyle(stampStyle);
                            setTextDisplay(displayFullName, displayDepartment,
                                displayPosition, displayDatetime);
                            Get.back();
                          },
                          child: Column(
                            children: [
                              Icon(LucideIcons.imagePlus,
                                  size: 64, color: theme.primaryColor),
                              SizedBox(height: 5),
                              MyText.bodyLarge(
                                'Thông tin\nchữ ký',
                                color: theme.colorScheme.onSurface,
                                textAlign: TextAlign.center,
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 20),
                    Divider(),
                    SizedBox(height: 10),
                    MyText.titleMedium('Thông tin hiển thị:', fontWeight: 600),
                    CheckboxListTile(
                      contentPadding: EdgeInsets.zero,
                      title: MyText.bodyMedium('Họ và tên'),
                      value: displayFullName,
                      onChanged: (bool? value) {
                        displayFullName = value ?? false;
                        setStateDialog(() {});
                        setTextDisplay(displayFullName, displayDepartment,
                            displayPosition, displayDatetime);
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                    ),
                    CheckboxListTile(
                      contentPadding: EdgeInsets.zero,
                      title: MyText.bodyMedium('Phòng ban'),
                      value: displayDepartment,
                      onChanged: (bool? value) {
                        displayDepartment = value ?? false;
                        setStateDialog(() {});
                        setTextDisplay(displayFullName, displayDepartment,
                            displayPosition, displayDatetime);
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                    ),
                    CheckboxListTile(
                      contentPadding: EdgeInsets.zero,
                      title: MyText.bodyMedium('Chức vụ'),
                      value: displayPosition,
                      onChanged: (bool? value) {
                        displayPosition = value ?? false;
                        setStateDialog(() {});
                        setTextDisplay(displayFullName, displayDepartment,
                            displayPosition, displayDatetime);
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                    ),
                    CheckboxListTile(
                      contentPadding: EdgeInsets.zero,
                      title: MyText.bodyMedium('Thời gian ký'),
                      value: displayDatetime,
                      onChanged: (bool? value) {
                        displayDatetime = value ?? false;
                        setStateDialog(() {});
                        setTextDisplay(displayFullName, displayDepartment,
                            displayPosition, displayDatetime);
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                    ),
                    SizedBox(height: 10),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        MyButton.small(
                          onPressed: () {
                            Get.back();
                          },
                          child: MyText.bodyMedium('Huỷ', color: Colors.white),
                        ),
                        SizedBox(width: 10),
                        MyButton.small(
                          onPressed: () {
                            Get.back();
                          },
                          child: MyText.bodyMedium('Chọn', color: Colors.white),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        });
      });
}

showDrawSignatureDialog(BuildContext context,
    Function(List<int>? signature, String signType) onSignatureSaved) {
  final signatureController = SignatureController();
  showDialog(
      context: context,
      builder: (c) {
        return Dialog(
          insetPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 100),
          child: Container(
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Signature(
                  controller: signatureController,
                  width: 300,
                  height: 300,
                  backgroundColor: Colors.transparent,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    TextButton(
                      onPressed: () {
                        signatureController.clear();
                      },
                      child: Text('Xoá', style: TextStyle(color: Colors.blue)),
                    ),
                    TextButton(
                      onPressed: () async {
                        final exportController = SignatureController(
                          penStrokeWidth: 2,
                          penColor: Colors.black,
                          exportBackgroundColor: Colors.white,
                          points: signatureController.points,
                        );
                        List<int>? signatureImageBytes =
                            await exportController.toPngBytes();
                        onSignatureSaved(signatureImageBytes, "draw");
                        exportController.dispose();

                        Get.back();
                      },
                      child: Text('Xác nhận',
                          style: TextStyle(color: Colors.blue)),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      });
}

showSelectSignatureUrlDialog(List<Map<String, String>> sampleSignatureUrls,
    Function(List<int>? signature, String signType) onSignatureChange) {
  Map<String, String> signNames = {
    "imageChuKy": "Chữ ký",
    "imageChuKyTat": "Chữ ký tắt",
    "imageMoc": "Con dấu (mộc)",
  };
  showDialog(
      context: Get.context!,
      builder: (c) {
        return Dialog(
          insetPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: Container(
            width: double.infinity,
            height: MediaQuery.of(c).size.height * 0.5,
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                MyText.titleMedium('Chọn chữ ký từ URL', fontWeight: 700),
                SizedBox(height: 20),
                Expanded(
                  child: ListView.separated(
                    shrinkWrap: true,
                    itemCount: sampleSignatureUrls.length,
                    separatorBuilder: (context, index) => Divider(
                      color: Colors.grey,
                    ),
                    itemBuilder: (context, index) {
                      final url = sampleSignatureUrls[index];
                      return ListTile(
                        contentPadding: EdgeInsets.zero,
                        leading: null,
                        trailing: Image.network(
                          url.values.first,
                          width: 200,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            Icons.error_outline,
                            color: Colors.deepOrangeAccent.shade200,
                          ),
                          fit: BoxFit.contain,
                        ),
                        title: Text(signNames[url.keys.first]!),
                        onTap: () async {
                          try {
                            List<int>? signatureImageBytes =
                                await networkImageToByte(url.values.first);
                            onSignatureChange(
                                signatureImageBytes, url.keys.first);
                            Get.back();
                          } catch (e) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                  content: Text('Không thể tải chữ ký từ URL')),
                            );
                            Get.back();
                          }
                        },
                      );
                    },
                  ),
                ),
                SizedBox(height: 10),
                MyButton.small(
                  onPressed: () {
                    Get.back();
                  },
                  child: MyText.bodyMedium('Huỷ', color: Colors.white),
                ),
              ],
            ),
          ),
        );
      });
}
