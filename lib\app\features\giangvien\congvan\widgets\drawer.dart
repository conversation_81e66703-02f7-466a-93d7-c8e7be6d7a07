
import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/config/themes/custom_theme.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import '../congvan_controller.dart';

Widget buildDrawer(BuildContext context, CongVanController controller) {
  var theme = Theme.of(context);
  return MyContainer.none(
    margin:
    MySpacing.fromLTRB(16, MySpacing.safeAreaTop(context) + 16, 16, 80),
    borderRadiusAll: 4,
    clipBehavior: Clip.antiAliasWithSaveLayer,
    color: theme.scaffoldBackgroundColor,
    child: Drawer(
        child: SingleChildScrollView(
          child: Container(
            color: theme.scaffoldBackgroundColor,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  padding: MySpacing.only(left: 20, bottom: 0, top: 8, right: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      MySpacing.height(16),
                      MyContainer(
                        padding: MySpacing.fromLTRB(12, 4, 12, 4),
                        borderRadiusAll: 4,
                        color: theme.primaryColor,
                        borderColor: theme.primaryColor,
                        bordered: true,
                        child: MyText.bodyLarge("Quản lý công văn",
                            color: theme.colorScheme.onPrimary,
                            fontWeight: 700,
                            letterSpacing: 0.2),
                      ),
                    ],
                  ),
                ),
                MySpacing.height(8),
                const Divider(
                  thickness: 1,
                ),
                MySpacing.height(16),
                Container(
                  margin: MySpacing.x(20),
                  child: Column(
                    children: [
                      MySpacing.height(10),
                      InkWell(
                        onTap: () {
                          controller.changeDocType(DocTypeId.caNhan);
                          controller.closeDrawer();
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 12,
                              borderRadiusAll: 4,
                              color: CustomTheme.blue.withAlpha(20),
                              child: Icon(LucideIcons.fileText,
                                size: 20,
                                color: CustomTheme.blue,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Cá nhân',
                                color: CustomTheme.blue,
                                fontWeight: 900,
                              ),
                            ),
                          ],
                        ),
                      ),
                      MySpacing.height(10),
                      InkWell(
                        onTap: () {
                          controller.changeDocType(DocTypeId.v2aCqDen);
                          controller.closeDrawer();
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 12,
                              borderRadiusAll: 4,
                              color: CustomTheme.peach.withAlpha(20),
                              child: Icon(LucideIcons.fileText,
                                size: 20,
                                color: CustomTheme.peach,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Văn bản chính quyền',
                                color: CustomTheme.peach,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down)
                          ],
                        ),
                      ),
                      MySpacing.height(5),
                      Padding(
                        padding: const EdgeInsets.only(left: 12.0),
                        child: Column(
                          children: [
                            InkWell(
                              onTap: () {
                                controller.changeDocType(DocTypeId.v2aCqDen);
                                controller.closeDrawer();
                              },
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              child: Row(
                                children: [
                                  MyContainer(
                                    paddingAll: 12,
                                    borderRadiusAll: 4,
                                    color: CustomTheme.peach.withAlpha(20),
                                    child: Icon(LucideIcons.panelBottomClose,
                                      size: 20,
                                      color: CustomTheme.peach,
                                    ),
                                  ),
                                  MySpacing.width(16),
                                  const Expanded(
                                    child: MyText.bodyLarge(
                                      'Văn bản đến',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            MySpacing.height(2),
                            InkWell(
                              onTap: () {
                                controller.changeDocType(DocTypeId.v2bCqDi);
                                controller.closeDrawer();
                              },
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              child: Row(
                                children: [
                                  MyContainer(
                                    paddingAll: 10,
                                    borderRadiusAll: 4,
                                    color: CustomTheme.peach.withAlpha(20),
                                    child: Icon(LucideIcons.panelBottomOpen,
                                      size: 20,
                                      color: CustomTheme.peach,
                                    ),
                                  ),
                                  MySpacing.width(16),
                                  const Expanded(
                                    child: MyText.bodyLarge(
                                      'Văn bản đi',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            MySpacing.height(2),
                            InkWell(
                              onTap: () {
                                controller.changeDocType(DocTypeId.v2cCqNb);
                                controller.closeDrawer();
                              },
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              child: Row(
                                children: [
                                  MyContainer(
                                    paddingAll: 10,
                                    borderRadiusAll: 4,
                                    color: CustomTheme.peach.withAlpha(20),
                                    child: Icon(LucideIcons.building2,
                                      size: 20,
                                      color: CustomTheme.peach,
                                    ),
                                  ),
                                  MySpacing.width(16),
                                  const Expanded(
                                    child: MyText.bodyLarge(
                                      'Văn bản nội bộ',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      MySpacing.height(10),
                      InkWell(
                        onTap: () {
                          //launchChangeLog();
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 12,
                              borderRadiusAll: 4,
                              color: CustomTheme.darkGreen.withAlpha(20),
                              child: Icon(LucideIcons.fileText,
                                size: 20,
                                color: CustomTheme.darkGreen,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Văn bản Đảng',
                                color: CustomTheme.darkGreen,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down)
                          ],
                        ),
                      ),
                      MySpacing.height(5),
                      Padding(
                        padding: const EdgeInsets.only(left: 12.0),
                        child: Column(
                          children: [
                            InkWell(
                              onTap: () {
                                controller.changeDocType(DocTypeId.v1aDaDen);
                                controller.closeDrawer();
                              },
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              child: Row(
                                children: [
                                  MyContainer(
                                    paddingAll: 10,
                                    borderRadiusAll: 4,
                                    color: CustomTheme.darkGreen.withAlpha(20),
                                    child: Icon(LucideIcons.panelBottomClose,
                                      size: 20,
                                      color: CustomTheme.darkGreen,
                                    ),
                                  ),
                                  MySpacing.width(16),
                                  const Expanded(
                                    child: MyText.bodyLarge(
                                      'Văn bản đến',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            MySpacing.height(2),
                            InkWell(
                              onTap: () {
                                controller.changeDocType(DocTypeId.v1bDaDi);
                                controller.closeDrawer();
                              },
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              child: Row(
                                children: [
                                  MyContainer(
                                    paddingAll: 10,
                                    borderRadiusAll: 4,
                                    color: CustomTheme.darkGreen.withAlpha(20),
                                    child: Icon(LucideIcons.panelBottomOpen,
                                      size: 20,
                                      color: CustomTheme.darkGreen,
                                    ),
                                  ),
                                  MySpacing.width(16),
                                  const Expanded(
                                    child: MyText.bodyLarge(
                                      'Văn bản đi',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            MySpacing.height(2),
                            InkWell(
                              onTap: () {
                                controller.changeDocType(DocTypeId.v1cDaNb);
                                controller.closeDrawer();
                              },
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              child: Row(
                                children: [
                                  MyContainer(
                                    paddingAll: 10,
                                    borderRadiusAll: 4,
                                    color: CustomTheme.darkGreen.withAlpha(20),
                                    child: Icon(LucideIcons.building2,
                                      size: 20,
                                      color: CustomTheme.darkGreen,
                                    ),
                                  ),
                                  MySpacing.width(16),
                                  const Expanded(
                                    child: MyText.bodyLarge(
                                      'Văn bản nội bộ',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      MySpacing.height(10),
                      InkWell(
                        onTap: () {
                          //launchChangeLog();
                        },
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        child: Row(
                          children: [
                            MyContainer(
                              paddingAll: 12,
                              borderRadiusAll: 4,
                              color: CustomTheme.purple.withAlpha(20),
                              child: Icon(LucideIcons.fileText,
                                size: 20,
                                color: CustomTheme.purple,
                              ),
                            ),
                            MySpacing.width(16),
                            Expanded(
                              child: MyText.titleMedium(
                                'Văn bản Công đoàn',
                                color: CustomTheme.purple,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down)
                          ],
                        ),
                      ),
                      MySpacing.height(5),
                      Padding(
                        padding: const EdgeInsets.only(left: 12.0),
                        child: Column(
                          children: [
                            InkWell(
                              onTap: () {
                                controller.changeDocType(DocTypeId.v3aCdDen);
                                controller.closeDrawer();
                              },
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              child: Row(
                                children: [
                                  MyContainer(
                                    paddingAll: 10,
                                    borderRadiusAll: 4,
                                    color: CustomTheme.purple.withAlpha(20),
                                    child: Icon(LucideIcons.panelBottomClose,
                                      size: 20,
                                      color: CustomTheme.purple,
                                    ),
                                  ),
                                  MySpacing.width(16),
                                  const Expanded(
                                    child: MyText.bodyLarge(
                                      'Văn bản đến',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            MySpacing.height(2),
                            InkWell(
                              onTap: () {
                                controller.changeDocType(DocTypeId.v3bCdDi);
                                controller.closeDrawer();
                              },
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              child: Row(
                                children: [
                                  MyContainer(
                                    paddingAll: 10,
                                    borderRadiusAll: 4,
                                    color: CustomTheme.purple.withAlpha(20),
                                    child: Icon(LucideIcons.panelBottomOpen,
                                      size: 20,
                                      color: CustomTheme.purple,
                                    ),
                                  ),
                                  MySpacing.width(16),
                                  const Expanded(
                                    child: MyText.bodyLarge(
                                      'Văn bản đi',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            MySpacing.height(2),
                            InkWell(
                              onTap: () {
                                controller.changeDocType(DocTypeId.v3bCdDi);
                                controller.closeDrawer();
                              },
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              child: Row(
                                children: [
                                  MyContainer(
                                    paddingAll: 10,
                                    borderRadiusAll: 4,
                                    color: CustomTheme.purple.withAlpha(20),
                                    child: Icon(LucideIcons.building2,
                                      size: 20,
                                      color: CustomTheme.purple,
                                    ),
                                  ),
                                  MySpacing.width(16),
                                  const Expanded(
                                    child: MyText.bodyLarge(
                                      'Văn bản nội bộ',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        )),
  );
}
