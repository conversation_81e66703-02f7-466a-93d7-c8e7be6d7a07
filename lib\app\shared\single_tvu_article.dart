import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/data/models/wordpress/article.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_horizontal_list.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/extensions/extensions.dart';
import 'package:tvumobile/app/utils/localizations/strings_enum.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:share_plus/share_plus.dart';

class SingleTVUArticle extends StatefulWidget {
  final dynamic article;
  final String heroId;

  const SingleTVUArticle(this.article, this.heroId, {super.key});

  @override
  SingleTVUArticleState createState() => SingleTVUArticleState();
}

class SingleTVUArticleState extends State<SingleTVUArticle> {
  List<dynamic> relatedArticles = [];
  Future<List<dynamic>>? _futureRelatedArticles;

  @override
  void initState() {
    super.initState();
    _futureRelatedArticles = fetchRelatedArticles();
  }

  Future<List<dynamic>> fetchRelatedArticles() async {
    try {
      int postId = widget.article.id;
      int catId = widget.article.catId;
      var url = "${ApiPath.tvuWordpressURL}/wp-json/wp/v2/posts?exclude=$postId&categories[]=$catId&per_page=3";
      if (kDebugMode) {
        print(url);
      }
      var response = await http.get(Uri.parse(url));

      if (mounted) {
        if (response.statusCode == 200) {
          setState(() {
            relatedArticles = json
                .decode(response.body)
                .map((m) => Article.fromJson(m))
                .toList();
          });

          return relatedArticles;
        }
      }
    } on SocketException {
      throw 'No Internet connection';
    }
    return relatedArticles;
  }

  @override
  void dispose() {
    super.dispose();
    relatedArticles = [];
  }

  @override
  Widget build(BuildContext context) {
    final article = widget.article;
    final heroId = widget.heroId;
    final articleVideo = widget.article.video;
    String youtubeUrl = "";
    String dailymotionUrl = "";
    if (articleVideo.contains("youtube")) {
      youtubeUrl = articleVideo.split('?v=')[1];
    }
    if (articleVideo.contains("dailymotion")) {
      dailymotionUrl = articleVideo.split("/video/")[1];
    }
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.primaryColor.withOpacity(0.95),
        centerTitle: true,
        toolbarHeight: 42,
        leading: IconButton(onPressed: () { Get.back(); }, icon: Icon(LucideIcons.arrowLeft, color: theme.colorScheme.onPrimary,),),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            MyText.titleMedium(Strings.portal_chitiet.lang().toUpperCase(), color: theme.colorScheme.onPrimary,),
          ],
        ),
        actions: [
          IconButton(
            padding: const EdgeInsets.all(0),
            icon: Icon(
              Icons.share,
              color: theme.colorScheme.onPrimary,
              size: 24.0,
            ),
            onPressed: () {
              Share.share('Chia sẽ: ${article.link}');
            },
          ),
          MySpacing.width(20),
        ],
      ),
      body: Container(
          decoration: BoxDecoration(color: theme.colorScheme.surfaceContainer),
          child: SingleChildScrollView(
            scrollDirection: Axis.vertical,
            child: Column(
              children: <Widget>[
                Stack(
                  children: <Widget>[
                    Hero(
                      tag: heroId,
                      child: ClipRRect(
                        borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(60.0)),
                        child: ColorFiltered(
                          colorFilter: ColorFilter.mode(
                              Colors.black.withOpacity(0.3), BlendMode.overlay),
                          child: articleVideo != ""
                              ? articleVideo.contains("youtube")
                                  ? Container(
                                      padding: EdgeInsets.fromLTRB(
                                          0,
                                          MediaQuery.of(context).padding.top,
                                          0,
                                          0),
                                      decoration: const BoxDecoration(
                                          color: Colors.black),
                                      child: HtmlWidget(
                                        """
                                    <iframe src="https://www.youtube.com/embed/$youtubeUrl" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                                    """,
                                      ),
                                    )
                                  : articleVideo.contains("dailymotion")
                                      ? Container(
                                          padding: EdgeInsets.fromLTRB(
                                              0,
                                              MediaQuery.of(context)
                                                  .padding
                                                  .top,
                                              0,
                                              0),
                                          decoration: const BoxDecoration(
                                              color: Colors.black),
                                          child: HtmlWidget(
                                            """
                                    <iframe frameborder="0"
                                    src="https://www.dailymotion.com/embed/video/$dailymotionUrl?autoplay=1&mute=1"
                                    allowfullscreen allow="autoplay">
                                    </iframe>
                                    """,
                                          ),
                                        )
                                      : Container(
                                          padding: EdgeInsets.fromLTRB(
                                              0,
                                              MediaQuery.of(context)
                                                  .padding
                                                  .top,
                                              0,
                                              0),
                                          decoration: const BoxDecoration(
                                              color: Colors.black),
                                          child: HtmlWidget(
                                            """
                                    <video autoplay="" playsinline="" controls>
                                    <source type="video/mp4" src="$articleVideo">
                                    </video>
                                    """,
                                          ),
                                        )
                              : Image.network(
                                  article.image,
                                  fit: BoxFit.cover,
                                ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: MediaQuery.of(context).padding.top,
                      child: IconButton(
                        icon: const Icon(Icons.arrow_back),
                        color: Colors.white,
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.all(14),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Html(data: "<h2>${article.title}</h2>", style: {
                        "h2": Style(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w500,
                            fontSize: FontSize(1.3, Unit.em),
                            padding: HtmlPaddings.all(4)
                        ),
                      }),
                      Container(
                        padding: const EdgeInsets.all(16),
                        child: HtmlWidget(
                          article.content,
                          textStyle: Theme.of(context).textTheme.bodyLarge ??
                              const TextStyle(),
                        ),
                      ),
                    ],
                  ),
                ),
                _buildRelatedPosts(_futureRelatedArticles as Future<List<dynamic>>),
                //relatedPosts(_futureRelatedArticles as Future<List<dynamic>>)
              ],
            ),
          )),

    );
  }

  Widget _buildRelatedPosts(Future<List<dynamic>> latestArticles) {
    return FutureBuilder<List<dynamic>>(
      future: latestArticles,
      builder: (BuildContext context, AsyncSnapshot<List<dynamic>> articleSnapshot) {
        if (articleSnapshot.hasData) {
          if (articleSnapshot.data!.isEmpty) return Container();
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                padding: EdgeInsets.only(left: 16),
                child: MyText.titleLarge(
                  "Tin khác",
                  textAlign: TextAlign.left,
                  decoration: TextDecoration.underline,
                ),
              ),
              MyHorizontalList(
                padding: const EdgeInsets.all(0.0),
                wrapAlignment: WrapAlignment.spaceEvenly,
                itemCount: articleSnapshot.data!.length,
                itemBuilder: (BuildContext context, int i) {
                  return  _buildSingleLatestPosts(index: i, doc: articleSnapshot.data![i]);
                },
              ),
            ],
          );
        } else if (articleSnapshot.hasError) {
          return Container(
              height: 500,
              alignment: Alignment.center,
              child: Text("${articleSnapshot.error}"));
        }
        return Container(
            alignment: Alignment.center,
            width: MediaQuery.of(context).size.width,
            height: 150);
      },
    );
  }

  Widget _buildSingleLatestPosts({int? index, dynamic doc}) {
    var theme = LocalStorageServices.getThemeIsLight() ? AppTheme.light : AppTheme.dark;
    Article article = doc as Article;
    String heroKey ="$index-latest";
    return Padding(
      padding: MySpacing.right(16),
      child: InkWell(
        onTap: () {
          //Get.to(() =>  SingleTVUArticle(article, heroKey), transition: Transition.fadeIn);
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => SingleTVUArticle(article, heroKey),
            ),
          );
        },
        child: MyContainer(
          width: 300,
          height: 256,
          margin: const EdgeInsets.only(top: 8, bottom: 8, left: 8),
          padding: const EdgeInsets.all(0),
          color: theme.cardColor,
          borderRadiusAll: 12,
          borderColor: Colors.grey.withOpacity(0.2),
          bordered: true ,
          //decoration: boxDecorationRoundedWithShadow(12, backgroundColor: theme.cardColor),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Hero(
                  tag: heroKey,
                  child: Container(
                    height: (260 / 16) * 9,
                    decoration: BoxDecoration(
                        color: Colors
                            .transparent, //bgColor[_random.nextInt(bgColor.length)],
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(
                            image: NetworkImage(article.image == ""
                                ? AppImages.defaultImgUrl
                                : article.image.toString()),
                            fit: BoxFit.cover)),
                  )),

              Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  children: [
                    MyText.titleMedium(
                      article.title.toString(),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    MySpacing.height(8),
                    Row(
                      children: [
                        const Icon(Icons.schedule, size: 12),
                        MyText.labelMedium(
                          article.date.toString().substring(0, 10),
                          softWrap: true,
                          xMuted: true,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

}
