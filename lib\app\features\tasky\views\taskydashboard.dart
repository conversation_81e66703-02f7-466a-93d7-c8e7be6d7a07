// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:flutter_svg/flutter_svg.dart';

// import 'package:tvumobile/app/features/tasky/controllers/taskydashboard_controller.dart';

// class TaskyDashboardView extends GetView<TaskyDashboardController> {
//   const TaskyDashboardView({super.key});

//   @override
//   Widget build(BuildContext context) {
//     ThemeData theme = Theme.of(context);
//     // const Color customRedColor = Color.fromRGBO(229, 31, 31, 1.0);
//     const Color customBlueColor = Color.fromRGBO(34, 103, 206, 1);
//     const Color customGreyColor = Color.fromRGBO(56, 64, 75, 1);
//     //   return GetBuilder<TaskyDashboardController>(builder: (controller) {
//     //     return Scaffold(
//     //       body: IndexedStack(
//     //         index: controller.currentIndex,
//     //         children: controller.pages,
//     //       ),
//     //       bottomNavigationBar: BottomNavigationBar(
//     //         currentIndex: controller.currentIndex,
//     //         onTap: controller.onChanged,
//     //         selectedIconTheme: theme.iconTheme.copyWith(color: customRedColor),
//     //         selectedLabelStyle: Theme.of(context)
//     //             .textTheme
//     //             .bodyMedium!
//     //             .copyWith(color: customRedColor),
//     //         unselectedIconTheme: theme.iconTheme.copyWith(color: customGreyColor),
//     //         unselectedLabelStyle: Theme.of(context)
//     //             .textTheme
//     //             .bodyMedium!
//     //             .copyWith(color: customGreyColor),
//     //         type: BottomNavigationBarType.fixed,
//     //         showUnselectedLabels: true,
//     //         backgroundColor: theme.bottomNavigationBarTheme.backgroundColor,
//     //         selectedItemColor: customRedColor,
//     //         unselectedItemColor: customGreyColor,
//     //         items: [
//     //           BottomNavigationBarItem(
//     //               icon: SvgPicture.asset('assets/tasky/chart_pie.svg'),
//     //               label: 'Tổng quan',
//     //               activeIcon: SvgPicture.asset(
//     //                 'assets/tasky/chart_pie.svg',
//     //                 // ignore: deprecated_member_use
//     //                 color: customRedColor,
//     //               )),
//     //           BottomNavigationBarItem(
//     //               icon: SvgPicture.asset('assets/tasky/lightning_bolt.svg'),
//     //               label: 'Nhiệm vụ',
//     //               activeIcon: SvgPicture.asset(
//     //                 'assets/tasky/lightning_bolt.svg',
//     //                 colorFilter:
//     //                     ColorFilter.mode(customRedColor, BlendMode.srcIn),
//     //               )),
//     //           BottomNavigationBarItem(
//     //               icon: SvgPicture.asset('assets/tasky/inbox.svg'),
//     //               label: 'Hộp thư',
//     //               activeIcon: SvgPicture.asset(
//     //                 'assets/tasky/inbox.svg',
//     //                 // ignore: deprecated_member_use
//     //                 color: customRedColor,
//     //               )),
//     //           BottomNavigationBarItem(
//     //               icon: SvgPicture.asset('assets/tasky/user_circle.svg'),
//     //               label: 'Cá nhân',
//     //               activeIcon: SvgPicture.asset(
//     //                 'assets/tasky/user_circle.svg',
//     //                 // ignore: deprecated_member_use
//     //                 color: customRedColor,
//     //               ))
//     //         ],
//     //       ),
//     //     );
//     //   });
//     // }
//     return Obx(
//       () => Scaffold(
//         // body: IndexedStack(
//         //   index: controller.currentIndex.value,
//         //   children: controller.pages,
//         // ),
//         body: controller.pageBuilders[controller.currentIndex.value](),
//         bottomNavigationBar: BottomNavigationBar(
//           currentIndex: controller.currentIndex.value,
//           onTap: controller.onChanged,
//           selectedIconTheme: theme.iconTheme.copyWith(color: customBlueColor),
//           selectedLabelStyle: Theme.of(context)
//               .textTheme
//               .bodyMedium!
//               .copyWith(color: customBlueColor),
//           unselectedIconTheme: theme.iconTheme.copyWith(color: customGreyColor),
//           unselectedLabelStyle: Theme.of(context)
//               .textTheme
//               .bodyMedium!
//               .copyWith(color: customGreyColor),
//           type: BottomNavigationBarType.fixed,
//           showUnselectedLabels: true,
//           backgroundColor: theme.bottomNavigationBarTheme.backgroundColor,
//           selectedItemColor: customBlueColor,
//           unselectedItemColor: customGreyColor,
//           items: [
//             BottomNavigationBarItem(
//                 icon: SvgPicture.asset('assets/tasky/chart_pie.svg'),
//                 label: 'Tổng quan',
//                 activeIcon: SvgPicture.asset(
//                   'assets/tasky/chart_pie.svg',
//                   color: customBlueColor,
//                 )),
//             BottomNavigationBarItem(
//                 icon: SvgPicture.asset('assets/tasky/lightning_bolt.svg'),
//                 label: 'Nhiệm vụ',
//                 activeIcon: SvgPicture.asset(
//                   'assets/tasky/lightning_bolt.svg',
//                   colorFilter:
//                       ColorFilter.mode(customBlueColor, BlendMode.srcIn),
//                 )),
//             BottomNavigationBarItem(
//                 icon: SvgPicture.asset('assets/tasky/inbox.svg'),
//                 label: 'Hộp thư',
//                 activeIcon: SvgPicture.asset(
//                   'assets/tasky/inbox.svg',
//                   color: customBlueColor,
//                 )),
//             BottomNavigationBarItem(
//                 icon: SvgPicture.asset('assets/tasky/user_circle.svg'),
//                 label: 'Cá nhân',
//                 activeIcon: SvgPicture.asset(
//                   'assets/tasky/user_circle.svg',
//                   color: customBlueColor,
//                 ))
//           ],
//         ),
//       ),
//     );
//   }
// }
