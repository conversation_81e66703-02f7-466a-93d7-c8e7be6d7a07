import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'package:tvumobile/app/constans/constant_data.dart';
import 'package:tvumobile/app/deeplinkparser.dart';
import 'package:tvumobile/app/features/app_binding.dart';
import 'package:tvumobile/app/services/connectivity_controller.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:tvumobile/app/services/notification_service.dart';
import 'package:tvumobile/app/utils/helpers/device_info_helper.dart';
//import 'package:tvumobile/app/utils/helpers/notification_helper.dart';
import 'package:tvumobile/firebase_options.dart';
import 'package:syncfusion_localizations/syncfusion_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'app/config/routes/app_pages.dart';
import 'app/config/themes/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'app/config/translation/app_translations.dart';

//final shorebirdCodePush = ShorebirdCodePush();
//bool isShorebirdAvailable = false;
MConnectivityResult connectivityGlobal = MConnectivityResult.mobile;
NotificationService notificationService = NotificationService();
final updater = ShorebirdUpdater();

/// The main function of the application.
///
/// This function is the entry point of the application. It initializes
/// the Flutter framework, sets up the Firebase services, and runs the
/// application.
///
/// It also sets up the Shorebird code push service and initializes the
/// local notifications.
///
/// On Android, it sets up the Crashlytics service to report any uncaught
/// errors to the Firebase console.
///
/// On Windows, it sets up the uni_links_desktop package to handle deep
/// links.
///
/// Finally, it runs the application with the first screen specified by
/// the deep link parser.
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  //await LocalStorageServices.init();
  await GetStorage.init();
  DeviceInfoHelper.initializeDeviceInfo();
  deepLinkParser = DeepLinkParser();
  // inti fcm services
  if (!kIsWeb) {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  }

  int? patchNumber;
  updater.readCurrentPatch().then((currentPatch) {
    print('The current patch number is: ${currentPatch?.number}');
    patchNumber = currentPatch?.number;
  });
  //
  if (!kDebugMode && !kIsWeb) {
    if (!kIsWeb && Platform.isAndroid) {
      FirebaseCrashlytics.instance.setCustomKey(
        'shorebird_patch_number',
        '$patchNumber',
      );
      FlutterError.onError = (errorDetails) {
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
      };
      // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
      PlatformDispatcher.instance.onError = (error, stack) {
        FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
        return true;
      };
    }
  }

  // initialize local notifications
  // if (!kIsWeb && 1 == 2) {
  //   if (!Platform.isWindows) {
  //     var fcmtoken = LocalStorageServices.getFcmToken();
  //     if (fcmtoken != null) {
  //       await NotificationHelper().initNotification();
  //     }
  //   }
  //   if (Platform.isWindows) {
  //     // install uni_links_desktop
  //     //registerProtocol('vn.edu.tvu.itx.tvumobile');
  //   }
  // }

  String firstScreen = await deepLinkParser!.getFirstScreen();
  deepLinkParser!.listenLink();
  runApp(MyApp(
    firstScreen: firstScreen,
  ));
}

class MyApp extends StatelessWidget {
  final String firstScreen;
  const MyApp({super.key, required this.firstScreen});

  @override
  Widget build(BuildContext context) {
    //bool themeIsLight = LocalStorageServices.getThemeIsLight();
    return GetMaterialApp(
      title: 'TVU Mobile',
      debugShowCheckedModeBanner: false,
      useInheritedMediaQuery: true,
      translations: AppTranslations(),
      locale: const Locale('vi', 'VN'), //AppTranslations.locale,
      fallbackLocale: AppTranslations.fallbackLocale,
      initialRoute: firstScreen, //AppPages.SPLASH,
      initialBinding: AppBinding(),
      getPages: AppPages.routes,
      //theme: themeIsLight ? AppTheme.light : AppTheme.dark,
      builder: (context, widget) {
        bool themeIsLight = LocalStorageServices.getThemeIsLight();
        notificationService.initInfo();
        return Theme(
          data: themeIsLight
              ? AppTheme.light
              : AppTheme.dark, //MyTheme.getThemeData(isLight: themeIsLight),
          child: MediaQuery(
            // prevent font from scalling (some people use big/small device fonts)
            // but we want our app font to still the same and dont get affected
            data: MediaQuery.of(context)
                .copyWith(textScaler: const TextScaler.linear(1.0)),
            child: widget!,
          ),
        );
      },
      supportedLocales: const [
        Locale('vi'),
        Locale('en'),
      ],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        SfGlobalLocalizations.delegate
      ],
    );
  }
}
