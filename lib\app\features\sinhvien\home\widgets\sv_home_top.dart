import 'package:flutter/material.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/data/models/sch/tkb/tkb_tuan_giangvien.dart';
import 'package:tvumobile/app/features/sinhvien/home/<USER>';
import 'package:tvumobile/app/features/sinhvien/home/<USER>/sv_header.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/tkb_helper.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class SvHeaderTopComponent extends StatelessWidget {
  const SvHeaderTopComponent(this.controller, {super.key});
  final SvHomeController controller;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    var width = Get.width;
    width = (width - width/6)+5;
    return Stack(
      children: [
        SvHeader(controller),
        Positioned(
          top: 120,
          left: (width/6 - 28),
          child: Center(
            child: Container(
              width: width,
              alignment: Alignment.center,
              //decoration: boxDecorationRoundedWithShadow(12, backgroundColor: theme.cardColor, blurRadius: 0.0, spreadRadius: 0.5, shadowColor: Colors.grey[350]),
              child: Card(
                child: Obx(() {
                  if(controller.tkbs.isEmpty) {
                    return schedulingCardEmpty(width);
                  }
                  return buildCarousel(theme);

                }),
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget schedulingCard(double width, ThemeData theme, TkbTuanGiangVien tkb) {

    return Stack(
      children: [
        Align(
          alignment: Alignment.topCenter,
          child: Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child:
            Text.rich(
              TextSpan(
                text: "(${tkb.soTiet.toString()} tiết) ",
                children: [
                  TextSpan(
                    text: tkb.tenMon,
                    style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)
                  ),
                ],
              ),
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: Padding(
            padding: const EdgeInsets.only(top: 12.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
              Image.asset(
                'assets/vectors/icons8-task-100.png',
                width: 75,
                height: 75,
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MySpacing.height(12),
                  MyText.bodyMedium(
                    "Lớp: ${tkb.maLop} | Nhóm: ${tkb.nhomHoc}${tkb.nhomTH.toString().trim().isEmpty ? "" : " | Tổ TH: ${tkb.nhomTH}"}",
                    maxLines: 2,
                  ),
                  MyText.bodyMedium("Thời gian: ${dateToWeekday(tkb.batDau!)} - ngày ${DateFormat("dd/MM/yyyy").format(tkb.batDau!)}"),
                  Text.rich(
                    TextSpan(
                      text: tkb.batDau!.hour > 12 ? "Buổi chiều" : "Buổi sáng",
                      children: [
                        TextSpan(text: " | Bắt đầu từ: ", style: theme.textTheme.bodyMedium),
                        TextSpan(text: "${DateFormat("HH:mm").format(tkb.batDau!)} - ${DateFormat("HH:mm").format(tkb.ketThuc!)}", style: TextStyle(color: theme.primaryColor)),
                      ],
                    ),
                    style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold, color: theme.primaryColor),
                  ),
                  MySpacing.height(8),
                ],
              ),
              GestureDetector(
                onTap: () {
                  controller.onTapFunctions("tkb");
                },
                child: Icon(
                  LucideIcons.chevronRight,
                  size: 32,
                  color: theme.primaryColor,
                ),
              )
            ],
            ),
          ),
        ),
      ]
    );
  }

  Widget schedulingCardEmpty(double width) {
    var theme = LocalStorageServices.getThemeIsLight() ? AppTheme.light : AppTheme.dark;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Image.asset(
          'assets/vectors/icons8-task-100.png',
          width: 100,
          height: 100,
        ),
        SizedBox(
          height: 100,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Spacer(),
              MyText.labelSmall(
                "Lịch trình hôm nay: ${DateFormat("dd/MM/yyyy").format(DateTime.now())}",
                muted: true,
              ),

              MyText.labelMedium(
                "Bạn chưa có lịch trình 😆",
              ),
              Spacer(),
            ],
          ),
        ),
        Spacer(),
        GestureDetector(
          onTap: () {
            controller.onTapFunctions("tkb");
          },
          child: Icon(
            LucideIcons.chevronRight,
            size: 32,
            color: theme.primaryColor,
          ),
        )
      ],
    );
  }

  // Widget _indicator(bool isActive) {
  //   var theme = LocalStorageServices.getThemeIsLight() ? AppTheme.light : AppTheme.dark;
  //   return AnimatedContainer(
  //     duration: Duration(milliseconds: 300),
  //     curve: Curves.easeInToLinear,
  //     margin: MySpacing.symmetric(horizontal: 4.0),
  //     height: 8.0,
  //     width: 8,
  //     decoration: BoxDecoration(
  //       color: isActive
  //           ? mlColorLightBlue
  //           : mlColorLightBlue.withAlpha(120),
  //       borderRadius: BorderRadius.all(Radius.circular(4)),
  //     ),
  //   );
  // }
  //final _pageController = PageController(initialPage: 0);

  Widget buildCarousel(ThemeData theme) {
    List<TkbTuanGiangVien> tkbs = controller.tkbs;
    //int _numPages = tkbs.length;
    //PageController _pageController = PageController(initialPage: 0);
    //int _currentPage = 0;

    // List<Widget> _buildPageIndicatorStatic() {
    //   List<Widget> list = [];
    //   for (int i = 0; i < _numPages; i++) {
    //     list.add(i == _currentPage ? _indicator(true) : _indicator(false));
    //   }
    //   return list;
    // }
    List<Widget> pageList = [];
    for (int i = 0; i < tkbs.length; i++) {
      pageList.add(schedulingCard(Get.width, theme, tkbs[i]));
    }
    return FlutterCarousel(
      options: FlutterCarouselOptions(
        viewportFraction: 1.0,
        height: 95,
        autoPlay: true,
        autoPlayInterval: Duration(seconds: 5),
        slideIndicator: CircularSlideIndicator(
            slideIndicatorOptions: SlideIndicatorOptions(
                itemSpacing: 12,
                indicatorBackgroundColor: Colors.grey[100]!,
                indicatorBorderWidth: 1,
                indicatorRadius: 3,
                indicatorBorderColor: theme.colorScheme.primary.withOpacity(0.6)
            )
        ),
        indicatorMargin: 0,
      ),
      items: pageList.map((i) {
        return i;
      }).toList(),
    );

    // return Column(
    //   children: [
    //     SizedBox(
    //       width: 400,
    //       height: 85,
    //       child: PageView(
    //           pageSnapping: true,
    //           physics: ClampingScrollPhysics(),
    //           controller: _pageController,
    //           onPageChanged: (int page) {
    //             _currentPage = page;
    //           },
    //           children: pageList
    //       ),
    //     ),
    //     Container(
    //       padding: MySpacing.bottom(8),
    //       child: Row(
    //         mainAxisAlignment: MainAxisAlignment.center,
    //         children: _buildPageIndicatorStatic(),
    //       ),
    //     )
    //   ],
    // );

  }

}
