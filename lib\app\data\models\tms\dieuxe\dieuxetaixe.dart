import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxetaixe.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/giaydieuxe.dart';

class DieuXeTaiXe {
  DieuXeTaiXe({
    this.id,
    // this.phieuDieuXeId,
    this.xe,
    this.taiXe,
    this.ngayThang,
    this.ngayDi,
    this.ngayVe,
    this.soLenhDieuXe,
    // this.nguoiDung,
    this.canBoDiCung,
    this.soKmDuKien,
  });

  final int? id;
  // final int? phieuDieuXeId;
  final Xe? xe;
  final NguoiTao? taiXe;
  final DateTime? ngayThang;
  final DateTime? ngayDi;
  final DateTime? ngayVe;
  final String? soLenhDieuXe;
  // final TaiXe? nguoiDung;
  final String? canBoDiCung;
  final String? soKmDuKien;

  DieuXeTaiXe copyWith({
    int? id,
    // int? phieuDieuXeId,
    Xe? xe,
    NguoiTao? taiXe,
    DateTime? ngayDi,
    DateTime? ngayVe,
    String? soLenhDieuXe,
    // TaiXe? nguoiDung,
    String? canBoDiCung,
    String? soKmDuKien,
  }) {
    return DieuXeTaiXe(
      id: id ?? this.id,
      // phieuDieuXeId: phieuDieuXeId ?? this.phieuDieuXeId,
      xe: xe ?? this.xe,
      taiXe: taiXe ?? this.taiXe,
      ngayThang: ngayThang ?? ngayThang,
      ngayDi: ngayDi ?? this.ngayDi,
      ngayVe: ngayVe ?? this.ngayVe,
      soLenhDieuXe: soLenhDieuXe ?? this.soLenhDieuXe,
      // nguoiDung: nguoiDung ?? this.nguoiDung,
      canBoDiCung: canBoDiCung ?? this.canBoDiCung,
      soKmDuKien: soKmDuKien ?? this.soKmDuKien,
    );
  }

  factory DieuXeTaiXe.fromJson(Map<String, dynamic> json) {
    return DieuXeTaiXe(
      id: json["id"],
      // phieuDieuXeId: json["phieuDieuXeId"],
      xe: json["xe"] == null ? null : Xe.fromJson(json["xe"]),
      taiXe: json["taiXe"] == null ? null : NguoiTao.fromJson(json["taiXe"]),
      ngayThang: DateTime.tryParse(json["ngayThang"] ?? ""),
      ngayDi: DateTime.tryParse(json["ngayDi"] ?? ""),
      ngayVe: DateTime.tryParse(json["ngayVe"] ?? ""),
      soLenhDieuXe: json["soLenhDieuXe"],
      // nguoiDung:
      //     json["nguoiDung"] == null ? null : TaiXe.fromJson(json["nguoiDung"]),
      canBoDiCung: json["canBoDiCung"],
      soKmDuKien: json["soKmDuKien"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        // "phieuDieuXeId": phieuDieuXeId,
        "xe": xe?.toJson(),
        "taiXe": taiXe?.toJson(),
        "ngayThang": ngayThang?.toIso8601String(),
        "ngayDi": ngayDi?.toIso8601String(),
        "ngayVe": ngayVe?.toIso8601String(),
        "soLenhDieuXe": soLenhDieuXe,
        // "nguoiDung": nguoiDung?.toJson(),
        "canBoDiCung": canBoDiCung,
        "soKmDuKien": soKmDuKien,
      };

  static List<DieuXeTaiXe> parse(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed
        .map<DieuXeTaiXe>((json) => DieuXeTaiXe.fromJson(json))
        .toList();
  }

  @override
  String toString() {
    return "$id, $xe, $taiXe, $ngayThang, $ngayDi, $ngayVe, $soLenhDieuXe, $canBoDiCung, $soKmDuKien,"; //, $nguoiDung $phieuDieuXeId,
  }
}

class XeNvLai {
  XeNvLai({
    required this.xe,
    required this.nvLai,
  });

  final List<Xe>? xe;
  final List<NguoiTao>? nvLai;

  factory XeNvLai.empty() {
    return XeNvLai(
      xe: [],
      nvLai: [],
    );
  }

  XeNvLai copyWith({
    List<Xe>? xe,
    List<NguoiTao>? nvLai,
  }) {
    return XeNvLai(
      xe: xe ?? this.xe,
      nvLai: nvLai ?? this.nvLai,
    );
  }

  factory XeNvLai.fromJson(Map<String, dynamic> json) {
    return XeNvLai(
      xe: json["xe"] == null
          ? []
          : List<Xe>.from(json["xe"]!.map((x) => Xe.fromJson(x))),
      nvLai: json["nvLai"] == null
          ? []
          : List<NguoiTao>.from(
              json["nvLai"]!.map((x) => NguoiTao.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "xe": xe?.map((x) => x.toJson()).toList(),
        "nvLai": nvLai?.map((x) => x.toJson()).toList(),
      };

  @override
  String toString() {
    return "$xe, $nvLai, ";
  }
}

class Xe {
  Xe({
    this.id,
    this.tenXe,
    this.bienKiemSoat,
    this.soChoNgoi,
    this.nvLai,
  });

  final int? id;
  final String? tenXe;
  final String? bienKiemSoat;
  final String? soChoNgoi;
  final NguoiTao? nvLai;

  Xe copyWith({
    int? id,
    String? tenXe,
    String? bienKiemSoat,
    String? soChoNgoi,
    NguoiTao? nvLai,
  }) {
    return Xe(
      id: id ?? this.id,
      tenXe: tenXe ?? this.tenXe,
      bienKiemSoat: bienKiemSoat ?? this.bienKiemSoat,
      soChoNgoi: soChoNgoi ?? this.soChoNgoi,
      nvLai: nvLai ?? this.nvLai,
    );
  }

  factory Xe.fromJson(Map<String, dynamic> json) {
    return Xe(
      id: json["id"],
      tenXe: json["tenXe"],
      bienKiemSoat: json["bienKiemSoat"],
      soChoNgoi: json["soChoNgoi"],
      nvLai: json["nvLai"] == null ? null : NguoiTao.fromJson(json["nvLai"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "tenXe": tenXe,
        "bienKiemSoat": bienKiemSoat,
        "soChoNgoi": soChoNgoi,
        "nvLai": nvLai?.toJson(),
      };

  @override
  String toString() {
    return "$id, $tenXe, $bienKiemSoat, $soChoNgoi, $nvLai, ";
  }
}
