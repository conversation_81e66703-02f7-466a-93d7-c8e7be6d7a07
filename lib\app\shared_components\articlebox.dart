import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:tvumobile/app/data/models/wordpress/article.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';

Widget articleBox(BuildContext context, Article article, String heroId) {
  return MyContainer(
    width: 300,
    margin: const EdgeInsets.only(top: 8, bottom: 8, left: 8),
    padding: const EdgeInsets.all(0),
    child: Stack(
      children: <Widget>[
        Container(
          alignment: Alignment.bottomRight,
          margin: const EdgeInsets.fromLTRB(20, 16, 8, 0),
          child: Card(
            elevation: 6,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(110, 0, 0, 0),
              child: Column(
                children: <Widget>[
                  Container(
                    padding: const EdgeInsets.fromLTRB(8, 0, 4, 0),
                    child: Column(
                      children: <Widget>[
                        Html(
                          data: article.title!.length > 70
                              ? "<h2>${article.title!.substring(0, 70)}...</h2>"
                              : "<h2>${article.title}</h2>",
                          style: {
                            "h2": Style(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontWeight: FontWeight.w500,
                              fontSize: FontSize(1.05, Unit.em),
                              padding: HtmlPaddings.all(8),
                            )},
                        ),
                        Container(
                          alignment: Alignment.topLeft,
                          child: Container(
                            padding: const EdgeInsets.fromLTRB(4, 8, 4, 8),
                            child: Row(
                              children: <Widget>[
                                const Icon(
                                  Icons.timer,
                                  color: Colors.black45,
                                  size: 12.0,
                                ),
                                const SizedBox(
                                  width: 4,
                                ),
                                Text(
                                  article.date.toString(),
                                  style:
                                  Theme.of(context).textTheme.bodySmall,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        SizedBox(
          height: 170,
          width: 145,
          child: Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            elevation: 0,
            margin: const EdgeInsets.all(10),
            child: Hero(
              tag: heroId,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.0),
                child: Image.network(
                  article.image.toString(),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
        ),
        article.video != ""
            ? Positioned(
          left: 12,
          top: 12,
          child: Card(
            elevation: 8,
            shape: const CircleBorder(),
            clipBehavior: Clip.antiAlias,
            child: CircleAvatar(
              radius: 14,
              backgroundColor: Colors.transparent,
              child: Image.asset("assets/play-button.png"),
            ),
          ),
        )
            : Container(),
      ],
    ),
  );
}