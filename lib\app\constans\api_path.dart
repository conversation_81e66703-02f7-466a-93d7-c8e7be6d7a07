part of 'app_constants.dart';

/// all endpoint api
class ApiPath {
  // Example :
  // static const _BASE_URL = "https://api.flutterwithgia.com";
  // static const product = "$_BASE_URL/product";

  // static const String baseUrl = 'https://mobilegateway.tvu.edu.vn';
  static const String baseUrl = 'https://7b6679218bcf.ngrok-free.app';
  static const String tmsBaseUrl = 'https://tms.tvu.edu.vn/';

  static const String portalNews = '$baseUrl/portal/tvunews';
  static const String tsNews = '$baseUrl/portal/tvutsnews';
  static const String portalNewsContent =
      '$baseUrl/portal/tvunews?content=true';
  static const String eduNewsContent =
      'https://ttsv.tvu.edu.vn/api/web/w-locdsbaiviet';
  static const String apiGiangVienTKB = '$baseUrl/api/giangvien/tkb';
  static const String apiGiangVienCurrentWeekTKB =
      '$baseUrl/api/giangvien/currentweektkb';
  static const String apiGiangVienCVN = '$baseUrl/api/giangvien/congvannhan';
  static const String apiGiangVienCTCV =
      '$baseUrl/api/giangvien/chitietcongvan';
  static const String apiGiangVienFCM = '$baseUrl/api/giangvien/addfcm';

  // ------------------------------------ ĐƠN NGHỈ PHÉP ------------------------------------------
  static const String apiCheckLanhDaoOrBGH =
      '$baseUrl/api/giangvien/checklanhdaoorbgh';
  static const String apiGetDNPinfo =
      '$baseUrl/api/giangvien/getdonnghiphepinfo';
  static const String apiGetdsDNP = '$baseUrl/api/giangvien/getdsdonnghiphep';
  static const String apiGetdsDNPDonVi =
      '$baseUrl/api/giangvien/getdsdonnghiphepdonvi';
  static const String apiGetChiTietDNP =
      '$baseUrl/api/giangvien/getchitietdonnghiphep';
  static const String apiChuyenDNP =
      '$baseUrl/api/giangvien/chuyendonnghiphep'; // chuyển đơn và thu hồi đơn chờ duyệt
  static const String apiPostDNP =
      '$baseUrl/api/giangvien/createdonnghiphep'; // thêm đơn
  static const String apiPutDNP =
      '$baseUrl/api/giangvien/updatedonnghiphep'; // chỉnh sửa đơn
  static const String apiXoaDNP =
      '$baseUrl/api/giangvien/deletedonnghiphep'; // xóa đơn
  static const String apiDuyetDNP =
      '$baseUrl/api/giangvien/duyetdonnghiphep'; // duyệt đơn
  static const String apiTraDNP =
      '$baseUrl/api/giangvien/tradonnghiphep'; // trả đơn
  static const String apiTraDNPDaDuyet =
      '$baseUrl/api/giangvien/tradonnghiphepdaduyet'; // trả đơn đã duyệt

  // --------------------------------------- ĐIỀU XE ---------------------------------------------
  static const String apiGetDXinfo = '$baseUrl/api/giangvien/getdieuxeinfo';
  static const String apiCheckPhanQuyenDX =
      '$baseUrl/api/giangvien/checkphanquyendieuxe';
  static const String apiGetdsPhieuDX = '$baseUrl/api/giangvien/getdsdieuxe';
  static const String apiGetdsVienChuc = '$baseUrl/api/giangvien/getdsvienchuc';
  static const String apiGetdsXeNvLai = '$baseUrl/api/giangvien/getdsxenvlai';
  static const String apiGetChiTietPhieuDX =
      '$baseUrl/api/giangvien/getchitietphieudx';
  static const String apiGetDXTXTheoNgay =
      '$baseUrl/api/giangvien/getdxtxtheongay';
  static const String apiCreatePhieuDX =
      '$baseUrl/api/giangvien/createphieudieuxe';
  static const String apiUpdatePhieuDX =
      '$baseUrl/api/giangvien/updatephieudieuxe';
  static const String apiDeletePhieuDX =
      '$baseUrl/api/giangvien/deletephieudieuxe';
  static const String apiChuyenPhieuDX =
      '$baseUrl/api/giangvien/chuyenphieudieuxe'; // chuyển và thu hồi phiếu chờ duyệt
  static const String apiDuyetDXDonVi =
      '$baseUrl/api/giangvien/duyetdieuxedv'; // duyệt tại đơn vị
  static const String apiDuyetDXHcTc =
      '$baseUrl/api/giangvien/duyetdieuxehctc'; // duyệt tại phòng HCTC
  static const String apiTraPhieuDX =
      '$baseUrl/api/giangvien/traphieudieuxe'; // trả phiếu
  static const String apiDieuChinhPhieuDX =
      '$baseUrl/api/giangvien/dieuchinhphieudieuxe'; // yêu cầu điều chỉnh phiếu
  static const String apiMergePhieuDX =
      '$baseUrl/api/giangvien/mergephieudieuxe'; // ghép phiếu
  static const String apiTraPhieuDXDaDuyet =
      '$baseUrl/api/giangvien/traphieudieuxedaduyet'; // trả phiếu đã duyệt
  static const String apiCreateHDThueXe =
      '$baseUrl/api/giangvien/createhdthuexe'; // thêm hợp đồng thuê xe
  static const String apiUpdateHDThueXe =
      '$baseUrl/api/giangvien/updatehdthuexe'; // sửa hợp đồng thuê
  static const String apiDeleteHDThueXe =
      '$baseUrl/api/giangvien/deletehdthuexe'; // xóa hợp đồng thuê xe

  // -------------------------------------- CHẤM CÔNG --------------------------------------------
  static const String apiGetDMChamCong =
      '$baseUrl/api/giangvien/getloaichamcong';
  static const String apiPostWidgets = '$baseUrl/api/giangvien/getjson';

  // -------------------------------------- LƯƠNG THUẾ -------------------------------------------
  static const String apiGetLuongthang = '$baseUrl/api/giangvien/getluongthang';
  static const String apiGetThuethang = '$baseUrl/api/giangvien/getthuethang';
  static const String apiGetThuenam = '$baseUrl/api/giangvien/getthuenam';

  // ----------------------------------------- KÝ SỐ ---------------------------------------------
  static const String apiUploadImageGiangvien =
      '$baseUrl/api/Giangvien/uploadimage';
  static const String apiGetImageChuKyGiangvien =
      '$baseUrl/api/Giangvien/getimagechuky';
  static const String apiGetDSFileChoKyGiangvien =
      '$baseUrl/api/Giangvien/GetDSFileChoKy';
  static const String apiGetDSFileDaKyGiangvien =
      '$baseUrl/api/Giangvien/GetDSFileDaKy';

  // ----------------------------------------- .... ----------------------------------------------
  static const String apiPortalFCM = '$baseUrl/portal/addfcm';
  static const String apiGetDonviByQr = '$baseUrl/portal/getdonvibyqr';
  static const String apiPostDGDonvi = '$baseUrl/portal/danhgiadonvi';
  static const String apiGetXeByQr = '$baseUrl/portal/getxebyqr';
  static const String apiGetQrCaNhan = '$baseUrl/portal/getqrcanhan';

  static const String apiSinhVienTKB = '$baseUrl/api/sinhvien/tkb';
  static const String apiSinhVienCurrentWeekTKB =
      '$baseUrl/api/sinhvien/currentweektkb';
  static const String apiSinhVienDiem = '$baseUrl/api/sinhvien/diem';
  static const String apiSinhVienHp = '$baseUrl/api/sinhvien/hp';

  // ----------------------------------------- TASK ----------------------------------------------
  static const String apiTaskOverview = '$baseUrl/api/Projects/gettaskoverview';
  static const String apiTaskStatuses = '$baseUrl/api/Projects/gettaskstatus';
  static const String apiDonViTinh = '$baseUrl/api/Projects/getdonvitinh';
  static const String apiTaskStatistics =
      '$baseUrl/api/Projects/task-statistics';
  static const String apiAllTasks = '$baseUrl/api/Projects/gettasks';
  static const String apigetTeamMembers = '$baseUrl/api/Projects/team-members';
  static const String apigetVienChucs = '$baseUrl/api/Projects/findvienchucs';
  static const String apiGetDanhMucs = '$baseUrl/api/Projects/getdanhmucs';
  static const String apiGetDocuments = '$baseUrl/api/Projects/finddocuments';
  static const String apiGetDocumentById =
      '$baseUrl/api/Projects/finddocumentbyid';

  // Thêm task
  static const String apiCreateTask = '$baseUrl/api/Projects/createnewtasks';

  // Sửa task
  static const String apiUpdateTask = '$baseUrl/api/Projects/updatetask';

  // Xóa task
  static const String apiDeleteTask = '$baseUrl/api/Projects/deletetask';

  // Cập nhật trạng thái công việc, tự đánh giá và người giao việc đánh giá
  static const String apiUpdateTaskInfo =
      '$baseUrl/api/Projects/updatetaskinfo';

  // Thao tác với task resource
  static const String apiAddTaskResources =
      '$baseUrl/api/Projects/addtaskresource';
  static const String apiDeleteTaskResources =
      '$baseUrl/api/Projects/deletetaskresource';

  // Thao tác với task update info
  static const String apiAddTaskUpdateInfo =
      '$baseUrl/api/Projects/addtaskupdateinfo';
  static const String apiEditTaskUpdateInfo =
      '$baseUrl/api/Projects/edittaskupdateinfo';
  static const String apiDeleteTaskUpdateInfo =
      '$baseUrl/api/Projects/deletetaskupdateinfo';

  // Thao tác với task action
  static const String apiAddTaskAction = '$baseUrl/api/Projects/addtaskaction';
  static const String apiEditTaskAction =
      '$baseUrl/api/Projects/edittaskaction';
  static const String apiDeleteTaskAction =
      '$baseUrl/api/Projects/deletetaskaction';

  static const String tvuWordpressURL = 'https://www.tvu.edu.vn/';
}
