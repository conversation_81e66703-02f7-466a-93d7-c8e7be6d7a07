import 'package:flutter/material.dart';
import 'package:tvumobile/app/utils/helpers/app_helpers.dart';


class AssigneeTileWidget extends StatelessWidget {
  const AssigneeTileWidget(
      {super.key, required this.onTap, required this.selectedUser, required this.isChecked});
  final bool isChecked;
  final dynamic selectedUser;
  final Function onTap;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        radius: 20,
        backgroundImage: NetworkImage(selectedUser["hinhAnh"]!),
      ),
      title: Text(
        getHoTen(selectedUser),
        style: Theme.of(context).textTheme.bodyMedium,
      ),
      trailing: GestureDetector(
        onTap: () {
          onTap(selectedUser);
        },
        child: Material(
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(45),
              side: BorderSide(color: isChecked ? Colors.transparent : Colors.grey)),
          child: CircleAvatar(
            radius: 12,
            backgroundColor: isChecked ? Colors.green : Colors.transparent,
            child: Icon(
              Icons.check,
              color: isChecked ? Colors.white : Colors.grey,
              size: 18,
            ),
          ),
        ),
      ),
    );
  }
}
