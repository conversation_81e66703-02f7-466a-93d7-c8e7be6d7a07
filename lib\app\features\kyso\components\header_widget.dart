// header_widget.dart
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class HeaderWidget extends StatelessWidget {
  const HeaderWidget({
    super.key,
    required this.currentPage,
    required this.pdfViewerController,
    required this.signatureImageBytes,
    required this.signatureImageBytesPlaceholder,
    required this.showSignatureDialogue,
  });

  final int currentPage;
  final PdfViewerController pdfViewerController;
  final Uint8List? signatureImageBytes;
  final Uint8List? signatureImageBytesPlaceholder;
  final VoidCallback showSignatureDialogue;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Chữ ký hiện tại: '),
              Image.memory(
                signatureImageBytes ?? signatureImageBytesPlaceholder!,
                height: 50,
                width: 100,
              ),
              MyButton.small(
                onPressed: showSignatureDialogue,
                borderRadiusAll: 5,
                child:
                    MyText.labelLarge('Chọn chữ ký khác', color: Colors.white),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            child: Container(
              margin: const EdgeInsets.only(bottom: 8.0),
              decoration: const BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey,
                    blurRadius: 8,
                    offset: Offset(0, 1),
                  ),
                ],
              ),
              height: 60,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Trang: $currentPage/${pdfViewerController.pageCount}'),
                  const SizedBox(width: 20),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          currentPage <= 1 ? Colors.grey : Colors.blue),
                    ),
                    icon: const Icon(
                      Icons.first_page,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      pdfViewerController.firstPage();
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          currentPage <= 1 ? Colors.grey : Colors.blue),
                    ),
                    icon: const Icon(
                      Icons.navigate_before,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      pdfViewerController.jumpToPage(currentPage - 1);
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          pdfViewerController.pageCount == currentPage
                              ? Colors.grey
                              : Colors.blue),
                    ),
                    icon: const Icon(
                      Icons.navigate_next,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      pdfViewerController.jumpToPage(currentPage + 1);
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          pdfViewerController.pageCount == currentPage
                              ? Colors.grey
                              : Colors.blue),
                    ),
                    icon: const Icon(
                      Icons.last_page,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      pdfViewerController.lastPage();
                    },
                  ),
                  const SizedBox(width: 20),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Colors.lightBlue),
                    ),
                    icon: const Icon(
                      Icons.zoom_out_map,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      pdfViewerController.zoomLevel = 1.0;
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Colors.lightBlue),
                    ),
                    icon: const Icon(
                      Icons.zoom_in,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      pdfViewerController.zoomLevel = 2.0;
                    },
                  ),
                  IconButton(
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Colors.lightBlue),
                    ),
                    icon: const Icon(
                      Icons.zoom_out,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      pdfViewerController.zoomLevel = 0.5;
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
