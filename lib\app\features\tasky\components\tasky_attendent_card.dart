import 'package:flutter/material.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';


class AttendentCard extends StatelessWidget {
  const AttendentCard({
    required this.onPressedCheck,
    super.key, this.height = 100, this.elevation = 4.5,
  });

  final Function() onPressedCheck;
  final double height;
  final double elevation;
  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Card(
      elevation: elevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kBorderRadius),
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(kBorderRadius),
            child: Align(
              alignment: Alignment.bottomRight,
              child: Transform.translate(
                offset: const Offset(20, 0),
                child: SizedBox(
                  height: height.toDouble(),
                  width: 200,
                  child: Image.asset(ImageTaskyPath.timeManagement, fit: BoxFit.fitHeight,),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              left: kSpacing,
              top: 1,
              bottom: 4
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Hôm nay bạn có làm việc không?",
                  style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                MySpacing.height(8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    MyButton.small(
                      borderRadiusAll: 2,
                      elevation: 2,
                      onPressed: onPressedCheck,
                      child: MyText.labelLarge("Có mặt!", color: theme.colorScheme.onPrimary ),
                    ),
                    MySpacing.width(8),
                    MyButton.small(
                      backgroundColor: theme.colorScheme.primary.withOpacity(0.5),
                      borderRadiusAll: 2,
                      elevation: 2,
                      onPressed: onPressedCheck,
                      child: MyText.labelLarge("Khác?", color: theme.colorScheme.onPrimary ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
