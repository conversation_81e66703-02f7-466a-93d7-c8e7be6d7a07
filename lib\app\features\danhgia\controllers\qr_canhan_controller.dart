import 'package:flutter/foundation.dart';

import 'package:get/get.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';

import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class QrCaNhanController extends GetxController {
  ApiProvider apiProvider = Get.find();

  final RxString qrCaNhan = "".obs; // Lưu duyetai trong controller
  final RxBool isLoading = true.obs; // Trạng thái loading

  UserInfo? userinfo = LocalStorageServices.getUserInfo();

  @override
  void onInit() {
    super.onInit();
    if (kDebugMode) {
      print("QrCaNhanController onInit");
    }
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    if (kDebugMode) {
      print("QrCaNhanController onReady");
    }
    await getQRCa<PERSON>han();
  }

  // L<PERSON>y thông tin mã QR
  getQRCaNhan() async {
    final maQR = await apiProvider.getQrCaNhan();
    qrCaNhan.value = maQR;
    isLoading.value = false; // Trạng thái loading
    update();
  }
}
