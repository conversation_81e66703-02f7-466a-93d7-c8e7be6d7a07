import 'package:get/get.dart';
import 'package:tvumobile/app/services/api_provider.dart';

class HpSinhVienController extends GetxController {
  bool uiLoading = true;

  ApiProvider apiProvider = Get.find();

  late DateTime minDate, maxDate;

  @override
  Future<void> onInit() async {
    super.onInit();
  }

  @override
  Future<void> onReady() async {
    await getTKBFunctions();
  }
  getTKBFunctions() async {

  }

  void goBack() {
    Get.back();
    // Navigator.pop(context);
  }
}