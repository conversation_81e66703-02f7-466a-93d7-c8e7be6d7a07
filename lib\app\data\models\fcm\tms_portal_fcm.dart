import 'dart:convert';

class TmsPortalFcmModel {
  TmsPortalFcmModel({
    required this.id,
    required this.taiKhoanId,
    required this.vienChucId,
    required this.sinhVienId,
    required this.fcmToken,
    required this.vienChuc,
    required this.taiKhoan,
    required this.sinhVien,
  });

  final int? id;
  final int? taiKhoanId;
  final int? vienChucId;
  final int? sinhVienId;
  final String? fcmToken;
  final dynamic vienChuc;
  final dynamic taiKhoan;
  final dynamic sinhVien;

  factory TmsPortalFcmModel.fromJson(Map<String, dynamic> json){
    return TmsPortalFcmModel(
      id: json["id"],
      taiKhoanId: json["taiKhoanId"],
      vienChucId: json["vienChucId"],
      sinhVienId: json["sinhVienId"],
      fcmToken: json["fcmToken"],
      vienChuc: json["vienChuc"],
      taiKhoan: json["taiKhoan"],
      sinhVien: json["sinhVien"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "taiKhoanId": taiKhoanId,
    "vienChucId": vienChucId,
    "sinhVienId": sinhVienId,
    "fcmToken": fcmToken,
    "vienChuc": vienChuc,
    "taiKhoan": taiKhoan,
    "sinhVien": sinhVien,
  };

  @override
  String toString(){
    return "$id, $taiKhoanId, $vienChucId, $sinhVienId, $fcmToken, $vienChuc, $taiKhoan, $sinhVien, ";
  }

  String toJsonString() => json.encode(toJson());
  factory TmsPortalFcmModel.fromJsonString(String source) => TmsPortalFcmModel.fromJson(json.decode(source));
  static List<TmsPortalFcmModel> parseNews(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed.map<TmsPortalFcmModel>((json) => TmsPortalFcmModel.fromJson(json)).toList();
  }
}
