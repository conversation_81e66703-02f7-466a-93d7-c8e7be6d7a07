// ignore: use_string_in_part_of_directives
part of app_helpers;

class StringHelper {}

extension StringUtil on String {
  Color get toColor {
    String data = replaceAll("#", "");
    if (data.length == 6) {
      data = "FF$data";
    }
    return Color(int.parse("0x$data"));
  }

  String maxLength(int length) {
    if (length > this.length) {
      return this;
    } else {
      return substring(0, length);
    }
  }

  String toParagraph([bool addDash = false]) {
    return addDash ? "-\t$this" : "\t$this";
  }

  bool toBool([bool defaultValue = false]) {
    if (toString().compareTo('1') == 0 || toString().compareTo('true') == 0) {
      return true;
    } else if (toString().compareTo('0') == 0 ||
        toString().compareTo('false') == 0) {
      return false;
    }
    return defaultValue;
  }

  int toInt([int defaultValue = 0]) {
    try {
      return int.parse(this);
    } catch (e) {
      return defaultValue;
    }
  }

  double toDouble([double defaultValue = 0]) {
    try {
      return double.parse(this);
    } catch (e) {
      return defaultValue;
    }
  }

  String get getFileExt {
    if (this == "") {
      return "";
    }
    String tmp = split('.').last;
    return tmp;
  }

  Uri toUri() {
    return Uri.parse(this);
  }

  dynamic toJson() {
    return jsonDecode(this);
  }

  String getClips(int maxchar) {
    if (length <= maxchar) {
      return this;
    }
    String tmp = substring(0, maxchar - 6);
    tmp += '...${substring(length - 6)}';
    return tmp;
  }

  String get getAvatarCV {
    if (this == "") {
      return "CV";
    }
    String ret = "CV";
    ret = split(' ').map((word) => word[0].toUpperCase()).join('');
    return ret;
  }

  String get getAvatarDNP {
    if (this == "") {
      return "CD";
    }
    if (toLowerCase().contains("chờ duyệt")) {
      return "CD";
    }
    if (toLowerCase().contains("đơn vị đã duyệt")) {
      return "ĐVD";
    }
    if (toLowerCase().contains("hủy, đã thu hồi hoặc trả về")) {
      return "TH\nTV";
    }
    return split(' ').map((word) => word[0].toUpperCase()).join('');
  }
}
