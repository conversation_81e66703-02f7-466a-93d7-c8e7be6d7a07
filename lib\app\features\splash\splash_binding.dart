import 'package:get/get.dart';
import 'package:tvumobile/app/features/splash/splash_screen2_controller.dart';
import 'splash_controller.dart';

class SplashBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<SplashController>(SplashController());
  }
}

class Splash2Binding extends Bindings {
  @override
  void dependencies() {
    Get.put<SplashScreen2Controller>(SplashScreen2Controller());
  }
}
