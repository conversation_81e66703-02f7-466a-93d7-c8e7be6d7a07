import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Shows a custom info dialog
Future<void> showInfoDialog({
  required BuildContext context,
  required VoidCallback onOKButtonPressed,
  String title = '',
  String subTitle = '',
  TextAlign textTitleAlign = TextAlign.center,
  TextAlign textSubTitleAlign = TextAlign.center,
  Color? colorSubtitle,
  required Widget child,
}) async {
  ThemeData theme = Theme.of(Get.context!);
  return await showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              textAlign: textTitleAlign,
              style: const TextStyle(
                fontSize: 18.0,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (subTitle.isNotEmpty)
              Text(
                subTitle,
                textAlign: textSubTitleAlign,
                style: TextStyle(
                  fontSize: 14.0,
                  color: colorSubtitle ?? Colors.black54,
                ),
              ),
          ],
        ),
        content: child,
        actions: [
          TextButton(
            onPressed: onOKButtonPressed,
            child: Text(
              'OK',
              style: TextStyle(
                color: theme.colorScheme.secondary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      );
    },
  );
}

/// Shows a custom dialog
Future<void> showCustomDialog({
  required BuildContext context,
  required Widget child,
}) async {
  return await showDialog(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: child,
      );
    },
  );
}

/// GetX-specific dialog helpers
class GetxDialogHelper {
  /// Shows an info dialog using GetX
  static Future<void> showInfoDialog({
    required String title,
    required String message,
    String buttonText = 'OK',
    Widget? content,
    Color? colorTitle,
    Color? colorMessage,
  }) async {
    ThemeData theme = Theme.of(Get.context!);
    return Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
            color: colorTitle ?? Colors.black,
          ),
        ),
        content: content ??
            Text(
              message,
              style: TextStyle(
                fontSize: 14.0,
                color: colorMessage ?? Colors.black54,
              ),
            ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              buttonText,
              style: TextStyle(
                color: theme.colorScheme.secondary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  /// Shows a custom dialog using GetX
  static Future<void> showCustomDialog({
    required Widget child,
  }) async {
    return Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: child,
      ),
      barrierDismissible: true,
    );
  }

  /// Shows a success dialog
  static Future<void> showSuccessDialog({
    required String title,
    required String message,
  }) async {
    return showInfoDialog(
      title: title,
      message: message,
      colorTitle: Colors.green,
    );
  }

  /// Shows an error dialog
  static Future<void> showErrorDialog({
    String title = 'Error',
    required String message,
  }) async {
    return showInfoDialog(
      title: title,
      message: message,
      colorTitle: Colors.red,
    );
  }
}
