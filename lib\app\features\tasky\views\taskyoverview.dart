import 'package:circular_profile_avatar/circular_profile_avatar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/features/tasky/components/build_tasklist.dart';
import 'package:tvumobile/app/features/tasky/components/tasky_progress_card.dart';
import 'package:tvumobile/app/features/tasky/components/taskyappbar.dart';
import 'package:tvumobile/app/features/tasky/controllers/taskyoverview_controller.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/extensions/extensions.dart';

import '../../../shared_components/my_spacing.dart';

class TaskyOverview extends GetView<TaskyOverviewController> {
  const TaskyOverview({super.key});

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    ThemeData theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: buildAppBarTasky(context, "Quản lý công việc"),
      body: GetBuilder<TaskyOverviewController>(builder: (controller) {
        return RefreshIndicator(
          onRefresh: () async {
            // Gọi hàm getOverviews để làm mới dữ liệu
            await controller.getOverviews();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: SizedBox(
                  height: 130,
                  width: size.width,
                  child: controller.taskStats.isNotEmpty
                      ? ProgressCard(
                          data: ProgressCardData(
                            totalUndone:
                                "${controller.taskStats['todoTasks']}".toInt(),
                            totalTaskInProress:
                                "${controller.taskStats['inProgressTasks']}"
                                    .toInt(),
                            totalTaskDone:
                                "${controller.taskStats['completedTasks']}"
                                    .toInt(),
                          ),
                          onPressedCheck: () {
                            Get.toNamed(AppPages.TASK_CREATE);
                          },
                        )
                      : SkeletonListView(),
                ),
              ),
              Container(
                color: theme
                    .secondaryHeaderColor, //customGreyColor.withOpacity(.3),
                height: 45,
                width: size.width,
                child: Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 16.0),
                      child: Text(
                        "Đơn vị của bạn",
                        style: Theme.of(context)
                            .textTheme
                            .bodyLarge!
                            .copyWith(fontWeight: FontWeight.w600),
                      ),
                    )),
              ),
              MySpacing.height(8),
              Padding(
                padding:
                    const EdgeInsets.only(top: 0, left: 8, right: 8, bottom: 4),
                child: controller.teamMembers.isEmpty
                    ? SkeletonParagraph()
                    : Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: controller.teamMembers.map((ele) {
                          return CircularProfileAvatar(ele["hinhAnh"],
                              initialsText: Text(
                                ele["ten"],
                                style: const TextStyle(
                                    fontSize: 20, color: Colors.white),
                              ),
                              imageFit: BoxFit.cover,
                              radius: 32,
                              elevation: 2, onTap: () {
                            Logger().i(ele);
                          });
                        }).toList(),
                      ),
              ),
              MySpacing.height(8),
              /*FlutterImageStack(
                  imageList: controller.teamMembers.map((ele) => ele["hinhAnh"].toString()).toList(),
                  showTotalCount: true,
                  totalCount: controller.teamMembers.length,
                  itemRadius: 80, // Radius of each images
                  itemCount: controller.teamMembers.length, // Maximum number of images to be shown in stack
                  itemBorderWidth: 2, // Border width around the images
                ),
                */
              Container(
                color: theme
                    .secondaryHeaderColor, //customGreyColor.withOpacity(.3),
                height: 45,
                width: size.width,
                margin: EdgeInsets.only(bottom: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 16.0),
                        child: Text(
                          'Việc cần làm',
                          style: Theme.of(context)
                              .textTheme
                              .bodyLarge!
                              .copyWith(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(right: 16.0),
                      child: MyButton.text(
                        onPressed: () {
                          Get.toNamed(AppPages.TASKVIEW);
                        },
                        child: Row(
                          children: [
                            MyText.labelMedium('Xem tất cả',
                                fontWeight: 500,
                                xMuted: true,
                                color: theme.colorScheme.onSurface),
                            Icon(
                              Icons.navigate_next,
                              color: theme.colorScheme.onSurface,
                              size: 20,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (controller.allTasks.isEmpty)
                SizedBox(
                    height: MediaQuery.of(context).size.height - 500,
                    child: SkeletonListView())
              // Center(child: MyText.titleLarge("Không có dữ liệu"))
              else
                for (var task in controller.allTasks)
                  buildTaskList(context, task),
              // ---------------------------------------------------------------
              // Expanded(
              //   child: controller.allTasks.isEmpty? SkeletonListView() :
              //   ListView.separated(
              //       shrinkWrap: true,
              //       padding: const EdgeInsets.fromLTRB(8, 1, 8, 1),
              //       separatorBuilder: (context, index) => const Divider(color: customGreyColor,),
              //       itemCount: controller.allTasks.length,
              //       itemBuilder: (context, index) {
              //         return buildTaskList(context, controller.allTasks[index]);
              //       }
              //   )
              // ),
            ]),
          ),
        );
      }),
    );
  }

  Widget getCard12(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Card(
            color: Colors.deepOrangeAccent,
            shadowColor: Colors.deepOrangeAccent,
            elevation: 2,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text("Activity", style: theme.textTheme.titleMedium),
                ),
                Align(
                  alignment: Alignment.bottomRight,
                  child: Transform.translate(
                    offset: const Offset(10, 30),
                    child: SizedBox(
                      height: 120,
                      width: 200,
                      child: SvgPicture.asset(
                        ImageVectorPath.happy2,
                        fit: BoxFit.fitHeight,
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}
