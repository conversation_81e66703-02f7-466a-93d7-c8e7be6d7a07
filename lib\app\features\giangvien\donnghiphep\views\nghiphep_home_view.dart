import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/dnp_list.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/nghiphep_home_controller.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/nghiphep_home_appbar.dart';
import 'package:tvumobile/app/shared_components/empty_card.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class NghiPhepHomeView extends GetView<NghiPhepHomeController> {
  const NghiPhepHomeView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: NghiPhepAppBar(context, controller),
      body: SingleChildScrollView(
        padding: const EdgeInsets.fromLTRB(16, 4, 16, 4),
        child: Column(
          children: [
            // --------------------------------- THÔNG TIN CỦA BẠN ---------------------------------
            Card(
              child: Column(
                children: [
                  MyContainer(
                    padding: EdgeInsets.only(top: 5),
                    marginAll: 0,
                    height: 35,
                    width: double.infinity,
                    color: theme.secondaryHeaderColor,
                    child: MyText.titleMedium(
                      "Thông tin của bạn",
                      color: theme.colorScheme.onSurface,
                      textAlign: TextAlign.center,
                      fontWeight: 700,
                    ),
                  ),
                  MySpacing.height(8),
                  Obx(() {
                    if (controller.danghi.value < 0) {
                      return SkeletonLine();
                    }
                    return MyText.labelLarge(
                        "Số ngày phép đã nghỉ: ${controller.danghi.value}",
                        color: theme.colorScheme.onSurface);
                  }),
                  MySpacing.height(8),
                  Obx(() {
                    if (controller.conlai.value < 0) {
                      return SkeletonLine();
                    }
                    return MyText.labelLarge(
                        "Số ngày phép còn lại: ${controller.conlai.value}",
                        color: theme.colorScheme.onSurface);
                  }),
                  Divider(
                    color: Colors.grey[300],
                    height: 8,
                  ),
                  Obx(
                    () {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Column(
                            children: [
                              Center(
                                child: MyButton.small(
                                  onPressed: () {
                                    Get.toNamed(AppPages.DONNGHIPHEP,
                                        preventDuplicates: true);
                                  },
                                  child: Icon(
                                    LucideIcons.filePlus2,
                                    color: theme.colorScheme.onPrimary,
                                  ),
                                ),
                              ),
                              MyText.bodySmall(
                                "Tạo đơn",
                                color: theme.primaryColor,
                              ),
                            ],
                          ),
                          // MySpacing.width(28),
                          if (!controller.isLanhDaoOrBGH.value.contains("vc"))
                            Column(
                              children: [
                                Center(
                                  child: MyButton.small(
                                    onPressed: () {
                                      Get.toNamed(AppPages.DUYETDNP,
                                          preventDuplicates: true);
                                    },
                                    child: Icon(
                                      LucideIcons.fileCheck2,
                                      color: theme.colorScheme.onPrimary,
                                    ),
                                  ),
                                ),
                                MyText.bodySmall(
                                  "Duyệt đơn",
                                  color: theme.primaryColor,
                                ),
                              ],
                            ),
                          // MySpacing.width(28),
                          Column(
                            children: [
                              Center(
                                child: MyButton.small(
                                  onPressed: () {
                                    Get.toNamed(AppPages.LICHNGHIPHEP,
                                        preventDuplicates: true);
                                  },
                                  child: Icon(
                                    LucideIcons.calendarX2,
                                    color: theme.colorScheme.onPrimary,
                                  ),
                                ),
                              ),
                              MyText.bodySmall(
                                "Lịch nghỉ",
                                color: theme.primaryColor,
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                  MySpacing.height(8),
                ],
              ),
            ),

            // ------------------------------ DANH SÁCH ĐƠN NGHỈ PHÉP ------------------------------
            Card(
              child: Column(
                children: [
                  MyContainer(
                    padding: EdgeInsets.only(top: 5),
                    marginAll: 0,
                    height: 35,
                    width: double.infinity,
                    color: theme.secondaryHeaderColor,
                    child: MyText.bodyLarge(
                      "Danh sách đơn nghỉ phép",
                      color: theme.colorScheme.onSurface,
                      textAlign: TextAlign.center,
                      fontWeight: 700,
                    ),
                  ),
                  Obx(() {
                    return SizedBox(
                      height: controller.dsDNP.isEmpty
                          ? 300
                          : MediaQuery.of(context).size.height - 300,
                      child: Obx(() {
                        if (controller.dsDNP.isEmpty &&
                            !controller.loadedds.value) {
                          return SkeletonListView();
                        }
                        if (controller.dsDNP.isEmpty &&
                            controller.loadedds.value) {
                          //return emptyCard(context);
                          return const EmptyWidget(
                            message: 'Danh sách của bạn trống',
                            imageAsset: 'tasky/no_inbox.png',
                          );
                        }
                        return ListView.separated(
                          itemBuilder: (context, index) {
                            return buildItem(
                                context, controller.dsDNP[index], index);
                          },
                          itemCount: controller.dsDNP.length,
                          shrinkWrap: true,
                          separatorBuilder: (BuildContext context, int index) {
                            return Divider(
                              color: theme.dividerColor.withOpacity(0.3),
                            );
                          },
                        );
                      }),
                    );
                  }),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
