import 'package:flutter/material.dart';

Color mlPrimaryColor = Colors.blue.shade700;
Color mlPrimaryTextColor = Colors.blue.shade500;
Color mlColorBlue = Colors.blue;
Color mlColorDarkBlue = Colors.blue.shade700;
Color mlColorLightBlue = Colors.indigo.shade900;
Color mlColorRed = Colors.red;
Color mlColorCyan = Colors.cyan.shade300;
Color mlColorLightGrey = Colors.grey.shade300;
Color mlColorLightGrey100 = Colors.grey.shade100;
Color mlColorGreyShade = Color(0xfff5f5f5);

const appColorPrimary = Color(0xFF1157FA);
const iconColorPrimary = Color(0xFFFFFFFF);
const iconColorSecondary = Color(0xFFA8ABAD);
const appSecondaryBackgroundColor = Color(0xFF131d25);
const appTextColorPrimary = Color(0xFF212121);
const appTextColorSecondary = Color(0xFF5A5C5E);
const appShadowColor = Color(0x95E9EBF0);
const appColorPrimaryLight = Color(0xFFF9FAFF);

// Dark Theme Colors
const appBackgroundColorDark = Color(0xFF121212);
const cardBackgroundBlackDark = Color(0xFF1F1F1F);
const color_primary_black = Color(0xFF131d25);
const iconColorPrimaryDark = Color(0xFF212121);
const iconColorSecondaryDark = Color(0xFFA8ABAD);
const appShadowColorDark = Color(0x1A3E3942);
