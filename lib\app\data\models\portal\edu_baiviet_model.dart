import 'dart:convert';

class EduBaiVietModel {
  EduBaiVietModel({
    required this.id,
    required this.ky<PERSON><PERSON>,
    required this.isHienThi,
    required this.tieuDe,
    required this.tomTat,
    required this.noiDung,
    required this.video,
    required this.hinh<PERSON><PERSON><PERSON><PERSON>,
    required this.ngayDangTin,
    required this.nguoiDangTin,
    required this.ngayHieuChinh,
    required this.doUuTien,
    required this.isNews,
  });

  final String? id;
  final String? kyHieu;
  final bool? isHienThi;
  final String? tieuDe;
  final String? tomTat;
  final String? noiDung;
  final String? video;
  final String? hinhDaiDien;
  final DateTime? ngayDangTin;
  final String? nguoiDangTin;
  final DateTime? ngayHieuChinh;
  final int? doUuTien;
  final bool? isNews;

  EduBaiVietModel copyWith({
    String? id,
    String? kyHieu,
    bool? isHienThi,
    String? tieuDe,
    String? tomTat,
    String? noiDung,
    String? video,
    String? hinhDaiDien,
    DateTime? ngayDangTin,
    String? nguoiDangTin,
    DateTime? ngayHieuChinh,
    int? doUuTien,
    bool? isNews,
  }) {
    return EduBaiVietModel(
      id: id ?? this.id,
      kyHieu: kyHieu ?? this.kyHieu,
      isHienThi: isHienThi ?? this.isHienThi,
      tieuDe: tieuDe ?? this.tieuDe,
      tomTat: tomTat ?? this.tomTat,
      noiDung: noiDung ?? this.noiDung,
      video: video ?? this.video,
      hinhDaiDien: hinhDaiDien ?? this.hinhDaiDien,
      ngayDangTin: ngayDangTin ?? this.ngayDangTin,
      nguoiDangTin: nguoiDangTin ?? this.nguoiDangTin,
      ngayHieuChinh: ngayHieuChinh ?? this.ngayHieuChinh,
      doUuTien: doUuTien ?? this.doUuTien,
      isNews: isNews ?? this.isNews,
    );
  }

  factory EduBaiVietModel.fromJson(Map<String, dynamic> json){
    return EduBaiVietModel(
      id: json["id"],
      kyHieu: json["ky_hieu"],
      isHienThi: json["is_hien_thi"],
      tieuDe: json["tieu_de"],
      tomTat: json["tom_tat"],
      noiDung: json["noi_dung"],
      video: json["video"],
      hinhDaiDien: json["hinh_dai_dien"],
      ngayDangTin: DateTime.tryParse(json["ngay_dang_tin"] ?? ""),
      nguoiDangTin: json["nguoi_dang_tin"],
      ngayHieuChinh: DateTime.tryParse(json["ngay_hieu_chinh"] ?? ""),
      doUuTien: json["do_uu_tien"],
      isNews: json["is_news"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "ky_hieu": kyHieu,
    "is_hien_thi": isHienThi,
    "tieu_de": tieuDe,
    "tom_tat": tomTat,
    "noi_dung": noiDung,
    "video": video,
    "hinh_dai_dien": hinhDaiDien,
    "ngay_dang_tin": ngayDangTin?.toIso8601String(),
    "nguoi_dang_tin": nguoiDangTin,
    "ngay_hieu_chinh": ngayHieuChinh?.toIso8601String(),
    "do_uu_tien": doUuTien,
    "is_news": isNews,
  };

  @override
  String toString(){
    return "$id, $kyHieu, $isHienThi, $tieuDe, $tomTat, $noiDung, $video, $hinhDaiDien, $ngayDangTin, $nguoiDangTin, $ngayHieuChinh, $doUuTien, $isNews, ";
  }

  String toJsonString() => json.encode(toJson());
  factory EduBaiVietModel.fromJsonString(String source) => EduBaiVietModel.fromJson(json.decode(source));

  static List<EduBaiVietModel> parseNews(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed.map<EduBaiVietModel>((json) => EduBaiVietModel.fromJson(json)).toList();
  }
}
