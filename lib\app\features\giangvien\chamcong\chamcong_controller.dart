import 'package:get/get.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';
import 'package:tvumobile/app/data/models/tms/danhmuc.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/utils/helpers/dialog_helper.dart';


class ChamCongController extends GetxController {
  ApiProvider apiProvider = Get.find();
  List<DanhMuc> danhmuc = <DanhMuc>[].obs;
  final CalendarController calendarController = CalendarController();

  @override
  Future<void> onInit() async {
    super.onInit();

  }
  getDanhmuc() async {
    DialogHelper.showLoading();
    var dms = await apiProvider.getDanhMucChamCong();
    if(dms.isNotEmpty) {
      danhmuc.addAll(dms);
      update();
    }
    DialogHelper.hideLoading();
  }

  @override
  void onReady() {
    getDanhmuc();
  }

  @override
  void onClose() {

  }


}