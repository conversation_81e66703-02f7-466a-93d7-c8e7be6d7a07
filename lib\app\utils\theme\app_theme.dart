import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tvumobile/app/config/themes/colors.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:tvumobile/app/utils/theme/theme_type.dart';
import 'package:tvumobile/app/utils/ui/text_styles.dart';
import 'mlcolors.dart';

import 'custom_theme.dart';

class AppThemeData {
  //
  AppThemeData._();
  static ThemeData get theme =>
      themeType == ThemeType.light ? lightTheme : darkTheme;

  static ThemeType get themeType =>
      LocalStorageServices.getThemeIsLight() ? ThemeType.light : ThemeType.dark;

  static final ThemeData lightTheme = ThemeData(
    fontFamily: GoogleFonts.ibmPlexSans().fontFamily,
    scaffoldBackgroundColor: const Color(0xfffefefe),
    primaryColor: appColorPrimary,
    primaryColorDark: appColorPrimary,
    //errorColor: Colors.red,
    hoverColor: Colors.white54,
    dividerColor: viewLineColor,
    //fontFamily: GoogleFonts.openSans().fontFamily,
    appBarTheme: const AppBarTheme(
      color: white,
      iconTheme: IconThemeData(color: textPrimaryColor),
      systemOverlayStyle:
          SystemUiOverlayStyle(statusBarIconBrightness: Brightness.dark),
    ),
    textSelectionTheme: const TextSelectionThemeData(cursorColor: Colors.black),
    colorScheme:
        const ColorScheme.light(primary: appColorPrimary, error: Colors.red),
    cardTheme: CardThemeData(color: Colors.white),
    cardColor: Colors.white,
    iconTheme: const IconThemeData(color: textPrimaryColor),
    bottomSheetTheme: const BottomSheetThemeData(backgroundColor: whiteColor),
    textTheme: const TextTheme(
      labelLarge: TextStyle(color: appColorPrimary),
      titleLarge: TextStyle(color: textPrimaryColor),
      titleSmall: TextStyle(color: textSecondaryColor),
    ),
    dividerTheme:
        DividerThemeData(color: viewLineColor.withOpacity(0.5), thickness: 0.7),
    visualDensity: VisualDensity.adaptivePlatformDensity,
  ).copyWith(
    pageTransitionsTheme: const PageTransitionsTheme(
        builders: <TargetPlatform, PageTransitionsBuilder>{
          TargetPlatform.android: OpenUpwardsPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          TargetPlatform.linux: OpenUpwardsPageTransitionsBuilder(),
          TargetPlatform.macOS: OpenUpwardsPageTransitionsBuilder(),
        }),
  );

  static final ThemeData darkTheme = ThemeData(
    fontFamily: GoogleFonts.ibmPlexSans().fontFamily,
    scaffoldBackgroundColor: appBackgroundColorDark,
    highlightColor: appBackgroundColorDark,
    appBarTheme: const AppBarTheme(
      color: appBackgroundColorDark,
      iconTheme: IconThemeData(color: blackColor),
      systemOverlayStyle:
          SystemUiOverlayStyle(statusBarIconBrightness: Brightness.light),
    ),
    primaryColor: color_primary_black,
    dividerColor: const Color(0xFFDADADA).withOpacity(0.3),
    primaryColorDark: color_primary_black,
    textSelectionTheme: const TextSelectionThemeData(cursorColor: Colors.white),
    hoverColor: Colors.black12,
    //fontFamily: GoogleFonts.openSans().fontFamily,
    bottomSheetTheme:
        const BottomSheetThemeData(backgroundColor: appBackgroundColorDark),
    primaryTextTheme: TextTheme(
        titleLarge: primaryTextStyle(color: Colors.white70),
        labelSmall: primaryTextStyle(color: Colors.white)),
    cardTheme: const CardThemeData(color: cardBackgroundBlackDark),
    cardColor: appSecondaryBackgroundColor,
    iconTheme: const IconThemeData(color: whiteColor),
    textTheme: const TextTheme(
      labelLarge: TextStyle(color: color_primary_black),
      titleLarge: TextStyle(color: Colors.white70),
      titleSmall: TextStyle(color: Colors.white54),
    ),
    visualDensity: VisualDensity.adaptivePlatformDensity,
    dividerTheme:
        DividerThemeData(color: viewLineColor.withOpacity(0.5), thickness: 0.7),
    colorScheme: const ColorScheme.dark(
            primary: appBackgroundColorDark,
            onPrimary: Colors.white70,
            error: Color(0xFFCF6676))
        .copyWith(secondary: Colors.white70)
        .copyWith(error: const Color(0xFFCF6676)),
  ).copyWith(
    pageTransitionsTheme: const PageTransitionsTheme(
        builders: <TargetPlatform, PageTransitionsBuilder>{
          TargetPlatform.android: OpenUpwardsPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          TargetPlatform.linux: OpenUpwardsPageTransitionsBuilder(),
          TargetPlatform.macOS: OpenUpwardsPageTransitionsBuilder(),
        }),
  );

  static CustomTheme getCustomTheme([ThemeType? themeType]) {
    themeType = themeType ?? AppThemeData.themeType;
    if (themeType == ThemeType.light) return CustomTheme.lightCustomTheme;
    return CustomTheme.darkCustomTheme;
  }
}
