import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/features/tasky/controllers/taskdetail_controller.dart';
// import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/features/tasky/controllers/taskytaskview_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
// import 'package:tvumobile/app/services/local_storage_services.dart';

AppBar buildAppBarTasky(BuildContext context, String appbarTitle) {
  //UserInfo? userInfo = LocalStorageServices.getUserInfo();
  ThemeData theme = Theme.of(context);
  return AppBar(
    toolbarHeight: 48, // Set this height
    foregroundColor: theme.colorScheme.onPrimary,
    iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
    actions: [],
    flexibleSpace: Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      //color: theme.primaryColor,
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -80,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset(
                  'assets/images/app_icon.png',
                  height: 150,
                  width: 150,
                  opacity: const AlwaysStoppedAnimation(.15),
                )),
          ),
          Positioned(
            right: -7,
            top: -160,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: -21,
            top: -195,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          //----------------Data row----------------//
          Center(
            child: Padding(
              padding: const EdgeInsets.only(top: 24),
              child: InkWell(
                onTap: () {
                  //controller.openDrawer();
                },
                child: MyText.titleMedium(
                  appbarTitle.toUpperCase(),
                  color: theme.colorScheme.onPrimary,
                  fontWeight: 900,
                  letterSpacing: 0.01,
                ),
              ),
            ),
          )
        ],
      ),
    ),
  );
}

// ------------------------- APPBAR CHI TIẾT TASK -----------------------
AppBar buildAppBarTaskyDetail(
    BuildContext context, TaskDetailController controller) {
  //UserInfo? userInfo = LocalStorageServices.getUserInfo();
  ThemeData theme = Theme.of(context);
  return AppBar(
    toolbarHeight: 48, // Set this height
    foregroundColor: theme.colorScheme.onPrimary,
    iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
    actions: [
      PopupMenuButton<String>(
        color: theme.colorScheme.onPrimary,
        iconColor: theme.colorScheme.onPrimary,
        onSelected: (value) {
          if (value == 'edit') {
            // Điều hướng sang màn hình sửa
            Get.toNamed(AppPages.TASK_CREATE,
                arguments: {'task': controller.task});
          } else if (value == 'delete') {
            // Hiển thị dialog xác nhận xóa
            Get.defaultDialog(
              title: 'Xác nhận xóa',
              middleText: 'Bạn có chắc chắn muốn xóa nhiệm vụ này không?',
              textCancel: 'Hủy',
              textConfirm: 'Xóa',
              cancelTextColor: Color(0xFF1157FA),
              confirmTextColor: Colors.white,
              buttonColor: Color(0xFF1157FA),
              onConfirm: () async {
                controller.deleteTask();
              },
            );
          }
        },
        itemBuilder: (context) {
          return [
            PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  SizedBox(width: 15),
                  const Text("Sửa"),
                  SizedBox(width: 15),
                  Icon(
                    LucideIcons.pen,
                    size: 18,
                    color: Colors.black,
                  ),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  SizedBox(width: 15),
                  const Text("Xóa"),
                  SizedBox(width: 15),
                  Icon(
                    LucideIcons.trash,
                    size: 18,
                    color: Colors.black,
                  ),
                ],
              ),
            ),
          ];
        },
      ),
    ],
    flexibleSpace: Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      //color: theme.primaryColor,
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -80,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset(
                  'assets/images/app_icon.png',
                  height: 150,
                  width: 150,
                  opacity: const AlwaysStoppedAnimation(.15),
                )),
          ),
          Positioned(
            right: -7,
            top: -160,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: -21,
            top: -195,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          //----------------Data row----------------//
          // Center(
          //   child: Padding(
          //     padding: const EdgeInsets.only(top: 24),
          //     child: InkWell(
          //       onTap: () {
          //         //controller.openDrawer();
          //       },
          //       child: MyText.titleMedium(
          //         appbarTitle.toUpperCase(),
          //         color: theme.colorScheme.onPrimary,
          //         fontWeight: 900,
          //         letterSpacing: 0.01,
          //       ),
          //     ),
          //   ),
          // )
        ],
      ),
    ),
    title: Obx(() {
      return MyText.titleMedium(
        controller.appbarTitle.value.toUpperCase(),
        color: theme.colorScheme.onPrimary,
        letterSpacing: 0.01,
        fontWeight: 900,
      );
    }),
    centerTitle: true,
  );
}

AppBar buildAppBarTaskDetail(
    BuildContext context, TaskyTaskViewController controller) {
  //UserInfo? userInfo = LocalStorageServices.getUserInfo();
  ThemeData theme = Theme.of(context);
  return AppBar(
    toolbarHeight: 48, // Set this height
    foregroundColor: theme.colorScheme.onPrimary,
    iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
    actions: [],
    flexibleSpace: Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 1,
            blurRadius: 1,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
        borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      //color: theme.primaryColor,
      child: Stack(
        fit: StackFit.expand,
        children: [
          //----------------white circles decor----------------//
          Positioned(
            right: 0,
            top: -80,
            child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.05),
                radius: 111,
                child: Image.asset(
                  'assets/images/app_icon.png',
                  height: 150,
                  width: 150,
                  opacity: const AlwaysStoppedAnimation(.15),
                )),
          ),
          Positioned(
            right: -7,
            top: -160,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          Positioned(
            right: -21,
            top: -195,
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.05),
              radius: 111,
            ),
          ),
          //----------------Data row----------------//
          // Center(
          //   child: Padding(
          //     padding: const EdgeInsets.only(top: 24),
          //     child: InkWell(
          //       onTap: () {
          //         //controller.openDrawer();
          //       },
          //       child: MyText.titleMedium(
          //         appbarTitle.toUpperCase(),
          //         color: theme.colorScheme.onPrimary,
          //         fontWeight: 900,
          //         letterSpacing: 0.01,
          //       ),
          //     ),
          //   ),
          // )
        ],
      ),
    ),
    title: Obx(() {
      return MyText.titleMedium(
        controller.appbarTitle.value.toUpperCase(),
        color: theme.colorScheme.onPrimary,
        letterSpacing: 0.01,
        fontWeight: 900,
      );
    }),
    centerTitle: true,
  );
}
