import 'package:intl/intl.dart';

class TkbEdusoftModel {
  TkbEdusoftModel({
    required this.nhhk,
    required this.tuanHocKy,
    required this.tuanTuyetDoi,
    required this.thongTinTuan,
    required this.ngayBatDau,
    required this.ngayKetThuc,
    required this.dsThoi<PERSON>hoa<PERSON><PERSON>,
    required this.dsIdThoiKhoaBieuTrung,
  });

  final num? nhhk;
  final num? tuanHocKy;
  final num? tuanTuyetDoi;
  final String? thongTinTuan;
  final String? ngayBatDau;
  final String? ngayKetThuc;
  final List<DsThoiKhoaBieu> dsThoiKhoaBieu;
  final dynamic dsIdThoiKhoaBieuTrung;

  TkbEdusoftModel copyWith({
    num? nhhk,
    num? tuanHocKy,
    num? tuanTuyetDoi,
    String? thongTinTuan,
    String? ngayBatDau,
    String? ngayKetThuc,
    List<DsThoiKhoaBieu>? dsThoi<PERSON>hoaB<PERSON>,
    dynamic dsIdThoiKhoaBieuTrung,
  }) {
    return TkbEdusoftModel(
      nhhk: nhhk ?? this.nhhk,
      tuanHocKy: tuanHocKy ?? this.tuanHocKy,
      tuanTuyetDoi: tuanTuyetDoi ?? this.tuanTuyetDoi,
      thongTinTuan: thongTinTuan ?? this.thongTinTuan,
      ngayBatDau: ngayBatDau ?? this.ngayBatDau,
      ngayKetThuc: ngayKetThuc ?? this.ngayKetThuc,
      dsThoiKhoaBieu: dsThoiKhoaBieu ?? this.dsThoiKhoaBieu,
      dsIdThoiKhoaBieuTrung:
          dsIdThoiKhoaBieuTrung ?? this.dsIdThoiKhoaBieuTrung,
    );
  }

  factory TkbEdusoftModel.fromJson(Map<String, dynamic> json) {
    return TkbEdusoftModel(
      nhhk: json["nhhk"],
      tuanHocKy: json["tuan_hoc_ky"],
      tuanTuyetDoi: json["tuan_tuyet_doi"],
      thongTinTuan: json["thong_tin_tuan"],
      ngayBatDau: json["ngay_bat_dau"],
      ngayKetThuc: json["ngay_ket_thuc"],
      dsThoiKhoaBieu: json["ds_thoi_khoa_bieu"] == null
          ? []
          : DsThoiKhoaBieu.parseList(json["ds_thoi_khoa_bieu"]),
      dsIdThoiKhoaBieuTrung: json["ds_id_thoi_khoa_bieu_trung"],
    );
  }

  Map<String, dynamic> toJson() => {
        "nhhk": nhhk,
        "tuan_hoc_ky": tuanHocKy,
        "tuan_tuyet_doi": tuanTuyetDoi,
        "thong_tin_tuan": thongTinTuan,
        "ngay_bat_dau": ngayBatDau,
        "ngay_ket_thuc": ngayKetThuc,
        "ds_thoi_khoa_bieu": dsThoiKhoaBieu.map((x) => x.toJson()).toList(),
        "ds_id_thoi_khoa_bieu_trung": dsIdThoiKhoaBieuTrung,
      };

  @override
  String toString() {
    return "$nhhk, $tuanHocKy, $tuanTuyetDoi, $thongTinTuan, $ngayBatDau, $ngayKetThuc, $dsThoiKhoaBieu, $dsIdThoiKhoaBieuTrung, ";
  }

  static List<TkbEdusoftModel> parseList(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed
        .map<TkbEdusoftModel>((json) => TkbEdusoftModel.fromJson(json))
        .toList();
  }
}

class DsThoiKhoaBieu {
  DsThoiKhoaBieu({
    required this.isHkLienTruoc,
    required this.thuKieuSo,
    required this.tietBatDau,
    required this.soTiet,
    required this.maMon,
    required this.tenMon,
    required this.soTinChi,
    required this.idToHoc,
    required this.idTkb,
    required this.idToHop,
    required this.maNhom,
    required this.maToTh,
    required this.maToHop,
    required this.maGiangVien,
    required this.tenGiangVien,
    required this.maLop,
    required this.maPhong,
    required this.maCoSo,
    required this.ngayHoc,
    required this.idTuTao,
    required this.isFileBaiGiang,
    required this.idSinhHoat,
    required this.nhhk,
  });

  final num? isHkLienTruoc;
  final num? thuKieuSo;
  final num? tietBatDau;
  final num? soTiet;
  final String? maMon;
  final String? tenMon;
  final String? soTinChi;
  final int? idToHoc;
  final int? idTkb;
  final int? idToHop;
  final String? maNhom;
  final String? maToTh;
  final String? maToHop;
  final String? maGiangVien;
  final String? tenGiangVien;
  final String? maLop;
  final String? maPhong;
  final String? maCoSo;
  final DateTime? ngayHoc;
  final int? idTuTao;
  final bool? isFileBaiGiang;
  final int? idSinhHoat;
  final num? nhhk;

  DsThoiKhoaBieu copyWith({
    num? isHkLienTruoc,
    num? thuKieuSo,
    num? tietBatDau,
    num? soTiet,
    String? maMon,
    String? tenMon,
    String? soTinChi,
    int? idToHoc,
    int? idTkb,
    int? idToHop,
    String? maNhom,
    String? maToTh,
    String? maToHop,
    String? maGiangVien,
    String? tenGiangVien,
    String? maLop,
    String? maPhong,
    String? maCoSo,
    DateTime? ngayHoc,
    int? idTuTao,
    bool? isFileBaiGiang,
    int? idSinhHoat,
    num? nhhk,
  }) {
    return DsThoiKhoaBieu(
      isHkLienTruoc: isHkLienTruoc ?? this.isHkLienTruoc,
      thuKieuSo: thuKieuSo ?? this.thuKieuSo,
      tietBatDau: tietBatDau ?? this.tietBatDau,
      soTiet: soTiet ?? this.soTiet,
      maMon: maMon ?? this.maMon,
      tenMon: tenMon ?? this.tenMon,
      soTinChi: soTinChi ?? this.soTinChi,
      idToHoc: idToHoc ?? this.idToHoc,
      idTkb: idTkb ?? this.idTkb,
      idToHop: idToHop ?? this.idToHop,
      maNhom: maNhom ?? this.maNhom,
      maToTh: maToTh ?? this.maToTh,
      maToHop: maToHop ?? this.maToHop,
      maGiangVien: maGiangVien ?? this.maGiangVien,
      tenGiangVien: tenGiangVien ?? this.tenGiangVien,
      maLop: maLop ?? this.maLop,
      maPhong: maPhong ?? this.maPhong,
      maCoSo: maCoSo ?? this.maCoSo,
      ngayHoc: ngayHoc ?? this.ngayHoc,
      idTuTao: idTuTao ?? this.idTuTao,
      isFileBaiGiang: isFileBaiGiang ?? this.isFileBaiGiang,
      idSinhHoat: idSinhHoat ?? this.idSinhHoat,
      nhhk: nhhk ?? this.nhhk,
    );
  }

  factory DsThoiKhoaBieu.fromJson(Map<String, dynamic> json) {
    DateTime parseDate = DateFormat("yyyy-MM-dd'T'HH:mm:ss")
        .parse(json["ngay_hoc"] ?? ""); //2024-02-28T00:00:00
    return DsThoiKhoaBieu(
      isHkLienTruoc: json["is_hk_lien_truoc"],
      thuKieuSo: json["thu_kieu_so"],
      tietBatDau: json["tiet_bat_dau"],
      soTiet: json["so_tiet"],
      maMon: json["ma_mon"],
      tenMon: json["ten_mon"],
      soTinChi: json["so_tin_chi"],
      idToHoc: json["id_to_hoc"],
      idTkb: json["id_tkb"],
      idToHop: json["id_to_hop"],
      maNhom: json["ma_nhom"],
      maToTh: json["ma_to_th"],
      maToHop: json["ma_to_hop"],
      maGiangVien: json["ma_giang_vien"],
      tenGiangVien: json["ten_giang_vien"],
      maLop: json["ma_lop"],
      maPhong: json["ma_phong"],
      maCoSo: json["ma_co_so"],
      ngayHoc: parseDate,
      idTuTao: json["id_tu_tao"],
      isFileBaiGiang: json["is_file_bai_giang"],
      idSinhHoat: json["id_sinh_hoat"],
      nhhk: json["nhhk"],
    );
  }

  Map<String, dynamic> toJson() => {
        "is_hk_lien_truoc": isHkLienTruoc,
        "thu_kieu_so": thuKieuSo,
        "tiet_bat_dau": tietBatDau,
        "so_tiet": soTiet,
        "ma_mon": maMon,
        "ten_mon": tenMon,
        "so_tin_chi": soTinChi,
        "id_to_hoc": idToHoc,
        "id_tkb": idTkb,
        "id_to_hop": idToHop,
        "ma_nhom": maNhom,
        "ma_to_th": maToTh,
        "ma_to_hop": maToHop,
        "ma_giang_vien": maGiangVien,
        "ten_giang_vien": tenGiangVien,
        "ma_lop": maLop,
        "ma_phong": maPhong,
        "ma_co_so": maCoSo,
        "ngay_hoc": ngayHoc?.toIso8601String(),
        "id_tu_tao": idTuTao,
        "is_file_bai_giang": isFileBaiGiang,
        "id_sinh_hoat": idSinhHoat,
        "nhhk": nhhk,
      };

  @override
  String toString() {
    return "$isHkLienTruoc, $thuKieuSo, $tietBatDau, $soTiet, $maMon, $tenMon, $soTinChi, $idToHoc, $idTkb, $idToHop, $maNhom, $maToTh, $maToHop, $maGiangVien, $tenGiangVien, $maLop, $maPhong, $maCoSo, $ngayHoc, $idTuTao, $isFileBaiGiang, $idSinhHoat, $nhhk, ";
  }

  static List<DsThoiKhoaBieu> parseList(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed
        .map<DsThoiKhoaBieu>((json) => DsThoiKhoaBieu.fromJson(json))
        .toList();
  }
}
