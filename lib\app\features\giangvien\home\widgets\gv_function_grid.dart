import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/config/themes/custom_theme.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import '../gv_home_controller.dart';

// mock model
class SvFunctionGridModelMock {
  final String id;
  final String title;
  final String? titleLong;
  final String? iconPath;
  final String imagePath;
  final Color backgroundColor;
  final Color iconBackgroundColor;
  final bool enabled;
  final IconData? icon;

  SvFunctionGridModelMock({
    required this.id,
    required this.title,
    this.titleLong,
    this.iconPath,
    this.icon,
    required this.enabled,
    required this.imagePath,
    required this.backgroundColor,
    required this.iconBackgroundColor,
  });
}

class GvFunctionGrid extends StatelessWidget {
  GvFunctionGrid({super.key, required this.homeController});
  final GvHomeController homeController;

  final List<SvFunctionGridModelMock> datagv = [
    SvFunctionGridModelMock(
        id: 'task',
        title: "Giao việc",
        titleLong: "Giao việc cho viên chức",
        iconPath: 'assets/vectors/vocation.svg',
        imagePath: "assets/vectors/icons8-internship-100.png",
        backgroundColor: CustomTheme.green,
        iconBackgroundColor: CustomTheme.skyBlue,
        icon: LucideIcons.notebookPen,
        enabled: true),
    SvFunctionGridModelMock(
        id: 'congvan',
        title: "Công văn",
        titleLong: "Xem công văn nhận",
        iconPath: 'assets/vectors/tasks.svg',
        imagePath: "assets/vectors/icons8-vertical-timeline-100.png",
        backgroundColor: CustomTheme.red,
        iconBackgroundColor: CustomTheme.peach,
        icon: LucideIcons.bookCheck,
        enabled: true),
    SvFunctionGridModelMock(
        id: 'tkb',
        title: "TKB",
        titleLong: "Thời khoá biểu",
        icon: LucideIcons.calendarDays,
        iconPath: 'assets/vectors/alarm.svg',
        imagePath: "assets/vectors/icons8-task-100.png",
        backgroundColor: CustomTheme.orange,
        iconBackgroundColor: CustomTheme.occur,
        enabled: true),
    SvFunctionGridModelMock(
        id: 'kyso',
        title: "Ký số",
        titleLong: "Ký văn bản bằng chữ ký số",
        icon: LucideIcons.signature,
        iconPath: '',
        imagePath: "",
        backgroundColor: CustomTheme.orange,
        iconBackgroundColor: CustomTheme.red,
        enabled: true),
    SvFunctionGridModelMock(
        id: 'tcl',
        title: "Lương thuế",
        titleLong: "Điểm kết thúc HP",
        iconPath: 'assets/vectors/vocation.svg',
        imagePath: "assets/vectors/icons8-internship-100.png",
        backgroundColor: CustomTheme.green,
        iconBackgroundColor: CustomTheme.orange,
        icon: LucideIcons.salad,
        enabled: true),
    SvFunctionGridModelMock(
        id: 'dnp',
        title: "Nghỉ phép",
        titleLong: "Đơn nghỉ phép viên chức",
        iconPath: 'assets/vectors/vocation.svg',
        imagePath: "assets/vectors/icons8-internship-100.png",
        backgroundColor: CustomTheme.green,
        iconBackgroundColor: CustomTheme.darkGreen,
        icon: LucideIcons.calendarX,
        enabled: true),
    SvFunctionGridModelMock(
        id: 'dieuxe',
        title: "Điều xe",
        titleLong: "",
        iconPath: 'assets/vectors/vocation.svg',
        imagePath: "assets/vectors/icons8-internship-100.png",
        backgroundColor: CustomTheme.green,
        iconBackgroundColor: CustomTheme.green,
        icon: LucideIcons.carFront,
        enabled: true),
    SvFunctionGridModelMock(
        id: 'dso',
        title: "Tất cả",
        titleLong: "Điểm kết thúc HP",
        iconPath: 'assets/vectors/vocation.svg',
        imagePath: "assets/vectors/icons8-internship-100.png",
        backgroundColor: CustomTheme.green,
        iconBackgroundColor: CustomTheme.purple,
        icon: LucideIcons.blocks,
        enabled: false),
  ];

  @override
  Widget build(BuildContext context) {
    Theme.of(context);
    GvHomeController homeController = Get.find();
    return GridView.builder(
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: datagv.length,
      shrinkWrap: true,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemBuilder: (ctx, index) {
        var gridData = datagv[index];
        var opacity = 1.0;
        bool enabled = gridData.enabled;
        if (!enabled) {
          opacity = 0.5;
        }
        return InkWell(
            onTap: () => gridData.enabled == true
                ? homeController.onTapFunctions(gridData.id)
                : null,
            child: Opacity(
              opacity: opacity,
              child: Container(
                //padding: const EdgeInsets.only(left: 2, right: 2, top: 8, bottom: 2),
                decoration: const BoxDecoration(
                    // color: gridData.iconBackgroundColor.withAlpha(50),
                    //borderRadius: BorderRadius.circular(8),
                    //border: Border.all(color: gridData.iconBackgroundColor)
                    ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    gridData.icon != null
                        ? Icon(
                            gridData.icon,
                            size: 32,
                            color: gridData.iconBackgroundColor,
                          )
                        : Image.asset(
                            gridData.imagePath,
                            width: 48,
                          ),
                    MySpacing.height(2),
                    MyText.titleSmall(
                      gridData.title,
                    ),
                    //MyText.labelSmall(gridData.titleLong.toString(), style: theme.textTheme.labelSmall?.copyWith(fontSize: 8),),
                  ],
                ),
              ),
            ));
      },
    );
  }
}
