// ignore_for_file: deprecated_member_use

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;
import 'package:lazy_load_scrollview/lazy_load_scrollview.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/data/models/tms/congvan/cong_van_nhan.dart';
import 'package:tvumobile/app/features/giangvien/congvan/congvan_controller.dart';
import 'package:tvumobile/app/features/giangvien/congvan/widgets/cv_appbar.dart';
import 'package:tvumobile/app/features/giangvien/congvan/widgets/drawer.dart';
import 'package:tvumobile/app/shared_components/file_icon.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_horizontal_list.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/app_helpers.dart';
import 'package:url_launcher/url_launcher.dart';

class CongVanView extends GetView<CongVanController> {
  const CongVanView({super.key});

  @override
  Widget build(BuildContext context) {
    //userInfo = LocalStorageServices.getUserInfo()!;
    return Scaffold(
      key: controller.scaffoldKey,
      appBar: buildAppBar(context, controller),
      drawer: buildDrawer(context, controller),
      body: Obx(() {
        //List colors = [getColorFromHex('#5E97F6'), getColorFromHex('#4DD0E1'), getColorFromHex('#F06292'), getColorFromHex('#F6BF26')];
        return Stack(
          children: [
            Padding(
                padding: const EdgeInsets.only(top: 5),
                child: buildFilter(context)),
            if (controller.congVanNhan.isEmpty)
              Center(
                child: MyText.titleLarge("Không có dữ liệu"),
              ),
            if (controller.congVanNhan.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 50),
                child: LazyLoadScrollView(
                    onEndOfPage: controller.loadNextPage,
                    isLoading: controller.lastPage,
                    child: RefreshIndicator(
                      onRefresh: () => Future.sync(
                        () => controller.reFresh(),
                      ),
                      child: ListView.builder(
                        padding: const EdgeInsets.only(top: 15),
                        itemCount: controller.congVanNhan.length,
                        itemBuilder: (context, index) {
                          final doc = controller.congVanNhan[index];
                          return _buildItem(context, doc, index);
                        },
                        //separatorBuilder: (BuildContext context, int index) { return const Divider(height: 1,); },
                      ),
                    )),
              ),
          ],
        );
      }),
    );
  }

  Widget _buildItem(BuildContext context, TmsCongVanModel doc, int index) {
    ThemeData theme = Theme.of(context);
    //List colors = [getColorFromHex('#5E97F6'), getColorFromHex('#4DD0E1'), getColorFromHex('#F06292'), getColorFromHex('#F6BF26')];
    String heroKey = "doc_${doc.document!.id}";
    final width = Get.width;
    return MyContainer(
      padding: EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 8),
      onTap: () {
        //Get.toNamed(Routes.CV_DETAIL, parameters: {"cvid": doc.id.toString()});
        //Get.to(() => SingleCongVanNhanScreen(doc: doc, heroKey: heroKey),
        //    preventDuplicates: true, transition: Transition.fadeIn);
        Get.toNamed(Routes.CV_DETAIL,
            parameters: {heroKey: heroKey}, arguments: {"doc": doc});
      },
      width: width,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: colorFor(doc.document!.category!.ten.toString())),
            child: Center(
              child: MyText.labelLarge(
                doc.document!.category!.ten.toString().getAvatarCV,
                color: Colors.white,
              ),
            ),
          ),
          MySpacing.width(8),
          SizedBox(
            width: width - 76,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 200,
                      child: MyText.labelMedium(
                          doc.document!.noiBanHanh!.tenDonVi.toString() == ""
                              ? "[Không được cung cấp!]"
                              : doc.document!.noiBanHanh!.tenDonVi.toString(),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis),
                    ),
                    MyText.labelMedium(intl.DateFormat('dd/MM/yyyy').format(
                        doc.ngayCapNhat == null
                            ? doc.document!.ngayBanHanh!
                            : doc.ngayCapNhat!)),
                  ],
                ),
                MyText.labelLarge("${doc.document!.trichYeu}",
                    maxLines: 2,
                    fontWeight: 700,
                    overflow: TextOverflow.ellipsis,
                    color: doc.daXem == null
                        ? theme.colorScheme.onSurface.withOpacity(0.70)
                        : theme.colorScheme.onSurface.withOpacity(0.85)),
                MyText.bodySmall(
                    // ignore: prefer_interpolation_to_compose_strings
                    "${doc.nguoiChuyenCongVan!.ho}${doc.nguoiChuyenCongVan!.tenDem == null ? ' ' : ' ' + doc.nguoiChuyenCongVan!.tenDem + ' '}${doc.nguoiChuyenCongVan!.ten}"),
                MyHorizontalList(
                  padding: EdgeInsets.all(0.0),
                  wrapAlignment: WrapAlignment.spaceEvenly,
                  itemCount: doc.document!.cvFiles.length,
                  itemBuilder: (BuildContext context, int i) {
                    return MyContainer(
                      margin: EdgeInsets.only(top: 4),
                      borderRadiusAll: 4,
                      bordered: true,
                      paddingAll: 2,
                      onTap: () {
                        if (doc.document!.cvFiles[i].getFileExt
                            .contains('pdf')) {
                          controller.onFileTap(doc.document!.cvFiles, i);
                        } else {
                          launchUrl(
                              Uri.parse(
                                  "https://tms.tvu.edu.vn/${doc.document!.cvFiles[i].getFilePath}"),
                              mode: LaunchMode.externalApplication);
                        }
                      },
                      child: Row(
                        children: [
                          FileIcon(
                            '${doc.document!.cvFiles[i].filePath}',
                            size: 12,
                            color: Colors.redAccent,
                          ),
                          const SizedBox(
                            width: 3,
                          ),
                          MyText.labelMedium(
                            '${doc.document!.cvFiles[i].fileName?.getClips(20)}',
                            overflow: TextOverflow.clip,
                          )
                        ],
                      ),
                    );
                  },
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  buildFilter(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return SizedBox(
      height: 50,
      width: double.infinity,
      child: Padding(
        padding: const EdgeInsets.only(
            left: 16.0, right: 16.0, top: 5.0, bottom: 5.0),
        child: TextFormField(
            controller: controller.searchController,
            onChanged: (value) {
              //debounce search when typing using timer
              if (controller.debounce?.isActive ?? false) {
                controller.debounce?.cancel();
              }
              controller.debounce =
                  Timer(const Duration(milliseconds: 800), () {
                if (controller.searchController.text.length > 2) {
                  controller.loadCVBySearch(value);
                }
                if (controller.searchController.text.isEmpty) {
                  controller.reFresh();
                }
              });
            },
            decoration: InputDecoration(
                hintText: "Tìm kiếm",
                prefixIcon: Icon(Icons.search),
                suffixIcon: controller.searchController.text.length > 2
                    ? IconButton(
                        icon: Icon(Icons.clear),
                        onPressed: () {
                          controller.searchController.clear();
                          controller.reFresh();
                        },
                      )
                    : null,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                fillColor: theme.colorScheme.surface,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide:
                      BorderSide(color: theme.primaryColor.withOpacity(0.1)),
                ))),
      ),
    );
  }
}
