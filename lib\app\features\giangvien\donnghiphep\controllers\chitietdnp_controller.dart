import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepinfo.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepvienchuc.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/duyetdnp_controller.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/nghiphep_home_controller.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class ChiTietDNPController extends GetxController {
  ApiProvider apiProvider = Get.find();
  late DonNghiPhepInfo donNghiPhepInfo;
  final danghi = RxDouble(-1);
  final conlai = RxDouble(-1);
  final loaddnp = false.obs;
  late String? dnpId;
  final RxString duyetai = "".obs; // Lưu duyetai trong controller
  final RxString trangthai = "".obs; // Lưu trangthai trong controller
  final RxString tt = "".obs; // Hiển thị nút xóa trên AppBar
  DonNghiPhepVienChuc dnp = DonNghiPhepVienChuc();
  UserInfo? userinfo = LocalStorageServices.getUserInfo();

  @override
  void onInit() {
    super.onInit();
    if (kDebugMode) {
      print("ChiTietDNPController onInit");
    }
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    if (kDebugMode) {
      print("ChiTietDNPController onReady");
    }
    dnpId = Get.parameters['dnpId'] ?? Get.arguments['dnpId'] ?? "";
    duyetai.value =
        Get.parameters['duyettai'] ?? Get.arguments['duyettai'] ?? "";
    trangthai.value =
        Get.parameters['trangthai'] ?? Get.arguments['trangthai'] ?? "";
    getChiTietDNP();
    getDNPInfo();
  }

  // Lấy chi tiết đơn nghỉ phép của viên chức
  getChiTietDNP() async {
    final chitietdnp = await apiProvider.getChiTietDonNghiPhep(dnpId: dnpId);
    if (chitietdnp != null) {
      dnp = chitietdnp[0];
      loaddnp.value = true;
      tt.value = dnp.trangThai!;
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy đơn nghỉ phép với ID: ${dnp.id}',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
    update();
  }

  // Lấy thông tin nghỉ phép của viên chức
  Future<void> getDNPInfo() async {
    DonNghiPhepInfo? response = await apiProvider.getThongtinNghiPhep();
    donNghiPhepInfo = response;

    danghi.value = donNghiPhepInfo.soNgayDaNghi!;
    conlai.value = donNghiPhepInfo.soNgayConLai!;

    update();
  }

  // Chuyển đơn và thu hồi đơn nghỉ phép chờ duyệt
  Future<bool> chuyenDonNghiPhep() async {
    if (dnpId != null && dnpId!.isNotEmpty) {
      String success =
          await apiProvider.chuyenDonNghiPhep(dnpId: dnpId, waiting: true);

      if (success.contains("chuyển đơn")) {
        Get.snackbar(
          'Thành công',
          'Đã chuyển đơn nghỉ phép.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );
        refreshData(); // Đợi refreshData hoàn tất
        return true;
      } else if (success.contains("thu hồi")) {
        Get.snackbar(
          'Thành công',
          'Đã thu hồi đơn nghỉ phép.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.orange.withOpacity(0.8),
          colorText: Colors.white,
        );
        refreshData(); // Đợi refreshData hoàn tất
        return true;
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể chuyển đơn nghỉ phép. Vui lòng thử lại.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
        return false;
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy ID đơn nghỉ phép.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Duyệt đơn nghỉ phép
  Future<bool> duyetDonNghiPhep() async {
    if (dnpId != null && dnpId!.isNotEmpty) {
      String success = await apiProvider.duyetDonNghiPhep(
          dnpId: dnpId, duyettai: duyetai.value, waiting: true);

      if (success.contains("Đã duyệt")) {
        refreshData(); // Đợi refreshData hoàn tất
        Get.find<DuyetDNPController>().refreshOnResume(); // Làm mới danh sách
        Get.back();
        Get.snackbar(
          'Thành công',
          'Đã duyệt đơn nghỉ phép.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );

        return true;
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể duyệt đơn nghỉ phép. Vui lòng thử lại.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
        return false;
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy ID đơn nghỉ phép.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Trả đơn nghỉ phép (Không duyệt)
  Future<bool> traDonNghiPhep(String lyDoTraDon) async {
    if (dnpId != null && dnpId!.isNotEmpty) {
      String success = await apiProvider.traDonNghiPhep(
        dnpId: dnpId,
        lydo: lyDoTraDon,
        duyettai: duyetai.value,
        waiting: true,
      );

      if (success.contains("Đã trả đơn")) {
        refreshData(); // Đợi refreshData hoàn tất
        Get.find<DuyetDNPController>().refreshOnResume(); // Làm mới danh sách
        Get.back();
        Get.snackbar(
          'Thành công',
          'Đã trả đơn nghỉ phép.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );

        return true;
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể trả đơn nghỉ phép. Vui lòng thử lại.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
        return false;
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy ID đơn nghỉ phép.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Thu hồi đơn nghỉ phép đã duyệt
  Future<bool> thuHoiDonNghiPhep() async {
    if (dnpId != null && dnpId!.isNotEmpty) {
      String success = await apiProvider.thuHoiDonNghiPhep(
        dnpId: dnpId,
        duyettai: duyetai.value,
        waiting: true,
      );

      if (success.contains("Đã thu hồi")) {
        refreshData(); // Đợi refreshData hoàn tất
        Get.find<DuyetDNPController>().refreshOnResume(); // Làm mới danh sách
        Get.back();
        Get.snackbar(
          'Thành công',
          'Thu hồi đơn nghỉ phép đã duyệt.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );

        return true;
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể thu hồi đơn nghỉ phép. Vui lòng thử lại.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
        return false;
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy ID đơn nghỉ phép.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Thêm phương thức để làm mới dữ liệu khi quay về
  void refreshData() async {
    if (kDebugMode) {
      print("Refreshing ChiTietDNPController data");
    }
    await getChiTietDNP();
    await getDNPInfo();
  }

  // Hộp thoại xác nhận
  void showConfirmDialog(String action, String duyet, Function onConfirm) {
    Get.defaultDialog(
      title: "Xác nhận",
      middleText: "Bạn có chắc chắn muốn $action đơn$duyet?",
      confirm: TextButton(
        onPressed: () {
          onConfirm();
          Get.back(); // Đóng dialog sau khi xác nhận
        },
        style: TextButton.styleFrom(
          backgroundColor: Colors.green, // Màu xanh cho nút "OK"
        ),
        child: Text(
          "Có",
          style: TextStyle(color: Colors.white),
        ),
      ),
      cancel: TextButton(
        onPressed: () {
          Get.back(); // Đóng dialog khi nhấn "Hủy"
        },
        style: TextButton.styleFrom(
          backgroundColor: Colors.red, // Màu đỏ cho nút "Hủy"
        ),
        child: Text(
          "Không",
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  // Thêm phương thức xóa đơn
  Future<bool> deleteDonNghiPhep() async {
    if (dnpId != null && dnpId!.isNotEmpty) {
      try {
        final success = await apiProvider.xoaDonNghiPhep(dnpId: dnpId);
        if (success.contains("Đã xóa")) {
          Get.find<NghiPhepHomeController>().refreshData(); // Làm mới danh sách
          Get.back();
          Get.snackbar(
            'Thành công',
            'Đã xóa đơn nghỉ phép.',
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.green.withOpacity(0.8),
            colorText: Colors.white,
          );
          return true;
        } else {
          Get.snackbar(
            'Lỗi',
            'Không thể xóa đơn nghỉ phép. Vui lòng thử lại.',
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.red.withOpacity(0.8),
            colorText: Colors.white,
          );
          return false;
        }
      } catch (e) {
        Get.snackbar(
          'Lỗi',
          'Đã xảy ra lỗi khi xóa: $e',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Colors.white,
        );
        return false;
      }
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy ID đơn nghỉ phép.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
      return false;
    }
  }
}
