import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:tvumobile/main.dart';

class ConnectivityController extends GetxController {
  late StreamSubscription<List<ConnectivityResult>> _streamSubscription;
  final _connectionType = MConnectivityResult.none.obs;
  final Connectivity _connectivity = Connectivity();

  MConnectivityResult get connectionType => _connectionType.value;

  set connectionType(value) {
    _connectionType.value = value;
  }

  @override
  void onInit() {
    super.onInit();
    getConnectivityType();
    _streamSubscription =
        _connectivity.onConnectivityChanged.listen(_updateState);
  }

  Future<void> getConnectivityType() async {
    late List<ConnectivityResult> connectivityResult;
    try {
      connectivityResult = await (_connectivity.checkConnectivity());
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
    return _updateState(connectivityResult);
  }

  _updateState(List<ConnectivityResult> connectivityResult) {
    if (connectivityResult.contains(ConnectivityResult.mobile)) {
      connectionType = MConnectivityResult.mobile;
    } else if (connectivityResult.contains(ConnectivityResult.wifi)) {
      connectionType = MConnectivityResult.wifi;
    } else if (connectivityResult.contains(ConnectivityResult.ethernet)) {
      connectionType = MConnectivityResult.wifi;
    } else if (connectivityResult.contains(ConnectivityResult.vpn)) {
      connectionType = MConnectivityResult.wifi;
    } else if (connectivityResult.contains(ConnectivityResult.bluetooth)) {
      print('Failed to get connection type');
    } else if (connectivityResult.contains(ConnectivityResult.other)) {
      print('Failed to get connection type');
    } else if (connectivityResult.contains(ConnectivityResult.none)) {
      connectionType = MConnectivityResult.none;
    }
    connectivityGlobal = connectionType;
    if (kDebugMode) {
      print(connectionType);
    }
  }

  @override
  void onClose() {
    _streamSubscription.cancel();
  }
}

enum MConnectivityResult { none, wifi, mobile }