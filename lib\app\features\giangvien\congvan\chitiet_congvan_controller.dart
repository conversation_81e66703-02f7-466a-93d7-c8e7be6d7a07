
import 'package:get/get.dart';
import 'package:tvumobile/app/data/models/tms/congvan/cong_van_nhan.dart';
import 'package:tvumobile/app/services/api_provider.dart';

class ChiTietCongVanController extends GetxController {
  ApiProvider apiProvider = Get.find();
  late String? cvId;
  late String? heroKey;
  final congVanNhan = <TmsCongVanModel>[].obs;
  TmsCongVanModel? doc;

  @override
  Future<void> onInit() async {
    super.onInit();

    // log('Arguments: ${Get.arguments}');
    doc = Get.arguments != null ? Get.arguments["doc"] : null;
    cvId = Get.parameters['cvId'] ?? Get.arguments['cvId'] ?? "";
    // log('doc: $doc');
    // log('cvId: $cvId');
    // log('Get.parameters_doc: ${Get.parameters['doc']}');
    if (doc == null) {
      String dStr = Get.parameters['doc'] ?? "";
      if (dStr != "") {
        doc = TmsCongVanModel.fromJsonString(dStr);
      } else {
        if (cvId != "") {
          await getDatas();
        } else {
          //Map<String, String> params = initialUri.queryParameters;
          //cvId = params['id']!;
          //await getDatas();
        }
      }
    }
    //heroKey = Get.parameters['heroKey'] ?? "heroKey";
    heroKey = "doc_${doc?.document!.id}";
  }

  getDatas() async {
    // log('Arguments: $cvId');
    final tkbData = await apiProvider.loadCongVanchitiet(cvId!);
    if (tkbData != null) {
      congVanNhan.add(tkbData);
      update();
    }
  }

  @override
  void onReady() {
    if (doc != null) {
      congVanNhan.add(doc!);
      update();
    }
    //initialUri = Uri();
  }
}
