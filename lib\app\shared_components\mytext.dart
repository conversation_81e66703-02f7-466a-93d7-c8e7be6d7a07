import 'package:flutter/material.dart';


enum MyTextType {
  displayLarge,
  displayMedium,
  displaySmall,
  headlineLarge,
  headlineMedium,
  headlineSmall,
  titleLarge,
  titleMedium,
  titleSmall,
  bodyLarge,
  bodyMedium,
  bodySmall,
  labelLarge,
  labelMedium,
  labelSmall,
}

Map<int, FontWeight> _defaultFontWeight = {
  100: FontWeight.w100,
  200: FontWeight.w200,
  300: FontWeight.w300,
  400: FontWeight.w300,
  500: FontWeight.w400,
  600: FontWeight.w500,
  700: FontWeight.w600,
  800: FontWeight.w700,
  900: FontWeight.w800,
};

class MyText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final int? fontWeight;
  final bool muted, xMuted;
  final double? letterSpacing;
  final Color? color;
  final TextDecoration decoration;
  final double? height;
  final double wordSpacing;
  final double? fontSize;

  final TextAlign? textAlign;
  final int? maxLines;
  final Locale? locale;
  final TextOverflow? overflow;
  final String? semanticsLabel;
  final bool? softWrap;
  final StrutStyle? strutStyle;
  final TextDirection? textDirection;
  final TextHeightBehavior? textHeightBehavior;
  final double? textScaleFactor;
  final TextWidthBasis? textWidthBasis;
  final MyTextType textType;

  const MyText(this.text,
      {super.key,
      this.style,
      this.fontWeight,
      this.muted = false,
      this.xMuted = false,
      this.letterSpacing,
      this.color,
      required this.decoration,
      this.height,
      required this.wordSpacing,
      this.fontSize,
      this.textAlign,
      this.maxLines,
      this.locale,
      this.overflow,
      this.semanticsLabel,
      this.softWrap,
      this.strutStyle,
      this.textDirection,
      this.textHeightBehavior,
      this.textScaleFactor,
      this.textWidthBasis,
      this.textType = MyTextType.bodyMedium});

  const MyText.displayLarge(this.text, {super.key, this.textType = MyTextType.displayLarge, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.displayMedium(this.text, {super.key, this.textType = MyTextType.displayMedium, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.displaySmall(this.text, {super.key, this.textType = MyTextType.displaySmall, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.headlineLarge(this.text, {super.key, this.textType = MyTextType.headlineLarge, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.headlineMedium(this.text, {super.key, this.textType = MyTextType.headlineMedium, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.headlineSmall(this.text, {super.key, this.textType = MyTextType.headlineSmall, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.bodyLarge(this.text, {super.key, this.textType = MyTextType.bodyLarge, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.bodyMedium(this.text, {super.key, this.textType = MyTextType.bodyMedium, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.bodySmall(this.text, {super.key, this.textType = MyTextType.bodySmall, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.titleLarge(this.text, {super.key, this.textType = MyTextType.titleLarge, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.titleMedium(this.text, {super.key, this.textType = MyTextType.titleMedium, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.titleSmall(this.text, {super.key, this.textType = MyTextType.titleSmall, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.labelLarge(this.text, {super.key, this.textType = MyTextType.labelLarge, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.labelMedium(this.text, {super.key, this.textType = MyTextType.labelMedium, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  const MyText.labelSmall(this.text, {super.key, this.textType = MyTextType.labelSmall, this.style, this.fontWeight, this.muted = false, this.xMuted = false, this.letterSpacing, this.color, this.decoration = TextDecoration.none, this.height, this.wordSpacing = 0, this.fontSize, this.textAlign, this.maxLines, this.locale, this.overflow, this.semanticsLabel, this.softWrap, this.strutStyle, this.textDirection, this.textHeightBehavior, this.textScaleFactor, this.textWidthBasis});

  @override
  Widget build(BuildContext context) {

    ThemeData theme = Theme.of(context);
    TextStyle textStyle = style ?? TextStyle();
    switch(textType) {
      case MyTextType.displayLarge:
        textStyle = theme.textTheme.displayLarge!;
        break;
      case MyTextType.displayMedium:
        textStyle = theme.textTheme.displayMedium!;
        break;
      case MyTextType.displaySmall:
        textStyle = theme.textTheme.displaySmall!;
        break;
      case MyTextType.headlineLarge:
        textStyle = theme.textTheme.headlineLarge!;
        break;
      case MyTextType.headlineMedium:
        textStyle = theme.textTheme.headlineMedium!;
        break;
      case MyTextType.headlineSmall:
        textStyle = theme.textTheme.headlineSmall!;
        break;
      case MyTextType.titleLarge:
        textStyle = theme.textTheme.titleLarge!;
        break;
      case MyTextType.titleMedium:
        textStyle = theme.textTheme.titleMedium!;
        break;
      case MyTextType.titleSmall:
        textStyle = theme.textTheme.titleSmall!;
        break;
      case MyTextType.bodyLarge:
        textStyle = theme.textTheme.bodyLarge!;
        break;
      case MyTextType.bodyMedium:
        textStyle = theme.textTheme.bodyMedium!;
        break;
      case MyTextType.bodySmall:
        textStyle = theme.textTheme.bodySmall!;
        break;
      case MyTextType.labelLarge:
        textStyle = theme.textTheme.labelLarge!;
        break;
      case MyTextType.labelMedium:
        textStyle = theme.textTheme.labelMedium!;
        break;
      case MyTextType.labelSmall:
        textStyle = theme.textTheme.labelSmall!;
        break;
    }
    if(fontWeight != null) {
      textStyle = textStyle.copyWith(fontWeight: _defaultFontWeight[fontWeight]);
    }
    if(color != null) {
      textStyle = textStyle.copyWith(color: color);
    }
    if(letterSpacing != null) {
      textStyle = textStyle.copyWith(letterSpacing: letterSpacing);
    }
    if(height != null) {
      textStyle = textStyle.copyWith(height: height);
    }
    if(decoration != TextDecoration.none) {
      textStyle = textStyle.copyWith(decoration: decoration);
    }
    if(wordSpacing != 0) {
      textStyle = textStyle.copyWith(wordSpacing: wordSpacing);
    }
    if(fontSize != null) {
      textStyle = textStyle.copyWith(fontSize: fontSize);
    }
    Color? finalColor = textStyle.color;
    finalColor = xMuted
        ? finalColor?.withAlpha(160)
        : (muted ? finalColor?.withAlpha(200) : finalColor);
    if(finalColor != null) {
      textStyle = textStyle.copyWith(color: finalColor);
    }

    return Text(
      text,
      style: textStyle ,
      textAlign: textAlign,
      maxLines: maxLines,
      locale: locale,
      overflow: overflow,
      semanticsLabel: semanticsLabel,
      softWrap: softWrap,
      strutStyle: strutStyle,
      textDirection: textDirection,
      // textDirection: textDirection ?? MyAppTheme.textDirection,
      textHeightBehavior: textHeightBehavior,
      textScaleFactor: textScaleFactor,
      //textScaler: TextScaler.linear(textScaleFactor!),
      textWidthBasis: textWidthBasis,
      key: key,
    );
  }
}
