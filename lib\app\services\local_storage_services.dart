import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/config/translation/app_translations.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/utils/oauth2/src/credentials.dart';

/// Contains all services to get data from local storage with improved error handling
class LocalStorageServices {
  static final LocalStorageServices _localStorageServices =
      LocalStorageServices._internal();

  factory LocalStorageServices() {
    return _localStorageServices;
  }
  LocalStorageServices._internal();

  static final _logger = Logger();
  static final _sharedPreferences = GetStorage();

  /// Initialize local storage with error handling
  static Future<bool> init() async {
    try {
      await GetStorage.init();
      return true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Failed to initialize local storage: $e');
      }
      return false;
    }
  }

  /// Set if the app is new with error handling
  static Future<bool> setIsNew(bool isNew) async {
    try {
      await _sharedPreferences.write(StorageConstants.newbieKey, isNew);
      return true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error setting isNew: $e');
      }
      return false;
    }
  }

  /// Get if the app is new with default fallback
  static bool getIsNew() {
    try {
      return _sharedPreferences.read<bool>(StorageConstants.newbieKey) ?? true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error reading isNew: $e');
      }
      return true;
    }
  }

  /// Set theme with error handling
  static Future<bool> setThemeIsLight(bool lightTheme) async {
    try {
      await _sharedPreferences.write(
          StorageConstants.lightThemeKey, lightTheme);
      return true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error setting theme: $e');
      }
      return false;
    }
  }

  /// Get theme with default fallback
  static bool getThemeIsLight() {
    try {
      return _sharedPreferences.read<bool>(StorageConstants.lightThemeKey) ??
          true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error reading theme: $e');
      }
      return true;
    }
  }

  /// Set current language with error handling
  static Future<bool> setCurrentLanguage(String languageCode) async {
    try {
      await _sharedPreferences.write(
          StorageConstants.currentLocalKey, languageCode);
      return true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error setting language: $e');
      }
      return false;
    }
  }

  /// Get current locale with safe fallback
  static Locale getCurrentLocal() {
    try {
      String? langCode =
          _sharedPreferences.read<String>(StorageConstants.currentLocalKey);
      return langCode != null
          ? AppTranslations.localeKeys[langCode] ??
              AppTranslations.defaultLocale
          : AppTranslations.defaultLocale;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error reading current locale: $e');
      }
      return AppTranslations.defaultLocale;
    }
  }

  /// Set FCM token with error handling
  static Future<bool> setFcmToken(String token) async {
    try {
      await _sharedPreferences.write(StorageConstants.fcmTokenKey, token);
      return true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error setting FCM token: $e');
      }
      return false;
    }
  }

  /// Get FCM token with safe retrieval
  static String? getFcmToken() {
    try {
      return _sharedPreferences.read<String>(StorageConstants.fcmTokenKey);
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error reading FCM token: $e');
      }
      return null;
    }
  }

  /// Clear all data except language with comprehensive error handling
  static Future<bool> clearExceptLanguage() async {
    try {
      final currentLanguage =
          _sharedPreferences.read<String>(StorageConstants.currentLocalKey);

      await _sharedPreferences.erase();

      if (currentLanguage != null) {
        await setCurrentLanguage(currentLanguage);
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error clearing storage except language: $e');
      }
      return false;
    }
  }

  /// Clear all data with error handling
  static Future<bool> clear() async {
    try {
      await _sharedPreferences.erase();
      return true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error clearing all storage: $e');
      }
      return false;
    }
  }

  /// Set access token with error handling
  static Future<bool> setAccessToken(String token) async {
    try {
      await _sharedPreferences.write(StorageConstants.token, token);
      return true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error setting access token: $e');
      }
      return false;
    }
  }

  /// Get access token with safe retrieval
  static String? getAccessToken() {
    try {
      return _sharedPreferences.read<String>(StorageConstants.token);
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error reading access token: $e');
      }
      return null;
    }
  }

  /// Set user info with comprehensive error handling
  static Future<bool> setUserInfo(UserInfo userInfo) async {
    try {
      if (kDebugMode) {
        _logger.i(userInfo.toJsonString());
      }
      await _sharedPreferences.write(
          StorageConstants.userInfo, userInfo.toJsonString());
      return true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error setting user info: $e');
      }
      return false;
    }
  }

  /// Get user info with robust error handling
  static UserInfo? getUserInfo() {
    String? user;
    try {
      user = _sharedPreferences.read<String>(StorageConstants.userInfo);
      if (user != null) {
        return UserInfo.fromJsonString(user);
      }
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error parsing user info: $e');
        _logger.i('Raw user info: $user');
      }
    }
    return null;
  }

  /// Set credentials with error handling
  static Future<bool> setCredential(Credentials token) async {
    try {
      await _sharedPreferences.write(
          StorageConstants.credential, token.toJson());
      return true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error setting credentials: $e');
      }
      return false;
    }
  }

  /// Set credential string with error handling
  static Future<bool> setCredentialString(String token) async {
    try {
      await _sharedPreferences.write(StorageConstants.credential, token);
      return true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error setting credential string: $e');
      }
      return false;
    }
  }

  /// Get credentials with safe retrieval
  static Credentials? getCredential() {
    try {
      String? cred =
          _sharedPreferences.read<String>(StorageConstants.credential);
      return (cred != null && cred.isNotEmpty)
          ? Credentials.fromJson(cred)
          : null;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error reading credentials: $e');
      }
      return null;
    }
  }

  /// Get credential string with safe retrieval
  static String? getCredentialString() {
    try {
      return _sharedPreferences.read<String>(StorageConstants.credential);
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error reading credential string: $e');
      }
      return null;
    }
  }

  /// Logout user with comprehensive error handling
  static Future<bool> logoutUser() async {
    try {
      await _sharedPreferences.write(StorageConstants.userInfo, "");
      await _sharedPreferences.write(StorageConstants.credential, "");
      await _sharedPreferences.write(StorageConstants.token, "");
      return true;
    } catch (e) {
      if (kDebugMode) {
        _logger.e('Error logging out user: $e');
      }
      return false;
    }
  }

  static Future<void> saveSignature(String filePath) async {
    await _sharedPreferences.write(StorageConstants.signatureFile, filePath);
  }

  static String? getSignature() {
    return _sharedPreferences.read<String>(StorageConstants.signatureFile);
  }
}
