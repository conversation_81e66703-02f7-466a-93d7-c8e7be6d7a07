import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/tasky/Task.dart';
import 'package:tvumobile/app/data/models/tms/danhmuc.dart';
import 'package:tvumobile/app/utils/helpers/pagination_filter.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:tvumobile/app/utils/extensions/list_extentions.dart';

class TaskyTaskViewController extends GetxController {
  ApiProvider apiProvider = Get.find();

  final _paginationFilter = PaginationFilter().obs;
  final _lastPage = false.obs;
  int get limit => _paginationFilter.value.limit;
  int get page => _paginationFilter.value.page;
  bool get lastPage => _lastPage.value;

  var taskStats = {}.obs;
  List<dynamic> teamMembers = [].obs;
  List<Task> allTasks = <Task>[].obs;
  List<Task> allTasksFromApi = [];
  UserInfo? userInfo;
  Timer? debounce = Timer(Duration(seconds: 1), () {});
  TextEditingController searchController = TextEditingController();

  // Khai báo drawer
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final RxString currentView = "DN".obs;
  final RxString loainv = "received".obs; // Lưu loaiNV trong controller
  final currentDataAvaliable = true.obs;
  final appbarTitle = "Công việc".obs;
  final currentYear = DateTime.now().year.obs;
  final maxYear = DateTime.now().year.obs;
  final maxMonth = DateTime.now().month;
  final currentMonth = DateTime.now().month.obs;
  final taskInfo = <Task>[].obs;
  ScrollController monthScroll = ScrollController();
  final viFormat = NumberFormat.currency(locale: "vi");
  final isLoading = false.obs; // Thêm biến để theo dõi trạng thái loading

  final RxList<String> statusList = <String>[
    "Tất cả",
    "Assigned",
    "In Process",
    "Pending",
    "Completed",
    "Declined",
    "Closed",
    "Canceled",
    "Removed"
  ].obs;
  final RxString currentStatus = "Tất cả".obs;

  @override
  void onInit() {
    super.onInit();
    if (kDebugMode) {
      print("TaskyTaskViewController onInit");
    }
    _paginationFilter.value.limit = 30;
    _paginationFilter.value.page = 1;
    userInfo = LocalStorageServices.getUserInfo();
    ever(_paginationFilter, (_) => refreshTasks());
    ever(currentStatus, (_) => _filterTasksByStatus());
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    if (kDebugMode) {
      print("TaskyTaskViewController onReady");
    }
    currentView.listen(changeView);
    currentView.value = "DN";
  }

  // ----------------- CHỌN THEO TRẠNG THÁI TASK --------------------
  Future<void> _filterTasksByStatus() async {
    if (currentStatus.value == "Tất cả") {
      allTasks.assignAll(allTasksFromApi);
    } else {
      allTasks.assignAll(
        allTasksFromApi
            .where((task) => task.trangThai?.maDanhMuc == currentStatus.value)
            .toList(),
      );
    }
    update();
  }

  // Hàm ánh xạ trạng thái sang tên hiển thị tiếng Việt
  String getStatusDisplayName(String status) {
    switch (status) {
      case "Tất cả":
        return "Tất cả";
      case "Assigned":
        return "Vừa được giao";
      case "In Process":
        return "Đang thực hiện";
      case "Pending":
        return "Đang chờ";
      case "Completed":
        return "Hoàn thành";
      case "Declined":
        return "Bị từ chối";
      case "Closed":
        return "Đã đóng";
      case "Canceled":
        return "Đã hủy";
      case "Removed":
        return "Đã xóa";
      default:
        return status;
    }
  }

  // ----------------- DRAWER (MENU BÊN) CÁC MỤC --------------------

  // Mở drawer
  void openDrawer() {
    scaffoldKey.currentState?.openDrawer();
  }

  // Đóng drawer
  void closeDrawer() {
    if (scaffoldKey.currentState!.isEndDrawerOpen) {
      scaffoldKey.currentState!.closeEndDrawer();
    } else {
      scaffoldKey.currentState!.closeDrawer();
    }
  }

  // Xử lý sự kiện khi người dùng chọn 1 mục trong drawer
  void drawerClick(String view) {
    currentView.value = view;
    closeDrawer();
    //changeTitle();
    update();
  }

  // --------------------------------------------------------------

  // -------------- THAY ĐỔI THÔNG TIN NHIỆM VỤ QUA DANH MỤC --------------

  // Thay đổi chế độ xem và cập nhật dữ liệu tương ứng
  Future<void> changeView(data) async {
    currentDataAvaliable.value = true;
    taskInfo.clear();

    switch (currentView.value) {
      case "DN":
        maxYear.value = DateTime.now().year;
        loainv.value = "received"; // Nhiệm vụ đã nhận
        appbarTitle.value = "Công việc được giao";
        isLoading.value = true; // Bắt đầu loading
        scrolltoFocus();
        await refreshTasks();
        isLoading.value = false; // Kết thúc loading
        break;
      case "DG":
        maxYear.value = DateTime.now().year;
        loainv.value = "assigned"; // Nhiệm vụ đã giao
        appbarTitle.value = "Công việc đã giao";
        isLoading.value = true; // Bắt đầu loading
        scrolltoFocus();
        await refreshTasks();
        isLoading.value = false; // Kết thúc loading
        break;
      case "GS":
        maxYear.value = DateTime.now().year;
        loainv.value = "supervised"; // Nhiệm vụ giám sát
        appbarTitle.value = "Công việc giám sát";
        isLoading.value = true; // Bắt đầu loading
        scrolltoFocus();
        await refreshTasks();
        isLoading.value = false; // Kết thúc loading
        break;
    }
    update();
  }

  // Cuộn danh sách tháng đến vị trí của tháng hiện tại
  scrolltoFocus() {
    //if(currentMonth.value <= 5) return;
    double ti = 1.0;
    ti = currentMonth.value == 5
        ? ((4) * currentMonth.value) * 1.0
        : ((16 + 16 + 6) * currentMonth.value) * 1.0;
    if (currentMonth.value <= 4) ti = 1.0;
    //print(ti);
    monthScroll.animateTo(ti,
        duration: Duration(milliseconds: 200), curve: Curves.linear);
  }

  // Tăng năm hiện tại lên 1
  Future<void> currentYearPlus() async {
    // if (currentView.value == "TN") {
    //   if (thueNam.value >= maxYear.value) return;
    //   thueNam.value += 1;
    //   changeView(null);
    //   return;
    // }
    if (currentYear.value >= maxYear.value) return;
    currentYear.value += 1;
    // isLoading.value = true; // Bắt đầu loading
    await changeView(null); // Chờ dữ liệu tải xong
    // isLoading.value = false; // Kết thúc loading
  }

  // Giảm năm hiện tại xuống 1
  Future<void> currentYearMinus() async {
    // if (currentView.value == "TN") {
    //   if (thueNam.value <= 2006) return;
    //   thueNam.value -= 1;
    //   changeView(null);
    //   return;
    // }
    if (currentYear.value <= 2006) return;
    currentYear.value -= 1;
    // isLoading.value = true; // Bắt đầu loading
    await changeView(null); // Chờ dữ liệu tải xong
    // isLoading.value = false; // Kết thúc loading
  }

  // Xử lý khi người dùng chọn 1 tháng
  Future<void> onMonthTap(int m) async {
    if (currentYear.value == maxYear.value) {
      if (m > maxMonth) {
        return;
      }
      currentMonth.value = m;
    } else {
      currentMonth.value = m;
    }
    // isLoading.value = true; // Bắt đầu loading
    await changeView(null); // Chờ dữ liệu tải xong
    // isLoading.value = false; // Kết thúc loading
  }

  // ---------------------------------------------------------------------

  // Lấy danh sách tất cả các nhiệm vụ (tasks) từ API với phân trang.
  _getTaskList() async {
    if (kDebugMode) {
      print("TaskyTaskViewController _getTaskList");
    }
    var tmp = await apiProvider.getAllTasks(
        page: _paginationFilter.value.page,
        pageSize: _paginationFilter.value.limit,
        waiting: false,
        month: currentMonth.value,
        year: currentYear.value);
    if (tmp.isNotEmpty) {
      allTasks.clear();
      allTasks.addAll(tmp);
      update();
    }
  }

  // Tải thêm tasks khi người dùng cuộn đến cuối danh sách (load more).
  Future<void> loadMoreTasks() async {
    if (kDebugMode) {
      print("TaskyTaskViewController loadMoreTasks");
    }
    if (_lastPage.value) {
      showLastPage();
      return;
    }
    _paginationFilter.value.page += 1;
    var tmp = await apiProvider.getAllTasks(
        page: _paginationFilter.value.page,
        pageSize: _paginationFilter.value.limit,
        taskRole: loainv.value,
        month: currentMonth.value,
        year: currentYear.value);
    if (tmp.isEmpty) {
      _lastPage.value = true;
      showLastPage();
    } else {
      // allTasks.addAll(tmp);
      allTasksFromApi.addAll(tmp);
      await _filterTasksByStatus();
      update();
    }
  }

  // ---------- Làm mới danh sách tasks (reset về trang đầu). ----------
  Future<void> refreshTasks() async {
    if (kDebugMode) {
      print("TaskyTaskViewController refreshTaskswith loaiNV: ${loainv.value}");
    }
    _paginationFilter.value.page = 1;
    _lastPage.value = false;
    allTasksFromApi.clear();
    allTasks.clear();
    // update();
    var tmp = await apiProvider.getAllTasks(
        page: _paginationFilter.value.page,
        pageSize: _paginationFilter.value.limit,
        taskRole: loainv.value,
        month: currentMonth.value,
        year: currentYear.value);

    if (tmp.isEmpty) {
      _lastPage.value = true;
      // showLastPage();
      update();
    } else {
      // Sắp xếp allTasks theo endDateDuKien
      tmp.sort((a, b) {
        if (a.endDateDuKien == null && b.endDateDuKien == null) return 0;
        if (a.endDateDuKien == null) return 1;
        if (b.endDateDuKien == null) return -1;
        return a.endDateDuKien!.compareTo(b.endDateDuKien!);
      });

      // allTasks.addAll(tmp);
      allTasksFromApi.addAll(tmp);
      await _filterTasksByStatus();
      update();
    }
  }

  // ---------- Hiển thị thông báo khi không còn dữ liệu để tải. ----------
  showLastPage() {
    Get.snackbar("Thông báo", "Đã cuộn đến cuối",
        snackPosition: SnackPosition.BOTTOM,
        duration: Duration(seconds: 1),
        backgroundColor: Colors.grey,
        overlayColor: Colors.black.withOpacity(0.5));
  }

  // --------------------------------- CÁC HÀM CHƯA SỬ DỤNG -----------------------------
  // --------------- Lấy danh sách tasks theo tag (danh mục). ---------------
  Future<void> loadTasksByTag(DanhMuc tag) async {
    if (kDebugMode) {
      print("TaskyTaskViewController loadTasksByTag");
    }
    _paginationFilter.value.page = 1;
    _lastPage.value = false;
    allTasks.clear();
    update();
    var tmp = await apiProvider.getAllTasks(taskId: 0, tag: tag.id);
    if (tmp.isEmpty) {
      _lastPage.value = true;
    } else {
      allTasks.addAll(tmp);
      update();
    }
  }

  // --------------- Lấy danh sách tasks theo trạng thái. ---------------
  Future<void> loadTasksByStatus(DanhMuc status) async {
    if (kDebugMode) {
      print("TaskyTaskViewController loadTasksByStatus");
    }
    _paginationFilter.value.page = 1;
    _lastPage.value = false;
    allTasks.clear();
    update();
    var tmp = await apiProvider.getAllTasks(taskId: 0, status: status.id);
    if (tmp.isEmpty) {
      _lastPage.value = true;
    } else {
      allTasks.addAll(tmp);
      update();
    }
  }

  // --------------- Lấy danh sách tasks theo loại nhiệm vụ. ---------------
  Future<void> loadTasksByTaskType(DanhMuc taskType) async {
    if (kDebugMode) {
      print("TaskyTaskViewController loadTasksByTaskType");
    }
    _paginationFilter.value.page = 1;
    _lastPage.value = false;
    allTasks.clear();
    update();
    var tmp = await apiProvider.getAllTasks(taskId: 0, taskType: taskType.id);
    if (tmp.isEmpty) {
      _lastPage.value = true;
    } else {
      allTasks.addAll(tmp);
      update();
    }
  }

  // ---------- Lấy danh sách tasks theo từ khóa tìm kiếm. ----------
  Future<void> loadTasksBySearch(String searchQuery) async {
    if (kDebugMode) {
      print("TaskyTaskViewController loadTasksBySearch");
    }
    _paginationFilter.value.page = 1;
    _lastPage.value = false;
    allTasks.clear();
    update();
    var tmp = await apiProvider.getAllTasks(searchQuery: searchQuery);
    if (tmp.isEmpty) {
      _lastPage.value = true;
    } else {
      allTasks.addAll(tmp);
      update();
    }
  }

  // ---------- Lấy danh sách tasks với phân trang tùy chỉnh. ----------
  Future<void> loadTasksByPagination(int page, int limit) async {
    if (kDebugMode) {
      print("TaskyTaskViewController loadTasksByPagination");
    }
    _paginationFilter.value.page = page;
    _paginationFilter.value.limit = limit;
    _lastPage.value = false;
    allTasks.clear();
    update();
    var tmp = await apiProvider.getAllTasks(page: page, pageSize: limit);
    if (tmp.isEmpty) {
      _lastPage.value = true;
      showLastPage();
    } else {
      allTasks.addAll(Task.parse(tmp));
      update();
    }
  }

  // ---------- Lấy thông tin tổng quan về tasks, thành viên đội, và thống kê. ----------
  getOverviews() async {
    Map<String, dynamic> tmp = await apiProvider.getTaskOverview(taskLimit: 20);
    if (tmp.isNotEmpty) {
      teamMembers = tmp["teamMembers"];
      var tmp1 = teamMembers
          .where((w) => w["tenChucVu"].toString().toLowerCase() != "viên chức")
          .toList();
      var tmp2 = teamMembers
          .where((w) => w["tenChucVu"].toString().toLowerCase() == "viên chức")
          .toList();
      tmp1 = tmp1.orderBy("tenChucVu", "desc");
      tmp2 = tmp2.orderBy("ten", "asc");
      teamMembers.clear();
      teamMembers.addAll(tmp1);
      teamMembers.addAll(tmp2);
      update();
      taskStats.value = tmp["taskStatistics"];
      update();
      allTasks.clear();
      //print(tmp["tasks"]);
      var tmp3 = Task.parse(tmp["tasks"]);
      allTasks.addAll(tmp3);
      update();
    }
  }
  // ------------------------------------------------------------------------------------
}
