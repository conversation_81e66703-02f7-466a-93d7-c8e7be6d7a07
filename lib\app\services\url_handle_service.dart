import 'package:app_links/app_links.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/services/api_provider.dart';

class UrlHandleService extends GetxService {

  static String _code = "";
  static String get code => _code;
  static bool get hasCode => _code.isNotEmpty;

  static String _path = "";
  static String get path => _path;

  static bool _initialUriIsHandled = false;

  static void reset() => _code = "";
  static Uri initialUri = Uri();
  ApiProvider apiProvider = Get.find();
  final _appLinks = AppLinks();

  @override
  Future<void> onInit() async {
    super.onInit();
    if (!_initialUriIsHandled) {
      _initialUriIsHandled = true;
      try {
        final uri = await _appLinks.getInitialLink();
        if (uri == null) {
          Logger().e('no initial uri');
        } else {
          initialUri = uri;
          if (!kIsWeb) {
            uniHandle(uri);
          }
          Logger().e('got initial uri: $uri');
        }
      } on PlatformException {
        // Platform messages may fail but we ignore the exception
        Logger().e('falied to get initial uri');
      } on FormatException catch (err) {
        Logger().e('malformed initial uri', error: err);
      }
    }

    if (!kIsWeb) {
      _appLinks.uriLinkStream.listen((Uri? uri) async {
        uniHandle(uri);
        Logger().e('got initial uri: $uri');
      }, onError: (error) {
        Logger().e('malformed initial uri', error: error);
      });
    }
  }

  @override
  void onClose() {
    initialUri = Uri();
  }

  @override
  void onReady() {

  }

  Future<void> uniHandle(Uri? uri) async {
    if (uri == null || uri.path.isEmpty) return;
    initialUri = uri;
    Map<String, String> params = uri.queryParameters;
    _code = params['code'] ?? "";
    if (initialUri
        .toString()
        .toLowerCase()
        .contains("danhgiachatluongphucvu".toLowerCase())) {
      _code = params['donviId'] ?? params['donviid'] ?? "";
      _path = "danhgiachatluongphucvu";
      //Get.put(DanhGiaController(),);
      Get.toNamed(AppPages.DANHGIA,
          parameters: {"donviId": code, "type": "dv"}, preventDuplicates: true);
    } else if (initialUri.toString().toLowerCase().contains("giaydieuxe") &&
        initialUri.toString().toLowerCase().contains("danhgiachatluong")) {
      _code = params['Id'] ?? params['id'] ?? "";
      _path = "danhgiachatluong";
      if (kDebugMode) {
        print("*****************: ${initialUri.toString().toLowerCase()}");
      }
      //Get.put(DanhGiaController(),);
      Get.toNamed(Routes.DANHGIA,
          parameters: {"donviId": code, "type": "xe"}, preventDuplicates: true);
    } else if (initialUri.toString().toLowerCase().contains("congvan") &&
        initialUri.toString().toLowerCase().contains("chitiet")) {
      //Get.put(
      //  ChiTietCongVanController(),
      //);
      if (kDebugMode) {
        print("got congvan");
      }
      Get.toNamed(Routes.CV_DETAIL,
          parameters: {"cvId": params['id']!}, preventDuplicates: true);
    } else if (initialUri.host.toLowerCase().contains("sso.tvu.edu.vn")) {
      return;
    } else {
      return;
    }
  }
}