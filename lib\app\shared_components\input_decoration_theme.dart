
import 'package:flutter/material.dart';

InputDecorationTheme GlobalInputDecoration(BuildContext context) {
  final theme = Theme.of(context);
  return InputDecorationTheme(
      disabledBorder: OutlineInputBorder(
        borderSide:
        BorderSide(color: theme.dividerColor, width: 1),
      ),
      enabledBorder: const OutlineInputBorder(
        borderSide:
        BorderSide(color: Color(0x747985FF),width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: theme.colorScheme.tertiary, width: 1),
      ),
      errorBorder: const OutlineInputBorder(
        borderSide: BorderSide(color: Colors.red, width: 1),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide:
        BorderSide(color: Colors.red[500]!, width: 1),
      ),
      floatingLabelStyle: theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.onSurface),
      //filled: true,
      //fillColor: Get.isDarkMode
      //    ? theme.scaffoldBackgroundColor.withOpacity(0.3)
      //    : Colors.grey[100]?.withOpacity(0.8),
      contentPadding: const EdgeInsets.all(8)
  );
}