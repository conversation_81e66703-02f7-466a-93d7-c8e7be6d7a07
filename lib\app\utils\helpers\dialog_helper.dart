import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:tvumobile/app/constans/app_constants.dart';


class DialogHelper {
  static get context => null;

  ///show error dialog
  static void showErrorDialog(String title, String description) {
    Get.dialog(
      Dialog(
        elevation: 6,
        shadowColor: Colors.grey[400],
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Lottie.asset(
                'animations/apiError.json',
                height: 120,
                repeat: true,
                reverse: true,
                fit: BoxFit.cover,
              ),
              Text(
                title,
                style: TextStyle(
                  color: Colors.redAccent,
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              ),
              SizedBox(height: 20),
              <PERSON>zed<PERSON>ox(height: 30),
              SizedBox(
                width: 100,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  onPressed: () {
                    if (Get.isDialogOpen!) Get.back();
                  },
                  child: Text("Okay"),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///show loading
  static void showLoading() {
    //  var theme = Theme.of(context);
    Get.dialog(
      Center(
        child: Container(
          height: 80,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            shape: BoxShape.circle,
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // App Icon
              Container(
                height: 50,
                width: 50,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  image: DecorationImage(
                    image: AssetImage(
                      AppImages.kAppIcon150,
                    ),
                  ),
                ),
              ),
              // Loader
              SizedBox(
                height: 60,
                width: 60,
                child: const CircularProgressIndicator(),
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(.1),
    );
  }

  ///hide loading
  static Future<void> hideLoading() async {
    if (Get.isDialogOpen != null) {
      if (Get.isSnackbarOpen) {
        await AppConfig.kMySnackBarDuration.delay();
        Get.closeAllSnackbars();
        Get.back();
      } else {
        Get.back();
      }
    }
  }
}
