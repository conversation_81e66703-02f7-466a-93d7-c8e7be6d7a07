import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/constans/constant_data.dart';
import 'package:tvumobile/firebase_options.dart';

import 'api_provider.dart';

@pragma('vm:entry-point')
Future<void> firebaseMessageBackgroundHandle(RemoteMessage message) async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  Logger().i("BackGround Message :: ${message.messageId}");
  NotificationService notificationService = NotificationService();
  await notificationService.initInfo();
  notificationService.display(message);
}

@pragma('vm:entry-point')
Future<void> firebaseMessageBackgroundTapedHandle(
    NotificationResponse message) async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  log("================firebaseMessageBackgroundTapedHandle=================");
  Logger().i("BackGround Tap Message ID:: ${message.id}");
  Logger().i("BackGround Tap Message payload:: ${message.payload}");
}

class NotificationService {
  // FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  // ignore: unused_field
  final List<String> _topics = [
    "tvu_everyone",
    "tvu_giangvien",
    "tvu_vienchuc",
    "tvu_sinhven"
  ];
  late AndroidNotificationChannel channel;

  /// Initialize the [FlutterLocalNotificationsPlugin] package.
  late FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin;
  bool isFlutterLocalNotificationsInitialized = false;
  NotificationDetails? notificationDetailsBoth;

  initInfo() async {
    if (isFlutterLocalNotificationsInitialized) {
      return;
    } else {
      flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
      isFlutterLocalNotificationsInitialized = true;
    }
    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
    var request = await FirebaseMessaging.instance.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (request.authorizationStatus == AuthorizationStatus.authorized ||
        request.authorizationStatus == AuthorizationStatus.provisional) {
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/launcher_icon');
      var iosInitializationSettings = const DarwinInitializationSettings();
      final InitializationSettings initializationSettings =
          InitializationSettings(
              android: initializationSettingsAndroid,
              iOS: iosInitializationSettings);
      await flutterLocalNotificationsPlugin.initialize(initializationSettings,
          onDidReceiveNotificationResponse: (payload) {
        log("================onDidReceiveNotificationResponse=================");
        //Logger().i('Notification payload: ${payload.payload}');
        handleNotificationTap(payload.payload!);
      },
          onDidReceiveBackgroundNotificationResponse:
              firebaseMessageBackgroundTapedHandle);
      await setupInteractedMessage();
      await initChannels();
      updateFCM();
    }
  }

  Future<void> initChannels() async {
    channel = const AndroidNotificationChannel(
      "tvu_channel",
      "Thông báo của TVU",
      description: 'Hiển thị thông báo của TVU',
      importance: Importance.high,
    );
    AndroidNotificationDetails notificationDetails = AndroidNotificationDetails(
        channel.id, channel.name,
        channelDescription: channel.description,
        importance: Importance.high,
        priority: Priority.high,
        ticker: 'ticker');
    const DarwinNotificationDetails darwinNotificationDetails =
        DarwinNotificationDetails(
            presentAlert: true, presentBadge: true, presentSound: true);
    notificationDetailsBoth = NotificationDetails(
        android: notificationDetails, iOS: darwinNotificationDetails);
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  Future<void> setupInteractedMessage() async {
    log("::::::::::::setupInteractedMessage:::::::::::::::::");
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      //FirebaseMessaging.onBackgroundMessage((message) => firebaseMessageBackgroundHandle(message));
      firebaseMessageBackgroundHandle(initialMessage);
    }
    FirebaseMessaging.onBackgroundMessage(firebaseMessageBackgroundHandle);

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      Logger().i("::::::::::::onMessage:::::::::::::::::");
      if (message.notification != null) {
        Logger().i(message.notification.toString());
        display(message);
      }
    });
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      Logger().i("::::::::::::MessageOpenedApp:::::::::::::::::");

      if (message.notification != null) {
        // display(message);

        //log(message.data.toString());
        handleNotificationTap(message.data);
        //String orderId = message.data['orderId'];
        // if (message.data['type'] == 'vendor_order') {
        //   push(navigatorKey.currentContext!, OrderDetailsScreen(orderId: orderId));
        // } else if (message.data['type'] == 'cab_order') {
        //   push(navigatorKey.currentContext!, CabOrderDetailScreen(orderId: orderId));
        // } else if (message.data['type'] == 'parcel_order') {
        //   push(navigatorKey.currentContext!, ParcelOrderDetailScreen(orderId: orderId));
        // } else if (message.data['type'] == 'rental_order') {
        //   push(navigatorKey.currentContext!, RenatalSummaryScreen(orderId: orderId));
        // } else if (message.data['type'] == 'provider_order') {
        //   push(navigatorKey.currentContext!, OnDemandOrderDetailsScreen(orderId: orderId));
        // } else if (message.data['type'] == 'cab_parcel_chat' || message.data['type'] == 'vendor_chat' || message.data['type'] == 'provider_chat') {
        //   push(
        //       navigatorKey.currentContext!,
        //       ChatScreens(
        //         orderId: orderId,
        //         customerId: message.data['customerId'],
        //         customerName: message.data['customerName'],
        //         customerProfileImage: message.data['customerProfileImage'],
        //         restaurantId: message.data['restaurantId'],
        //         restaurantName: message.data['restaurantName'],
        //         restaurantProfileImage: message.data['restaurantProfileImage'],
        //         token: message.data['token'],
        //         chatType: message.data['chatType'],
        //         type: message.data['type'],
        //       ));
        // } else if (message.data['type'] == 'dine_in') {
        //   pushReplacement(
        //       navigatorKey.currentContext!,
        //       ContainerScreen(
        //         user: MyAppState.currentUser,
        //         drawerSelection: DrawerSelection.MyBooking,
        //         appBarTitle: 'Dine-In Bookings'.tr(),
        //         currentWidget: MyBookingScreen(),
        //       ));
        // } else {
        //   /// receive message through inbox
        //   push(
        //       navigatorKey.currentContext!,
        //       ChatScreens(
        //         orderId: orderId,
        //         customerId: message.data['customerId'],
        //         customerName: message.data['customerName'],
        //         customerProfileImage: message.data['customerProfileImage'],
        //         restaurantId: message.data['restaurantId'],
        //         restaurantName: message.data['restaurantName'],
        //         restaurantProfileImage: message.data['restaurantProfileImage'],
        //         token: message.data['token'],
        //         chatType: message.data['chatType'],
        //       ));
        // }
      }
    });
    Logger().i("::::::::::::Permission authorized:::::::::::::::::");
    await FirebaseMessaging.instance.subscribeToTopic("tvu_everyone");
    await FirebaseMessaging.instance.subscribeToTopic("tvu_giangvien");
    await FirebaseMessaging.instance.subscribeToTopic("tvu_vienchuc");
    await FirebaseMessaging.instance.subscribeToTopic("tvu_sinhven");
  }

  static getToken() async {
    String? token = await FirebaseMessaging.instance.getToken();
    return token!;
  }

  void handleNotificationTap(dynamic payload) {
    log("handleNotificationTap tap payload: $payload");
    var data = <String, dynamic>{};
    if (payload == null) {
      return;
    } else if (payload is String) {
      data = json.decode(payload);
    } else if (payload is Map<String, dynamic>) {
      data = payload;
    }
    //Logger().i('NotificationType  CongVan payload: ${NotificationType.CongVan.toString()}');
    //Logger().i('NotificationType  CongVan payload: ${NotificationType.CongVan.name}');
    if (data.containsKey('type') && data['type'] != null) {
      var type = NotificationType.values
          .firstWhere((element) => element.name == data['type']);
      Logger().i('Type payload: ${type.toString()}');
      switch (type) {
        case NotificationType.TaoMoiNghiPhep:
        case NotificationType.DonNghiPhepCanDuyet:
        case NotificationType.DonNghiPhepDaThuHoi:
        case NotificationType.DonNghiPhepTraVe:
        case NotificationType.DonNghiPhepDaDuyet:
          Get.toNamed(AppPages.NGHIPHEP, preventDuplicates: true);
          break;
        case NotificationType.CongVan:
          Get.toNamed(AppPages.CV_DETAIL,
              preventDuplicates: true,
              arguments: {'type': type, 'cvId': data['refid']});
          break;
        default:
          break;
      }
    }
  }

  Future<void> updateFCM() async {
    ApiProvider apiProvider = Get.put(ApiProvider());
    var fcmToken = await getToken();
    if (fcmToken != null) {
      log("================updateFCM=================");
      apiProvider.addPortalFCM(fcmToken);
    }
  }

  display(RemoteMessage message) async {
    log('Got a message whilst in the foreground!');
    log('Message title: ${message.notification!.title.toString()}');
    log('Message body: ${message.notification!.body.toString()}');
    log('Message data: ${message.data.toString()}');
    try {
      // final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      await FlutterLocalNotificationsPlugin().show(
        0,
        message.notification!.title,
        message.notification!.body,
        notificationDetailsBoth,
        payload: jsonEncode(message.data),
      );
    } on Exception catch (e) {
      log(e.toString());
    }
  }
}
