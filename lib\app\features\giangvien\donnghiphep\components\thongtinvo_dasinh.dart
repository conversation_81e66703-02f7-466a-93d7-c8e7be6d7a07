// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/controllers/donnghiphep_controller.dart';
import 'package:tvumobile/app/shared_components/form_builder_segmented_control.dart';
import 'package:tvumobile/app/shared_components/input_decoration_theme.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class ThongTinVoDaSinhWidget extends StatefulWidget {
  final DonNghiPhepController dnpManager;
  const ThongTinVoDaSinhWidget({
    super.key,
    required this.dnpManager,
  });

  @override
  State<ThongTinVoDaSinhWidget> createState() => _ThongtinWidgetState();
}

class _ThongtinWidgetState extends State<ThongTinVoDaSinhWidget> {
  int id = 0;

  Map<String, dynamic> formdata = {
    'conthu': 1,
    'stpt': 'SinhThuong',
    'nvs': DateTime.now(),
    'denngay': DateTime.now(),
    'tungay': DateTime.now()
  };
  final formKey = GlobalKey<FormBuilderState>();
  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    final myDecoration =
        const InputDecoration().applyDefaults(GlobalInputDecoration(context));
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.primaryColor.withOpacity(0.95),
        title: MyText.titleMedium(
          "Nghỉ dưỡng sau thai sản".tr.toUpperCase(),
          color: theme.colorScheme.onPrimary,
        ),
        iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
        centerTitle: true,
      ),
      body: MyContainer(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(10),
                child: Column(
                  children: [
                    const MyContainer(
                      child: MyText.titleLarge("Vui lòng nhập thông tin sinh"),
                    ),
                    FormBuilder(
                      key: formKey,
                      child: Column(
                        children: [
                          // --------------------------- NGÀY SINH CON ---------------------------
                          FormBuilderDateTimePicker(
                            name: 'nvs',
                            initialEntryMode: DatePickerEntryMode.calendar,
                            initialValue: formdata['nvs'],
                            inputType: InputType.date,
                            format: DateFormat('dd.MM.yyyy'),
                            decoration: myDecoration.copyWith(
                              labelText: 'Ngày sinh con',
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.close),
                                onPressed: () {
                                  formKey.currentState!.fields['date']
                                      ?.didChange(null);
                                },
                              ),
                              contentPadding: const EdgeInsets.all(8),
                            ),
                            initialTime: const TimeOfDay(hour: 8, minute: 0),
                            onChanged: (value) {
                              formdata['nvs'] = value ?? DateTime.now();
                            },
                            locale: const Locale('vi'),
                            cancelText: "Hủy bỏ",
                            confirmText: "Xác nhận",
                            // locale: const Locale.fromSubtags(languageCode: 'fr'),
                          ),
                          const SizedBox(height: 14),

                          // --------------------- SINH THƯỜNG / PHẪU THUẬT ----------------------
                          FormBuilderSegmentedControl(
                            name: 'stpt',
                            initialValue: formdata['stpt'],
                            padding: const EdgeInsets.all(8),
                            segmentPadding: const EdgeInsets.only(top: 8),
                            widgetPadding: const EdgeInsets.all(8),
                            options: const [
                              FormBuilderFieldOption(
                                value: "SinhThuong",
                                child: Text("Sinh thường"),
                              ),
                              FormBuilderFieldOption(
                                value: "PhauThuat",
                                child: Text("Phẩu thuật"),
                              )
                            ],
                            onChanged: (value) {
                              formdata['stpt'] = value!;
                              //calculationDiem();
                            },
                            onSaved: (newValue) {
                              formdata['stpt'] = newValue!;
                            },
                            decoration: myDecoration.copyWith(
                              labelText: 'Sinh thường/phẫu thuật',
                              // contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          const SizedBox(height: 14),

                          // ------------------------------ SỐ CON -------------------------------
                          FormBuilderSegmentedControl<int>(
                            name: 'conthu',
                            initialValue: formdata['conthu'],
                            padding: const EdgeInsets.all(8),
                            segmentPadding: const EdgeInsets.only(top: 8),
                            widgetPadding: const EdgeInsets.all(8),
                            options: [1, 2, 3, 4, 5, 6, 7, 8]
                                .map((ele) => FormBuilderFieldOption(
                                      value: ele,
                                      child: Text(ele.toString()),
                                    ))
                                .toList(growable: true),
                            onChanged: (value) {
                              formdata['conthu'] = value!;
                              //calculationDiem();
                            },
                            onSaved: (newValue) {
                              formdata['conthu'] = newValue!;
                            },
                            decoration: myDecoration.copyWith(
                              labelText: 'Số con',
                              // contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          const SizedBox(height: 14),

                          // --------------------------- NGHỈ TRƯỚC ĐÓ ---------------------------
                          FormBuilderDateRangePicker(
                            name: 'ngaynghi',
                            firstDate: DateTime(DateTime.now().year - 1),
                            lastDate: DateTime(DateTime.now().year + 1),
                            format: DateFormat('dd.MM.yyyy'),
                            initialValue: DateTimeRange(
                                start: DateTime.now(), end: DateTime.now()),
                            onChanged: (value) {
                              if (value != null) {
                                formdata['tungay'] = value.start;
                                formdata['denngay'] = value.end;
                              }
                            },
                            onSaved: (newValue) {
                              if (newValue != null) {
                                formdata['tungay'] = newValue.start;
                                formdata['denngay'] = newValue.end;
                              }
                            },
                            decoration: myDecoration.copyWith(
                              labelText: 'Đã nghỉ trước đó',
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.close),
                                onPressed: () {
                                  formKey.currentState!.fields['ngaynghi']
                                      ?.didChange(null);
                                },
                              ),
                            ),
                            locale: const Locale('vi'),
                            cancelText: "Hủy bỏ",
                            confirmText: "Xác nhận",
                            style: theme.textTheme.titleMedium,
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // --------------------------- NÚT XÁC NHẬN ----------------------------
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.scaffoldBackgroundColor,
                border: Border(
                  top: BorderSide(color: theme.dividerColor, width: 0.1),
                ),
              ),
              child: MyButton.block(
                  backgroundColor: theme.primaryColor.withOpacity(0.95),
                  onPressed: () {
                    bool isValid = formKey.currentState!.saveAndValidate();
                    debugPrint(formKey.currentState?.value.toString());
                    if (isValid) {
                      widget.dnpManager.thongtinDasinh = formdata;
                      widget.dnpManager.tinhNgayNghi();
                      widget.dnpManager.tinhSoNgayThuc();
                      //widget.dnpManager.update();
                      Navigator.pop(context);
                    }
                  },
                  child: MyText.labelLarge(
                    'Xác nhận thông tin',
                    color: theme.colorScheme.onPrimary,
                    fontWeight: 900,
                  )),
            ),
          ],
        ),
      ),
    );
  }
}
