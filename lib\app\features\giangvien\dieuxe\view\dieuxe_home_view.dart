import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lazy_load_scrollview/lazy_load_scrollview.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_appbar.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_drawer.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_filter.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_listDX.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_home_controller.dart';
import 'package:tvumobile/app/features/giangvien/donnghiphep/components/empty_widget.dart';

class DieuXeHomeView extends GetView<DieuXeHomeController> {
  const DieuXeHomeView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      key: controller.scaffoldKey,
      appBar: DieuXeAppbar(context, controller),
      endDrawer: DieuXeDrawer(context, controller),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Get.toNamed(AppPages.DIEUXE_CREATE);
        },
        backgroundColor: theme.primaryColor,
        tooltip: 'Tạo phiếu điều xe',
        child: Icon(LucideIcons.plus),
      ),
      body: Obx(() {
        return Column(children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(8, 8, 8, 0),
            child: Column(
              children: [
                // View PHIẾU ĐIỀU XE mới
                if (controller.currentView.value == "moi") ...[
                  if (controller.phanQuyen.value == "canhan") ...[
                    DieuXeStatusFilter(theme, controller,
                        statusList: controller.statusListNew),
                  ] else if (controller.phanQuyen.value == "truongdonvi" ||
                      controller.phanQuyen.value == "phonghcth") ...[
                    DieuXeStatusFilter(theme, controller,
                        statusList: controller.statusListDVNew),
                  ],
                  SizedBox(height: 5),
                ]
                // View PHIẾU ĐIỀU XE đã duyệt
                else if (controller.currentView.value == "duyet") ...[
                  DieuXeStatusFilter(theme, controller,
                      statusList: controller.statusListApproved),
                  SizedBox(height: 5),
                ]
                // View PHIẾU ĐIỀU XE đơn vị đã duyệt
                else if (controller.currentView.value == "donviduyet") ...[
                  DieuXeStatusFilter(theme, controller,
                      statusList: controller.statusListDVApproved),
                  SizedBox(height: 5),
                ]
                // View PHIẾU ĐIỀU XE phòng HCTH đã duyệt
                else if (controller.currentView.value == "hcthduyet") ...[
                  DieuXeStatusFilter(theme, controller,
                      statusList: controller.statusListHCTHApproved),
                  SizedBox(height: 5),
                ],

                // Padding(
                //   padding: const EdgeInsets.only(top: 5),
                //   child: buildTaskFilter(context),
                // ),
              ],
            ),
          ),
          Expanded(
            child: controller.isLoading.value
                ? const Center(
                    child:
                        CircularProgressIndicator(), // Hiển thị spinner khi đang tải
                  )
                : controller.dsDX.isEmpty
                    ? EmptyWidget(
                        message: 'Bạn chưa tạo phiếu điều xe',
                        imageAsset: 'tasky/no_inbox.png',
                      )
                    : LazyLoadScrollView(
                        onEndOfPage: controller.loadMoreDieuXe,
                        isLoading: controller.lastPage,
                        child: RefreshIndicator(
                          onRefresh: () =>
                              Future.sync(() => controller.loadDieuXe()),
                          child: ListView.builder(
                            padding: const EdgeInsets.only(top: 5, bottom: 5),
                            itemCount: controller.dsDX.length,
                            itemBuilder: (context, index) {
                              final dx = controller.dsDX[index];
                              return DieuXeList(context, dx, controller);
                            },
                          ),
                        ),
                      ),
          ),
        ]);
      }),
    );
  }
}
