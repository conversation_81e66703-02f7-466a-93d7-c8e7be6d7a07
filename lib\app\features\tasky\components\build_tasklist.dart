import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/config/themes/colors.dart';
import 'package:tvumobile/app/data/models/tasky/Task.dart';
import 'package:tvumobile/app/data/models/tms/danhmuc.dart';
import 'package:tvumobile/app/features/tasky/components/tasky_dialog.dart';
import 'package:tvumobile/app/features/tasky/controllers/taskdetail_controller.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/extensions/date_time_extension.dart';
import 'package:tvumobile/app/shared_components/my_horizontal_list.dart';

// ------------- HIỂN THỊ DANH SÁCH TASK ----------------
buildTaskList(BuildContext context, Task task) {
  DateFormat format = DateFormat("dd/MM/yyyy");
  return MyContainer.none(
    padding: const EdgeInsets.fromLTRB(12, 0, 12, 2),
    margin: const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
    border: Border.all(color: Colors.grey[300]!),
    borderRadius: BorderRadius.circular(8),
    onTap: () {
      Get.toNamed(AppPages.TASK_DETAIL, arguments: task);
    },
    child: Container(
      padding: EdgeInsets.all(5),
      decoration: BoxDecoration(
          color: task.trangThai != null
              ? getStatusColors(task.trangThai!).withOpacity(0.2) // Màu nền mờ
              : Colors.grey.withOpacity(0.2), // Màu mặc định nếu null
          borderRadius: BorderRadius.circular(4), // Bo góc nhẹ cho nền
          border: Border.all(
            color: task.trangThai != null
                ? getStatusColors(task.trangThai!)
                    .withOpacity(0.2) // Màu nền mờ
                : Colors.grey.withOpacity(0.2),
          )),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            children: [
              Icon(
                LucideIcons.notepadText,
                size: 28,
                color: getStatusColors(task.trangThai!),
                semanticLabel: "Status",
              ),
              SizedBox(height: 4),
              Container(
                constraints: BoxConstraints(maxWidth: 70),
                padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  // color: getStatusColors(task.trangThai!).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: MyText.labelMedium(
                  task.trangThai!.tenDanhMuc!,
                  color:
                      getStatusColors(task.trangThai!).computeLuminance() > 0.2
                          ? Colors.black
                          : task.trangThai!.maDanhMuc == "Assigned"
                              ? Colors.black
                              : Colors.white,
                  maxLines: 3,
                  textAlign: TextAlign.center,
                  softWrap: true,
                ),
              ),
            ],
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: MyText.titleMedium(
                        task.taskName.toString(),
                        overflow: TextOverflow.ellipsis,
                        fontWeight: 600,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: _getDueDateColor(
                            task.endDateDuKien!, task.trangThai!.maDanhMuc),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: MyText.bodyMedium(
                        format.format(task.endDateDuKien!),
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4),
                MyText.bodyMedium(
                  task.moTa.toString(),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  color: Colors.grey[600],
                ),
                SizedBox(height: 8),
                // if (task.nguoiGiaoViec!.id == task.nguoiThucHien!.id)
                //   _buildAssigneeandPerformRow(task)
                // else
                _buildAssigneeRow(task),
                if (task.nguoiGiamSat != null) _buildSupervisorRow(task),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

// ---------- HIỂN THỊ NG GIAO VÀ NG TẠO VIỆC -----------
Widget _buildAssigneeandPerformRow(Task task) {
  return Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      Flexible(
        flex: 1,
        child: _buildAvatarWithLabel(
          avatar: task.nguoiGiaoViec!.hinhAnh.toString(),
          label: "Tự giao việc và thực hiện:",
          name: getHoTen(task.nguoiGiaoViec),
        ),
      ),
    ],
  );
}

// ---------- HIỂN THỊ NG GIAO VÀ NG TẠO VIỆC -----------
Widget _buildAssigneeRow(Task task) {
  return Column(
    mainAxisSize: MainAxisSize.min,
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        children: [
          Flexible(
            flex: 1,
            child: _buildAvatarWithLabel(
              avatar: task.nguoiGiaoViec!.hinhAnh.toString(),
              label: "Giao việc:",
              name: getHoTen(task.nguoiGiaoViec),
            ),
          ),
        ],
      ),
      // Danh sách người thực hiện
      // if (task.danhSachNguoiThucHien != null &&
      //     task.danhSachNguoiThucHien!.isNotEmpty)
      //   Column(
      //     crossAxisAlignment: CrossAxisAlignment.start,
      //     children: [
      //       ...task.danhSachNguoiThucHien!.map((nguoi) => Padding(
      //             padding: const EdgeInsets.only(
      //                 top: 4.0), // Khoảng cách giữa các người
      //             child: _buildAvatarWithLabel(
      //               avatar: nguoi.hinhAnh ?? "",
      //               label: "Thực hiện", // Không cần label lặp lại
      //               name: getHoTen(nguoi),
      //             ),
      //           )),
      //     ],
      //   )
      // else
      //   _buildAvatarWithLabel(
      //     avatar: "",
      //     label: "Thực hiện:",
      //     name: "Chưa có người thực hiện",
      //   ),
      if (task.danhSachNguoiThucHien != null &&
          task.danhSachNguoiThucHien!.isNotEmpty) ...[
        MyHorizontalList(
          padding: EdgeInsets.zero,
          wrapAlignment: WrapAlignment.start,
          itemCount: task.danhSachNguoiThucHien!.length,
          itemBuilder: (BuildContext context, int index) {
            final nguoi = task.danhSachNguoiThucHien![index];
            return Padding(
              padding: const EdgeInsets.only(
                right: 12,
                top: 8,
              ), // Khoảng cách giữa các người
              child: _buildAvatarWithLabel(
                avatar: nguoi.hinhAnh ?? "",
                label: "Thực hiện", // Không cần label lặp lại
                name: getHoTen(nguoi),
              ),
            );
          },
        )
      ] else
        _buildAvatarWithLabel(
          avatar: "",
          label: "Thực hiện:",
          name: "Chưa có người thực hiện",
        ),
    ],
  );
}

// ----------------- HIỂN THỊ NG GIÁM SÁT ---------------
Widget _buildSupervisorRow(Task task) {
  return Padding(
    padding: const EdgeInsets.only(top: 4.0),
    child: _buildAvatarWithLabel(
      avatar: task.nguoiGiamSat!.hinhAnh.toString(),
      label: "Giám sát:",
      name: getHoTen(task.nguoiGiamSat),
    ),
  );
}

Widget _buildAvatarWithLabel({
  required String avatar,
  required String label,
  required String? name,
}) {
  return Row(
    mainAxisSize: MainAxisSize.min,
    children: [
      CircleAvatar(
        backgroundImage: NetworkImage(avatar),
        radius: 12,
        backgroundColor: Colors.grey[300],
      ),
      SizedBox(width: 4),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          MyText.labelMedium(
            label,
            color: Colors.grey,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 2),
          ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: 150, // Giới hạn chiều rộng tối đa
              minWidth: 50, // Đảm bảo chiều rộng tối thiểu cho tên ngắn
            ),
            child: MyText.bodyMedium(
              name ?? 'Không xác định',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    ],
  );
}

// -------------- HIỂN THỊ CHI TIẾT TASK ----------------
buildTaskDetailList(
    BuildContext context, TaskDetailController controller, Task task) {
  DateFormat format = DateFormat("dd/MM/yyyy");
  ThemeData theme = Theme.of(context);

  // Kiểm tra vai trò người dùng
  final bool isNguoiGiaoViec =
      task.nguoiGiaoViec!.id.toString() == controller.userInfo!.vienchucid;
  final bool isNguoiThucHien = task.danhSachNguoiThucHien!
      .any((nguoi) => nguoi.id.toString() == controller.userInfo!.vienchucid);

  // Tìm người thực hiện có id khớp với vienchucid
  final Nguoi? nguoiThucHien = isNguoiThucHien
      ? task.danhSachNguoiThucHien!.firstWhere(
          (nguoi) => nguoi.id.toString() == controller.userInfo!.vienchucid)
      : null;

  // Kiểm tra vai trò người dùng
  // final bool isNguoiGiaoViec = task.nguoiGiaoViec!.id.toString() == "18279";
  // final bool isNguoiThucHien = task.danhSachNguoiThucHien!
  //     .any((nguoi) => nguoi.id.toString() == "18279");

  // // Tìm người thực hiện có id khớp với vienchucid
  // final Nguoi? nguoiThucHien = isNguoiThucHien
  //     ? task.danhSachNguoiThucHien!
  //         .firstWhere((nguoi) => nguoi.id.toString() == "18279")
  //     : null;

  return MyContainer.none(
    padding: const EdgeInsets.fromLTRB(16, 6, 8, 6),
    border: Border(bottom: BorderSide(color: Colors.grey[400]!, width: 1)),
    bordered: true,
    // onTap: () {
    //   Get.toNamed(AppPages.TASK_DETAIL, arguments: task);
    // },
    child: SizedBox(
      width: MediaQuery.of(context).size.width - 16,
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        // ---------------------- TÊN CÔNG VIỆC ----------------------
        MyText.bodyLarge(
          task.taskName!,
          fontWeight: 700,
        ),
        Divider(
          color: Colors.grey[300],
          height: 16,
        ),
        // ----------------------- NGƯỜI GIAO ------------------------
        Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundImage:
                  NetworkImage(task.nguoiGiaoViec!.hinhAnh.toString()),
            ),
            SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MyText.titleMedium(
                    getHoTen(task.nguoiGiaoViec),
                    softWrap: true,
                  ),
                  Text.rich(
                      softWrap: true,
                      style: theme.textTheme.bodyMedium,
                      TextSpan(
                        children: [
                          TextSpan(text: "đã giao vào: "),
                          WidgetSpan(
                            child: Icon(
                              LucideIcons.calendar,
                              size: 16,
                            ),
                          ),
                          TextSpan(text: " "),
                          TextSpan(
                            text: format.format(task.startDay!),
                          ),
                          TextSpan(
                            text: " (${task.startDay!.timeAgo})",
                          ),
                        ],
                      )),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 14),
        // ------------------------ NGƯỜI TẠO ------------------------
        Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundImage: NetworkImage(task.nguoiTao!.hinhAnh.toString()),
            ),
            SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MyText.titleMedium(
                    getHoTen(task.nguoiTao),
                    softWrap: true,
                  ),
                  Text.rich(
                    style: theme.textTheme.bodyMedium,
                    TextSpan(
                      children: [
                        TextSpan(text: "đã tạo vào: "),
                        WidgetSpan(
                          child: Icon(
                            LucideIcons.calendar,
                            size: 16,
                          ),
                        ),
                        TextSpan(text: " "),
                        TextSpan(
                          text: format.format(task.ngayTao!),
                        ),
                        TextSpan(
                          text: " (${task.ngayTao!.timeAgo})",
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 12),
        // ---------------------- NGÀY KẾT THÚC ----------------------
        MyContainer.bordered(
          color: Color(0xFF1157FA),
          padding: const EdgeInsets.only(left: 8, right: 8, top: 4, bottom: 4),
          borderColor: getStatusColors(task.trangThai!),
          child: Row(
            children: [
              Icon(
                LucideIcons.calendarCheck,
                size: 16,
                color: white,
              ),
              MyText.bodyMedium(
                " Thời hạn: ",
                color: white,
              ),
              MyText.titleMedium(
                format.format(task.endDateDuKien!),
                color: white,
              ),
              MyText.bodyMedium(
                " (${task.endDateDuKien!.timeAgo})",
                fontSize: 15,
                color: white,
              ),
            ],
          ),
        ),
        SizedBox(height: 12),
        // -------------------- CHI TIẾT CÔNG VIỆC -------------------
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  LucideIcons.atSign,
                  size: 16,
                ),
                SizedBox(width: 4),
                MyText.bodyMedium("Chi tiết công việc: ",
                    decoration: TextDecoration.underline),
              ],
            ),
            MyText.bodyLarge(
              task.moTa!,
              fontWeight: 500,
            ),
          ],
        ),
        Divider(
          color: Colors.grey[300],
          height: 8,
        ),
        SizedBox(height: 6),
        // ---------------------- LOẠI CÔNG VIỆC ---------------------
        Row(
          children: [
            Icon(
              LucideIcons.boxes,
              size: 16,
            ),
            SizedBox(width: 4),
            MyText.bodyMedium("Loại: "),
            Expanded(
              child: MyText.bodyLarge(
                "${task.taskType!.tenDanhMuc}",
                softWrap: true,
                overflow: TextOverflow.visible,
                decoration: TextDecoration.underline,
              ),
            ),
          ],
        ),
        SizedBox(height: 6),

        // ---------------------- TRẠNG THÁI -------------------------
        Row(
          children: [
            Icon(
              LucideIcons.notepadTextDashed,
              size: 16,
            ),
            SizedBox(width: 4),
            MyText.bodyMedium("Trạng thái: "),
            MyContainer.bordered(
              onTap: () {
                showUpdateTaskStatusDialog(controller);
              },
              padding: const EdgeInsets.only(left: 8, right: 8),
              color: getStatusColors(task.trangThai!),
              child: Row(
                children: [
                  MyText.bodyLarge(
                    task.trangThai!.tenDanhMuc.toString(),
                    color: Colors.white,
                  ),
                  SizedBox(width: 4),
                  Icon(
                    LucideIcons.arrowBigUpDash,
                    size: 16,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 6),
        // ---------------------- NGƯỜI GIÁM SÁT ---------------------
        if (task.nguoiGiamSat != null) ...[
          SizedBox(height: 6),
          Row(
            children: [
              Icon(
                LucideIcons.eye,
                size: 16,
              ),
              SizedBox(width: 4),
              MyText.bodySmall("Người giám sát: "),
              CircleAvatar(
                radius: 8,
                backgroundImage:
                    NetworkImage(task.nguoiGiamSat!.hinhAnh.toString()),
              ),
              SizedBox(width: 2),
              MyText.bodyMedium(getHoTen(task.nguoiGiamSat)),
            ],
          ),
        ],
        // ----------------------- TỰ ĐÁNH GIÁ -----------------------
        SizedBox(height: 6),
        Divider(
          color: Colors.grey[300],
          height: 8,
        ),
        Row(
          children: [
            Icon(
              LucideIcons.star,
              size: 16,
            ),
            SizedBox(width: 4),
            MyText.bodyMedium("Người thực hiện tự đánh giá: "),
          ],
        ),
        SizedBox(height: 6),

        // --- Nếu là người giao việc, hiển thị danh sách tất cả người thực hiện ---
        if (isNguoiGiaoViec) ...[
          Column(
            children: task.danhSachNguoiThucHien!.map((nguoi) {
              final bool isCurrentUser =
                  nguoi.id.toString() == controller.userInfo!.vienchucid;
              // final bool isCurrentUser = nguoi.id.toString() == "18279";
              return Column(
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundImage: NetworkImage(nguoi.hinhAnh.toString()),
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                MyText.titleMedium(
                                  "${nguoi.ho} ${nguoi.tenDem} ${nguoi.ten}",
                                  softWrap: true,
                                ),
                                Text.rich(
                                  softWrap: true,
                                  style: theme.textTheme.bodyMedium,
                                  TextSpan(
                                    children: [
                                      WidgetSpan(
                                        child: Icon(
                                          LucideIcons.target,
                                          size: 16,
                                        ),
                                      ),
                                      TextSpan(
                                        text: nguoi.tuDanhGiaMucDoHoanThanh !=
                                                null
                                            ? " ${nguoi.tuDanhGiaMucDoHoanThanh}/100% tiến độ công việc"
                                            : " Chưa đánh giá",
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            // Chỉ hiển thị nút tự đánh giá cho chính người dùng
                            if (isCurrentUser) ...[
                              IconButton(
                                icon: Icon(
                                  LucideIcons.settings2,
                                  size: 20,
                                  color: Color(0xFF1157FA),
                                ),
                                onPressed: () {
                                  showSelfEvaluationDialog(
                                    controller,
                                    nguoi: nguoi,
                                  );
                                },
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                ],
              );
            }).toList(),
          ),
        ]
        // Nếu là người thực hiện, chỉ hiển thị thông tin của chính mình
        else if (isNguoiThucHien && nguoiThucHien != null) ...[
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundImage: NetworkImage(nguoiThucHien.hinhAnh.toString()),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        MyText.titleMedium(
                          getHoTen(nguoiThucHien),
                          softWrap: true,
                        ),
                        Text.rich(
                          softWrap: true,
                          style: theme.textTheme.bodyMedium,
                          TextSpan(
                            children: [
                              WidgetSpan(
                                child: Icon(
                                  LucideIcons.target,
                                  size: 16,
                                ),
                              ),
                              TextSpan(
                                text: nguoiThucHien.tuDanhGiaMucDoHoanThanh !=
                                        null
                                    ? " ${nguoiThucHien.tuDanhGiaMucDoHoanThanh}/100% tiến độ công việc"
                                    : " Chưa đánh giá",
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    IconButton(
                      icon: Icon(
                        LucideIcons.settings2,
                        size: 20,
                        color: Color(0xFF1157FA),
                      ),
                      onPressed: () {
                        showSelfEvaluationDialog(
                          controller,
                          nguoi: nguoiThucHien,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],

        // ---------------- NGƯỜI GIAO VIỆC ĐÁNH GIÁ -----------------
        // Thêm mục "Đánh giá công việc" (chỉ hiển thị nếu userId == nguoiGiaoViecId)
        SizedBox(height: 4),
        Divider(
          color: Colors.grey[300],
          height: 4,
        ),
        SizedBox(height: 6),
        Row(
          children: [
            Icon(
              LucideIcons.notebookPen,
              size: 16,
            ),
            SizedBox(width: 4),
            MyText.bodyMedium("Người giao việc đánh giá: "),
          ],
        ),
        SizedBox(height: 6),

        // Nếu là người giao việc, hiển thị danh sách tất cả người thực hiện
        if (isNguoiGiaoViec) ...[
          Column(
            children: task.danhSachNguoiThucHien!.map((nguoi) {
              return Column(
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundImage: NetworkImage(nguoi.hinhAnh.toString()),
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                MyText.titleMedium(
                                  "${nguoi.ho} ${nguoi.tenDem} ${nguoi.ten}",
                                  softWrap: true,
                                ),
                                Text.rich(
                                  softWrap: true,
                                  style: theme.textTheme.bodyMedium,
                                  TextSpan(
                                    children: [
                                      WidgetSpan(
                                        child: Icon(
                                          LucideIcons.target,
                                          size: 16,
                                        ),
                                      ),
                                      TextSpan(
                                        text: nguoi.nguoiDuyetDanhGiaMucDoHoanThanh !=
                                                null
                                            ? " ${nguoi.nguoiDuyetDanhGiaMucDoHoanThanh}/100% tiến độ công việc"
                                            : " Chưa đánh giá",
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            IconButton(
                              icon: Icon(
                                LucideIcons.settings2,
                                size: 20,
                                color: Color(0xFF1157FA),
                              ),
                              onPressed: () {
                                showEvaluationDialog(
                                  controller,
                                  nguoi: nguoi,
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  if (nguoi.nguoiDuyetGhiChu != null &&
                      nguoi.nguoiDuyetGhiChu != "") ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Icon(
                          LucideIcons.cornerDownRight,
                          size: 16,
                        ),
                        SizedBox(width: 4),
                        Expanded(
                          child: MyText.bodyMedium(
                            softWrap: true,
                            overflow: TextOverflow.visible,
                            "Ghi chú: ${nguoi.nguoiDuyetGhiChu}",
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 8,
                    )
                  ],
                ],
              );
            }).toList(),
          ),
        ]
        // Nếu là người thực hiện, chỉ hiển thị thông tin của chính mình
        else if (isNguoiThucHien && nguoiThucHien != null) ...[
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundImage: NetworkImage(nguoiThucHien.hinhAnh.toString()),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        MyText.titleMedium(
                          getHoTen(nguoiThucHien),
                          softWrap: true,
                        ),
                        Text.rich(
                          softWrap: true,
                          style: theme.textTheme.bodyMedium,
                          TextSpan(
                            children: [
                              WidgetSpan(
                                child: Icon(
                                  LucideIcons.target,
                                  size: 16,
                                ),
                              ),
                              TextSpan(
                                text: nguoiThucHien
                                            .nguoiDuyetDanhGiaMucDoHoanThanh !=
                                        null
                                    ? " ${nguoiThucHien.nguoiDuyetDanhGiaMucDoHoanThanh}/100% tiến độ công việc"
                                    : " Chưa đánh giá",
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          if (nguoiThucHien.nguoiDuyetGhiChu != null &&
              nguoiThucHien.nguoiDuyetGhiChu != "") ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Icon(
                  LucideIcons.cornerDownRight,
                  size: 16,
                ),
                SizedBox(width: 4),
                Expanded(
                  child: MyText.bodyMedium(
                    softWrap: true,
                    "Ghi chú: ${nguoiThucHien.nguoiDuyetGhiChu!}",
                  ),
                ),
              ],
            ),
          ],
        ],
      ]),
    ),
  );
}

Color _getDueDateColor(DateTime dueDate, String? danhmuc) {
  final now = DateTime.now();
  final difference = dueDate.difference(now).inDays;

  // if (difference < 0) {
  //   return Colors.red;
  // } else if (difference <= 2) {
  //   return Colors.orange;
  // } else if (difference <= 7) {
  //   return Colors.blue;
  // } else {
  //   return Colors.green;
  // }

  switch (danhmuc) {
    case "Assigned":
      if (difference <= 2) {
        return getColorFromHex("#FF2A04"); // Đỏ
      } else if (difference <= 7) {
        return getColorFromHex("#FFAF3D"); // Cam
      } else {
        return getColorFromHex("#7B8089"); // Xám
      }
    case "In Process":
      if (difference <= 2) {
        return getColorFromHex("#FF2A04"); // Đỏ
      } else if (difference <= 7) {
        return getColorFromHex("#FFAF3D"); // Cam
      } else {
        return getColorFromHex("#64D9FF"); // Xanh nhạt
      }
    case "Completed":
      return getColorFromHex("#00E200"); // Xanh lá
    case "Declined":
      return getColorFromHex("#FFAF3D"); // Cam
    case "Pending":
      if (difference <= 2) {
        return getColorFromHex("#FF2A04"); // Đỏ
      } else if (difference <= 7) {
        return getColorFromHex("#FFAF3D"); // Cam
      } else {
        return getColorFromHex("#FAD800").withOpacity(1.0); // Vàng
      }
    case "Canceled":
    case "Removed":
    case "Closed":
      return getColorFromHex("#FF2A04"); // Đỏ
    default:
      return getColorFromHex("#7B8089"); // Xám
  }
}

getStatusColors(DanhMuc status) {
  switch (status.maDanhMuc) {
    // case "Assigned":
    //   return getColorFromHex("#7B8089"); // Xám
    case "In Process":
      return getColorFromHex("#64D9FF"); // Xanh nhạt
    case "Completed":
      return getColorFromHex("#00E200"); // Xanh lá
    case "Declined":
      return getColorFromHex("#FFAF3D"); // Cam
    case "Pending":
      return getColorFromHex("#FAD800").withOpacity(1.0); // Vàng
    case "Canceled":
    case "Removed":
    case "Closed":
      return getColorFromHex("#FF2A04"); // Đỏ
    default:
      return getColorFromHex("#7B8089"); // Xám
  }
}

getTextStatusColors(DanhMuc status) {
  switch (status.maDanhMuc) {
    case "Assigned":
      return getColorFromHex("#7B8089");
    case "In Process":
      return getColorFromHex("#64D9FF");
    case "Completed":
      return getColorFromHex("#00E200");
    case "Declined":
      return getColorFromHex("#FFAF3D");
    case "Canceled":
    case "Removed":
    case "Closed":
      return getColorFromHex("#FEFEFE");
    default:
      return getColorFromHex("#000000");
  }
}

String getHoTen(Nguoi? nguoi) {
  if (nguoi == null) return "";
  var ht = "";
  ht = nguoi.ho!.isEmpty ? "" : "${nguoi.ho} ";
  ht += nguoi.tenDem!.isEmpty ? "" : "${nguoi.tenDem} ";
  ht += nguoi.ten!.isEmpty ? "" : nguoi.ten.toString();
  return ht;
}
