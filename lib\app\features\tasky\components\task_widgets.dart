
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class AppIcon extends StatelessWidget {
  final Color? color;
  final double? height;
  final double? width;
  final String? icon;
  final String? darkThemeIcon;
  final bool isPng;
  final bool hasDarkVersion;

  const AppIcon({
    super.key,
    required this.icon,
    this.color,
    this.height,
    this.width,
    this.isPng = false,
    this.hasDarkVersion = false,
    this.darkThemeIcon,
  });

  String? get iconToDisplay {
    if (!hasDarkVersion) return icon;

    if (Theme.of(Get.context!).brightness == Brightness.dark) {
      return darkThemeIcon ?? icon!.replaceFirst('.', '_dark.');
    }
    return icon;
  }

  @override
  Widget build(BuildContext context) {
    // Unfortunately, flutter svg does not support all svg files
    // Therefore, there will be support for png icons as well
    return isPng
        ? Image(
      image: AssetImage(iconToDisplay!),
      color: color,
      height: height,
      width: width,
    )
        : SvgPicture.asset(
      iconToDisplay!,
      // ignore: deprecated_member_use
      color: color,
      height: height,
      width: width,
    );
  }
}

class SvgIcons {
  SvgIcons._();
  static const String tab_bar_tasks = 'assets/svgs/tasks.svg';
  static const String lock = 'assets/svgs/lock.svg';
  static const String description = 'assets/svgs/description.svg';
  static const String edit = 'assets/icons/edit.svg';
  static const String filter = 'assets/icons/filter.svg';
  static const String home = 'assets/icons/home.svg';
  static const String notification = 'assets/icons/notification.svg';
  static const String search = 'assets/icons/search.svg';
  static const String settings = 'assets/icons/settings.svg';
  static const String user = 'assets/icons/user.svg';
}

class InfoTile extends StatelessWidget {
  final Widget? icon;
  final String? caption;
  final String? subtitle;
  final TextStyle? captionStyle;
  final TextStyle? subtitleStyle;
  final Widget? subtitleWidget;
  final Widget? suffix;
  final Function()? onTap;
  final bool privateIconVisible;

  const InfoTile({
    super.key,
    this.icon,
    this.caption,
    this.captionStyle,
    this.onTap,
    this.subtitle,
    this.subtitleStyle,
    this.subtitleWidget,
    this.suffix,
    this.privateIconVisible = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          SizedBox(width: 72, child: icon),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (caption != null)
                  Text.rich(
                    TextSpan(
                      style: captionStyle ?? Theme.of(context).textTheme.labelLarge,
                      children: [
                        ...privateIconVisible
                            ? [
                          WidgetSpan(
                            child: AppIcon(icon: SvgIcons.lock),
                            alignment: PlaceholderAlignment.middle,
                          ),
                          const WidgetSpan(
                            child: SizedBox(width: 4),
                          ),
                        ]
                            : [],
                        TextSpan(text: caption),
                      ],
                    ),
                  ),
                if (subtitleWidget != null) subtitleWidget!,
                if (subtitleWidget == null && subtitle != null)
                  Text(subtitle!,
                      style: subtitleStyle ?? Theme.of(context).textTheme.titleSmall)
              ],
            ),
          ),
          if (suffix != null) suffix!,
          if (suffix == null) const SizedBox(width: 16),
        ],
      ),
    );
  }
}