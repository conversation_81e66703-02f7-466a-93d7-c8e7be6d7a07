import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:tvumobile/app/features/kyso/components/resizeble.dart';
import 'package:tvumobile/app/features/kyso/models/pdf_viewer_state.dart';
import 'package:tvumobile/app/features/kyso/models/signature_model.dart';

/// This function is used to change the layout on each page
///
/// [contentViewSize] get display size content
/// [pageSizes] get a list of page size data
List<Rect> changePdfPageLayout({
  required Size contentViewSize,
  required List<Size> pageSizes,
}) {
  List<Rect> rectLayout = [];
  double pageTopDistance = 0.0;

  // Display more than one page layout
  if (pageSizes.length != 1) {
    pageSizes.map((e) {
      rectLayout.add(
        Rect.fromLTWH(
          0,
          pageTopDistance,
          contentViewSize.width,
          e.height / 1.7,
        ),
      );
      pageTopDistance += e.height / 1.65;
    }).toList();
  }

  // Display only one page layout
  else {
    rectLayout.add(
      Rect.fromCenter(
        center: Offset(
          contentViewSize.width / 2,
          contentViewSize.height / 2,
        ),
        width: contentViewSize.width,
        height: pageSizes[0].height / 1.7,
      ),
    );
  }
  return rectLayout;
}

/// This function is used to get the paper size of the document
///
/// [pdfFile] to enter a PDF file that will be added a signature.
/// [currentPage] to get the page size of a PDF document.
Future<Size?> getSizeDocument({
  required Uint8List pdfFile,
  required int currentPage,
}) async {
  try {
    // Get document
    final PdfDocument document = PdfDocument(
      inputBytes: pdfFile,
    );

    // Get the existing PDF and page size.
    final pageSize = document.pages[currentPage - 1].size;
    debugPrint('PAGE SIZE : $pageSize');

    return pageSize;
  } catch (e) {
    throw e.toString();
  }
}

Widget buildPageOverlay({
  required BuildContext context,
  required int pageNumber,
  required Rect pageRect,
  required PdfViewerState pdfViewerState,
  required List<SignatureModel> signatures,
  required List<String> availableSignatures,
  required VoidCallback? Function(String id) onPressDelete,
  String? fullName,
  String? department,
  String? personposition,
}) {
  return Stack(
    children: [
      // Display existing signatures for this page
      ...signatures
          //.where((signature) => signature.pageNumber == pageNumber)
          .map((e) => Resizeble(
                key: ValueKey(e.id),
                unSelect: e.onSelected!,
                width: e.fixedWidth,
                height: e.fixedHeight,
                top: e.fixedTop,
                left: e.fixedLeft,
                onPressDelete: () => onPressDelete(e.id),
                onSelected: () {},
                documentWidthSize: e.documentSize?.width,
                documentHeightSize: e.documentSize?.height,
                documentWidthView: e.documentViewSize?.width,
                documentHeightView: e.documentViewSize?.height,
                child: Image.memory(
                  e.signature!,
                  fit: BoxFit.fill,
                  color:
                      (e.onSelected == true) ? Colors.blue[800] : Colors.black,
                ),
              )),
    ],
  );
}
