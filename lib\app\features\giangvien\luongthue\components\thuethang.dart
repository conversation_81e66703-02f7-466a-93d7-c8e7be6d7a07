

import 'package:flutter/material.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/luongthuethang_controller.dart';
import 'package:tvumobile/app/shared_components/my_container.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

Widget buildThueThangInfo(LuongThueController controller, ThemeData theme) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Center(
          child: Column(
            children: [
              const Divider(),
              MyText.titleMedium(
                "Thông tin thuế tháng ${controller.currentMonth}/${controller
                    .currentYear}".toUpperCase(),
                fontWeight: 900,
              ),
              const Divider(),
            ],
          ),
        ),
      ),
      if (controller.thueThangInfo.isEmpty)
        controller.currentDataAvaliable.value ?
        SizedBox(
          height: 200,
          child: SkeletonListView(),
        ) : Center(child: MyText.titleLarge("KHÔNG CÓ DỮ LIỆU")),
      if (controller.thueThangInfo.isNotEmpty)
        _buildLuongThangDetails(controller, theme),
    ],
  );
}

Widget _buildLuongThangDetails(LuongThueController controller, ThemeData theme) {

  final thueThang = controller.thueThangInfo.first;
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Center(
        child: Column(
          children: [
            const MyText.titleMedium("THUẾ PHẢI NỘP"),
            MyText.titleLarge(
              controller.viFormat.format(thueThang.thuephainop),
              fontWeight: 900,
              fontSize: 24,
            ),
            const SizedBox(height: 16,),
            const MyText.titleMedium("THU NHẬP CHỊU THUẾ"),
            MyText.titleLarge(
              controller.viFormat.format(thueThang.thunhapchiuthue),
              fontWeight: 900,
              fontSize: 24,
            ),
          ],
        ),
      ),
      MySpacing.height(16),
      _buildSectionHeader(theme, "THU NHẬP"),
      _buildLuongItem(controller, theme, "Tổng thu nhập:", thueThang.tongthunhap!.toDouble()),
      _buildLuongItem(controller, theme, "Lương tháng:", thueThang.luong!.toDouble()),
      _buildLuongItem(controller,
          theme, "Thu nhập ngoài lương:", thueThang.tnngoailuong!.toDouble()),
      _buildSectionHeader(theme, "GIẢM TRỪ"),
      _buildLuongItem(controller,
          theme, "Giảm trừ bản thân:", thueThang.giamtrubanthan!.toDouble()),
      _buildLuongItem(controller, theme, "Giảm trừ gia cảnh:", thueThang.giamtrugiacanh!.toDouble()),
      _buildLuongItem(controller, theme, "Bảo hiểm:",
          thueThang.baohiem!.toDouble()),
    ],
  );
}

Widget _buildSectionHeader(ThemeData theme, String title) {
  return Align(
    alignment: Alignment.centerLeft,
    child: MyContainer(
      paddingAll: 6,
      width: double.infinity,
      color: theme.secondaryHeaderColor,
      child: MyText.titleMedium(title),
    ),
  );
}

Widget _buildLuongItem(LuongThueController controller, ThemeData theme, String label, double value) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4.0),
    child: Row(
      children: [
        SizedBox(
          width: 200,
          child: MyText.titleMedium(label),
        ),
        MyText.titleMedium(controller.viFormat.format(value)),
      ],
    ),
  );
}