import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/giaydieuxe.dart';
import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepinfo.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_create_dsvccongtac.dart';
import 'package:tvumobile/app/utils/helpers/app_helpers.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxeinfo.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_fileupload.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_create_controller.dart';
import 'package:tvumobile/app/features/tasky/components/assignee_tile_widget.dart';
import 'package:tvumobile/app/shared_components/appbar.dart';
import 'package:tvumobile/app/shared_components/input_decoration_theme.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class DieuXeCreateView extends GetView<DieuXeCreateController> {
  const DieuXeCreateView({super.key});

  @override
  Widget build(BuildContext context) {
    final myDecoration = const InputDecoration().applyDefaults(
      GlobalInputDecoration(context),
    );
    final theme = Theme.of(context);
    final width = MediaQuery.of(context).size.width;
    return Obx(() {
      return Scaffold(
        appBar: appBarTitle(
          context,
          controller.isMergeMode
              ? 'ghép phiếu điều xe'
              : controller.isEditMode
              ? 'sửa giấy điều xe'
              : 'tạo mới giấy điều xe',
        ),
        body: controller.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(
                        left: 16.0,
                        right: 16.0,
                        bottom: 16.0,
                        top: 4,
                      ),
                      child: FormBuilder(
                        key: controller.formKey,
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            Align(
                              alignment: Alignment.center,
                              child: Padding(
                                padding: EdgeInsets.all(8.0),
                                child: MyText.bodyMedium(
                                  controller.isEditMode
                                      ? "Chỉnh sửa thông tin giấy đề nghị điều xe"
                                      : "Cung cấp thông tin tạo giấy đề nghị điều xe",
                                  decoration: TextDecoration.underline,
                                  color: theme.colorScheme.onSurface,
                                  fontSize: 15,
                                  fontWeight: 700,
                                ),
                              ),
                            ),

                            // ĐƠN VỊ
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Padding(
                                padding: const EdgeInsets.only(
                                  bottom: 0.0,
                                  top: 0.0,
                                ),
                                child: MyText.titleMedium(
                                  "Đơn vị: ${controller.donvi.value}",
                                  color: theme.colorScheme.onSurface,
                                  fontSize: 15,
                                  fontWeight: 700,
                                ),
                              ),
                            ),

                            // HIỂN THỊ TÊN ĐƠN VỊ KHI GHÉP PHIẾU
                            if (controller.isMergeMode) ...[
                              FormBuilderTextField(
                                name: 'donvighep',
                                initialValue:
                                    controller.formData['donvighep']
                                        ?.toString() ??
                                    '',
                                maxLines: null,
                                readOnly: true,
                                onChanged: (value) =>
                                    controller.formData['donvighep'] = value,
                                onSaved: (newValue) =>
                                    controller.formData['donvighep'] =
                                        newValue ?? '',
                                style: theme.textTheme.labelLarge,
                              ),
                              MySpacing.height(14),
                            ],

                            // NƠI CÔNG TÁC
                            MyText.bodyMedium('Nơi công tác:', fontWeight: 700),
                            FormBuilderTextField(
                              name: 'noicongtac',
                              validator: FormBuilderValidators.required(
                                errorText: 'Vui lòng nhập địa chỉ nơi công tác',
                              ),
                              decoration: InputDecoration(
                                hintText: 'Nhập địa chỉ nơi công tác',
                                hintStyle: TextStyle(
                                  color: theme.colorScheme.onSurface
                                      .withOpacity(0.3),
                                ),
                              ),
                              initialValue:
                                  controller.formData['noicongtac']
                                      ?.toString() ??
                                  '',
                              maxLines: controller.isMergeMode ? null : 2,
                              onChanged: (value) =>
                                  controller.formData['noicongtac'] = value,
                              onSaved: (newValue) =>
                                  controller.formData['noicongtac'] =
                                      newValue ?? '',
                              style: theme.textTheme.labelLarge,
                            ),
                            MySpacing.height(14),

                            // NỘI DUNG CÔNG TÁC
                            MyText.bodyMedium('Nội dung:', fontWeight: 700),
                            FormBuilderTextField(
                              name: 'noidung',
                              validator: FormBuilderValidators.required(
                                errorText: 'Vui lòng nhập nội dung công việc',
                              ),
                              decoration: InputDecoration(
                                hintText: 'Nhập nội dung công việc',
                                hintStyle: TextStyle(
                                  color: theme.colorScheme.onSurface
                                      .withOpacity(0.3),
                                ),
                              ),
                              initialValue:
                                  controller.formData['noidung']?.toString() ??
                                  '',
                              maxLines: controller.isMergeMode ? null : 2,
                              onChanged: (value) =>
                                  controller.formData['noidung'] = value,
                              onSaved: (newValue) =>
                                  controller.formData['noidung'] =
                                      newValue ?? '',
                              style: theme.textTheme.labelLarge,
                            ),
                            MySpacing.height(14),

                            // ĐỊA ĐIỂM ĐI
                            MyText.bodyMedium('Địa điểm đi:', fontWeight: 700),
                            FormBuilderTextField(
                              name: 'diadiemdi',
                              validator: FormBuilderValidators.required(
                                errorText: 'Vui lòng nhập địa điểm đi',
                              ),
                              decoration: InputDecoration(
                                hintText: 'Nhập địa điểm đi',
                                hintStyle: TextStyle(
                                  color: theme.colorScheme.onSurface
                                      .withOpacity(0.3),
                                ),
                              ),
                              initialValue:
                                  controller.formData['diadiemdi']
                                      ?.toString() ??
                                  '',
                              maxLines: controller.isMergeMode ? null : 2,
                              onChanged: (value) =>
                                  controller.formData['diadiemdi'] = value,
                              onSaved: (newValue) =>
                                  controller.formData['diadiemdi'] =
                                      newValue ?? '',
                              style: theme.textTheme.labelLarge,
                            ),

                            MySpacing.height(14),

                            // ĐỊA ĐIỂM VỀ
                            MyText.bodyMedium('Địa điểm về:', fontWeight: 700),
                            FormBuilderTextField(
                              name: 'diadiemve',
                              validator: FormBuilderValidators.required(
                                errorText: 'Vui lòng nhập địa điểm về',
                              ),
                              decoration: InputDecoration(
                                hintText: 'Nhập địa điểm về',
                                hintStyle: TextStyle(
                                  color: theme.colorScheme.onSurface
                                      .withOpacity(0.3),
                                ),
                              ),
                              initialValue:
                                  controller.formData['diadiemve']
                                      ?.toString() ??
                                  '',
                              maxLines: controller.isMergeMode ? null : 2,
                              onChanged: (value) =>
                                  controller.formData['diadiemve'] = value,
                              onSaved: (newValue) =>
                                  controller.formData['diadiemve'] =
                                      newValue ?? '',
                              style: theme.textTheme.labelLarge,
                            ),
                            MySpacing.height(14),

                            // NGÀY ĐI & NGÀY VỀ
                            MyText.bodyMedium(
                              'Ngày đi & ngày về:',
                              fontWeight: 700,
                            ),
                            FormBuilderDateRangePicker(
                              name: 'startendday',
                              firstDate: DateTime(DateTime.now().year - 1),
                              lastDate: DateTime(DateTime.now().year + 1),
                              format: DateFormat('dd/MM/yyyy'),
                              validator: FormBuilderValidators.required(
                                errorText:
                                    'Vui lòng chọn khoảng thời gian công tác',
                              ),
                              initialValue: controller.formData["startendday"],
                              onChanged: (value) =>
                                  controller.formData["startendday"] = value,
                              onSaved: (newValue) =>
                                  controller.formData["startendday"] = newValue,
                              decoration: InputDecoration(
                                contentPadding: const EdgeInsets.fromLTRB(
                                  4,
                                  12,
                                  4,
                                  1,
                                ),
                                hintText: 'Thời gian công tác dự kiến',
                                suffixIcon: IconButton(
                                  padding: const EdgeInsets.all(0),
                                  icon: const Icon(Icons.close),
                                  onPressed: () => controller
                                      .formKey
                                      .currentState!
                                      .fields['startendday']
                                      ?.didChange(null),
                                ),
                              ),
                              locale: const Locale('vi'),
                              cancelText: "Hủy bỏ",
                              confirmText: "Xác nhận",
                              saveText: "Lưu",
                              fieldStartLabelText: "Ngày đi",
                              fieldEndLabelText: "Ngày về",
                              fieldStartHintText: "Ngày đi",
                              fieldEndHintText: "Ngày về",

                              ///TODO: add prefix
                              // startValuePrefix: "Từ ngày: ",
                              // endValuePrefix: "Đến ngày: ",
                            ),
                            MySpacing.height(14),

                            // THỜI GIAN ĐI & THỜI GIAN VỀ KHI GHÉP PHIẾU
                            if (controller.isMergeMode) ...[
                              // THỜI GIAN ĐI
                              MyText.bodyMedium(
                                'Thời gian đi:',
                                fontWeight: 700,
                              ),
                              FormBuilderTextField(
                                name: 'thoigiandighep',
                                // validator: FormBuilderValidators.required(
                                //     errorText: 'Vui lòng nhập thời gian đi'),
                                decoration: InputDecoration(
                                  hintText: 'Nhập thời gian đi',
                                  hintStyle: TextStyle(
                                    color: theme.colorScheme.onSurface
                                        .withOpacity(0.3),
                                  ),
                                ),
                                initialValue:
                                    controller.formData['thoigiandighep']
                                        ?.toString() ??
                                    '',
                                maxLines: controller.isMergeMode ? null : 2,
                                onChanged: (value) =>
                                    controller.formData['thoigiandighep'] =
                                        value,
                                onSaved: (newValue) =>
                                    controller.formData['thoigiandighep'] =
                                        newValue ?? '',
                                style: theme.textTheme.labelLarge,
                              ),
                              MySpacing.height(14),

                              // THỜI GIAN VỀ
                              MyText.bodyMedium(
                                'Thời gian về:',
                                fontWeight: 700,
                              ),
                              FormBuilderTextField(
                                name: 'thoigianveghep',
                                // validator: FormBuilderValidators.required(
                                //     errorText: 'Vui lòng nhập thời gian về'),
                                decoration: InputDecoration(
                                  hintText: 'Nhập thời gian về',
                                  hintStyle: TextStyle(
                                    color: theme.colorScheme.onSurface
                                        .withOpacity(0.3),
                                  ),
                                ),
                                initialValue:
                                    controller.formData['thoigianveghep']
                                        ?.toString() ??
                                    '',
                                maxLines: controller.isMergeMode ? null : 2,
                                onChanged: (value) =>
                                    controller.formData['thoigianveghep'] =
                                        value,
                                onSaved: (newValue) =>
                                    controller.formData['thoigianveghep'] =
                                        newValue ?? '',
                                style: theme.textTheme.labelLarge,
                              ),
                              MySpacing.height(14),
                            ]
                            // THỜI GIAN ĐI & THỜI GIAN VỀ
                            else ...[
                              SizedBox(
                                width: width,
                                child: Row(
                                  children: [
                                    // Thời gian đi
                                    SizedBox(
                                      width: width / 2 - 22,
                                      child: FormBuilderDateTimePicker(
                                        name: "thoigiandi",
                                        decoration: myDecoration.copyWith(
                                          labelText: 'Thời gian đi',
                                        ),
                                        initialValue:
                                            controller.formData['thoigiandi'] !=
                                                null
                                            ? DateTime.fromMillisecondsSinceEpoch(
                                                controller
                                                        .formData['thoigiandi']
                                                    as int,
                                              )
                                            : null,
                                        validator:
                                            FormBuilderValidators.required(
                                              errorText:
                                                  'Vui lòng chọn thời gian đi',
                                            ),
                                        inputType: InputType.time,
                                        format: DateFormat("HH:mm"),
                                        onChanged: (DateTime? value) {
                                          if (value != null) {
                                            controller
                                                .formData['thoigiandi'] = value
                                                .millisecondsSinceEpoch; // Lưu dưới dạng timestamp
                                          }
                                        },
                                        onSaved: (DateTime? newValue) {
                                          if (newValue != null) {
                                            controller.formData['thoigiandi'] =
                                                newValue.millisecondsSinceEpoch;
                                          }
                                        },
                                        style: theme.textTheme.labelLarge,
                                        firstDate:
                                            DateTime.now(), // Ngày bắt đầu
                                        lastDate: DateTime(
                                          2100,
                                        ), // Ngày kết thúc
                                      ),
                                    ),
                                    MySpacing.width(8),
                                    // Thời gian về
                                    SizedBox(
                                      width: width / 2 - 18,
                                      child: FormBuilderDateTimePicker(
                                        name: "thoigianve",
                                        decoration: myDecoration.copyWith(
                                          labelText: 'Thời gian về',
                                        ),
                                        initialValue:
                                            controller.formData['thoigianve'] !=
                                                null
                                            ? DateTime.fromMillisecondsSinceEpoch(
                                                controller
                                                        .formData['thoigianve']
                                                    as int,
                                              )
                                            : null,
                                        // validator: FormBuilderValidators.required(
                                        //     errorText:
                                        //         'Vui lòng chọn thời gian về'),
                                        inputType: InputType.time,
                                        format: DateFormat("HH:mm"),
                                        onChanged: (DateTime? value) {
                                          if (value != null) {
                                            controller.formData['thoigianve'] =
                                                value.millisecondsSinceEpoch;
                                          }
                                        },
                                        onSaved: (DateTime? newValue) {
                                          if (newValue != null) {
                                            controller.formData['thoigianve'] =
                                                newValue.millisecondsSinceEpoch;
                                          }
                                        },
                                        style: theme.textTheme.labelLarge,
                                        firstDate:
                                            DateTime.now(), // Ngày bắt đầu
                                        lastDate: DateTime(
                                          2100,
                                        ), // Ngày kết thúc
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              MySpacing.height(14),
                            ],

                            // THÀNH PHẦN ĐI CÔNG TÁC
                            MyText.bodyMedium(
                              'Thành phần đi công tác:',
                              fontWeight: 700,
                            ),
                            GestureDetector(
                              onTap: () async {
                                // Mở modal bottom sheet để chọn viên chức công tác
                                await showMaterialModalBottomSheet(
                                  context: context,
                                  elevation: 8,
                                  isDismissible: true,
                                  enableDrag: false,
                                  builder: (context) => VienChucCongTacWidget(),
                                );
                                // if (result != null) {
                                //   // Lưu danh sách viên chức từ result vào formData
                                //   controller.formData['vienchuc'] =
                                //       result['vienchuc'] as List<dynamic>? ??
                                //           [];
                                // }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: theme.colorScheme.onSurface
                                        .withOpacity(0.2),
                                  ),
                                  borderRadius: BorderRadius.circular(2),
                                ),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 10,
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        controller.selectedVienChuc.isEmpty
                                            ? 'Thành phần đi công tác'
                                            : 'Đã chọn ${controller.selectedVienChuc.length} viên chức',
                                      ),
                                    ),
                                    Icon(
                                      Icons.arrow_drop_down,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            MySpacing.height(14),

                            if (controller.isMergeMode) ...[
                              // ĐƠN VỊ QUYẾT TOÁN
                              MyText.bodyMedium(
                                'Đơn vị quyết toán:',
                                fontWeight: 700,
                              ),
                              FormBuilderDropdown<DonVi>(
                                name: 'donviquyettoan',
                                decoration: myDecoration.copyWith(
                                  hintText: 'Chọn đơn vị quyết toán',
                                ),
                                validator: FormBuilderValidators.compose([
                                  FormBuilderValidators.required(
                                    errorText:
                                        'Vui lòng chọn đơn vị quyết toán',
                                  ),
                                ]),
                                items: controller.listDVQuyetToan
                                    .map(
                                      (ele) => DropdownMenuItem(
                                        alignment: AlignmentDirectional.center,
                                        value: ele,
                                        child: Text("${ele.tenDonVi}"),
                                      ),
                                    )
                                    .toList(),
                                onChanged: (value) {
                                  controller.formData['donviquyettoan'] =
                                      value?.id;
                                },
                                onSaved: (newValue) {
                                  controller.formData['donviquyettoan'] =
                                      newValue?.id;
                                },
                                // initialValue: controller
                                //                 .pdx.ttDieuXe?.taiXe!.id !=
                                //             null &&
                                //         controller.nvLaiList.any((taixe) =>
                                //             taixe.id ==
                                //             controller.pdx.ttDieuXe?.taiXe!.id)
                                //     ? controller.nvLaiList.firstWhere((taixe) =>
                                //         taixe.id ==
                                //         controller.pdx.ttDieuXe?.taiXe!.id)
                                //     : null,
                              ),
                              MySpacing.height(14),
                            ]
                            // FILE ĐÍNH KÈM
                            else ...[
                              MyText.bodyMedium(
                                'File đính kèm',
                                fontWeight: 700,
                              ),
                              controller.isEditMode
                                  ? Container(
                                      decoration: BoxDecoration(
                                        color: theme.primaryColor,
                                        borderRadius: BorderRadius.all(
                                          Radius.circular(2),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(6.0),
                                        child: SizedBox(
                                          width: double
                                              .infinity, // Giới hạn chiều rộng
                                          child: Row(
                                            children: [
                                              Icon(
                                                LucideIcons.paperclip,
                                                size: 15,
                                                color: Colors.white,
                                              ),
                                              SizedBox(width: 5),
                                              Expanded(
                                                child: MyText.bodyMedium(
                                                  (controller.formData['document'] !=
                                                              null &&
                                                          controller
                                                              .formData['document']
                                                              .isNotEmpty)
                                                      ? controller.getFileName(
                                                          controller
                                                              .formData['document']!
                                                              .toString(),
                                                        )
                                                      : 'Không có file đính kèm',
                                                  color: Colors.white,
                                                  fontSize: 14,
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    )
                                  : DieuXeFileUpload(
                                      key: controller.uploadWidgetKey,
                                      onFilesSelected: (files) {
                                        controller.selectedFiles = files;
                                      },
                                      onUploadProgress: (value) {},
                                      theme: FileUploadTheme(
                                        primaryColor:
                                            theme.colorScheme.primaryContainer,
                                        backgroundColor:
                                            theme.scaffoldBackgroundColor,
                                        borderColor:
                                            theme.colorScheme.secondary,
                                        textColor: theme.colorScheme.onPrimary,
                                        errorColor: theme.colorScheme.error,
                                        borderRadius: 4.0,
                                        contentPadding: EdgeInsets.fromLTRB(
                                          0,
                                          0,
                                          0,
                                          0,
                                        ),
                                        titleStyle: theme.textTheme.labelLarge!,
                                        subtitleStyle:
                                            theme.textTheme.labelMedium!,
                                      ),
                                      maxFileSizeMB: 10,
                                      allowMultiple: true,
                                      allowedExtensions: [
                                        'jpg',
                                        'jpeg',
                                        'png',
                                        'pdf',
                                        'doc',
                                      ],
                                    ),
                              MySpacing.height(14),
                            ],

                            // SWITCH VIÊN CHỨC NGOÀI TRƯỜNG
                            FormBuilderSwitch(
                              title: const MyText.bodyLarge(
                                'Viên chức ngoài trường?',
                              ),
                              name: 'vcngoaitruong',
                              initialValue:
                                  controller.formData['vcngoaitruong'] ?? false,
                              onChanged: (value) {
                                controller.hienvcngoaitruong.value = value!;
                                controller.formData["vcngoaitruong"] = value;
                                controller.update();
                              },
                              onSaved: (newValue) {
                                controller.formData["vcngoaitruong"] = newValue;
                              },
                              decoration: myDecoration,
                            ),
                            // HIỂN THỊ VIÊN CHỨC NGOÀI TRƯỜNG
                            Visibility(
                              visible: controller.hienvcngoaitruong.value,
                              child: const SizedBox(height: 14),
                            ),
                            Visibility(
                              visible: controller.hienvcngoaitruong.value,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // HỌ TÊN VIÊN CHỨC NGOÀI TRƯỜNG
                                  MyText.bodyMedium(
                                    'Họ tên viên chức:',
                                    fontWeight: 700,
                                  ),
                                  Container(
                                    decoration: BoxDecoration(
                                      image: DecorationImage(
                                        alignment: Alignment.bottomRight,
                                        fit: BoxFit.fitHeight,
                                        image: AssetImage(
                                          "assets/images/training.png",
                                        ),
                                        opacity: 0.01,
                                      ),
                                    ),
                                    child: FormBuilderTextField(
                                      name: 'vienchucngoaitruong',
                                      validator: FormBuilderValidators.required(
                                        errorText:
                                            'Vui lòng nhập danh sách viên chức ngoài trường',
                                      ),
                                      decoration: InputDecoration(
                                        hintText:
                                            'Nhập danh sách viên chức ngoài trường',
                                        hintStyle: TextStyle(
                                          color: theme.colorScheme.onSurface
                                              .withOpacity(0.3),
                                        ),
                                      ),
                                      initialValue:
                                          controller
                                              .formData['vienchucngoaitruong']
                                              ?.toString() ??
                                          '',
                                      maxLines: controller.isMergeMode
                                          ? null
                                          : 2,
                                      onChanged: (value) =>
                                          controller
                                                  .formData['vienchucngoaitruong'] =
                                              value,
                                      onSaved: (newValue) =>
                                          controller
                                                  .formData['vienchucngoaitruong'] =
                                              newValue ?? '',
                                      style: theme.textTheme.labelLarge,
                                    ),
                                  ),
                                  MySpacing.height(14),

                                  // GHI CHÚ
                                  MyText.bodyMedium(
                                    'Ghi chú:',
                                    fontWeight: 700,
                                  ),
                                  FormBuilderTextField(
                                    name: 'donvivienchucngoaitruong',
                                    // validator: FormBuilderValidators.required(
                                    //     errorText: 'Vui lòng nhập ghi chú'),
                                    decoration: InputDecoration(
                                      hintText: 'Nhập ghi chú',
                                      hintStyle: TextStyle(
                                        color: theme.colorScheme.onSurface
                                            .withOpacity(0.3),
                                      ),
                                    ),
                                    initialValue:
                                        controller
                                            .formData['donvivienchucngoaitruong']
                                            ?.toString() ??
                                        '',
                                    maxLines: 2,
                                    onChanged: (value) =>
                                        controller
                                                .formData['donvivienchucngoaitruong'] =
                                            value,
                                    onSaved: (newValue) =>
                                        controller
                                                .formData['donvivienchucngoaitruong'] =
                                            newValue ?? '',
                                    style: theme.textTheme.labelLarge,
                                  ),
                                ],
                              ),
                            ),
                            MySpacing.height(14),
                            // ------------------------------------------------------------------------------------

                            // LÃNH ĐẠO ĐƠN VỊ DUYỆT (KHI CHỌN CHUYỂN ĐƠN CHỜ DUYỆT)
                            if ((!controller.trangthai.value
                                    .toLowerCase()
                                    .contains("yêu cầu điều chỉnh")) &&
                                controller.isMergeMode == false)
                              FormBuilderDropdown<LanhDaoDonviList>(
                                name: 'donviduyet',
                                decoration: myDecoration.copyWith(
                                  labelText: 'Lãnh đạo đơn vị duyệt',
                                ),
                                validator: FormBuilderValidators.compose([
                                  FormBuilderValidators.required(
                                    errorText: 'Vui lòng chọn lãnh đạo duyệt',
                                  ),
                                ]),
                                items: controller.lanhDaoDV
                                    .map(
                                      (ele) => DropdownMenuItem(
                                        alignment: AlignmentDirectional.center,
                                        value: ele,
                                        child: Text(
                                          "${ele.getHoTen()}(${ele.tenChucVu})",
                                        ),
                                      ),
                                    )
                                    .toList(),
                                onChanged: (value) {
                                  controller.formData['donviduyet'] = value!;
                                },
                                onSaved: (newValue) {
                                  controller.formData['donviduyet'] = newValue!;
                                },
                              ),
                            const SizedBox(height: 14),

                            // SWITCH CHUYỂN ĐƠN CHỜ DUYỆT
                            if (controller.isEditMode == false &&
                                controller.isMergeMode == false)
                              FormBuilderSwitch(
                                title: const MyText.bodyLarge(
                                  'Chuyển đơn chờ duyệt',
                                ),
                                name: 'chuyendon',
                                initialValue: false,
                                onChanged: (value) {
                                  controller.choduyet.value = value!;
                                  controller.formData["chuyendon"] = value;
                                  controller.update();
                                },
                                onSaved: (newValue) {
                                  controller.formData["chuyendon"] = newValue;
                                },
                                decoration: myDecoration,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    color: Colors.transparent,
                    child: Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: MyButton.block(
                            onPressed: () async {
                              bool isValid = controller.formKey.currentState!
                                  .validate();
                              if (isValid) {
                                controller.formKey.currentState!.save();
                                if (controller.isMergeMode) {
                                  await controller
                                      .ghepPhieuDieuXe(); // Gọi ghepPhieuDieuXe nếu ghép phiếu
                                } else {
                                  await controller
                                      .taoSuaPhieuDieuXe(); // Gọi taoMoiCongViec nếu tạo mới
                                }
                                // print("Form data: ${controller.formData}");
                              }
                            },
                            child: MyText.labelLarge(
                              controller.isMergeMode
                                  ? 'Ghép phiếu'
                                  : controller.isEditMode
                                  ? 'Cập nhật'
                                  : controller.choduyet.value
                                  ? "Chuyển phiếu & Chờ duyệt"
                                  : "Tạo mới",
                              fontWeight: 900,
                              color: theme.colorScheme.onPrimary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      );
    });
  }
}
