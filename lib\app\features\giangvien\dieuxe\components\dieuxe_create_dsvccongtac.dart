import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxeinfo.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_create_controller.dart';
import 'package:tvumobile/app/shared_components/input_decoration_theme.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class VienChucCongTacWidget extends GetView<DieuXeCreateController> {
  const VienChucCongTacWidget({super.key});

  @override
  Widget build(BuildContext context) {
    // --------------------------------------------- KHAI BÁO ----------------------------------------------
    Map<String, dynamic> formdata = {};

    final myDecoration = const InputDecoration().applyDefaults(
      GlobalInputDecoration(context),
    );
    final theme = Theme.of(context);
    final width = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height; // Chiều cao AppBar
    final availableHeight =
        screenHeight - appBarHeight - 32; // Trừ padding top/bottom (16+16)
    // -----------------------------------------------------------------------------------------------------

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.primaryColor.withOpacity(0.95),
        title: MyText.titleMedium(
          "THÀNH PHẦN ĐI CÔNG TÁC".tr.toUpperCase(),
          color: theme.colorScheme.onPrimary,
          fontWeight: 700,
        ),
        iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(
                left: 16.0,
                right: 16.0,
                bottom: 16.0,
                top: 4,
              ),
              child: Column(
                children: [
                  FormBuilder(
                    key: controller.formKeyVCCT,
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        // Align(
                        //   alignment: Alignment.center,
                        //   child: MyContainer(
                        //     child: MyText.titleMedium(
                        //       "Chọn viên chức đi công tác",
                        //       fontWeight: 700,
                        //       decoration: TextDecoration.underline,
                        //     ),
                        //   ),
                        // ),

                        // ------------------------------- ĐƠN VỊ ------------------------------
                        if (!controller.isMergeMode) ...[
                          Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: SizedBox(
                              height: 40, // Giới hạn chiều cao cho dropdown
                              child: controller.donviList.isNotEmpty
                                  ? FormBuilderField<DonViList>(
                                      name: 'donvi',
                                      initialValue:
                                          controller.selectedDonVi.value,
                                      // onSaved: (newValue) {
                                      //   controller.formData['donvi'] = newValue;
                                      // },
                                      builder: (FormFieldState<DonViList?> field) {
                                        return DropdownSearch<DonViList>(
                                          items: (filter, loadProps) =>
                                              controller.donviList
                                                  .where(
                                                    (u) => u.tenDonVi!
                                                        .toLowerCase()
                                                        .contains(
                                                          filter.toLowerCase(),
                                                        ),
                                                  )
                                                  .toList(),
                                          selectedItem: field.value,
                                          itemAsString: (DonViList u) =>
                                              u.tenDonVi!,

                                          decoratorProps:
                                              DropDownDecoratorProps(
                                                decoration: myDecoration
                                                    .copyWith(
                                                      labelText: 'Chọn đơn vị',
                                                      border:
                                                          OutlineInputBorder(),
                                                    ),
                                              ),
                                          popupProps: PopupProps.menu(
                                            showSearchBox: true,
                                            searchFieldProps: TextFieldProps(
                                              decoration: InputDecoration(
                                                hintText: "Tìm đơn vị...",
                                                border: OutlineInputBorder(),
                                              ),
                                            ),
                                          ),
                                          onChanged: (DonViList? value) {
                                            field.didChange(value);
                                            controller.updateSelectedDonVi(
                                              value,
                                            );
                                          },
                                        );
                                      },
                                    )
                                  : const SkeletonLine(),
                            ),
                          ),
                          const SizedBox(height: 8),

                          // ------------------------ DANH SÁCH VIÊN CHỨC ------------------------
                          MyText.bodyMedium(
                            'Danh sách viên chức:',
                            fontWeight: 700,
                            decoration: TextDecoration.underline,
                          ),
                          Obx(
                            () => Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (controller.isLoadingVienChuc.value)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child:
                                          SkeletonLine(), // Hiển thị skeleton khi loading
                                    )
                                  else if (controller.vienChucList.isEmpty &&
                                      controller.selectedDonVi.value != null)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: MyText.bodyMedium(
                                        "Không có viên chức nào trong đơn vị này",
                                        color: theme.colorScheme.onSurface
                                            .withOpacity(0.6),
                                      ),
                                    )
                                  else
                                    Container(
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: theme.primaryColor,
                                          width: 1.0,
                                        ),
                                        borderRadius: BorderRadius.circular(
                                          4.0,
                                        ),
                                      ),
                                      height: availableHeight * 0.33,
                                      child: ListView.builder(
                                        physics:
                                            AlwaysScrollableScrollPhysics(),
                                        padding: EdgeInsets.all(0),
                                        itemCount:
                                            controller.vienChucList.length,
                                        itemBuilder: (context, index) {
                                          final vienChuc =
                                              controller.vienChucList[index];
                                          bool isChecked = controller
                                              .selectedVienChuc
                                              .any(
                                                (vc) =>
                                                    vc['id'] == vienChuc['id'],
                                              );
                                          return Container(
                                            decoration: BoxDecoration(
                                              border: Border.symmetric(
                                                horizontal: BorderSide(
                                                  color: theme
                                                      .colorScheme
                                                      .onSurface
                                                      .withOpacity(0.2),
                                                  width: 1.0,
                                                ),
                                              ),
                                            ),
                                            child: CheckboxListTile(
                                              title: MyText.bodyLarge(
                                                "${vienChuc['ho']} ${vienChuc['tenDem'] ?? ''} ${vienChuc['ten']} (${vienChuc['chucVu']})",
                                              ),
                                              // value: controller.selectedVienChuc
                                              //     .contains(vienChuc),
                                              value: isChecked,
                                              onChanged: (bool? value) {
                                                if (value == true) {
                                                  controller
                                                      .addSelectedVienChuc(
                                                        vienChuc,
                                                      );
                                                } else {
                                                  controller
                                                      .removeSelectedVienChuc(
                                                        vienChuc,
                                                      );
                                                }
                                              },
                                              secondary: CircleAvatar(
                                                radius: 20,
                                                backgroundImage: NetworkImage(
                                                  vienChuc["hinhAnh"]
                                                      .toString(),
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: 8),
                        ],

                        // ----------------------- VIÊN CHỨC ĐI CÔNG TÁC -----------------------
                        MyText.bodyMedium(
                          'Danh sách viên chức đã chọn:',
                          fontWeight: 700,
                          decoration: TextDecoration.underline,
                        ),
                        Obx(
                          () => controller.selectedVienChuc.isNotEmpty
                              ? Padding(
                                  padding: const EdgeInsets.only(top: 5.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: theme.primaryColor,
                                            width: 1.0,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            4.0,
                                          ),
                                        ),
                                        height: controller.isMergeMode
                                            ? availableHeight * 0.8
                                            : availableHeight * 0.33,
                                        child: ListView.builder(
                                          physics:
                                              AlwaysScrollableScrollPhysics(),
                                          padding: EdgeInsets.all(0),
                                          itemCount: controller
                                              .selectedVienChuc
                                              .length,
                                          itemBuilder: (context, index) {
                                            final vienChuc = controller
                                                .selectedVienChuc[index];
                                            return Container(
                                              decoration: BoxDecoration(
                                                border: Border.symmetric(
                                                  horizontal: BorderSide(
                                                    color: theme
                                                        .colorScheme
                                                        .onSurface
                                                        .withOpacity(0.2),
                                                    width: 1.0,
                                                  ),
                                                ),
                                              ),
                                              child: ListTile(
                                                title: Text(
                                                  "${vienChuc['ho']} ${vienChuc['tenDem'] ?? ''} ${vienChuc['ten']} (${vienChuc['chucVu']})",
                                                ),
                                                subtitle: MyText.bodySmall(
                                                  "${vienChuc['donVi']}",
                                                ),
                                                leading: CircleAvatar(
                                                  backgroundImage: NetworkImage(
                                                    vienChuc["hinhAnh"]
                                                        .toString(),
                                                  ),
                                                ),
                                                trailing: controller.isMergeMode
                                                    ? null
                                                    : IconButton(
                                                        icon: Icon(
                                                          Icons.remove_circle,
                                                          color: theme
                                                              .colorScheme
                                                              .error,
                                                        ),
                                                        onPressed: () => controller
                                                            .removeSelectedVienChuc(
                                                              vienChuc,
                                                            ),
                                                      ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : Padding(
                                  padding: const EdgeInsets.only(top: 5.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: theme.primaryColor
                                                .withOpacity(0.2),
                                            width: 1.0,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            4.0,
                                          ),
                                        ),
                                        height: availableHeight * 0.33,
                                      ),
                                    ],
                                  ),
                                ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              border: Border(
                top: BorderSide(color: theme.dividerColor, width: 0.1),
              ),
            ),
            child: MyButton.block(
              backgroundColor: theme.primaryColor.withOpacity(0.95),
              onPressed: () {
                // Lưu danh sách viên chức đã chọn vào formData
                controller.formData['vienchuc'] = controller.selectedVienChuc
                    .toList();

                // Kiểm tra và lưu toàn bộ form
                bool isValid = controller.formKeyVCCT.currentState!
                    .saveAndValidate();
                if (isValid) {
                  Navigator.pop(context);
                }
              },
              child: MyText.labelLarge(
                'Xác nhận thông tin',
                color: theme.colorScheme.onPrimary,
                fontWeight: 900,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
