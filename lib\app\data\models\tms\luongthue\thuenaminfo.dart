import 'dart:convert';

class ThueNamInfo {
  ThueNamInfo({
    required this.id,
    required this.nam,
    required this.vienChucId,
    required this.mavienchuc,
    required this.hoten,
    required this.mst,
    required this.cmnd,
    required this.tongthunhapchiuthue,
    required this.tongsotiengiamtrugiacanh,
    required this.cacloaiquy,
    required this.baohiemduoctru,
    required this.thunhaptinhthue,
    required this.tongsothuephainop,
    required this.tongsothueTncndakhautru,
    required this.sothuedanopthua,
    required this.sothueconphainop,
  });

  final int? id;
  final num? nam;
  final num? vienChucId;
  final String? mavienchuc;
  final String? hoten;
  final String? mst;
  final dynamic cmnd;
  final num? tongthunhapchiuthue;
  final num? tongsotiengiamtrugiacanh;
  final num? cacloaiquy;
  final num? baohiemduoctru;
  final num? thunhaptinhthue;
  final num? tongsothuephainop;
  final num? tongsothueTncndakhautru;
  final num? sothuedanopthua;
  final num? sothueconphainop;

  ThueNamInfo copyWith({
    int? id,
    num? nam,
    num? vienChucId,
    String? mavienchuc,
    String? hoten,
    String? mst,
    dynamic cmnd,
    num? tongthunhapchiuthue,
    num? tongsotiengiamtrugiacanh,
    num? cacloaiquy,
    num? baohiemduoctru,
    num? thunhaptinhthue,
    num? tongsothuephainop,
    num? tongsothueTncndakhautru,
    num? sothuedanopthua,
    num? sothueconphainop,
  }) {
    return ThueNamInfo(
      id: id ?? this.id,
      nam: nam ?? this.nam,
      vienChucId: vienChucId ?? this.vienChucId,
      mavienchuc: mavienchuc ?? this.mavienchuc,
      hoten: hoten ?? this.hoten,
      mst: mst ?? this.mst,
      cmnd: cmnd ?? this.cmnd,
      tongthunhapchiuthue: tongthunhapchiuthue ?? this.tongthunhapchiuthue,
      tongsotiengiamtrugiacanh: tongsotiengiamtrugiacanh ?? this.tongsotiengiamtrugiacanh,
      cacloaiquy: cacloaiquy ?? this.cacloaiquy,
      baohiemduoctru: baohiemduoctru ?? this.baohiemduoctru,
      thunhaptinhthue: thunhaptinhthue ?? this.thunhaptinhthue,
      tongsothuephainop: tongsothuephainop ?? this.tongsothuephainop,
      tongsothueTncndakhautru: tongsothueTncndakhautru ?? this.tongsothueTncndakhautru,
      sothuedanopthua: sothuedanopthua ?? this.sothuedanopthua,
      sothueconphainop: sothueconphainop ?? this.sothueconphainop,
    );
  }

  factory ThueNamInfo.fromJson(Map<String, dynamic> json){
    return ThueNamInfo(
      id: json["id"],
      nam: json["nam"],
      vienChucId: json["vienChucId"],
      mavienchuc: json["mavienchuc"],
      hoten: json["hoten"],
      mst: json["mst"],
      cmnd: json["cmnd"],
      tongthunhapchiuthue: json["tongthunhapchiuthue"],
      tongsotiengiamtrugiacanh: json["tongsotiengiamtrugiacanh"],
      cacloaiquy: json["cacloaiquy"],
      baohiemduoctru: json["baohiemduoctru"],
      thunhaptinhthue: json["thunhaptinhthue"],
      tongsothuephainop: json["tongsothuephainop"],
      tongsothueTncndakhautru: json["tongsothueTncndakhautru"],
      sothuedanopthua: json["sothuedanopthua"],
      sothueconphainop: json["sothueconphainop"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "nam": nam,
    "vienChucId": vienChucId,
    "mavienchuc": mavienchuc,
    "hoten": hoten,
    "mst": mst,
    "cmnd": cmnd,
    "tongthunhapchiuthue": tongthunhapchiuthue,
    "tongsotiengiamtrugiacanh": tongsotiengiamtrugiacanh,
    "cacloaiquy": cacloaiquy,
    "baohiemduoctru": baohiemduoctru,
    "thunhaptinhthue": thunhaptinhthue,
    "tongsothuephainop": tongsothuephainop,
    "tongsothueTncndakhautru": tongsothueTncndakhautru,
    "sothuedanopthua": sothuedanopthua,
    "sothueconphainop": sothueconphainop,
  };

  factory ThueNamInfo.fromJsonString(String source) =>
      ThueNamInfo.fromJson(json.decode(source));

  @override
  String toString(){
    return "$id, $nam, $vienChucId, $mavienchuc, $hoten, $mst, $cmnd, $tongthunhapchiuthue, $tongsotiengiamtrugiacanh, $cacloaiquy, $baohiemduoctru, $thunhaptinhthue, $tongsothuephainop, $tongsothueTncndakhautru, $sothuedanopthua, $sothueconphainop, ";
  }
}
