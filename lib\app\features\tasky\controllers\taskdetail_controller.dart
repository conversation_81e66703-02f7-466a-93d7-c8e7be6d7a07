import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_toggle_tab/flutter_toggle_tab.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/tasky/Task.dart';
import 'package:tvumobile/app/data/models/tms/congvan/cong_van_nhan.dart';
import 'package:tvumobile/app/data/models/tms/danhmuc.dart';
import 'package:tvumobile/app/features/tasky/components/file_upload_widget.dart';
import 'package:tvumobile/app/features/tasky/components/tasky_dialog.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:tvumobile/app/utils/extensions/extensions.dart';

class TaskDetailController extends GetxController {
  Task task = Task();
  bool isGettingTask = true;
  ApiProvider apiProvider = Get.find();
  List<DataTab> get listTextTabToggle => [
        DataTab(title: "Hoạt động"),
        DataTab(title: "Trao đổi"),
        DataTab(title: "Đính kèm"),
      ];

  int tabTextIndexSelected = 0;
  List<DanhMuc> taskStatuses = <DanhMuc>[];
  List<DanhMuc> taskDonViTinh = <DanhMuc>[];
  List<Widget Function(TaskDetailController)> navigation = [];
  final DateFormat dateFormat = DateFormat("dd/MM/yyyy");
  final uploadWidgetKey = GlobalKey<FileUploadWidgetState>();
  UserInfo? userInfo = LocalStorageServices.getUserInfo();
  late final Nguoi nguoiThucHien;
  final formKey = GlobalKey<FormBuilderState>();
  final Map<String, dynamic> formData = {};
  final appbarTitle = "".obs;

  // Biến lưu danh sách công văn
  // final RxList<Map<String, dynamic>> taskDocuments =
  //     <Map<String, dynamic>>[].obs;

  // Biến lưu công văn (chỉ lưu một công văn duy nhất)
  final Rx<Map<String, dynamic>?> taskDocument =
      Rx<Map<String, dynamic>?>(null);

  @override
  Future<void> onInit() async {
    super.onInit();
    isGettingTask = true;
    update();

    if (Get.arguments != null && Get.arguments is Task) {
      task = Get.arguments as Task;
    } else if (Get.parameters.containsKey('id') &&
        Get.parameters['id'] != null) {
      task.id = int.parse(Get.parameters['id']!);
      await apiGetTask();
    } else {
      log("No task data provided");
    }

    // Kiểm tra và lấy thông tin công văn từ documentId
    // if (task.taskResource != null && task.taskResource!.isNotEmpty) {
    //   for (var resource in task.taskResource!) {
    //     final documentId = resource.documentId;
    //     if (documentId != null && documentId != 0) {
    //       await fetchDocumentById(documentId);
    //     }
    //   }
    // }

    // Kiểm tra và lấy thông tin công văn từ documentId (chỉ lấy công văn đầu tiên)
    if (task.taskResource != null && task.taskResource!.isNotEmpty) {
      if (task.taskResource!.any((resource) =>
          resource.documentId != null && resource.documentId != 0)) {
        final resourceWithDocument = task.taskResource!.firstWhere(
          (resource) => resource.documentId != null && resource.documentId != 0,
        );
        final documentId = resourceWithDocument.documentId as int;
        await fetchDocumentById(documentId);
      }
    }

    appbarTitle.value = "CHI TIẾT CÔNG VIỆC";

    navigation = [
      tabHoatDong,
      tabTraoDoi,
      tabDinhKem,
    ];

    // Lấy danh sách đơn vị tính
    await fetchDonViTinh();

    isGettingTask = false;
    log("::::::::::Task Detail Controller is initialized:");
    log(task.toJson().toString());

    update();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    log("::::::::::Task Detail Controller is ready:::::::::::");
    log(task.toJson().toString());
    List<DanhMuc> tmpStatus = await apiProvider.getTaskStatus(limit: 5);
    if (tmpStatus.isNotEmpty) {
      taskStatuses.clear();
      taskStatuses.addAll(tmpStatus);
      update();
    }
  }

  // Hàm lấy danh sách đơn vị tính từ API
  Future<void> fetchDonViTinh() async {
    try {
      List<DanhMuc> tmpDvTinh = await apiProvider.getDonViTinh(limit: 5);
      if (tmpDvTinh.isNotEmpty) {
        taskDonViTinh.clear();
        taskDonViTinh.addAll(tmpDvTinh);
        update(); // Cập nhật trạng thái để UI phản ánh dữ liệu mới
      }
    } catch (e) {
      log('Error fetching donViTinh: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể tải danh sách đơn vị tính',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }

  // Lấy chi tiết công việc từ server
  Future<void> apiGetTask() async {
    var response = await apiProvider.getAllTasks(taskId: task.id!);
    log("Raw response from server: $response");
    if (response.isNotEmpty) {
      task = response[0];
      isGettingTask = false;
    } else {
      Get.snackbar(
        'Lỗi',
        'Không tìm thấy công việc với ID: ${task.id}',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
    update();
  }

  // Cập nhật chỉ số tab được chọn
  void updateTabIndex(int index) {
    tabTextIndexSelected = index;
    update();
  }

  // Xử lý cập nhật trạng thái công việc
  void updateTaskStatus(dynamic status) async {
    try {
      task.trangThai = status;
      update();
      await apiProvider.updateTaskInfo(taskId: task.id!, status: status.id);
      update();
      Get.back();
      Get.snackbar(
        'Cập nhật thành công',
        'Trạng thái công việc đã được cập nhật',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.withOpacity(0.8),
        colorText: Colors.white,
      );
    } catch (e) {
      log('Error updating task status: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể cập nhật trạng thái',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }

  // Xử lý cập nhật tự đánh giá
  void updateSelfEvaluation(
      {required Nguoi nguoi, required int completionValue}) async {
    try {
      await apiProvider.updateTaskInfo(
        taskId: task.id!,
        vienchucId: nguoi.id,
        tudanhgia: completionValue,
      );
      nguoi.tuDanhGiaMucDoHoanThanh = completionValue;
      await apiGetTask();
      update();
      Get.back();
      Get.snackbar(
        'Thành công',
        'Đã cập nhật mức độ hoàn thành',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.withOpacity(0.8),
        colorText: Colors.white,
      );
    } catch (e) {
      log('Error updating self-evaluation: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể cập nhật mức độ hoàn thành',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }

  // Xử lý cập nhật đánh giá công việc
  void updateEvaluation({
    required Nguoi nguoi,
    required int completionValue,
    required String note,
  }) async {
    try {
      await apiProvider.updateTaskInfo(
        taskId: task.id!,
        vienchucId: nguoi.id,
        nguoiduyetdanhgia: completionValue,
        nguoiduyetghichu: note,
      );
      nguoi.nguoiDuyetDanhGiaMucDoHoanThanh = completionValue;
      nguoi.nguoiDuyetGhiChu = note;
      await apiGetTask();
      update();
      Get.back();
      Get.snackbar(
        'Thành công',
        'Đã cập nhật đánh giá công việc',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.withOpacity(0.8),
        colorText: Colors.white,
      );
    } catch (e) {
      log('Error updating evaluation: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể cập nhật đánh giá công việc',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }

  // ---------------- THAO TÁC VỚI TASK UPDATE INFO -----------------
  // Xử lý thêm thông tin trao đổi
  void addMessage({required String updateInfo}) async {
    try {
      await apiProvider.addTaskUpdateInfo(
        taskId: task.id!,
        updateInfo: updateInfo,
      );
      await apiGetTask();
      update();
      Get.back();
      Get.snackbar(
        'Thành công',
        'Đã gửi thông tin trao đổi',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.withOpacity(0.8),
        colorText: Colors.white,
      );
    } catch (e) {
      log('Error updating evaluation: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể gửi thông tin trao đổi',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }

  // Chỉnh sửa tin nhắn (trao đổi thông tin)
  Future<void> updateMessage({
    required int updateInfoId,
    required String updateInfo,
  }) async {
    try {
      // Gọi API để chỉnh sửa tin nhắn
      await apiProvider.updateTaskUpdateInfo(
        updateInfoId: updateInfoId,
        updateInfo: updateInfo,
      );

      // Cập nhật danh sách tin nhắn
      await apiGetTask();
      update();
      Get.back(); // Đóng dialog
      Get.snackbar('Thông báo', 'Cập nhật tin nhắn thành công!',
          backgroundColor: Colors.green, colorText: Colors.white);
    } catch (e) {
      Get.snackbar('Lỗi', 'Cập nhật tin nhắn thất bại: $e',
          backgroundColor: Colors.red, colorText: Colors.white);
    }
  }

  // Xóa tin nhắn (trao đổi thông tin)
  Future<void> deleteMessage(int updateInfoId) async {
    try {
      // Gọi API để xóa tin nhắn
      await apiProvider.deleteTaskUpdateInfo(updateInfoId);

      // Cập nhật danh sách tin nhắn
      await apiGetTask();
      update();
      Get.back(); // Đóng dialog xác nhận
      Get.snackbar('Thông báo', 'Xóa tin nhắn thành công!',
          backgroundColor: Colors.green, colorText: Colors.white);
    } catch (e) {
      Get.back(); // Đóng dialog xác nhận
      Get.snackbar('Lỗi', 'Xóa tin nhắn thất bại: $e',
          backgroundColor: Colors.red, colorText: Colors.white);
    }
  }

  // ----------------- THAO TÁC VỚI TASK ACTION ---------------------
  // Xử lý thêm hoạt động mới
  void addSubTask({
    required String moTa,
    required String lyDoChuaHoanThanh,
    required int khoiLuongCongViec,
    required int tongSoGioThucHien,
    required DateTime ngayKetThuc,
    required int donViTinhId,
    required int tiLeHoanThanh,
  }) async {
    try {
      await apiProvider.addTaskAction(
        taskId: task.id!,
        moTa: moTa,
        lyDoChuaHoanThanh: lyDoChuaHoanThanh,
        khoiLuongCongViec: khoiLuongCongViec,
        tongSoGioThucHien: tongSoGioThucHien,
        ngayKetThuc: ngayKetThuc,
        donViTinhId: donViTinhId,
        tiLeHoanThanh: tiLeHoanThanh,
      );
      await apiGetTask();
      update();
      Get.back();
      Get.snackbar(
        'Thành công',
        'Đã thêm hoạt động mới',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.withOpacity(0.8),
        colorText: Colors.white,
      );
    } catch (e) {
      log('Error adding action: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể thêm hoạt động mới',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }

  // Chỉnh sửa hoạt động (subtask)
  Future<void> updateSubTask({
    required int actionId,
    required String moTa,
    required String lyDoChuaHoanThanh,
    required int khoiLuongCongViec,
    required int tongSoGioThucHien,
    required int tiLeHoanThanh,
    required DateTime ngayKetThuc,
    required int donViTinhId,
  }) async {
    try {
      // Gọi API để chỉnh sửa hoạt động
      await apiProvider.updateTaskAction(
        actionId: actionId,
        moTa: moTa,
        lyDoChuaHoanThanh: lyDoChuaHoanThanh,
        khoiLuongCongViec: khoiLuongCongViec,
        tongSoGioThucHien: tongSoGioThucHien,
        tiLeHoanThanh: tiLeHoanThanh,
        ngayKetThuc: ngayKetThuc,
        donViTinhId: donViTinhId,
      );

      // Cập nhật danh sách hoạt động
      await apiGetTask();
      update();
      Get.back(); // Đóng dialog
      Get.snackbar('Thông báo', 'Cập nhật hoạt động thành công!',
          backgroundColor: Colors.green, colorText: Colors.white);
    } catch (e) {
      Get.snackbar('Lỗi', 'Cập nhật hoạt động thất bại: $e',
          backgroundColor: Colors.red, colorText: Colors.white);
    }
  }

  // Xóa hoạt động (subtask)
  Future<void> deleteSubTask(int actionId) async {
    try {
      // Gọi API để xóa hoạt động
      await apiProvider.deleteTaskAction(actionId);

      // Cập nhật danh sách hoạt động
      await apiGetTask();
      update();
      Get.back(); // Đóng dialog xác nhận
      Get.snackbar('Thông báo', 'Xóa hoạt động thành công!',
          backgroundColor: Colors.green, colorText: Colors.white);
    } catch (e) {
      Get.back(); // Đóng dialog xác nhận
      Get.snackbar('Lỗi', 'Xóa hoạt động thất bại: $e',
          backgroundColor: Colors.red, colorText: Colors.white);
    }
  }

  // ----------------- THAO TÁC VỚI TASK RESOURCE -------------------
  // Xử lý thêm file đính kèm
  void addResource({required File file}) async {
    try {
      await apiProvider.addTaskResource(
        taskId: task.id!,
        file: file,
      );
      await apiGetTask();
      update();
      Get.back();
      Get.snackbar(
        'Thành công',
        'Đã thêm file đính kèm',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.withOpacity(0.8),
        colorText: Colors.white,
      );
    } catch (e) {
      log('Error updating evaluation: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể thêm file đính kèm',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }

  // Xóa file đính kèm
  Future<void> deleteResource(int resourceId) async {
    try {
      // Gọi API để xóa file đính kèm
      await apiProvider.deleteTaskResource(resourceId);

      // Cập nhật danh sách file đính kèm
      await apiGetTask();
      update();
      Get.back(); // Đóng dialog xác nhận
      Get.snackbar('Thông báo', 'Xóa file đính kèm thành công!',
          backgroundColor: Colors.green, colorText: Colors.white);
    } catch (e) {
      Get.back(); // Đóng dialog xác nhận
      Get.snackbar('Lỗi', 'Xóa file đính kèm thất bại: $e',
          backgroundColor: Colors.red, colorText: Colors.white);
    }
  }

  // ------------------------ XÓA CÔNG VIỆC -------------------------
  Future<void> deleteTask() async {
    try {
      // Gọi API để xóa task
      await apiProvider.deleteTask(task.id!);

      // Thông báo thành công
      Get.back(); // Đóng màn hình chi tiết task (nếu cần)
      // Đóng dialog xác nhận xóa
      Get.back();
      Get.snackbar(
        'Thông báo',
        'Xóa công việc thành công!',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.back(); // Đóng dialog xác nhận xóa
      Get.snackbar(
        'Lỗi',
        'Xóa công việc thất bại: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      log('Error deleting task: $e');
    }
  }

  // -------------------- LẤY CÔNG VĂN THEO ID ----------------------
  Future<void> fetchDocumentById(int documentId) async {
    try {
      final response = await apiProvider.fetchDocumentById(documentId);
      // if (response.isNotEmpty) {
      //   taskDocuments.add(response[0] as Map<String, dynamic>);
      //   update(); // Cập nhật trạng thái để UI phản ánh dữ liệu mới
      // }

      log("Response for documentId $documentId: $response");
      if (response.isNotEmpty) {
        taskDocument.value = response;
        update();
      }
    } catch (e) {
      log('Error fetching document: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể tải thông tin công văn với ID $documentId',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }
}
