class DonNghiPhepVienChuc {
  DonNghiPhepVienChuc({
    this.id,
    this.loaiNghiPhep,
    this.nguoiDuyet1,
    this.nguoiDuyet2,
    this.nguoiDuyet3,
    this.nguoiDuyetCongDoan,
    this.tuNgay,
    this.denNgay,
    this.soNgay,
    this.trangThai,
    this.lyDo,
    this.ghiChu,
    this.buoi,
    this.ngayNguoiDuyet1,
    this.ngayNguoiDuyet2,
    this.ngayNguoiDuyet3,
    this.ngayNguoiDuyetCongDoan,
    this.ghiChuTraDon1,
    this.ghiChuTraDon2,
    this.ghiChuTraDon3,
    this.ghiChuTraDonCongDoan,
    this.vienChuc,
    this.ngaySinhCon,
    this.thaiSanDauOmTuNgay,
    this.thaiSanDauOmDenNgay,
    this.sinhThuongPhauThuat,
    this.soCon,
    this.taiBenh<PERSON>ien,
    this.diemLuatBhxh,
    this.conThu,
    this.hoTenVo,
    this.ngaySinhCuaVo,
    this.cmndCanCuocCuaVo,
    this.canNang,
    this.bghDuyetTrucTiep,
  });

  final int? id;
  final LoaiNghiPhep? loaiNghiPhep;
  final NguoiDuyet1? nguoiDuyet1;
  final NguoiDuyet1? nguoiDuyet2;
  final NguoiDuyet1? nguoiDuyet3;
  final NguoiDuyet1? nguoiDuyetCongDoan;
  final DateTime? tuNgay;
  final DateTime? denNgay;
  final num? soNgay;
  final String? trangThai;
  final String? lyDo;
  final String? ghiChu;
  final String? buoi;
  final DateTime? ngayNguoiDuyet1;
  final DateTime? ngayNguoiDuyet2;
  final DateTime? ngayNguoiDuyet3;
  final DateTime? ngayNguoiDuyetCongDoan;
  final String? ghiChuTraDon1;
  final String? ghiChuTraDon2;
  final String? ghiChuTraDon3;
  final String? ghiChuTraDonCongDoan;
  final VienChuc? vienChuc;
  // Dành cho các loại nghỉ phép đặc biệt
  final DateTime? ngaySinhCon;
  final DateTime? thaiSanDauOmTuNgay;
  final DateTime? thaiSanDauOmDenNgay;
  final String? sinhThuongPhauThuat;
  final String? soCon;
  final String? taiBenhVien;
  final String? diemLuatBhxh;
  final String? conThu;
  final String? hoTenVo;
  final DateTime? ngaySinhCuaVo;
  final String? cmndCanCuocCuaVo;
  final String? canNang;
  final bool? bghDuyetTrucTiep;

  DonNghiPhepVienChuc copyWith({
    int? id,
    LoaiNghiPhep? loaiNghiPhep,
    NguoiDuyet1? nguoiDuyet1,
    NguoiDuyet1? nguoiDuyet2,
    NguoiDuyet1? nguoiDuyet3,
    NguoiDuyet1? nguoiDuyetCongDoan,
    DateTime? tuNgay,
    DateTime? denNgay,
    num? soNgay,
    String? trangThai,
    String? lyDo,
    String? ghiChu,
    String? buoi,
    DateTime? ngayNguoiDuyet1,
    DateTime? ngayNguoiDuyet2,
    DateTime? ngayNguoiDuyet3,
    DateTime? ngayNguoiDuyetCongDoan,
    String? ghiChuTraDon1,
    String? ghiChuTraDon2,
    String? ghiChuTraDon3,
    String? ghiChuTraDonCongDoan,
    VienChuc? vienChuc,
    DateTime? ngaySinhCon,
    DateTime? thaiSanDauOmTuNgay,
    DateTime? thaiSanDauOmDenNgay,
    String? sinhThuongPhauThuat,
    String? soCon,
    String? taiBenhVien,
    String? diemLuatBhxh,
    String? conThu,
    String? hoTenVo,
    DateTime? ngaySinhCuaVo,
    String? cmndCanCuocCuaVo,
    String? canNang,
    bool? bghDuyetTrucTiep,
  }) {
    return DonNghiPhepVienChuc(
      id: id ?? this.id,
      loaiNghiPhep: loaiNghiPhep ?? this.loaiNghiPhep,
      nguoiDuyet1: nguoiDuyet1 ?? this.nguoiDuyet1,
      nguoiDuyet2: nguoiDuyet2 ?? this.nguoiDuyet2,
      nguoiDuyet3: nguoiDuyet3 ?? this.nguoiDuyet3,
      nguoiDuyetCongDoan: nguoiDuyetCongDoan ?? this.nguoiDuyetCongDoan,
      tuNgay: tuNgay ?? this.tuNgay,
      denNgay: denNgay ?? this.denNgay,
      soNgay: soNgay ?? this.soNgay,
      trangThai: trangThai ?? this.trangThai,
      lyDo: lyDo ?? this.lyDo,
      ghiChu: ghiChu ?? this.ghiChu,
      buoi: buoi ?? this.buoi,
      ngayNguoiDuyet1: ngayNguoiDuyet1 ?? this.ngayNguoiDuyet1,
      ngayNguoiDuyet2: ngayNguoiDuyet2 ?? this.ngayNguoiDuyet2,
      ngayNguoiDuyet3: ngayNguoiDuyet3 ?? this.ngayNguoiDuyet3,
      ngayNguoiDuyetCongDoan:
          ngayNguoiDuyetCongDoan ?? this.ngayNguoiDuyetCongDoan,
      ghiChuTraDon1: ghiChuTraDon1 ?? this.ghiChuTraDon1,
      ghiChuTraDon2: ghiChuTraDon2 ?? this.ghiChuTraDon2,
      ghiChuTraDon3: ghiChuTraDon3 ?? this.ghiChuTraDon3,
      ghiChuTraDonCongDoan: ghiChuTraDonCongDoan ?? this.ghiChuTraDonCongDoan,
      vienChuc: vienChuc ?? this.vienChuc,
      ngaySinhCon: ngaySinhCon ?? this.ngaySinhCon,
      thaiSanDauOmTuNgay: thaiSanDauOmTuNgay ?? this.thaiSanDauOmTuNgay,
      thaiSanDauOmDenNgay: thaiSanDauOmDenNgay ?? this.thaiSanDauOmDenNgay,
      sinhThuongPhauThuat: sinhThuongPhauThuat ?? this.sinhThuongPhauThuat,
      soCon: soCon ?? this.soCon,
      taiBenhVien: taiBenhVien ?? this.taiBenhVien,
      diemLuatBhxh: diemLuatBhxh ?? this.diemLuatBhxh,
      conThu: conThu ?? this.conThu,
      hoTenVo: hoTenVo ?? this.hoTenVo,
      ngaySinhCuaVo: ngaySinhCuaVo ?? this.ngaySinhCuaVo,
      cmndCanCuocCuaVo: cmndCanCuocCuaVo ?? this.cmndCanCuocCuaVo,
      canNang: canNang ?? this.canNang,
      bghDuyetTrucTiep: bghDuyetTrucTiep ?? this.bghDuyetTrucTiep,
    );
  }

  factory DonNghiPhepVienChuc.fromJson(Map<String, dynamic> json) {
    return DonNghiPhepVienChuc(
      id: json["id"],
      loaiNghiPhep: json["loaiNghiPhep"] == null
          ? null
          : LoaiNghiPhep.fromJson(json["loaiNghiPhep"]),
      nguoiDuyet1: json["nguoiDuyet1"] == null
          ? null
          : NguoiDuyet1.fromJson(json["nguoiDuyet1"]),
      nguoiDuyet2: json["nguoiDuyet2"] == null
          ? null
          : NguoiDuyet1.fromJson(json["nguoiDuyet2"]),
      nguoiDuyet3: json["nguoiDuyet3"] == null
          ? null
          : NguoiDuyet1.fromJson(json["nguoiDuyet3"]),
      nguoiDuyetCongDoan: json["nguoiDuyetCongDoan"] == null
          ? null
          : NguoiDuyet1.fromJson(json["nguoiDuyetCongDoan"]),
      tuNgay: DateTime.tryParse(json["tuNgay"] ?? ""),
      denNgay: DateTime.tryParse(json["denNgay"] ?? ""),
      soNgay: json["soNgay"],
      trangThai: json["trangThai"],
      lyDo: json["lyDo"],
      ghiChu: json["ghiChu"],
      buoi: json["buoi"],
      ngayNguoiDuyet1: DateTime.tryParse(json["ngayNguoiDuyet1"] ?? ""),
      ngayNguoiDuyet2: DateTime.tryParse(json["ngayNguoiDuyet2"] ?? ""),
      ngayNguoiDuyet3: DateTime.tryParse(json["ngayNguoiDuyet3"] ?? ""),
      ngayNguoiDuyetCongDoan:
          DateTime.tryParse(json["ngayNguoiDuyetCongDoan"] ?? ""),
      ghiChuTraDon1: json["ghiChuTraDon1"],
      ghiChuTraDon2: json["ghiChuTraDon2"],
      ghiChuTraDon3: json["ghiChuTraDon3"],
      ghiChuTraDonCongDoan: json["ghiChuTraDonCongDoan"],
      vienChuc:
          json["vienChuc"] == null ? null : VienChuc.fromJson(json["vienChuc"]),
      ngaySinhCon: DateTime.tryParse(json["ngaySinhCon"] ?? ""),
      thaiSanDauOmTuNgay: DateTime.tryParse(json["thaiSanDauOmTuNgay"] ?? ""),
      thaiSanDauOmDenNgay: DateTime.tryParse(json["thaiSanDauOmDenNgay"] ?? ""),
      sinhThuongPhauThuat: json["sinhThuongPhauThuat"],
      soCon: json["soCon"],
      taiBenhVien: json["taiBenhVien"],
      diemLuatBhxh: json["diemLuatBhxh"],
      conThu: json["conThu"],
      hoTenVo: json["hoTenVo"],
      ngaySinhCuaVo: DateTime.tryParse(json["ngaySinhCuaVo"] ?? ""),
      cmndCanCuocCuaVo: json["cmndCanCuocCuaVo"],
      canNang: json["canNang"],
      bghDuyetTrucTiep: json["bghDuyetTrucTiep"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "loaiNghiPhep": loaiNghiPhep?.toJson(),
        "nguoiDuyet1": nguoiDuyet1?.toJson(),
        "nguoiDuyet2": nguoiDuyet2,
        "nguoiDuyet3": nguoiDuyet3,
        "nguoiDuyetCongDoan": nguoiDuyetCongDoan,
        "tuNgay": tuNgay?.toIso8601String(),
        "denNgay": denNgay?.toIso8601String(),
        "soNgay": soNgay,
        "trangThai": trangThai,
        "lyDo": lyDo,
        "ghiChu": ghiChu,
        "buoi": buoi,
        "ngayNguoiDuyet1": ngayNguoiDuyet1?.toIso8601String(),
        "ngayNguoiDuyet2": ngayNguoiDuyet2?.toIso8601String(),
        "ngayNguoiDuyet3": ngayNguoiDuyet3?.toIso8601String(),
        "ngayNguoiDuyetCongDoan": ngayNguoiDuyetCongDoan?.toIso8601String(),
        "ghiChuTraDon1": ghiChuTraDon1,
        "ghiChuTraDon2": ghiChuTraDon2,
        "ghiChuTraDon3": ghiChuTraDon3,
        "ghiChuTraDonCongDoan": ghiChuTraDonCongDoan,
        "vienChuc": vienChuc?.toJson(),
        "ngaySinhCon": ngaySinhCon?.toIso8601String(),
        "thaiSanDauOmTuNgay": thaiSanDauOmTuNgay?.toIso8601String(),
        "thaiSanDauOmDenNgay": thaiSanDauOmDenNgay?.toIso8601String(),
        "sinhThuongPhauThuat": sinhThuongPhauThuat,
        "soCon": soCon,
        "taiBenhVien": taiBenhVien,
        "diemLuatBhxh": diemLuatBhxh,
        "conThu": conThu,
        "hoTenVo": hoTenVo,
        "ngaySinhCuaVo": ngaySinhCuaVo?.toIso8601String(),
        "cmndCanCuocCuaVo": cmndCanCuocCuaVo,
        "canNang": canNang,
        "bghDuyetTrucTiep": bghDuyetTrucTiep,
      };
  static List<DonNghiPhepVienChuc> parse(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed
        .map<DonNghiPhepVienChuc>((json) => DonNghiPhepVienChuc.fromJson(json))
        .toList();
  }

  @override
  String toString() {
    return "$id, $loaiNghiPhep, $nguoiDuyet1, $nguoiDuyet2, $nguoiDuyet3, $nguoiDuyetCongDoan, $tuNgay, $denNgay, $soNgay, $trangThai, $lyDo, $ghiChu, $buoi, $ngayNguoiDuyet1, $ngayNguoiDuyet2, $ngayNguoiDuyet3, $ngayNguoiDuyetCongDoan, $ngayNguoiDuyetCongDoan, $ghiChuTraDon1, ,$ghiChuTraDon2, $ghiChuTraDon3, $ghiChuTraDonCongDoan, $vienChuc, $ngaySinhCon, $thaiSanDauOmTuNgay, $thaiSanDauOmDenNgay, $sinhThuongPhauThuat, $soCon, $taiBenhVien, $diemLuatBhxh, $conThu, $hoTenVo, $ngaySinhCuaVo, $cmndCanCuocCuaVo, $canNang, $bghDuyetTrucTiep, ";
  }
}

class LoaiNghiPhep {
  LoaiNghiPhep({
    required this.id,
    required this.tenLoaiNghiPhep,
  });

  final int? id;
  final String? tenLoaiNghiPhep;

  LoaiNghiPhep copyWith({
    int? id,
    String? tenLoaiNghiPhep,
  }) {
    return LoaiNghiPhep(
      id: id ?? this.id,
      tenLoaiNghiPhep: tenLoaiNghiPhep ?? this.tenLoaiNghiPhep,
    );
  }

  factory LoaiNghiPhep.fromJson(Map<String, dynamic> json) {
    return LoaiNghiPhep(
      id: json["id"],
      tenLoaiNghiPhep: json["tenLoaiNghiPhep"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "tenLoaiNghiPhep": tenLoaiNghiPhep,
      };

  @override
  String toString() {
    return "$id, $tenLoaiNghiPhep, ";
  }
}

class NguoiDuyet1 {
  NguoiDuyet1({
    required this.id,
    required this.ho,
    required this.tenDem,
    required this.ten,
  });

  final int? id;
  final String? ho;
  final String? tenDem;
  final String? ten;

  NguoiDuyet1 copyWith({
    int? id,
    String? ho,
    String? tenDem,
    String? ten,
  }) {
    return NguoiDuyet1(
      id: id ?? this.id,
      ho: ho ?? this.ho,
      tenDem: tenDem ?? this.tenDem,
      ten: ten ?? this.ten,
    );
  }

  factory NguoiDuyet1.fromJson(Map<String, dynamic> json) {
    return NguoiDuyet1(
      id: json["id"],
      ho: json["ho"],
      tenDem: json["tenDem"],
      ten: json["ten"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "ho": ho,
        "tenDem": tenDem,
        "ten": ten,
      };

  @override
  String toString() {
    return "$id, $ho, $tenDem, $ten, ";
  }
}

class VienChuc {
  VienChuc({
    required this.id,
    required this.ho,
    required this.tenDem,
    required this.ten,
    required this.maVienChuc,
    required this.chucVu,
    required this.donVi,
  });

  final int? id;
  final String? ho;
  final String? tenDem;
  final String? ten;
  final String? maVienChuc;
  final String? chucVu;
  final String? donVi;

  VienChuc copyWith({
    int? id,
    String? ho,
    String? tenDem,
    String? ten,
    String? maVienChuc,
    String? chucVu,
    String? donVi,
  }) {
    return VienChuc(
      id: id ?? this.id,
      ho: ho ?? this.ho,
      tenDem: tenDem ?? this.tenDem,
      ten: ten ?? this.ten,
      maVienChuc: maVienChuc ?? this.maVienChuc,
      chucVu: chucVu ?? this.chucVu,
      donVi: donVi ?? this.donVi,
    );
  }

  factory VienChuc.fromJson(Map<String, dynamic> json) {
    return VienChuc(
      id: json["id"],
      ho: json["ho"],
      tenDem: json["tenDem"],
      ten: json["ten"],
      maVienChuc: json["maVienChuc"],
      chucVu: json["chucVu"],
      donVi: json["donVi"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "ho": ho,
        "tenDem": tenDem,
        "ten": ten,
        "maVienChuc": maVienChuc,
        "chucVu": chucVu,
        "donVi": donVi,
      };

  @override
  String toString() {
    return "$id, $ho, $tenDem, $ten, $maVienChuc, $chucVu, $donVi, ";
  }
}
