import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:tvumobile/app/data/models/tasky/Task.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class TaskPdfView extends StatelessWidget {
  const TaskPdfView(this.resources, this.selectedIndex, {super.key});

  final List<TaskResource> resources;
  final int selectedIndex;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Get.theme;
    final resource = resources[selectedIndex];
    // Lấy tên tệp từ fileDinhKemPath
    String fileName = resource.fileDinhKemPath?.toString().split('\\').last ??
        "Tệp không tên";

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.primaryColor.withOpacity(0.95),
        centerTitle: true,
        toolbarHeight: 42,
        leading: IconButton(
          onPressed: () {
            Get.back();
          },
          icon: Icon(
            LucideIcons.arrowLeft,
            color: theme.colorScheme.onPrimary,
          ),
        ),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            MyText.titleMedium(
              "FILE ĐÍNH KÈM".toUpperCase(),
              color: theme.colorScheme.onPrimary,
            ),
          ],
        ),
        actions: [
          Icon(
            LucideIcons.fileMinus,
            size: 16,
            color: theme.colorScheme.onPrimary,
          ),
          MySpacing.width(20),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 10, right: 10),
            child: Column(
              children: [
                Row(
                  children: [
                    Text("File: "),
                    Text(
                      fileName.length > 30
                          ? "${fileName.substring(0, 30)}..."
                          : fileName,
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: SfPdfViewer.network(
              "https://tms.tvu.edu.vn/${resource.fileDinhKemPath}",
            ),
          ),
        ],
      ),
    );
  }
}
