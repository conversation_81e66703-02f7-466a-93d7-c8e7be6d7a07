import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_detail_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/helpers/date_time_extension.dart';

Widget HopDongThueXeTab(
    BuildContext context, DieuXeDetailController controller) {
  final theme = Theme.of(context);
  return ListView(
    shrinkWrap: true,
    children: [
      Padding(
        padding: EdgeInsets.only(top: 5),
        child: Column(
          children: [
            // -------------------------- ĐƠN VỊ THUÊ XE ---------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.building2,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  Si<PERSON><PERSON><PERSON>(
                    width: 120,
                    child: MyText.labelLarge(
                      " Đơn vị thuê xe:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.hopDongThueXe?.donVi!.tenDonVi ?? "",
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            // ------------------------ ĐƠN VỊ CHO THUÊ XE -------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.building,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Đơn vị cho thuê:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.hopDongThueXe?.donViChoThueXe ?? "",
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            // ----------------------------- LOẠI XE -------------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.car,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Loại xe:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.hopDongThueXe?.loaiXe ?? "",
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            // -------------------------- BIỂN KIỂM SOÁT ---------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.squareAsterisk,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Biển số:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.hopDongThueXe?.bienSo ?? "",
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            // ------------------------ NỘI DUNG HỢP ĐỒNG --------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.filePenLine,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Nội dung HĐ:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.hopDongThueXe?.noiDung ?? "",
                      color: Colors.black87,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            // ------------------------- GIÁ TRỊ HỢP ĐỒNG --------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.handCoins,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Giá trị hợp đồng:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.hopDongThueXe?.giaTriHopDong.toString() ??
                          "",
                      color: Colors.black87,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            // ----------------------------- NGÀY ĐI -------------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.calendarArrowUp,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Từ ngày:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.hopDongThueXe?.tuNgay!
                              .toFormat(format: "dd/MM/yyyy") ??
                          '-',
                      color: Colors.black87,
                      maxLines: 3,
                      softWrap: true,
                    ),
                  ),
                ],
              ),
            ),

            // ----------------------------- NGÀY VỀ -------------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.calendarArrowDown,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Đến ngày:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.hopDongThueXe?.denNgay!
                              .toFormat(format: "dd/MM/yyyy") ??
                          '-',
                      color: Colors.black87,
                      maxLines: 3,
                      softWrap: true,
                    ),
                  ),
                ],
              ),
            ),

            // ----------------------------- GHI CHÚ -------------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.penLine,
                    color: theme.primaryColor,
                    size: 15,
                  ),
                  SizedBox(
                    width: 120,
                    child: MyText.labelLarge(
                      " Ghi chú:",
                      color: theme.primaryColor,
                      fontWeight: 700,
                    ),
                  ),
                  Expanded(
                    child: MyText.bodyMedium(
                      controller.pdx.hopDongThueXe?.ghiChu ?? "",
                      color: Colors.black87,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      )
    ],
  );
}
