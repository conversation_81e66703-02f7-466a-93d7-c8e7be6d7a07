class DanhMuc {
  DanhMuc({
    required this.id,
    required this.loaiDanhMucId,
    required this.maDanhMuc,
    required this.tenDanhMuc,
  });

  final int? id;
  final int? loaiDanhMucId;
  final String? maDanhMuc;
  final String? tenDanhMuc;

  DanhMuc copyWith({
    int? id,
    int? loaiDanhMucId,
    String? maDanhMuc,
    String? tenDanhMuc,
  }) {
    return DanhMuc(
      id: id ?? this.id,
      loaiDanhMucId: loaiDanhMucId ?? this.loaiDanhMucId,
      maDanhMuc: maDanhMuc ?? this.maDanhMuc,
      tenDanhMuc: tenDanhMuc ?? this.tenDanhMuc,
    );
  }

  factory DanhMuc.fromJson(Map<String, dynamic> json){
    return DanhMuc(
      id: json["id"],
      loaiDanhMucId: json["loaiDanhMucId"],
      maDanhMuc: json["maDanhMuc"],
      tenDanhMuc: json["tenDanhMuc"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "loaiDanhMucId": loaiDanhMucId,
    "maDanhMuc": maDanhMuc,
    "tenDanhMuc": tenDanhMuc,
  };

  @override
  String toString(){
    return "$id, $loaiDanhMucId, $maDanhMuc, $tenDanhMuc, ";
  }

  static List<DanhMuc> parse(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed
        .map<DanhMuc>((json) => DanhMuc.fromJson(json))
        .toList();
  }
}
