import 'package:flutter_toggle_tab/flutter_toggle_tab.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tvumobile/app/features/tasky/components/build_tasklist.dart';
import 'package:tvumobile/app/features/tasky/components/empty_widget.dart';
import 'package:tvumobile/app/features/tasky/components/task_widgets.dart';
import 'package:tvumobile/app/features/tasky/components/taskyappbar.dart';
import 'package:tvumobile/app/features/tasky/controllers/taskdetail_controller.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';

class TaskDetailView extends GetView<TaskDetailController> {
  const TaskDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    DateFormat format = DateFormat("dd/MM/yyyy");
    Color? customColor = const Color(0xFF1157FA);
    return Scaffold(
      appBar: buildAppBarTaskyDetail(context, controller),
      body: GetBuilder<TaskDetailController>(
        builder: (controller) {
          if (controller.isGettingTask) {
            return const Center(
              child:
                  CircularProgressIndicator(), // Hiển thị loading rõ ràng hơn
            );
          }
          if (controller.task.id == null) {
            return const EmptyWidget(
              imageAsset: 'tasky/no_task.png',
              message: 'Không tìm thấy công việc',
            );
          }
          return RefreshIndicator(
            onRefresh: () async {
              // Gọi hàm làm mới dữ liệu
              await controller.apiGetTask();
            },
            child: SizedBox(
              height: MediaQuery.of(context).size.height - 100,
              child: ListView(children: [
                Padding(
                  padding:
                      EdgeInsets.only(top: 5, left: 2, right: 2, bottom: 16),
                  child:
                      buildTaskDetailList(context, controller, controller.task),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 16, right: 16),
                  child: FlutterToggleTab(
                    width: 90, // width in percent
                    borderRadius: 30,
                    height: 35,
                    selectedIndex: controller.tabTextIndexSelected,
                    selectedBackgroundColors: [customColor, customColor],
                    selectedTextStyle: TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.w700,
                    ),
                    unSelectedTextStyle: TextStyle(
                      color: Colors.black87,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    dataTabs: controller.listTextTabToggle,
                    selectedLabelIndex: (index) {
                      controller.updateTabIndex(index);
                    },
                    isScroll: false,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.only(left: 16.0, right: 16.0, top: 8.0),
                  // child: controller.navigation[controller.tabTextIndexSelected],
                  child: controller
                      .navigation[controller.tabTextIndexSelected](controller),
                ),
              ]),
            ),
          );
        },
      ),
    );
  }

  // Widget TaskCard(BuildContext context) {
  //   ThemeData theme = Theme.of(context);
  //   return Column(children: [
  //     Row(children: [
  //       Padding(
  //         padding: const EdgeInsets.only(top: 16),
  //         child: SizedBox(
  //           width: 72,
  //           child: AppIcon(
  //             icon: SvgIcons.tab_bar_tasks,
  //             color: theme.colorScheme.onSurface.withOpacity(0.6),
  //           ),
  //         ),
  //       ),
  //       Expanded(
  //         child: Padding(
  //           padding: const EdgeInsets.only(top: 26, right: 16),
  //           child: Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               Text(
  //                 'task',
  //               ),
  //               Obx(
  //                 () => Text(
  //                   controller.task.taskName!,
  //                   maxLines: 3,
  //                   overflow: TextOverflow.ellipsis,
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       ),
  //     ]),
  //     Padding(
  //       padding:
  //           const EdgeInsets.only(left: 72, bottom: 16, top: 16, right: 16),
  //       child: MyButton(child: Text("data")),
  //     ),
  //   ]);
  // }
}
