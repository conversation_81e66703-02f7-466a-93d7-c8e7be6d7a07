class PhieuDanhGiaChatLuongPhucVuModel {
  PhieuDanhGiaChatLuongPhucVuModel({
    required this.id,
    required this.maQrdonViId,
    required this.noiDungCongViec,
    required this.score,
    required this.comment,
    required this.tenNguoiDanhGia,
    required this.vienChucDanhGiaId,
    required this.sinhVienDanhGiaId,
    required this.nhomDanhGia,
    required this.ngayDanhGia,
    required this.maQrdonVi,
    required this.sinhVienDanhGia,
    required this.vienChucDanhGia,
  });

  final int? id;
  final num? maQrdonViId;
  final dynamic noiDungCongViec;
  final num? score;
  final String? comment;
  final String? tenNguoiDanhGia;
  final num? vienChucDanhGiaId;
  final dynamic sinhVienDanhGiaId;
  final dynamic nhomDanhGia;
  final DateTime? ngayDanhGia;
  final dynamic maQrdonVi;
  final dynamic sinhVienDanhGia;
  final dynamic vienChucDanhGia;

  PhieuDanhGiaChatLuongPhucVuModel copyWith({
    int? id,
    num? maQrdonViId,
    dynamic noiDungCongViec,
    num? score,
    String? comment,
    String? tenNguoiDanhGia,
    num? vienChucDanhGiaId,
    dynamic sinhVienDanhGiaId,
    dynamic nhomDanhGia,
    DateTime? ngayDanhGia,
    dynamic maQrdonVi,
    dynamic sinhVienDanhGia,
    dynamic vienChucDanhGia,
  }) {
    return PhieuDanhGiaChatLuongPhucVuModel(
      id: id ?? this.id,
      maQrdonViId: maQrdonViId ?? this.maQrdonViId,
      noiDungCongViec: noiDungCongViec ?? this.noiDungCongViec,
      score: score ?? this.score,
      comment: comment ?? this.comment,
      tenNguoiDanhGia: tenNguoiDanhGia ?? this.tenNguoiDanhGia,
      vienChucDanhGiaId: vienChucDanhGiaId ?? this.vienChucDanhGiaId,
      sinhVienDanhGiaId: sinhVienDanhGiaId ?? this.sinhVienDanhGiaId,
      nhomDanhGia: nhomDanhGia ?? this.nhomDanhGia,
      ngayDanhGia: ngayDanhGia ?? this.ngayDanhGia,
      maQrdonVi: maQrdonVi ?? this.maQrdonVi,
      sinhVienDanhGia: sinhVienDanhGia ?? this.sinhVienDanhGia,
      vienChucDanhGia: vienChucDanhGia ?? this.vienChucDanhGia,
    );
  }

  factory PhieuDanhGiaChatLuongPhucVuModel.fromJson(Map<String, dynamic> json){
    return PhieuDanhGiaChatLuongPhucVuModel(
      id: json["id"],
      maQrdonViId: json["maQrdonViId"],
      noiDungCongViec: json["noiDungCongViec"],
      score: json["score"],
      comment: json["comment"],
      tenNguoiDanhGia: json["tenNguoiDanhGia"],
      vienChucDanhGiaId: json["vienChucDanhGiaId"],
      sinhVienDanhGiaId: json["sinhVienDanhGiaId"],
      nhomDanhGia: json["nhomDanhGia"],
      ngayDanhGia: DateTime.tryParse(json["ngayDanhGia"] ?? ""),
      maQrdonVi: json["maQrdonVi"],
      sinhVienDanhGia: json["sinhVienDanhGia"],
      vienChucDanhGia: json["vienChucDanhGia"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "maQrdonViId": maQrdonViId,
    "noiDungCongViec": noiDungCongViec,
    "score": score,
    "comment": comment,
    "tenNguoiDanhGia": tenNguoiDanhGia,
    "vienChucDanhGiaId": vienChucDanhGiaId,
    "sinhVienDanhGiaId": sinhVienDanhGiaId,
    "nhomDanhGia": nhomDanhGia,
    "ngayDanhGia": ngayDanhGia?.toIso8601String(),
    "maQrdonVi": maQrdonVi,
    "sinhVienDanhGia": sinhVienDanhGia,
    "vienChucDanhGia": vienChucDanhGia,
  };

  @override
  String toString(){
    return "$id, $maQrdonViId, $noiDungCongViec, $score, $comment, $tenNguoiDanhGia, $vienChucDanhGiaId, $sinhVienDanhGiaId, $nhomDanhGia, $ngayDanhGia, $maQrdonVi, $sinhVienDanhGia, $vienChucDanhGia, ";
  }
}
