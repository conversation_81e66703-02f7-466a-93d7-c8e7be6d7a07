import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/components/luongthang.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/components/thuenam.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/components/thuethang.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/luongthuethang_controller.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/widgets/luongthang_drawer.dart';
import 'package:tvumobile/app/features/giangvien/luongthue/widgets/luongthangappbar.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class LuongThueView extends GetView<LuongThueController> {
  const LuongThueView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return GetBuilder<LuongThueController>(
        builder: (_) => Scaffold(
          key: controller.scaffoldKey,
          appBar: LuongThangAppBar(context, controller),
          endDrawer: buildLuongThangDrawer(context, controller),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildYearSelector(theme),
                MySpacing.height(8),
                if(controller.currentView.value != "TN")
                  _buildMonthSelector(theme),
                if(controller.currentView.value != "TN")
                  MySpacing.height(8),
                controller.currentView.value == "TO" ?
                buildLuongThangInfo(controller, theme) :
                controller.currentView.value == "TT" ?
                buildThueThangInfo(controller, theme) :
                buildThueNamInfo(controller, theme)
              ],
            )
          ),
        )
    );
  }

  Widget _buildYearSelector(ThemeData theme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildYearNavigationButton(
          theme,
          onTap: controller.currentYearMinus,
          icon: Icons.arrow_back_ios_new, label: "Trước".tr,
          isEnabled: controller.currentYear.value > 2006, islast: false,
        ),
        MyText.titleLarge(
          controller.currentView.value == "TN" ? "Năm ${controller.thueNam.value}" :
          "Năm ${controller.currentYear.value}",
          style: theme.textTheme.titleMedium!.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        _buildYearNavigationButton(
          theme,
          onTap: controller.currentYearPlus,
          icon: Icons.arrow_forward_ios,
          label: "Sau".tr,
          isEnabled: controller.currentView.value == "TN" ? controller.thueNam < controller.maxYear.value :
          controller.currentYear.value < controller.maxYear.value,
          islast: true,
        ),
      ],
    );
  }

  Widget _buildYearNavigationButton(ThemeData theme, {
    required VoidCallback onTap,
    required IconData icon,
    required String label,
    required bool isEnabled,
    required bool islast,
  }) {
    return GestureDetector(
      onTap: isEnabled ? onTap : null,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if(!islast)
            Icon(
              icon,
              size: 17,
              color: isEnabled ? theme.colorScheme.onSurface : theme
                  .disabledColor,
            ),
          if(!islast)
            const SizedBox(width: 4),
          MyText.labelLarge(
            label,
            color: isEnabled ? theme.colorScheme.onSurface : theme
                .disabledColor,
            fontWeight: 700,
          ),
          if(islast)
            const SizedBox(width: 4),
          if(islast)
            Icon(
              icon,
              size: 17,
              color: isEnabled ? theme.colorScheme.onSurface : theme
                  .disabledColor,
            ),
        ],
      ),
    );
  }

  Widget _buildMonthSelector(ThemeData theme) {
    return SizedBox(
      height: 70,
      child: ListView.separated(
        controller: controller.monthScroll,
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          final month = index + 1;
          return _buildMonthItem(theme, month);
        },
        separatorBuilder: (context, index) => const SizedBox(width: 2),
        itemCount: 12,
      ),
    );
  }

  Widget _buildMonthItem(ThemeData theme, int month) {
    return GestureDetector(
      onTap: () => controller.onMonthTap(month),
      child: Container(alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(7),
          color: _getMonthColor(theme, month),
          border: Border.all(
            color: theme.scaffoldBackgroundColor,
          ),
        ),
        padding: const EdgeInsets.all(8),
        child: SizedBox(
          child: Column(
            children: [
              MyText.labelSmall(
                "tháng",
                textAlign: TextAlign.center,
                color: theme.colorScheme.onPrimary,
              ),
              MyText.titleLarge(
                "$month",
                textAlign: TextAlign.center,
                color: theme.colorScheme.onPrimary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getMonthColor(ThemeData theme, int month) {
    if (controller.currentYear.value == controller.maxYear.value) {
      if (month > controller.maxMonth) {
        return theme.primaryColor.withOpacity(0.25);
      }
      if (month == controller.currentMonth.value) {
        return theme.primaryColor;
      }
      return theme.primaryColor.withOpacity(0.7);
    } else {
      if (month == controller.currentMonth.value) {
        return theme.primaryColor;
      }
      return theme.primaryColor.withOpacity(0.7);
    }
  }
}