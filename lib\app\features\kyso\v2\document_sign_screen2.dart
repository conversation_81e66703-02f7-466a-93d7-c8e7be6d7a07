// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
// ignore: library_prefixes
import 'package:pdf_render/pdf_render_widgets.dart' as pdfRender;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tvumobile/app/features/kyso/components/intro.dart';
import 'package:tvumobile/app/features/kyso/v2/helpers/signature_dialog.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

import 'dialog_utils.dart';
import 'pdf_viewer_controller.dart';
import 'resizeble.dart';
import 'signature_model.dart';
import 'utils.dart';

class PdfViewerScreen extends GetView<PdfViewerController> {
  final File? pdfFile;
  final String? pdfUrl;
  final String? signatureUrl;
  //controller = Get.put(PdfViewerController());
  //final signatureController = Get.put(SignatureController());

  PdfViewerScreen({
    super.key,
    this.pdfFile,
    this.pdfUrl,
    this.signatureUrl,
  }) {
    // if (pdfUrl != null) {
    //   controller.loadInitFiles(pdfUrl);
    // }
    // if (signatureUrl != null) {
    //   controller.loadInitSign(signatureUrl);
    // }
  }

  // key shared signature
  final keySignature = 'key_signature';
  final keyListRecentFile = 'key_list_recent_file';
  TapDownDetails? doubleTapDetails;
  final pdfRender.PdfViewerController pdfVieweCtrl =
      pdfRender.PdfViewerController();
  int currentPage = 1;
  Size documentSize = const Size(0, 0);
  Size documentViewSize = const Size(0, 0);
  double currentZoomRatio = 1.0;

  @override
  Widget build(BuildContext context) {
    //File pdfFile2 = File.

    // Listen to controller state changes
    ever(controller.pageSize, (Size value) => documentSize = value);
    ever(controller.rect, (Rect? value) {
      if (value != null) {
        documentViewSize = value.size;
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('PDF Viewer'),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => showHelpDialog(context),
            // Navigate to intro guide screen
          ),
          // Action to assign signature
          IconButton(
            icon: Icon(MdiIcons.fountainPenTip),
            onPressed: () async {
              controller.onSelectedSignature(onSelected: false);
            },
          ),

          // Action to save signed pdf file
          Obx(() {
            if (controller.listSignature.isNotEmpty) {
              return IconButton(
                onPressed: () async {
                  bool pathDocument = await controller.addSigantureToPdf();

                  controller.onSelectedSignature(onSelected: false);

                  if (pathDocument) {
                    //TODO: san sang ky
                  } else {
                    await showInfoDialog(
                      context: context,
                      onOKButtonPressed: () => Navigator.pop(context),
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.only(
                            top: 10,
                            bottom: 20,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                MdiIcons.fileAlert,
                                color: Colors.red,
                                size: 32,
                              ),
                              SizedBox(width: 5),
                              Text(
                                "Failed to save",
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.red,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }
                },
                icon: Icon(MdiIcons.downloadCircleOutline),
              );
            } else {
              return const SizedBox(
                key: ValueKey('Action Save'),
              );
            }
          })
        ],
      ),
      body: Obx(() {
        if (controller.pdfBytesDocument.value == null) {
          return Center(
            child: CircularProgressIndicator(
              color: Theme.of(context).colorScheme.secondary,
            ),
          );
        }
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: controller.toolbarSize,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey,
                    width: 1.0,
                  ),
                  top: BorderSide(
                    color: Colors.grey,
                    width: 1.0,
                  ),
                ),
              ),
              child: Obx(() {
                if (controller.viewerInited.value) {
                  return buildHeader();
                }
                return Container();
              }),
            ),
            Container(
              padding: EdgeInsets.all(0),
              height: MediaQuery.of(context).size.height -
                  controller.toolbarSize -
                  kToolbarHeight -
                  100,
              child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  // Supporting double-tap gesture on the viewer.
                  onDoubleTapDown: (details) => doubleTapDetails = details,
                  onDoubleTap: () {
                    if (pdfVieweCtrl.zoomRatio > 1) {
                      pdfVieweCtrl.ready?.setZoomRatio(
                        zoomRatio: 1,
                      );
                      currentZoomRatio = pdfVieweCtrl.ready?.zoomRatio ?? 0;
                    } else {
                      pdfVieweCtrl.ready?.setZoomRatio(
                        zoomRatio: pdfVieweCtrl.zoomRatio * 1.8,
                        center: doubleTapDetails!.localPosition,
                      );
                      currentZoomRatio = pdfVieweCtrl.ready?.zoomRatio ?? 0;
                    }
                  },
                  // onLongPressStart: (details) {
                  //   print(
                  //       "onLongPressEnd: ${details.localPosition}, ${details.globalPosition}");
                  //   controller.getSignature(
                  //       currentPage: currentPage,
                  //       documentSize: documentSize,
                  //       documentViewSize: documentViewSize,
                  //       localPosition: details.localPosition);
                  // },
                  // // Action to unselect signature or implement signature
                  // onTapUp: (details) {
                  //   print(
                  //       "onTapUp: ${details.localPosition}, ${details.globalPosition}");
                  //   controller.onSelectedSignature(onSelected: false);
                  // },
                  child: pdfRender.PdfViewer.openData(
                    key: controller.pdfWidgetKey,
                    controller.pdfBytesDocument.value!,
                    viewerController: pdfVieweCtrl,
                    params: pdfRender.PdfViewerParams(
                      padding: controller.pdwViewerPadding,
                      // onPageTapUp: (details, pageNumber, pageRect) {
                      //   // print("*" * 20);
                      //   // print("onPageTapUp:");
                      //   // print(details);
                      //   // print("onPageTapUp: $pageNumber, $pageRect");
                      //   // print("*" * 20);
                      //   controller.onSelectedSignature(onSelected: false);
                      // },
                      // onPageLongPressStart: (details, pageNumber, pageRect) {
                      //   print("*" * 20);
                      //   print("onPageLongPressStart:");
                      //   print(
                      //       "Details: ${details.localPosition}, ${details.globalPosition}");
                      //   print("onPageLongPressStart: $pageNumber, $pageRect");
                      //   print("*" * 20);
                      //   controller.getSignature(
                      //       currentPage: pageNumber,
                      //       documentSize: documentSize,
                      //       documentViewSize: documentViewSize,
                      //       localPosition: details.localPosition);
                      // },
                      // Enables zooming
                      panEnabled: true,
                      scaleEnabled: true,
                      layoutPages: (contentViewSize, pageSizes) {
                        //debugPrint("layoutPages: $contentViewSize, $pageSizes");
                        // Change PDF page layout
                        return Utils.changePdfPageLayout(
                          contentViewSize: contentViewSize,
                          pageSizes: pageSizes,
                        );
                      },
                      onInteractionEnd: (details) {
                        // Get page size document when interaction end
                        if (details.scaleVelocity != 0) {
                          controller.getPageSizeDocument();
                        }
                        debugPrint(
                            "onInteractionEnd: ${details.velocity}, Pointer: ${pdfVieweCtrl.ready?.zoomRatio}");
                        if (pdfVieweCtrl.ready?.zoomRatio != currentZoomRatio) {
                          currentZoomRatio = pdfVieweCtrl.ready?.zoomRatio ?? 0;
                        } else if (currentZoomRatio == 1.0) {
                          pdfVieweCtrl.goToPointInPage(
                              pageNumber: currentPage,
                              x: 0,
                              y: 0,
                              zoomRatio: pdfVieweCtrl.ready?.zoomRatio ??
                                  currentZoomRatio,
                              anchor: pdfRender.PdfViewerAnchor.top);
                        }
                      },
                      onViewerControllerInitialized: (p0) {
                        Rect? pageRect = pdfVieweCtrl.ready?.getPageRect(1);
                        controller.viewerInited.value = true;
                        p0.addListener(() {
                          // Listen for page document changes
                          final currentPagePdf = p0.ready?.currentPageNumber;
                          currentZoomRatio = pdfVieweCtrl.ready?.zoomRatio ?? 0;
                          currentPage = currentPagePdf!;

                          pageRect =
                              pdfVieweCtrl.ready?.getPageRect(currentPagePdf);
                          // Listen current page value and rect page value
                          controller.loadFilePdf(
                              currentPage: currentPagePdf, rect: pageRect);
                        });

                        // Enter rect page value when viewer controller initialized
                        controller.loadFilePdf(
                            rect: pageRect,
                            viewRect: pdfVieweCtrl.ready?.viewRect,
                            viewOffset: pdfVieweCtrl.ready?.offset);

                        // Get page size document when viewer controller initialized
                        print("call getPageSizeDocument outside listen");
                        controller.getPageSizeDocument();

                        // Enter total page when viewer controller initialized
                        controller.getTotalPage(p0.ready?.pageCount);
                      },
                      buildPageOverlay: (context, pageNumber, pageRect) =>
                          Obx(() {
                        debugPrint(
                            "Pagenumber: $pageNumber, Current page rect: $pageRect");
                        debugPrint(
                            "List sign: ${controller.listSignature.isNotEmpty}");
                        //print(controller.listSignature[0].currentPage);
                        if (controller.listSignature.isNotEmpty) {
                          debugPrint(
                              "buildPageOverlay: ${controller.listSignature.length} , pageNumber: $pageNumber");
                          // Filter Signature by current page
                          final filterSignature = controller.listSignature
                              .where((e) => e.currentPage == pageNumber)
                              .toList();
                          debugPrint("Filtered: $filterSignature");
                          if (filterSignature.isNotEmpty) {
                            print(
                                "filterSignature ${filterSignature[0].fixedLeft} ,${filterSignature[0].fixedTop}");
                          }
                          double addedH = 1;
                          double baseFontSize = 12.0;
                          double textHeight = 0;
                          if (filterSignature.isNotEmpty) {
                            var tl = 0;
                            if (controller.displayFullName) {
                              tl += 1;
                            }
                            if (controller.displayDatetime) {
                              tl += 1;
                            }
                            if (controller.displayDepartment) {
                              tl += 1;
                            }
                            if (controller.displayPosition) {
                              tl += 1;
                            }
                            textHeight = tl * (baseFontSize);
                          }
                          addedH = textHeight + 1;
                          double addedW = 0;
                          if (controller.stampStyle) {
                            addedH = 0;
                            addedW = textHeight;
                          }
                          return Stack(
                            children: filterSignature
                                .map(
                                  (e) => Resizeble(
                                    key: ValueKey(e.id),
                                    unSelect: e.onSelected!,
                                    fullname: controller.displayFullName
                                        ? e.signerFullName
                                        : '',
                                    width: e.fixedWidth,
                                    height: e.fixedHeight!,
                                    top: e.fixedTop,
                                    left: e.fixedLeft,
                                    onPressDelete: () {
                                      controller.deleteSignatureScaling(
                                          signature: e);
                                    },
                                    onSelected: () {
                                      // Get object Signature
                                      controller.onModelSignatureChange(
                                        signatureModel: e,
                                      );

                                      // Selected signature
                                      controller.onSelectedSignature(
                                        onSelected: true,
                                        signatureModel: e,
                                      );
                                    },
                                    documentWidthSize: e.documentSize?.width,
                                    documentHeightSize: e.documentSize?.height,
                                    documentWidthView:
                                        e.documentViewSize?.width,
                                    documentHeightView:
                                        e.documentViewSize?.height,
                                    onFromLTWH: (scaleLeft,
                                        scaleTop,
                                        scaleWidth,
                                        scaleHeight,
                                        left,
                                        top,
                                        width,
                                        height) {
                                      if (e.onSelected!) {
                                        controller.replaceValueLTWH(
                                          signatureModel: e,
                                          scaleLeftValue: scaleLeft,
                                          scaleTopValue: scaleTop,
                                          scaleWidthValue: scaleWidth,
                                          scaleHeightValue: scaleHeight,
                                          fixedLeftValue: left,
                                          fixedTopValue: top,
                                          fixedWidthValue: width,
                                          fixedHeightValue: height,
                                        );
                                      }
                                    },
                                    child: Image.memory(
                                      e.signature!,
                                      fit: BoxFit.fill,
                                      color: (e.onSelected == true)
                                          ? Colors.blue[800]
                                          : null,
                                    ),
                                    onDragChild: (left, top) {
                                      controller.replaceValueLTWH(
                                        signatureModel: e,
                                        fixedLeftValue: left,
                                        fixedTopValue: top,
                                      );
                                      controller.update();
                                    },
                                    onResizeChild: (left, top, width, heigth) {
                                      controller.replaceValueLTWH(
                                        signatureModel: e,
                                        fixedLeftValue: left,
                                        fixedTopValue: top,
                                        fixedWidthValue: width,
                                        fixedHeightValue: heigth,
                                      );
                                      controller.update();
                                    },
                                  ),
                                )
                                .toList(),
                          );
                        }
                        return const SizedBox(key: ValueKey('SizedBox'));
                      }),

                      // Displays an indicator when the file has not loaded
                      buildPagePlaceholder: (context, pageNumber, pageRect) =>
                          Center(
                        child: CircularProgressIndicator(
                          color: Theme.of(context).colorScheme.secondary,
                        ),
                      ),
                    ),
                  )),
            ),
          ],
        );
      }),
      floatingActionButton: Obx(() => SafeArea(
            child: (controller.totalPage.value != 1)
                ? FloatingActionButton(
                    onPressed: () => pdfVieweCtrl.ready?.goToPage(
                      pageNumber: pdfVieweCtrl.pageCount,
                    ),
                    child: Icon(MdiIcons.pageLast, color: Colors.white),
                  )
                : const SizedBox(),
          )),
    );
  }

  /// This function is used to display thumbnails and some actions like share and open file
  ///
  /// [context] A handle to the location of a widget in the widget tree.
  /// [pathDocument] get document path
  Future<void> dialogThumbnailPdf({
    required BuildContext context,
    required String pathDocument,
  }) async {
    showInfoDialog(
      onOKButtonPressed: () => Navigator.pop(context),
      title: pathDocument.split('/').last,
      textTitleAlign: TextAlign.left,
      subTitle: 'Data saved successfully',
      colorSubtitle: Colors.green,
      textSubTitleAlign: TextAlign.left,
      context: context,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Preview pdf file
            Container(
              margin: const EdgeInsets.only(right: 20.0),
              height: MediaQuery.of(context).size.height * 0.2,
              width: MediaQuery.of(context).size.width * 0.3,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: Colors.black),
              ),
              // child: Thumbnail(
              //   dataResolver: () async =>
              //       await File(pathDocument).readAsBytes(),
              //   mimeType: 'application/pdf',
              //   widgetSize: MediaQuery.of(context).size.height * 0.19,
              // ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Action to share pdf file
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    visualDensity: const VisualDensity(
                      horizontal: -4,
                      vertical: -4,
                    ),
                  ),
                  //onPressed: () => Share.shareFiles([pathDocument]),
                  onPressed: () {},
                  child: Row(
                    children: [
                      Icon(
                        MdiIcons.shareVariantOutline,
                        color: Theme.of(context).colorScheme.secondary,
                        size: 24,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      const Text(
                        'Share',
                        style: TextStyle(
                          fontSize: 16.0,
                          color: Colors.black,
                        ),
                      )
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),

                // Action to open pdf file
                TextButton(
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    visualDensity: const VisualDensity(
                      horizontal: -4,
                      vertical: -4,
                    ),
                  ),
                  //onPressed: () => OpenFile.open(pathDocument),
                  onPressed: () {},
                  child: Row(
                    children: [
                      Icon(
                        MdiIcons.folderOpenOutline,
                        color: Theme.of(context).colorScheme.secondary,
                        size: 24,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      const Text(
                        'Open File',
                        style: TextStyle(
                          fontSize: 16.0,
                          color: Colors.black,
                        ),
                      )
                    ],
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget buildHeader() {
    ThemeData theme = Theme.of(Get.context!);
    return Container(
      padding: EdgeInsets.only(top: 8),
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              MyText.bodyMedium('Chữ ký \nhiện tại:'),
              Obx(() {
                if (controller.signatureBytes.value == null) {
                  return SizedBox();
                }
                return Image.memory(
                  controller.signatureBytes.value!,
                  height: 50,
                  width: 120,
                );
              }),
              MyButton.small(
                onPressed: () {
                  showSignatureDialogue(
                      Get.context!,
                      controller.setTextDisplay,
                      controller.setStampStyle,
                      controller.availableSignatures,
                      controller.onSignatureSaved);
                },
                borderRadiusAll: 5,
                child: MyText.labelMedium('Chọn chữ ký khác',
                    color: theme.colorScheme.onPrimary),
              ),
            ],
          ),
          SizedBox(
            height: 5,
          ),
          ClipRRect(
            borderRadius: BorderRadius.circular(4.0),
            child: Container(
              margin: const EdgeInsets.only(bottom: 4.0),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey,
                    blurRadius: 8,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              height: 35,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                      'Trang: ${controller.currentPage.value}/${pdfVieweCtrl.pageCount}'),
                  SizedBox(width: 10),
                  IconButton(
                    visualDensity: VisualDensity(horizontal: -4, vertical: -4),
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          currentPage <= 1 ? Colors.grey : Colors.blue),
                    ),
                    icon: Icon(
                      Icons.first_page,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      currentPage = 1;
                      pdfVieweCtrl.goToPointInPage(
                          pageNumber: 1,
                          x: 0,
                          y: 0,
                          anchor: pdfRender.PdfViewerAnchor.top);
                      controller.currentPage.value = 1;
                      controller.update();
                    },
                  ),
                  SizedBox(width: 2),
                  IconButton(
                    visualDensity: VisualDensity(horizontal: -4, vertical: -4),
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          currentPage <= 1 ? Colors.grey : Colors.blue),
                    ),
                    icon: Icon(
                      Icons.navigate_before,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      currentPage -= 1;
                      pdfVieweCtrl.goToPointInPage(
                          pageNumber: currentPage,
                          x: 0,
                          y: 0,
                          anchor: pdfRender.PdfViewerAnchor.top);
                      controller.currentPage.value = currentPage;
                      controller.update();
                    },
                  ),
                  SizedBox(width: 2),
                  IconButton(
                    visualDensity: VisualDensity(horizontal: -4, vertical: -4),
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          pdfVieweCtrl.pageCount == currentPage
                              ? Colors.grey
                              : Colors.blue),
                    ),
                    icon: Icon(
                      Icons.navigate_next,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      currentPage += 1;
                      pdfVieweCtrl.goToPointInPage(
                          pageNumber: currentPage,
                          x: 0,
                          y: 0,
                          anchor: pdfRender.PdfViewerAnchor.top);
                      controller.currentPage.value = currentPage;
                      controller.update();
                    },
                  ),
                  SizedBox(width: 2),
                  IconButton(
                    visualDensity: VisualDensity(horizontal: -4, vertical: -4),
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(
                          pdfVieweCtrl.pageCount == controller.currentPage.value
                              ? Colors.grey
                              : Colors.blue),
                    ),
                    icon: Icon(
                      Icons.last_page,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      currentPage = pdfVieweCtrl.pageCount;
                      pdfVieweCtrl.goToPointInPage(
                          pageNumber: currentPage,
                          x: 0,
                          y: 0,
                          anchor: pdfRender.PdfViewerAnchor.top);
                      controller.currentPage.value = currentPage;
                      controller.update();
                    },
                  ),
                  SizedBox(width: 8),
                  IconButton(
                    visualDensity: VisualDensity(horizontal: -4, vertical: -4),
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Colors.lightBlue),
                    ),
                    icon: Icon(
                      controller.viewerZoom.value <= 1
                          ? Icons.zoom_out_map
                          : Icons.zoom_in_map,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      controller.viewerZoom.value =
                          controller.viewerZoom.value <= 1 ? 2 : 1;
                      pdfVieweCtrl.setZoomRatio(
                          zoomRatio: controller.viewerZoom.value);
                      controller.update();
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildSignatureWidget(SignatureModel signature) {
    var theme = Theme.of(Get.context!);
    List<Widget> textWidgets = [];
    // Calculate font size based on signature width for responsive text
    double baseFontSize = 12.0;
    double responsiveFontSize =
        math.max(baseFontSize * ((signature.scaleHeight ?? 150) / 200), 8.0);

    if (signature.isStampStyle ?? false) {
      textWidgets = [
        MyText.bodyMedium(
          'Người ký: ${signature.signerFullName ?? ''}',
          color: Colors.red,
          maxLines: 1,
          textAlign: TextAlign.left,
          overflow: TextOverflow.visible,
          fontSize: responsiveFontSize,
        ),
        MyText.bodyMedium(
          'Ngày ký: ${signature.signerDatetime ?? DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
          color: Colors.red,
          maxLines: 1,
          textAlign: TextAlign.left,
          overflow: TextOverflow.visible,
          fontSize: responsiveFontSize,
        ),
        MyText.bodyMedium(
          'Chức danh: ${signature.signerPosition ?? ''}',
          color: Colors.red,
          maxLines: 1,
          textAlign: TextAlign.left,
          overflow: TextOverflow.visible,
          fontSize: responsiveFontSize,
        ),
        MyText.bodyMedium(
          'Đơn vị: ${signature.signerDepartment ?? ''}',
          color: Colors.red,
          maxLines: 1,
          textAlign: TextAlign.left,
          overflow: TextOverflow.visible,
          fontSize: responsiveFontSize,
        ),
      ];
    } else {
      // Default signature text logic - only display if flags are set
      if (controller.displayFullName == true &&
          signature.signerFullName != null &&
          signature.signerFullName!.isNotEmpty) {
        textWidgets.add(MyText.bodyMedium(
          signature.signerFullName!,
          color: Colors.black,
          fontWeight: 900,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.visible,
          fontSize: responsiveFontSize,
        ));
      }
      if (controller.displayDatetime == true &&
          signature.signerDatetime != null &&
          signature.signerDatetime!.isNotEmpty) {
        textWidgets.add(MyText.bodyMedium(
          'Ngày ký: ${signature.signerDatetime!}',
          color: Colors.black,
          fontWeight: 900,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.visible,
          fontSize: responsiveFontSize,
        ));
      }
      if (controller.displayPosition == true &&
          signature.signerPosition != null &&
          signature.signerPosition!.isNotEmpty) {
        textWidgets.add(MyText.bodyMedium(
          'Chức danh: ${signature.signerPosition!}',
          color: Colors.black,
          fontWeight: 900,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.visible,
          fontSize: responsiveFontSize,
        ));
      }
      if (controller.displayDepartment == true &&
          signature.signerDepartment != null &&
          signature.signerDepartment!.isNotEmpty) {
        textWidgets.add(MyText.bodyMedium(
          'Đơn vị: ${signature.signerDepartment!}',
          color: Colors.black,
          fontWeight: 900,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.visible,
          fontSize: responsiveFontSize,
        ));
      }
    }
    return Column(
      children: [
        if (signature.isStampStyle ?? false)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.red, width: 2),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: textWidgets,
            ),
          )
        else
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                alignment: Alignment.center,
                child: Image.memory(
                  signature.signature!,
                  fit: BoxFit.contain,
                ),
              ),
              if (textWidgets.isNotEmpty)
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: textWidgets,
                ),
            ],
          ),
      ],
    );
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Image.memory(
          signature.signature!,
          fit: BoxFit.fill,
          color:
              (signature.onSelected == true) ? Colors.blue[800] : Colors.black,
        ),
        if (textWidgets.isNotEmpty)
          Column(
            mainAxisSize: MainAxisSize.min,
            children: textWidgets,
          ),
      ],
    );
  }
}
