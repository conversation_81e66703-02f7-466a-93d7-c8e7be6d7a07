import 'package:flutter/material.dart';

class TextDimensions {
  final double width;
  final double height;

  TextDimensions({required this.width, required this.height});
}

TextDimensions measureTextSize({
  required String text,
  required TextStyle style,
  required BuildContext context,
  double maxWidth = double.infinity,
  int? maxLines,
}) {
  // Create a TextPainter object
  final TextPainter textPainter = TextPainter(
    text: TextSpan(
      text: text,
      style: style,
    ),
    maxLines: maxLines,
    textDirection: TextDirection.ltr,
  );

  // Layout the text with constraints
  textPainter.layout(
    minWidth: 0,
    maxWidth: maxWidth,
  );

  // Return the dimensions
  return TextDimensions(
    width: textPainter.width,
    height: textPainter.height,
  );
}
