import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/tms/congvan/cong_van_nhan.dart';
import 'package:tvumobile/app/features/giangvien/congvan/single_pdfviewer_screen.dart';
import 'package:tvumobile/app/utils/helpers/pagination_filter.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';

class CongVanController extends GetxController {
  ApiProvider apiProvider = Get.find();
  final _congVanNhan = <TmsCongVanModel>[].obs;
  final _paginationFilter = PaginationFilter().obs;
  final _lastPage = false.obs;
  List<TmsCongVanModel> get congVanNhan => _congVanNhan.toList();
  int get limit => _paginationFilter.value.limit;
  int get _page => _paginationFilter.value.page;
  int get _docType => _paginationFilter.value.docType;
  bool get lastPage => _lastPage.value;
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final appbarTitle = "Tất cả văn bản".obs;
  UserInfo? userInfo;

  TextEditingController searchController = TextEditingController();
  String searchQuery = "";
  Timer? debounce;

  @override
  void onInit() {
    super.onInit();
    userInfo = LocalStorageServices.getUserInfo();
    ever(_paginationFilter, (_) => _getCongVanNhan());
  }

  @override
  void onReady() {
    _changePaginationFilter(1, 15, _docType);
  }

  void openDrawer() {
    scaffoldKey.currentState?.openDrawer();
  }

  void closeDrawer() {
    if (scaffoldKey.currentState!.isEndDrawerOpen) {
      scaffoldKey.currentState!.closeEndDrawer();
    } else {
      scaffoldKey.currentState!.closeDrawer();
    }
  }

  Future<void> _getCongVanNhan() async {
    final congVanNhanData = await apiProvider.loadCongVanNhan(pagefileter: _paginationFilter.value);
    if (congVanNhanData.isEmpty) {
      _lastPage.value = true;
    }
    _congVanNhan.addAll(congVanNhanData);
  }

  void changeTotalPerPage(int limitValue) {
    _congVanNhan.clear();
    _lastPage.value = false;
    _changePaginationFilter(_page, limitValue, _docType);
  }

  void changeDocType(int doctype) {
    _congVanNhan.clear();
    _lastPage.value = false;
    appbarTitle.value = DocTypeId.defaultText[doctype]!;
    _changePaginationFilter(
      _page,
      limit,
      doctype
    );
    closeDrawer();
  }

  void _changePaginationFilter(int page, int limit, int docType) {
    _paginationFilter.update((val) {
      val?.page = page;
      val?.limit = limit;
      val?.docType = docType;
      val?.searchQuery = searchQuery;
    });
  }

  void loadNextPage() => _changePaginationFilter(_page + 1, limit, _docType);
  void reFresh() => _changePaginationFilter(1, limit, _docType);

  void onFileTap(List<CvFile> cvFiles, int findex) {
    if (kDebugMode) {
      print(cvFiles.length);
    }
    Get.to(() => SinglePdfViewerScreen(cvFiles, findex), transition: Transition.fadeIn);
  }

  void loadCVBySearch(String value) {
    _congVanNhan.clear();
    _lastPage.value = false;
    searchQuery = value;
    _changePaginationFilter(1, limit, _docType);
  }

}