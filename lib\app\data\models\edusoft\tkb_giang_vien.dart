import 'dart:convert';

class TkbGiangVien {
  TkbGiangVien({
    required this.totalItems,
    required this.totalPages,
    required this.dsTietTrongNgay,
    required this.dsTuanTkb,
    required this.isDuocDiemDanh,
    required this.isDuocDkNghiDay,
    required this.isQuanLyHoc<PERSON>ieu,
    required this.isShowTiet,
  });

  final int? totalItems;
  final int? totalPages;
  final List<DsTietTrongNgay> dsTietTrongNgay;
  final List<DsTuanTkb> dsTuanTkb;
  final bool? isDuocDiemDanh;
  final bool? isDuocDkNghiDay;
  final bool? isQuanLyHocLieu;
  final bool? isShowTiet;

  TkbGiangVien copyWith({
    int? totalItems,
    int? totalPages,
    List<DsTietTrongNgay>? dsTietTrongNgay,
    List<DsTuanTkb>? dsTuanTkb,
    bool? isDuocDiemDanh,
    bool? isDuocDkNghiDay,
    bool? isQuanLyHoc<PERSON>ieu,
    bool? isShowTiet,
  }) {
    return TkbGiangVien(
      totalItems: totalItems ?? this.totalItems,
      totalPages: totalPages ?? this.totalPages,
      dsTietTrongNgay: dsTietTrongNgay ?? this.dsTietTrongNgay,
      dsTuanTkb: dsTuanTkb ?? this.dsTuanTkb,
      isDuocDiemDanh: isDuocDiemDanh ?? this.isDuocDiemDanh,
      isDuocDkNghiDay: isDuocDkNghiDay ?? this.isDuocDkNghiDay,
      isQuanLyHocLieu: isQuanLyHocLieu ?? this.isQuanLyHocLieu,
      isShowTiet: isShowTiet ?? this.isShowTiet,
    );
  }

  factory TkbGiangVien.fromJson(Map<String, dynamic> json) {
    return TkbGiangVien(
      totalItems: json["total_items"],
      totalPages: json["total_pages"],
      dsTietTrongNgay: json["ds_tiet_trong_ngay"] == null
          ? []
          : List<DsTietTrongNgay>.from(json["ds_tiet_trong_ngay"]!
              .map((x) => DsTietTrongNgay.fromJson(x))),
      dsTuanTkb: json["ds_tuan_tkb"] == null
          ? []
          : List<DsTuanTkb>.from(
              json["ds_tuan_tkb"]!.map((x) => DsTuanTkb.fromJson(x))),
      isDuocDiemDanh: json["is_duoc_diem_danh"],
      isDuocDkNghiDay: json["is_duoc_dk_nghi_day"],
      isQuanLyHocLieu: json["is_quan_ly_hoc_lieu"],
      isShowTiet: json["is_show_tiet"],
    );
  }

  Map<String, dynamic> toJson() => {
        "total_items": totalItems,
        "total_pages": totalPages,
        "ds_tiet_trong_ngay": dsTietTrongNgay.map((x) => x.toJson()).toList(),
        "ds_tuan_tkb": dsTuanTkb.map((x) => x.toJson()).toList(),
        "is_duoc_diem_danh": isDuocDiemDanh,
        "is_duoc_dk_nghi_day": isDuocDkNghiDay,
        "is_quan_ly_hoc_lieu": isQuanLyHocLieu,
        "is_show_tiet": isShowTiet,
      };

  @override
  String toString() {
    return "$totalItems, $totalPages, $dsTietTrongNgay, $dsTuanTkb, $isDuocDiemDanh, $isDuocDkNghiDay, $isQuanLyHocLieu, $isShowTiet, ";
  }

  String toJsonString() => json.encode(toJson());
  factory TkbGiangVien.fromJsonString(String source) =>
      TkbGiangVien.fromJson(json.decode(source));

  static List<TkbGiangVien> parseNews(List<dynamic> responseBody) {
    final parsed = responseBody.cast<Map<String, dynamic>>();
    return parsed
        .map<TkbGiangVien>((json) => TkbGiangVien.fromJson(json))
        .toList();
  }
}

class DsTietTrongNgay {
  DsTietTrongNgay({
    required this.tiet,
    required this.gioBatDau,
    required this.gioKetThuc,
    required this.soPhut,
    required this.nhhk,
  });

  final int? tiet;
  final String? gioBatDau;
  final dynamic gioKetThuc;
  final int? soPhut;
  final int? nhhk;

  DsTietTrongNgay copyWith({
    int? tiet,
    String? gioBatDau,
    dynamic gioKetThuc,
    int? soPhut,
    int? nhhk,
  }) {
    return DsTietTrongNgay(
      tiet: tiet ?? this.tiet,
      gioBatDau: gioBatDau ?? this.gioBatDau,
      gioKetThuc: gioKetThuc ?? this.gioKetThuc,
      soPhut: soPhut ?? this.soPhut,
      nhhk: nhhk ?? this.nhhk,
    );
  }

  factory DsTietTrongNgay.fromJson(Map<String, dynamic> json) {
    return DsTietTrongNgay(
      tiet: json["tiet"],
      gioBatDau: json["gio_bat_dau"],
      gioKetThuc: json["gio_ket_thuc"],
      soPhut: json["so_phut"],
      nhhk: json["nhhk"],
    );
  }

  Map<String, dynamic> toJson() => {
        "tiet": tiet,
        "gio_bat_dau": gioBatDau,
        "gio_ket_thuc": gioKetThuc,
        "so_phut": soPhut,
        "nhhk": nhhk,
      };

  @override
  String toString() {
    return "$tiet, $gioBatDau, $gioKetThuc, $soPhut, $nhhk, ";
  }
}

class DsTuanTkb {
  DsTuanTkb({
    required this.nhhk,
    required this.tuanHocKy,
    required this.tuanTuyetDoi,
    required this.thongTinTuan,
    required this.ngayBatDau,
    required this.ngayKetThuc,
    required this.dsThoiKhoaBieu,
    required this.dsIdThoiKhoaBieuTrung,
  });

  final int? nhhk;
  final int? tuanHocKy;
  final int? tuanTuyetDoi;
  final String? thongTinTuan;
  final String? ngayBatDau;
  final String? ngayKetThuc;
  final List<DsThoiKhoaBieu> dsThoiKhoaBieu;
  final List<dynamic> dsIdThoiKhoaBieuTrung;

  DsTuanTkb copyWith({
    int? nhhk,
    int? tuanHocKy,
    int? tuanTuyetDoi,
    String? thongTinTuan,
    String? ngayBatDau,
    String? ngayKetThuc,
    List<DsThoiKhoaBieu>? dsThoiKhoaBieu,
    List<dynamic>? dsIdThoiKhoaBieuTrung,
  }) {
    return DsTuanTkb(
      nhhk: nhhk ?? this.nhhk,
      tuanHocKy: tuanHocKy ?? this.tuanHocKy,
      tuanTuyetDoi: tuanTuyetDoi ?? this.tuanTuyetDoi,
      thongTinTuan: thongTinTuan ?? this.thongTinTuan,
      ngayBatDau: ngayBatDau ?? this.ngayBatDau,
      ngayKetThuc: ngayKetThuc ?? this.ngayKetThuc,
      dsThoiKhoaBieu: dsThoiKhoaBieu ?? this.dsThoiKhoaBieu,
      dsIdThoiKhoaBieuTrung:
          dsIdThoiKhoaBieuTrung ?? this.dsIdThoiKhoaBieuTrung,
    );
  }

  factory DsTuanTkb.fromJson(Map<String, dynamic> json) {
    return DsTuanTkb(
      nhhk: json["nhhk"],
      tuanHocKy: json["tuan_hoc_ky"],
      tuanTuyetDoi: json["tuan_tuyet_doi"],
      thongTinTuan: json["thong_tin_tuan"],
      ngayBatDau: json["ngay_bat_dau"],
      ngayKetThuc: json["ngay_ket_thuc"],
      dsThoiKhoaBieu: json["ds_thoi_khoa_bieu"] == null
          ? []
          : List<DsThoiKhoaBieu>.from(json["ds_thoi_khoa_bieu"]!
              .map((x) => DsThoiKhoaBieu.fromJson(x))),
      dsIdThoiKhoaBieuTrung: json["ds_id_thoi_khoa_bieu_trung"] == null
          ? []
          : List<dynamic>.from(
              json["ds_id_thoi_khoa_bieu_trung"]!.map((x) => x)),
    );
  }

  Map<String, dynamic> toJson() => {
        "nhhk": nhhk,
        "tuan_hoc_ky": tuanHocKy,
        "tuan_tuyet_doi": tuanTuyetDoi,
        "thong_tin_tuan": thongTinTuan,
        "ngay_bat_dau": ngayBatDau,
        "ngay_ket_thuc": ngayKetThuc,
        "ds_thoi_khoa_bieu": dsThoiKhoaBieu.map((x) => x.toJson()).toList(),
        "ds_id_thoi_khoa_bieu_trung":
            dsIdThoiKhoaBieuTrung.map((x) => x).toList(),
      };

  @override
  String toString() {
    return "$nhhk, $tuanHocKy, $tuanTuyetDoi, $thongTinTuan, $ngayBatDau, $ngayKetThuc, $dsThoiKhoaBieu, $dsIdThoiKhoaBieuTrung, ";
  }
}

class DsThoiKhoaBieu {
  DsThoiKhoaBieu({
    required this.isHkLienTruoc,
    required this.thuKieuSo,
    required this.tietBatDau,
    required this.soTiet,
    required this.maMon,
    required this.tenMon,
    required this.soTinChi,
    required this.idToHoc,
    required this.idTkb,
    required this.idToHop,
    required this.maNhom,
    required this.maToTh,
    required this.maToHop,
    required this.maGiangVien,
    required this.tenGiangVien,
    required this.maLop,
    required this.maPhong,
    required this.maCoSo,
    required this.ngayHoc,
    required this.idTuTao,
    required this.isFileBaiGiang,
    required this.idSinhHoat,
    required this.nhhk,
  });

  final int? isHkLienTruoc;
  final int? thuKieuSo;
  final int? tietBatDau;
  final int? soTiet;
  final String? maMon;
  final String? tenMon;
  final String? soTinChi;
  final int? idToHoc;
  final int? idTkb;
  final int? idToHop;
  final String? maNhom;
  final String? maToTh;
  final String? maToHop;
  final dynamic maGiangVien;
  final dynamic tenGiangVien;
  final String? maLop;
  final String? maPhong;
  final String? maCoSo;
  final DateTime? ngayHoc;
  final int? idTuTao;
  final bool? isFileBaiGiang;
  final int? idSinhHoat;
  final int? nhhk;

  DsThoiKhoaBieu copyWith({
    int? isHkLienTruoc,
    int? thuKieuSo,
    int? tietBatDau,
    int? soTiet,
    String? maMon,
    String? tenMon,
    String? soTinChi,
    int? idToHoc,
    int? idTkb,
    int? idToHop,
    String? maNhom,
    String? maToTh,
    String? maToHop,
    dynamic maGiangVien,
    dynamic tenGiangVien,
    String? maLop,
    String? maPhong,
    String? maCoSo,
    DateTime? ngayHoc,
    int? idTuTao,
    bool? isFileBaiGiang,
    int? idSinhHoat,
    int? nhhk,
  }) {
    return DsThoiKhoaBieu(
      isHkLienTruoc: isHkLienTruoc ?? this.isHkLienTruoc,
      thuKieuSo: thuKieuSo ?? this.thuKieuSo,
      tietBatDau: tietBatDau ?? this.tietBatDau,
      soTiet: soTiet ?? this.soTiet,
      maMon: maMon ?? this.maMon,
      tenMon: tenMon ?? this.tenMon,
      soTinChi: soTinChi ?? this.soTinChi,
      idToHoc: idToHoc ?? this.idToHoc,
      idTkb: idTkb ?? this.idTkb,
      idToHop: idToHop ?? this.idToHop,
      maNhom: maNhom ?? this.maNhom,
      maToTh: maToTh ?? this.maToTh,
      maToHop: maToHop ?? this.maToHop,
      maGiangVien: maGiangVien ?? this.maGiangVien,
      tenGiangVien: tenGiangVien ?? this.tenGiangVien,
      maLop: maLop ?? this.maLop,
      maPhong: maPhong ?? this.maPhong,
      maCoSo: maCoSo ?? this.maCoSo,
      ngayHoc: ngayHoc ?? this.ngayHoc,
      idTuTao: idTuTao ?? this.idTuTao,
      isFileBaiGiang: isFileBaiGiang ?? this.isFileBaiGiang,
      idSinhHoat: idSinhHoat ?? this.idSinhHoat,
      nhhk: nhhk ?? this.nhhk,
    );
  }

  factory DsThoiKhoaBieu.fromJson(Map<String, dynamic> json) {
    return DsThoiKhoaBieu(
      isHkLienTruoc: json["is_hk_lien_truoc"],
      thuKieuSo: json["thu_kieu_so"],
      tietBatDau: json["tiet_bat_dau"],
      soTiet: json["so_tiet"],
      maMon: json["ma_mon"],
      tenMon: json["ten_mon"],
      soTinChi: json["so_tin_chi"],
      idToHoc: json["id_to_hoc"],
      idTkb: json["id_tkb"],
      idToHop: json["id_to_hop"],
      maNhom: json["ma_nhom"],
      maToTh: json["ma_to_th"],
      maToHop: json["ma_to_hop"],
      maGiangVien: json["ma_giang_vien"],
      tenGiangVien: json["ten_giang_vien"],
      maLop: json["ma_lop"],
      maPhong: json["ma_phong"],
      maCoSo: json["ma_co_so"],
      ngayHoc: DateTime.tryParse(json["ngay_hoc"].toString().substring(0, 10)),
      idTuTao: json["id_tu_tao"],
      isFileBaiGiang: json["is_file_bai_giang"],
      idSinhHoat: json["id_sinh_hoat"],
      nhhk: json["nhhk"],
    );
  }

  Map<String, dynamic> toJson() => {
        "is_hk_lien_truoc": isHkLienTruoc,
        "thu_kieu_so": thuKieuSo,
        "tiet_bat_dau": tietBatDau,
        "so_tiet": soTiet,
        "ma_mon": maMon,
        "ten_mon": tenMon,
        "so_tin_chi": soTinChi,
        "id_to_hoc": idToHoc,
        "id_tkb": idTkb,
        "id_to_hop": idToHop,
        "ma_nhom": maNhom,
        "ma_to_th": maToTh,
        "ma_to_hop": maToHop,
        "ma_giang_vien": maGiangVien,
        "ten_giang_vien": tenGiangVien,
        "ma_lop": maLop,
        "ma_phong": maPhong,
        "ma_co_so": maCoSo,
        "ngay_hoc": ngayHoc?.toIso8601String(),
        "id_tu_tao": idTuTao,
        "is_file_bai_giang": isFileBaiGiang,
        "id_sinh_hoat": idSinhHoat,
        "nhhk": nhhk,
      };

  @override
  String toString() {
    return "$isHkLienTruoc, $thuKieuSo, $tietBatDau, $soTiet, $maMon, $tenMon, $soTinChi, $idToHoc, $idTkb, $idToHop, $maNhom, $maToTh, $maToHop, $maGiangVien, $tenGiangVien, $maLop, $maPhong, $maCoSo, $ngayHoc, $idTuTao, $isFileBaiGiang, $idSinhHoat, $nhhk, ";
  }
}
