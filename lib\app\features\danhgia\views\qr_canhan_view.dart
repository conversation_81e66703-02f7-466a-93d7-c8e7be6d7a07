import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/features/danhgia/controllers/qr_canhan_controller.dart';
import 'package:tvumobile/app/features/danhgia/qr_scanner_controller.dart';
import 'package:tvumobile/app/features/danhgia/widgets/qr_appbar.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class QrCaNhanView extends GetView<QrCaNhanController> {
  const QrCaNhanView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // Get.put(QrCaNhanController());
    return Scaffold(
      appBar: ScannerAppBar(context, "mã qr cá nhân"),
      body: Center(
        child: Obx(
          () => controller.isLoading.value
              ? const CircularProgressIndicator()
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Hiển thị mã QR
                    QrImageView(
                      data: controller.qrCaNhan.value,
                      version: QrVersions.auto,
                      size: 250.0, // Kích thước mã QR
                      gapless: false,
                      foregroundColor: Colors.black87, // Màu mã QR
                      backgroundColor: Colors.white, // Màu nền mã QR
                      errorStateBuilder: (context, error) {
                        return const Text(
                          'Có lỗi khi tạo mã QR!',
                          style: TextStyle(color: Colors.red),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    // Hiển thị mô tả hoặc thông tin bổ sung
                    MyText.bodyMedium(
                      'Quét mã QR này để xem thông tin cá nhân',
                      color: Colors.black87,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
        ),
      ),
    );
  }
}
