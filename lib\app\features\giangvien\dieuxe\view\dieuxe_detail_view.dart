import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:tvumobile/app/config/routes/app_pages.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_appbar.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_detail_dsdicongtac.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_detail_phieughep.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_detail_thuexe.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_detail_ttchitiet.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_detail_ttlaixe.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_dialog.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_hctcduyet.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_hdthuexe.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_detail_controller.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';
import 'package:tvumobile/app/utils/extensions/extensions.dart';

class DieuXeDetailView extends GetView<DieuXeDetailController> {
  const DieuXeDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // Biến trạng thái để theo dõi mở rộng/thu gọn
    var isExpanded = false.obs;
    return Obx(
      () => Scaffold(
        appBar: DieuXeDetailAppBar(context, controller),
        body: GetBuilder<DieuXeDetailController>(
          builder: (controller) => controller.loadpdx.value
              ? Column(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.fromLTRB(10, 8, 10, 10),
                        child: Column(
                          children: [
                            // -------------------------- ĐỀ NGHỊ ĐIỀU XE --------------------------
                            Align(
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    vertical: 2, horizontal: 8),
                                child: MyText.titleLarge(
                                  "ĐỀ NGHỊ ĐIỀU XE",
                                  fontWeight: 700,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),

                            // Thanh tab của THÔNG TIN PHIẾU ĐIỀU XE
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 0, horizontal: 16),
                              child: Obx(
                                () => Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // Nút mũi tên trái
                                    IconButton(
                                      icon: Icon(
                                        LucideIcons.chevronLeft,
                                        color: controller.currentPage.value > 0
                                            ? theme.primaryColor
                                            : Colors.grey,
                                      ),
                                      onPressed: controller.currentPage.value >
                                              0
                                          ? () {
                                              controller.currentPage.value--;
                                              controller.pageController
                                                  .animateToPage(
                                                controller.currentPage.value,
                                                duration:
                                                    Duration(milliseconds: 300),
                                                curve: Curves.easeInOut,
                                              );
                                            }
                                          : null,
                                    ),
                                    // Hiển thị tên tab hiện tại
                                    Expanded(
                                      child: MyText.bodyLarge(
                                        controller.currentPage.value == 0
                                            ? "Thông tin chi tiết"
                                            : controller.currentPage.value == 1
                                                ? "Danh sách đi công tác"
                                                : controller.currentPage
                                                            .value ==
                                                        2
                                                    ? controller.phieughep.value
                                                        ? "Phiếu ghép"
                                                        : controller.pdx
                                                                    .trangThai ==
                                                                "Thuê xe ngoài"
                                                            ? "Hợp đồng thuê xe"
                                                            : "Thông tin xe, nhân viên lái"
                                                    : controller.pdx
                                                                .trangThai ==
                                                            "Thuê xe ngoài"
                                                        ? "Hợp đồng thuê xe"
                                                        : "Thông tin xe, nhân viên lái",
                                        color: theme.primaryColor,
                                        fontWeight: 700,
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    // Nút mũi tên phải
                                    IconButton(
                                      icon: Icon(
                                        LucideIcons.chevronRight,
                                        color: controller.currentPage.value <
                                                controller.maxTabs
                                            ? theme.primaryColor
                                            : Colors.grey,
                                      ),
                                      onPressed: controller.currentPage.value <
                                              controller.maxTabs
                                          ? () {
                                              controller.currentPage.value++;
                                              controller.pageController
                                                  .animateToPage(
                                                controller.currentPage.value,
                                                duration:
                                                    Duration(milliseconds: 300),
                                                curve: Curves.easeInOut,
                                              );
                                            }
                                          : null,
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            Divider(
                              color: theme.dividerColor.withOpacity(0.5),
                              thickness: 1,
                              height: 0,
                            ),

                            Expanded(
                              child: PageView(
                                controller: controller.pageController,
                                onPageChanged: (index) {
                                  controller.currentPage.value = index;
                                },
                                children: [
                                  // ---------------------- TAB THÔNG TIN CHI TIẾT -----------------------
                                  ThongTinChiTietTab(context, controller),

                                  // -------------------- TAB DANH SÁCH ĐI CÔNG TÁC ----------------------
                                  DsDiCongTacTab(context, controller),

                                  // ------------------ TAB THÔNG TIN NHÂN VIÊN LÁI XE -------------------
                                  if (controller.pdx.trangThai!
                                      .toLowerCase()
                                      .contains("phòng hc-tc đã duyệt"))
                                    ThongTinLaiXeTab(context, controller),
                                  // ------------------ TAB THÔNG TIN HỢP ĐỒNG THUÊ XE -------------------
                                  if (controller.pdx.trangThai!
                                      .toLowerCase()
                                      .contains("thuê xe ngoài"))
                                    if (controller.pdx.hopDongThueXe != null)
                                      HopDongThueXeTab(context, controller)
                                    else if (controller.pdx.hopDongThueXe ==
                                        null)
                                      Align(
                                        child: Text(
                                            "Chưa cập nhật hợp đồng thuê xe"),
                                      ),

                                  // -------------------------- TAB PHIẾU GHÉP ---------------------------
                                  if (controller.phieughep.value)
                                    PhieuGhepTab(context, controller),
                                ],
                              ),
                            ),

                            // DieuXeDivider(context, "thông tin duyệt"),
                          ],
                        ),
                      ),
                    ),

                    Divider(color: theme.dividerColor.withOpacity(0.3)),

                    // -------------------------- NÚT HOẠT ĐỘNG --------------------------
                    Padding(
                      padding: const EdgeInsets.fromLTRB(18, 8, 16, 16),
                      child: Row(
                        children: [
                          Expanded(
                            // flex: 1,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                // -------------------------------- CHỈNH SỬA & CHUYỂN ĐƠN ---------------------------------
                                // --------------------- TRẠNG THÁI "KHỞI TẠO VÀ YÊU CẦU ĐIỀU CHỈNH" -----------------------
                                if (controller.pdx.trangThai != null &&
                                    (controller.pdx.trangThai!
                                            .toLowerCase()
                                            .contains("khởi tạo") ||
                                        controller.pdx.trangThai!
                                            .toLowerCase()
                                            .contains("điều chỉnh"))) ...[
                                  // -------------------- NÚT CHỈNH SỬA --------------------
                                  MyButton.medium(
                                    backgroundColor: Colors.amber,
                                    onPressed: () async {
                                      Get.toNamed(Routes.DIEUXE_CREATE,
                                          arguments: {'id': controller.pdx.id});
                                    },
                                    child: Row(
                                      children: [
                                        Icon(
                                          LucideIcons.wrench,
                                          color: Colors.white,
                                          size: 15,
                                        ),
                                        SizedBox(width: 5),
                                        MyText.labelLarge(
                                          'Chỉnh sửa',
                                          fontWeight: 900,
                                          color: theme.colorScheme.onPrimary,
                                        )
                                      ],
                                    ),
                                  ),

                                  // -------------------- NÚT CHUYỂN ĐƠN -------------------
                                  MyButton.medium(
                                    onPressed: () async {
                                      if (controller.phanQuyen.value ==
                                          "canhan") {
                                        // Hiển thị hộp thoại xác nhận trước khi chuyển đơn
                                        controller.showConfirmDialog(
                                            "chuyển", "", "", () async {
                                          await controller.chuyenDieuXe();
                                        });
                                      } else if (controller.phanQuyen.value ==
                                              "truongdonvi" ||
                                          controller.phanQuyen.value ==
                                              "phonghcth") {
                                        // Hiển thị hộp thoại xác nhận trước khi chuyển đơn
                                        controller.showConfirmDialog(
                                            "chuyển", " đến phòng HCTH", "",
                                            () async {
                                          await controller.duyetDieuXeDV();
                                        });
                                      }
                                    },
                                    child: Row(
                                      children: [
                                        Icon(
                                          LucideIcons.rocket,
                                          color: Colors.white,
                                          size: 15,
                                        ),
                                        SizedBox(width: 5),
                                        MyText.labelLarge(
                                          'Chuyển phiếu',
                                          fontWeight: 900,
                                          color: theme.colorScheme.onPrimary,
                                        ),
                                      ],
                                    ),
                                  )
                                ]

                                // ----------------------------- THU HỒI & DUYỆT PHIẾU ĐIỀU XE -----------------------------
                                // -------------------------------- TRẠNG THÁI "CHỜ DUYỆT" ---------------------------------
                                else if (controller.pdx.trangThai != null &&
                                    controller.pdx.trangThai!
                                        .toLowerCase()
                                        .contains("chờ")) ...[
                                  // ----------------------------- THU HỒI PHIẾU ĐANG CHỜ DUYỆT ----------------------------
                                  if (controller.currentView == "moi" &&
                                      controller.pdx.nguoiTao!.id.toString() ==
                                          controller.userinfo!.vienchucid) ...[
                                    // -------------- NÚT THU HỒI PHIẾU CHỜ DUYỆT --------------
                                    MyButton.medium(
                                      backgroundColor: Colors.red,
                                      onPressed: () async {
                                        controller.showConfirmDialog(
                                            "thu hồi", "", "", () async {
                                          await controller.chuyenDieuXe();
                                        });
                                      },
                                      child: Row(
                                        children: [
                                          Icon(
                                            LucideIcons.rotateCcw,
                                            color: Colors.white,
                                            size: 15,
                                          ),
                                          SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Thu hồi',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ]
                                  // ---------------------------------- DUYỆT TẠI ĐƠN VỊ -----------------------------------
                                  else if (controller.currentView ==
                                          "donviduyet" &&
                                      ((controller.phanQuyen.value ==
                                                  "truongdonvi" &&
                                              controller.pdx.donViDuyet!.id
                                                      .toString() ==
                                                  controller
                                                      .userinfo!.vienchucid) ||
                                          controller.phanQuyen.value ==
                                              "phonghcth")) ...[
                                    // --------- NÚT KHÔNG DUYỆT & YÊU CẦU ĐIỀU CHỈNH ----------
                                    MyButton.medium(
                                      backgroundColor: Colors.amber[900],
                                      onPressed: () async {
                                        showDieuXeReasonDialog(controller,
                                            pdx: controller.pdx);
                                      },
                                      child: Row(
                                        children: [
                                          SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Hủy/Điều chỉnh',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),

                                    // -------------------- NÚT DUYỆT ĐƠN ----------------------
                                    MyButton.medium(
                                      backgroundColor: Colors.green,
                                      onPressed: () async {
                                        controller.showConfirmDialog(
                                            "duyệt và chuyển",
                                            " đến phòng HCTH",
                                            "", () async {
                                          await controller.duyetDieuXeDV();
                                        });
                                      },
                                      child: Row(
                                        children: [
                                          // Icon(
                                          //   LucideIcons.circleCheck,
                                          //   color: Colors.white,
                                          //   size: 15,
                                          // ),
                                          // SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Duyệt điều xe',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ]
                                ],

                                // ------------------------ HỦY VÀ THU HỒI PHIẾU ĐÃ DUYỆT Ở ĐƠN VỊ -------------------------
                                // --------------------------------- TRẠNG THÁI "ĐÃ DUYỆT" ---------------------------------
                                if (controller.pdx.trangThai!
                                    .toLowerCase()
                                    .contains("đơn vị đã duyệt")) ...[
                                  if (controller.currentView == "donviduyet" &&
                                      (controller.phanQuyen.value ==
                                              "phonghcth" ||
                                          (controller.phanQuyen.value ==
                                                  "truongdonvi" &&
                                              controller.pdx.donViDuyet?.id ==
                                                  controller
                                                      .userinfo!.vienchucid!
                                                      .toInt()))) ...[
                                    // 18279))) ...[
                                    // Trường hợp người tạo phiếu không phải là trưởng đơn vị
                                    if (controller.pdx.nguoiTao!.id !=
                                        controller.userinfo!.vienchucid!
                                            .toInt()) ...[
                                      // 18279) ...[
                                      // --------------- NÚT THU HỒI PHIẾU ĐÃ DUYỆT ---------------
                                      MyButton.medium(
                                        backgroundColor: Colors.amber,
                                        onPressed: () async {
                                          controller.showConfirmDialog(
                                              "thu hồi", " đã duyệt", "",
                                              () async {
                                            await controller.chuyenDieuXe();
                                          });
                                        },
                                        child: Row(
                                          children: [
                                            Icon(
                                              LucideIcons.rotateCcw,
                                              color: Colors.white,
                                              size: 15,
                                            ),
                                            SizedBox(width: 5),
                                            MyText.labelLarge(
                                              'Thu hồi duyệt',
                                              fontWeight: 900,
                                              color:
                                                  theme.colorScheme.onPrimary,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],

                                    // --------------- NÚT HỦY PHIẾU ĐÃ DUYỆT ---------------
                                    MyButton.medium(
                                      backgroundColor: Colors.red,
                                      onPressed: () async {
                                        controller.showConfirmDialog(
                                            "hủy", "", "", () async {
                                          await controller.traDieuXe("");
                                        });
                                      },
                                      child: Row(
                                        children: [
                                          Icon(
                                            LucideIcons.x,
                                            color: Colors.white,
                                            size: 15,
                                          ),
                                          SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Hủy đề nghị',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ]

                                  // -------------------------------- DUYỆT TẠI PHÒNG HCTH ---------------------------------
                                  else if (controller.phanQuyen.value ==
                                          "phonghcth" &&
                                      controller.currentView ==
                                          "hcthduyet") ...[
                                    // --------- NÚT KHÔNG DUYỆT & YÊU CẦU ĐIỀU CHỈNH ----------
                                    MyButton.medium(
                                      backgroundColor: Colors.amber[900],
                                      onPressed: () async {
                                        showDieuXeReasonDialog(controller,
                                            pdx: controller.pdx);
                                      },
                                      child: Row(
                                        children: [
                                          // Icon(
                                          //   LucideIcons.circleCheck,
                                          //   color: Colors.white,
                                          //   size: 15,
                                          // ),
                                          // SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Hủy/Điều chỉnh',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),

                                    // -------------------- NÚT DUYỆT ĐƠN ----------------------
                                    MyButton.medium(
                                      backgroundColor: Colors.green,
                                      onPressed: () async {
                                        await showMaterialModalBottomSheet(
                                          context: context,
                                          elevation: 8,
                                          isDismissible: true,
                                          enableDrag: false,
                                          builder: (context) =>
                                              HctcDuyetWidget(),
                                        );
                                      },
                                      child: Row(
                                        children: [
                                          // Icon(
                                          //   LucideIcons.circleCheck,
                                          //   color: Colors.white,
                                          //   size: 15,
                                          // ),
                                          // SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Duyệt điều xe',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ],

                                // ------------- HỦY VÀ CHỈNH NHÂN VIÊN LÁI XE CHO PHIẾU ĐÃ DUYỆT Ở PHÒNG HCTH --------------
                                // --------------------------- TRẠNG THÁI "PHÒNG HCTH ĐÃ DUYỆT" -----------------------------
                                if (controller.pdx.trangThai!
                                        .toLowerCase()
                                        .contains("phòng hc-tc đã duyệt") &&
                                    controller.phanQuyen.value == "phonghcth" &&
                                    controller.currentView == "hcthduyet") ...[
                                  // -------------------- NÚT HỦY ĐƠN ----------------------
                                  MyButton.medium(
                                    backgroundColor: Colors.red,
                                    onPressed: () async {
                                      showHuyDieuXeReasonDialog(controller,
                                          pdx: controller.pdx);
                                    },
                                    child: Row(
                                      children: [
                                        Icon(
                                          LucideIcons.x,
                                          color: Colors.white,
                                          size: 15,
                                        ),
                                        SizedBox(width: 5),
                                        MyText.labelLarge(
                                          'Hủy điều xe',
                                          fontWeight: 900,
                                          color: theme.colorScheme.onPrimary,
                                        ),
                                      ],
                                    ),
                                  ),

                                  // ----------------- NÚT CHỈNH NV LÁI XE -------------------
                                  MyButton.medium(
                                    backgroundColor: Colors.amber,
                                    onPressed: () async {
                                      await showMaterialModalBottomSheet(
                                        context: context,
                                        elevation: 8,
                                        isDismissible: true,
                                        enableDrag: false,
                                        builder: (context) => HctcDuyetWidget(),
                                      );
                                    },
                                    child: Row(
                                      children: [
                                        Icon(
                                          LucideIcons.wrench,
                                          color: Colors.white,
                                          size: 15,
                                        ),
                                        SizedBox(width: 5),
                                        MyText.labelLarge(
                                          'Điều chỉnh xe',
                                          fontWeight: 900,
                                          color: theme.colorScheme.onPrimary,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],

                                // --------------------------- THÊM - SỬA - XÓA HỢP ĐỒNG THUÊ XE ----------------------------
                                // ------------------------------ TRẠNG THÁI "THUÊ XE NGOÀI" --------------------------------
                                if (controller.pdx.trangThai!
                                        .toLowerCase()
                                        .contains("thuê xe ngoài") &&
                                    controller.phanQuyen.value == "phonghcth" &&
                                    controller.currentView == "hcthduyet") ...[
                                  if (controller.pdx.hopDongThueXe != null) ...[
                                    // ------------- NÚT XÓA HỢP ĐỒNG THUÊ XE ----------------
                                    MyButton.medium(
                                      backgroundColor: Colors.red,
                                      onPressed: () async {
                                        controller.showConfirmDialog("", "",
                                            "Bạn có chắc chắn muốn xóa hợp đồng điều xe này không?",
                                            () async {
                                          await controller
                                              .deleteHopDongThueXe();
                                        });
                                      },
                                      child: Row(
                                        children: [
                                          Icon(
                                            LucideIcons.x,
                                            color: Colors.white,
                                            size: 15,
                                          ),
                                          SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Xóa hợp đồng',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),

                                    // ----------------- NÚT SỬA HỢP ĐỒNG XE -------------------
                                    MyButton.medium(
                                      backgroundColor: Colors.amber,
                                      onPressed: () async {
                                        await showMaterialModalBottomSheet(
                                          context: context,
                                          elevation: 8,
                                          isDismissible: true,
                                          enableDrag: false,
                                          builder: (context) =>
                                              HdThueXeWidget(),
                                        );
                                      },
                                      child: Row(
                                        children: [
                                          Icon(
                                            LucideIcons.wrench,
                                            color: Colors.white,
                                            size: 15,
                                          ),
                                          SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Sửa hợp đồng',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ] else ...[
                                    // ------------ NÚT CẬP NHẬT HỢP ĐỒNG THUÊ XE --------------
                                    MyButton.medium(
                                      backgroundColor: Colors.blue,
                                      onPressed: () async {
                                        await showMaterialModalBottomSheet(
                                          context: context,
                                          elevation: 8,
                                          isDismissible: true,
                                          enableDrag: false,
                                          builder: (context) =>
                                              HdThueXeWidget(),
                                        );
                                      },
                                      child: Row(
                                        children: [
                                          // Icon(
                                          //   LucideIcons.wrench,
                                          //   color: Colors.white,
                                          //   size: 15,
                                          // ),
                                          // SizedBox(width: 5),
                                          MyText.labelLarge(
                                            'Cập nhật hợp đồng thuê xe',
                                            fontWeight: 900,
                                            color: theme.colorScheme.onPrimary,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ],
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              : Center(child: CircularProgressIndicator()),
        ),
      ),
    );
  }
}
