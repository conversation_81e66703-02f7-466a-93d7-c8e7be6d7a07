import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tvumobile/app/constans/app_constants.dart';
import 'package:tvumobile/app/data/models/auth/user_info.dart';
import 'package:tvumobile/app/data/models/edusoft/tkb_edusoft_model.dart';
import 'package:tvumobile/app/data/models/portal/edu_baiviet_model.dart';
import 'package:tvumobile/app/data/models/portal/portal_news_model.dart';
import 'package:tvumobile/app/data/models/sch/tkb/tkb_tuan_giangvien.dart';
import 'package:tvumobile/app/data/models/wordpress/article.dart';
import 'package:tvumobile/app/features/navbar/navbar_controller.dart';
import 'package:tvumobile/app/utils/helpers/tkb_helper.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/services/local_storage_services.dart';
import 'package:http/http.dart' as http;

class SvHomeController extends GetxController {
  final portalNews = <PortalNewsModel>[].obs;
  final tsNews = <PortalNewsModel>[].obs;
  final tkbs = <TkbTuanGiangVien>[].obs;
  final eduNews = <EduBaiVietModel>[].obs;

  ApiProvider apiProvider = Get.find();
  UserInfo? userInfo = LocalStorageServices.getUserInfo();
  NavbarController navbarController = Get.find();

  @override
  Future<void> onReady() async {
    await getDatas();
    if (!Platform.isWindows) {
      var fcmtoken = LocalStorageServices.getFcmToken();
      if (fcmtoken == null) {
        //NotificationHelper().openNotificationDialog().then((value) async => await NotificationHelper().updateFCM());
      }
    }
  }

  getDatas() async {
    final tkbData = await apiProvider.loadCurrentWeekTkbSinhVien();
    if (tkbData.isEmpty) {
      tkbs.value = [];
    } else {
      var tmp = getTodayTkb(tkbData);
      if (tmp != null) {
        tkbs.addAll(tmp);
      }
    }
    update();
    await fetchLatestArticles(1);
    // final portalNewsData = await apiProvider.loadPortalNews();
    // if (portalNewsData.isEmpty) {
    //   portalNews.value = [];
    // } else {
    //   portalNews.addAll(portalNewsData);
    // }
    update();
    final tsNewsData = await apiProvider.loadTuyenSinhNews();
    if (tsNewsData.isEmpty) {
      tsNews.value = [];
    } else {
      tsNews.addAll(tsNewsData);
    }
    update();
    final eduNewsData = await apiProvider.loadBaiVietEdu();
    if (eduNewsData.isEmpty) {
      eduNews.value = [];
    } else {
      eduNews.addAll(eduNewsData);
    }
    update();
  }

  onTapFunctions(String id) {
    if (id == "dso" || id == "dsotl") {
      navbarController.onTap(2);
    }
    if (id == "tkb" || id == "tkbhk") {
      navbarController.onTap(1);
    }
  }

  List<TkbTuanGiangVien>? getTodayTkb(List<TkbEdusoftModel> tkbs) {
    List<TkbTuanGiangVien> lists = [];
    for (var tkbtuan in tkbs) {
      for (var tkb in tkbtuan.dsThoiKhoaBieu) {
        //if(tkb.ngayHoc!.isToday) {
        TkbTuanGiangVien tkbTuanGiangVien = TkbTuanGiangVien();
        //DateTime ngayday = tkb.ngayHoc;
        tkbTuanGiangVien.tenMon = tkb.tenMon;
        tkbTuanGiangVien.batDau = getTietBD(tkb.ngayHoc!, tkb.tietBatDau!);
        tkbTuanGiangVien.ketThuc =
            getTietBD(tkb.ngayHoc!, tkb.tietBatDau! + tkb.soTiet!);
        tkbTuanGiangVien.tenPhong = tkb.maPhong;
        tkbTuanGiangVien.maLop = tkb.maLop;
        tkbTuanGiangVien.maMon = tkb.maMon;
        tkbTuanGiangVien.nhomHoc = tkb.maNhom;
        tkbTuanGiangVien.nhomTH = tkb.maToTh;
        tkbTuanGiangVien.soTiet = tkb.soTiet.toString();
        lists.add(tkbTuanGiangVien);
        //}
      }
    }
    return lists;
  }

  final latestArticles = <dynamic>[].obs;
  Future<List<dynamic>> fetchLatestArticles(int page) async {
    try {
      var response = await http.get(Uri.parse(
          '${ApiPath.tvuWordpressURL}/wp-json/wp/v2/posts/?page=$page&per_page=10&_fields=id,date,title,custom,yoast_head_json.og_image,link,content,categories'));
      if (response.statusCode == 200) {
        latestArticles.addAll(json
            .decode(response.body)
            .map((m) => Article.fromJson(m))
            .toList());
        if (kDebugMode) {
          print(latestArticles);
        }
        return latestArticles;
      }
    } on SocketException {
      throw 'No Internet connection';
    }
    return latestArticles;
  }
}
