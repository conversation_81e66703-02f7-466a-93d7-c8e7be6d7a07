import 'dart:convert';

class ThueThangInfo {
  ThueThangInfo({
    required this.id,
    required this.nam,
    required this.thang,
    required this.vienChucId,
    required this.mavienchuc,
    required this.hoten,
    required this.mst,
    required this.cmnd,
    required this.luong,
    required this.tnngoailuong,
    required this.tongthunhap,
    required this.chuyenthue,
    required this.giam<PERSON><PERSON><PERSON><PERSON>,
    required this.giamtrug<PERSON><PERSON>h,
    required this.baohiem,
    required this.thunhapchiuthue,
    required this.thuephainop,
    required this.tamtruthue,
    required this.conphainop,
    required this.ghichu,
  });

  final int? id;
  final num? nam;
  final num? thang;
  final num? vienChucId;
  final String? mavienchuc;
  final String? hoten;
  final String? mst;
  final String? cmnd;
  final num? luong;
  final num? tnngoailuong;
  final num? tongthunhap;
  final num? chuyenthue;
  final num? giamtrubanthan;
  final num? giamtrugiacanh;
  final num? baohiem;
  final num? thunhapchiuthue;
  final num? thuephainop;
  final dynamic tamtruthue;
  final num? conphainop;
  final dynamic ghichu;

  ThueThangInfo copyWith({
    int? id,
    num? nam,
    num? thang,
    num? vienChucId,
    String? mavienchuc,
    String? hoten,
    String? mst,
    String? cmnd,
    num? luong,
    num? tnngoailuong,
    num? tongthunhap,
    num? chuyenthue,
    num? giamtrubanthan,
    num? giamtrugiacanh,
    num? baohiem,
    num? thunhapchiuthue,
    num? thuephainop,
    dynamic tamtruthue,
    num? conphainop,
    dynamic ghichu,
  }) {
    return ThueThangInfo(
      id: id ?? this.id,
      nam: nam ?? this.nam,
      thang: thang ?? this.thang,
      vienChucId: vienChucId ?? this.vienChucId,
      mavienchuc: mavienchuc ?? this.mavienchuc,
      hoten: hoten ?? this.hoten,
      mst: mst ?? this.mst,
      cmnd: cmnd ?? this.cmnd,
      luong: luong ?? this.luong,
      tnngoailuong: tnngoailuong ?? this.tnngoailuong,
      tongthunhap: tongthunhap ?? this.tongthunhap,
      chuyenthue: chuyenthue ?? this.chuyenthue,
      giamtrubanthan: giamtrubanthan ?? this.giamtrubanthan,
      giamtrugiacanh: giamtrugiacanh ?? this.giamtrugiacanh,
      baohiem: baohiem ?? this.baohiem,
      thunhapchiuthue: thunhapchiuthue ?? this.thunhapchiuthue,
      thuephainop: thuephainop ?? this.thuephainop,
      tamtruthue: tamtruthue ?? this.tamtruthue,
      conphainop: conphainop ?? this.conphainop,
      ghichu: ghichu ?? this.ghichu,
    );
  }

  factory ThueThangInfo.fromJson(Map<String, dynamic> json){
    return ThueThangInfo(
      id: json["id"],
      nam: json["nam"],
      thang: json["thang"],
      vienChucId: json["vienChucId"],
      mavienchuc: json["mavienchuc"],
      hoten: json["hoten"],
      mst: json["mst"],
      cmnd: json["cmnd"],
      luong: json["luong"],
      tnngoailuong: json["tnngoailuong"],
      tongthunhap: json["tongthunhap"],
      chuyenthue: json["chuyenthue"],
      giamtrubanthan: json["giamtrubanthan"],
      giamtrugiacanh: json["giamtrugiacanh"],
      baohiem: json["baohiem"],
      thunhapchiuthue: json["thunhapchiuthue"],
      thuephainop: json["thuephainop"],
      tamtruthue: json["tamtruthue"],
      conphainop: json["conphainop"],
      ghichu: json["ghichu"],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "nam": nam,
    "thang": thang,
    "vienChucId": vienChucId,
    "mavienchuc": mavienchuc,
    "hoten": hoten,
    "mst": mst,
    "cmnd": cmnd,
    "luong": luong,
    "tnngoailuong": tnngoailuong,
    "tongthunhap": tongthunhap,
    "chuyenthue": chuyenthue,
    "giamtrubanthan": giamtrubanthan,
    "giamtrugiacanh": giamtrugiacanh,
    "baohiem": baohiem,
    "thunhapchiuthue": thunhapchiuthue,
    "thuephainop": thuephainop,
    "tamtruthue": tamtruthue,
    "conphainop": conphainop,
    "ghichu": ghichu,
  };

  factory ThueThangInfo.fromJsonString(String source) =>
      ThueThangInfo.fromJson(json.decode(source));

  @override
  String toString(){
    return "$id, $nam, $thang, $vienChucId, $mavienchuc, $hoten, $mst, $cmnd, $luong, $tnngoailuong, $tongthunhap, $chuyenthue, $giamtrubanthan, $giamtrugiacanh, $baohiem, $thunhapchiuthue, $thuephainop, $tamtruthue, $conphainop, $ghichu, ";
  }
}
