import 'package:tvumobile/app/data/models/tms/donnghiphep/donnghiphepinfo.dart';

class DieuXeInfo {
  DieuXeInfo({
    required this.lanhDaoDonviList,
    required this.donviList,
    required this.donVi,
  });

  final List<LanhDaoDonviList> lanhDaoDonviList;
  final List<DonViList> donviList;
  final DonViList? donVi;

  factory DieuXeInfo.empty() {
    return DieuXeInfo(
      lanhDaoDonviList: [],
      donviList: [],
      donVi: DonViList.empty(),
    );
  }

  DieuXeInfo copyWith({
    List<LanhDaoDonviList>? lanhDaoDonviList,
    List<DonViList>? donviList,
    DonViList? donVi,
  }) {
    return DieuXeInfo(
      lanhDaoDonviList: lanhDaoDonviList ?? this.lanhDaoDonviList,
      donviList: donviList ?? this.donviList,
      donVi: donVi ?? this.donVi,
    );
  }

  factory DieuXeInfo.fromJson(Map<String, dynamic> json) {
    return DieuXeInfo(
      lanhDaoDonviList: json["lanhDaoDonviList"] == null
          ? []
          : List<LanhDaoDonviList>.from(json["lanhDaoDonviList"]!
              .map((x) => LanhDaoDonviList.fromJson(x))),
      donviList: json["donviList"] == null
          ? []
          : List<DonViList>.from(
              json["donviList"]!.map((x) => DonViList.fromJson(x))),
      donVi: json["donVi"] == null ? null : DonViList.fromJson(json["donVi"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "lanhDaoDonviList": lanhDaoDonviList.map((x) => x.toJson()).toList(),
        "donviList": donviList.map((x) => x.toJson()).toList(),
        "donVi": donVi?.toJson(),
      };

  @override
  String toString() {
    return "$lanhDaoDonviList, $donviList, $donVi, ";
  }
}

// class DonVi {
//   DonVi({
//     required this.id,
//     required this.tenDonVi,
//   });

//   final int? id;
//   final String? tenDonVi;
//   // Add this factory constructor in the DonVi class
//   factory DonVi.empty() {
//     return DonVi(
//       id: 0,
//       tenDonVi: '',
//     );
//   }

//   DonVi copyWith({
//     int? id,
//     String? tenDonVi,
//   }) {
//     return DonVi(
//       id: id ?? this.id,
//       tenDonVi: tenDonVi ?? this.tenDonVi,
//     );
//   }

//   factory DonVi.fromJson(Map<String, dynamic> json) {
//     return DonVi(
//       id: json["id"],
//       tenDonVi: json["tenDonVi"],
//     );
//   }

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "tenDonVi": tenDonVi,
//       };

//   @override
//   String toString() {
//     return "$id, $tenDonVi, ";
//   }
// }

// class LanhDaoDonviList {
//   LanhDaoDonviList({
//     required this.id,
//     required this.ho,
//     required this.tenDem,
//     required this.ten,
//     required this.tenChucVu,
//     required this.maVienChuc,
//   });

//   final int? id;
//   final String? ho;
//   final String? tenDem;
//   final String? ten;
//   final String? tenChucVu;
//   final String? maVienChuc;
//   // Add this factory constructor in the LanhDaoDonviList class
//   factory LanhDaoDonviList.empty() {
//     return LanhDaoDonviList(
//       id: 0,
//       ho: '',
//       tenDem: '',
//       ten: '',
//       tenChucVu: '',
//       maVienChuc: '',
//     );
//   }

//   LanhDaoDonviList copyWith({
//     int? id,
//     String? ho,
//     String? tenDem,
//     String? ten,
//     String? tenChucVu,
//     String? maVienChuc,
//   }) {
//     return LanhDaoDonviList(
//       id: id ?? this.id,
//       ho: ho ?? this.ho,
//       tenDem: tenDem ?? this.tenDem,
//       ten: ten ?? this.ten,
//       tenChucVu: tenChucVu ?? this.tenChucVu,
//       maVienChuc: maVienChuc ?? this.maVienChuc,
//     );
//   }

//   factory LanhDaoDonviList.fromJson(Map<String, dynamic> json) {
//     return LanhDaoDonviList(
//       id: json["id"],
//       ho: json["ho"],
//       tenDem: json["tenDem"],
//       ten: json["ten"],
//       tenChucVu: json["tenChucVu"],
//       maVienChuc: json["maVienChuc"],
//     );
//   }

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "ho": ho,
//         "tenDem": tenDem,
//         "ten": ten,
//         "tenChucVu": tenChucVu,
//         "maVienChuc": maVienChuc,
//       };

//   @override
//   String toString() {
//     return "$id, $ho, $tenDem, $ten, $tenChucVu, $maVienChuc, ";
//   }
// }

class DonViList {
  DonViList({
    required this.id,
    required this.tenDonVi,
  });

  final int? id;
  final String? tenDonVi;
  // Add this factory constructor in the DonViList class
  factory DonViList.empty() {
    return DonViList(
      id: 0,
      tenDonVi: '',
    );
  }

  DonViList copyWith({
    int? id,
    String? tenDonVi,
  }) {
    return DonViList(
      id: id ?? this.id,
      tenDonVi: tenDonVi ?? this.tenDonVi,
    );
  }

  factory DonViList.fromJson(Map<String, dynamic> json) {
    return DonViList(
      id: json["id"],
      tenDonVi: json["tenDonVi"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "tenDonVi": tenDonVi,
      };

  @override
  String toString() {
    return "$id, $tenDonVi, ";
  }
}
