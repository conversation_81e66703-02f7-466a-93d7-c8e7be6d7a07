import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:tvumobile/app/utils/localizations/translator.dart';

extension StringUtil on String {
  Color get toColor {
    String data = replaceAll("#", "");
    if (data.length == 6) {
      data = "FF$data";
    }
    return Color(int.parse("0x$data"));
  }

  String maxLength(int length) {
    if (length > this.length) {
      return this;
    } else {
      return substring(0, length);
    }
  }

  String toParagraph([bool addDash = false]) {
    return addDash ? "-\t$this" : "\t$this";
  }

  bool toBool([bool defaultValue = false]) {
    if (toString().compareTo('1') == 0 || toString().compareTo('true') == 0) {
      return true;
    } else if (toString().compareTo('0') == 0 ||
        toString().compareTo('false') == 0) {
      return false;
    }
    return defaultValue;
  }

  int toInt([int defaultValue = 0]) {
    try {
      return int.parse(this);
    } catch (e) {
      return defaultValue;
    }
  }

  double toDouble([double defaultValue = 0]) {
    try {
      return double.parse(this);
    } catch (e) {
      return defaultValue;
    }
  }

  String get getFileExt {
    if (this == "") {
      return "";
    }
    String tmp = split('.').last;
    return tmp;
  }
}

//------ App Localization Extension --------------------------//

extension StringLocalization on String {
  String lang() {
    return Translator.translate(this);
  }
}

Color colorFor(String text){
  var hash = 0;
  for (var i = 0; i < text.length; i++) {
    hash = text.codeUnitAt(i)+ ((hash << 5) - hash);
  }
  final finalHash = hash.abs() % (256*256*256);
  if (kDebugMode) {
    print(finalHash);
  }
  final red = ((finalHash & 0xFF0000) >> 16);
  final blue = ((finalHash & 0xFF00) >> 8);
  final green = ((finalHash & 0xFF));
  final color = Color.fromRGBO(red, green, blue, 1);
  return color;
}