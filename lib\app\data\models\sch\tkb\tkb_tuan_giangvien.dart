
import 'package:flutter/material.dart';

class TkbTuanGiangVien {

  /// Event name which is equivalent to subject property of [Appointment].
  String? tenMon;

  String? tenPhong;
  String? maLop;
  String? nhomHoc;
  String? nhomTH;
  String? maMon;
  String? soTiet;
  /// From which is equivalent to start time property of [Appointment].
  DateTime? batDau;

  /// To which is equivalent to end time property of [Appointment].
  DateTime? ketThuc;

  /// Background which is equivalent to color property of [Appointment].
  Color? background;

  /// IsAllDay which is equivalent to isAllDay property of [Appointment].
  bool? isAllDay = false;
  String? image = "assets/images/training.png";
  String? notes = "";
  IconData? icon = Icons.task;
}