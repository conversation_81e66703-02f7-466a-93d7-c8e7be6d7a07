import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:stylish_bottom_bar/stylish_bottom_bar.dart';
import 'package:tvumobile/app/config/themes/app_theme.dart';
import 'package:tvumobile/app/features/danhgia/controllers/qr_home_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

Obx QRCodeBottomBar(QrHomeController controller, BuildContext context,
    PageController pageController) {
  final theme = Theme.of(context);
  return Obx(
    () => StylishBottomBar(
      option: BubbleBarOptions(
        barStyle: BubbleBarStyle.vertical,
        bubbleFillStyle: BubbleFillStyle.fill,
      ),
      items: [
        BottomBarItem(
          icon: const Icon(LucideIcons.qrCode),
          backgroundColor: theme.primaryColor,
          selectedColor: theme.primaryColor,
          selectedIcon: Icon(
            LucideIcons.qrCode,
            color: Colors.white,
          ),
          unSelectedColor: mlPrimaryTextColor,
          title: MyText.labelMedium(
            'Mã QR cá nhân',
            color: Colors.white,
          ),
          borderColor: theme.primaryColor,
        ),
        BottomBarItem(
          icon: const Icon(LucideIcons.scanLine),
          backgroundColor: tPrimaryColor,
          selectedColor: theme.primaryColor,
          selectedIcon: Icon(
            LucideIcons.scanLine,
            color: Colors.white,
          ),
          unSelectedColor: mlPrimaryTextColor,
          title: MyText.labelMedium(
            'Quét mã QR',
            color: Colors.white,
          ),
          borderColor: theme.primaryColor,
        ),
        BottomBarItem(
          icon: const Icon(LucideIcons.microscope),
          backgroundColor: tPrimaryColor,
          selectedColor: theme.primaryColor,
          selectedIcon: Icon(
            LucideIcons.microscope,
            color: Colors.white,
          ),
          unSelectedColor: mlPrimaryTextColor,
          title: MyText.labelMedium(
            'Lý lịch khoa học',
            color: Colors.white,
          ),
          borderColor: theme.primaryColor,
        ),
      ],
      hasNotch: true,
      // fabLocation: StylishBarFabLocation.end,
      currentIndex: controller.selectedIndex.value,
      notchStyle: NotchStyle.square,
      // onTap: (index) {
      //   if (index == controller.selectedIndex.value) return;
      //   controller.onTap(index);
      // },
      onTap: (index) {
        // Cập nhật selectedIndex
        controller.selectedIndex.value = index;
        // Chuyển trang với hiệu ứng
        pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      },
      backgroundColor: theme.cardColor,
    ),
  );
}
