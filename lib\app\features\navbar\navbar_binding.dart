import 'package:get/get.dart';
import 'package:tvumobile/app/features/giangvien/congvan/congvan_controller.dart';
import 'package:tvumobile/app/features/giangvien/home/<USER>';
import 'package:tvumobile/app/features/giangvien/luongthue/luongthuethang_controller.dart';
import 'package:tvumobile/app/features/giangvien/tkbgiangvien/tkb_giangvien_controller.dart';
import 'package:tvumobile/app/features/sinhvien/diemsinhvien/diem_sinhvien_controller.dart';
import 'package:tvumobile/app/features/sinhvien/home/<USER>';
import 'package:tvumobile/app/features/sinhvien/hpsinhvien/hp_sinhvien_controller.dart';
import 'package:tvumobile/app/features/sinhvien/tkbsinhvien/tkb_sinhvien_controller.dart';
import 'navbar_controller.dart';

class NavbarBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<NavbarController>(
      () => NavbarController(),
    );

    Get.lazyPut<GvHomeController>(
      () => GvHomeController(),
    );

    Get.lazyPut<CongVanController>(
      () => CongVanController(),
    );

    Get.lazyPut<TkbGiangVienController>(
      () => TkbGiangVienController(),
    );

    Get.lazyPut<GvHomeController>(
      () => GvHomeController(),
    );

    Get.lazyPut<LuongThueController>(
      () => LuongThueController(),
    );

    Get.lazyPut<TkbSinhVienController>(
      () => TkbSinhVienController(),
    );

    Get.lazyPut<DiemSinhVienController>(
      () => DiemSinhVienController(),
    );

    Get.lazyPut<HpSinhVienController>(
      () => HpSinhVienController(),
    );
    Get.lazyPut<SvHomeController>(
      () => SvHomeController(),
    );
  }
}
