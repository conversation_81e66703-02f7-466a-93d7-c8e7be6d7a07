import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_divider.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_detail_controller.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

Widget DsDiCongTacTab(BuildContext context, DieuXeDetailController controller) {
  final theme = Theme.of(context);
  return ListView(
    shrinkWrap: true,
    children: [
      Padding(
        padding: const EdgeInsets.only(top: 5.0),
        child: Column(
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ------------------------- VIÊN CHỨC TRONG TRƯỜNG -------------------------
            if (controller.pdx.dsDiCongTac != null &&
                controller.pdx.dsDiCongTac!.isNotEmpty) ...[
              DieuXeDivider(context, "viên chức trường"),
              Padding(
                padding:
                    const EdgeInsets.only(top: 4, left: 4, right: 4, bottom: 8),
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: theme.colorScheme.onSurface.withOpacity(0.2),
                      width: 1.0,
                    ),
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.35,
                    ),
                    child: ListView.builder(
                      physics: AlwaysScrollableScrollPhysics(),
                      padding: EdgeInsets.all(0),
                      itemCount: controller.pdx.dsDiCongTac!.length,
                      itemBuilder: (context, index) {
                        final vienChuc = controller.pdx.dsDiCongTac![index];
                        return Container(
                          decoration: BoxDecoration(
                              border: Border.symmetric(
                                  horizontal: BorderSide(
                            color: theme.colorScheme.onSurface.withOpacity(0.2),
                            width: 1.0,
                          ))),
                          child: ListTile(
                            title: MyText.bodyLarge(
                              "${vienChuc.ho} ${vienChuc.tenDem ?? ""} ${vienChuc.ten} (${vienChuc.chucVu})",
                              fontWeight: 700,
                            ),
                            subtitle:
                                MyText.bodyMedium("Đơn vị: ${vienChuc.donVi}"),
                            leading: CircleAvatar(
                              backgroundImage:
                                  NetworkImage(vienChuc.hinhAnh.toString()),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],

            // ------------------------- VIÊN CHỨC NGOÀI TRƯỜNG -------------------------
            if (controller.pdx.vienChucNgoaiTruong != null &&
                controller.pdx.vienChucNgoaiTruong!.isNotEmpty) ...[
              DieuXeDivider(context, "viên chức ngoài trường"),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          LucideIcons.userRoundPen,
                          color: theme.primaryColor,
                          size: 15,
                        ),
                        MyText.labelLarge(
                          " Tên viên chức ngoài trường:",
                          color: theme.primaryColor,
                          fontWeight: 700,
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    // if (controller.isExpandedVienChuc.value)
                    MyText.bodyMedium(
                      "- ${controller.pdx.vienChucNgoaiTruong}",
                      color: Colors.black87,
                      maxLines: null, // Hiển thị toàn bộ khi mở rộng
                      softWrap: true,
                    )
                    // else ...[
                    //   Row(
                    //     mainAxisAlignment: MainAxisAlignment.start,
                    //     children: [
                    //       Expanded(
                    //         child: MyText.bodyMedium(
                    //           "- ${controller.pdx.vienChucNgoaiTruong}",
                    //           color: Colors.black87,
                    //           maxLines: 2,
                    //           softWrap: true,
                    //           overflow: TextOverflow.ellipsis,
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    //   Row(
                    //     mainAxisAlignment: MainAxisAlignment.end,
                    //     children: [
                    //       if (controller.pdx.vienChucNgoaiTruong != null &&
                    //           controller.pdx.vienChucNgoaiTruong!.length >=
                    //               97)
                    //         GestureDetector(
                    //           onTap: () => controller
                    //               .isExpandedVienChuc.value = true,
                    //           child: MyText.bodyMedium(
                    //             "Xem thêm",
                    //             color: Colors.blue,
                    //             fontWeight: 600,
                    //           ),
                    //         ),
                    //     ],
                    //   ),
                    // ],
                    // if (controller.isExpandedVienChuc.value)
                    //   Row(
                    //     mainAxisAlignment: MainAxisAlignment.end,
                    //     children: [
                    //       GestureDetector(
                    //         onTap: () =>
                    //             controller.isExpandedVienChuc.value = false,
                    //         child: MyText.bodyMedium(
                    //           "Thu gọn",
                    //           color: Colors.blue,
                    //           fontWeight: 600,
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                  ],
                ),
              ),
            ],

            // ------------------------------ GHI CHÚ ------------------------------
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        LucideIcons.penLine,
                        color: theme.primaryColor,
                        size: 15,
                      ),
                      MyText.labelLarge(
                        " Ghi chú:",
                        color: theme.primaryColor,
                        fontWeight: 700,
                      ),
                    ],
                  ),
                  SizedBox(height: 4),
                  // if (controller.isExpandedGhiChu.value)
                  MyText.bodyMedium(
                    "- ${controller.pdx.donViVienChucNgoaiTruong}",
                    color: Colors.black87,
                    maxLines: null, // Hiển thị toàn bộ khi mở rộng
                    softWrap: true,
                  )
                  // else ...[
                  //   Row(
                  //     mainAxisAlignment: MainAxisAlignment.start,
                  //     children: [
                  //       Expanded(
                  //         child: MyText.bodyMedium(
                  //           (controller.pdx.donViVienChucNgoaiTruong !=
                  //                       null &&
                  //                   controller.pdx.donViVienChucNgoaiTruong!
                  //                       .isNotEmpty)
                  //               ? "- ${controller.pdx.donViVienChucNgoaiTruong}"
                  //               : '(Không có ghi chú)',
                  //           color: Colors.black87,
                  //           maxLines: 2,
                  //           softWrap: true,
                  //           overflow: TextOverflow.ellipsis,
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  //   Row(
                  //     mainAxisAlignment: MainAxisAlignment.end,
                  //     children: [
                  //       if (controller.pdx.donViVienChucNgoaiTruong !=
                  //               null &&
                  //           controller
                  //                   .pdx.donViVienChucNgoaiTruong!.length >=
                  //               97)
                  //         GestureDetector(
                  //           onTap: () =>
                  //               controller.isExpandedGhiChu.value = true,
                  //           child: MyText.bodyMedium(
                  //             "Xem thêm",
                  //             color: Colors.blue,
                  //             fontWeight: 600,
                  //           ),
                  //         ),
                  //     ],
                  //   ),
                  // ],
                  // if (controller.isExpandedGhiChu.value)
                  //   Row(
                  //     mainAxisAlignment: MainAxisAlignment.end,
                  //     children: [
                  //       GestureDetector(
                  //         onTap: () =>
                  //             controller.isExpandedGhiChu.value = false,
                  //         child: MyText.bodyMedium(
                  //           "Thu gọn",
                  //           color: Colors.blue,
                  //           fontWeight: 600,
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                ],
              ),
            ),
          ],
        ),
      ),
    ],
  );
}
