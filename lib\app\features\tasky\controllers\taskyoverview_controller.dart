import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:tvumobile/app/data/models/tasky/Task.dart';
import 'package:tvumobile/app/services/api_provider.dart';
import 'package:tvumobile/app/utils/extensions/list_extentions.dart';

class TaskyOverviewController extends GetxController {
  ApiProvider apiProvider = Get.find();
  var taskStats = {}.obs;
  List<dynamic> teamMembers = [].obs;
  List<Task> allTasks = <Task>[].obs;

  @override
  void onInit() {
    super.onInit();
    if (kDebugMode) {
      print("TaskyOverviewController onInit");
    }
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    if (kDebugMode) {
      print("TaskyOverviewController onReady");
    }
    getOverviews();
  }

  Future<void> getOverviews() async {
    try {
      // Gọi API để lấy dữ liệu tổng quan
      Map<String, dynamic> tmp =
          await apiProvider.getTaskOverview(taskLimit: 5);
      print('API Response: $tmp'); // In dữ liệu từ API

      if (tmp.isNotEmpty) {
        // Cập nhật danh sách thành viên nhóm
        teamMembers = tmp["teamMembers"];
        print('Team Members Raw: $teamMembers');

        var tmp1 = teamMembers
            .where(
                (w) => w["tenChucVu"].toString().toLowerCase() != "viên chức")
            .toList();
        var tmp2 = teamMembers
            .where(
                (w) => w["tenChucVu"].toString().toLowerCase() == "viên chức")
            .toList();
        tmp1 = tmp1.orderBy("tenChucVu", "desc");
        tmp2 = tmp2.orderBy("ten", "asc");

        teamMembers.clear();
        teamMembers.addAll(tmp1);
        teamMembers.addAll(tmp2);
        print('Team Members Final: $teamMembers');

        // Cập nhật thống kê công việc
        Logger().i(tmp["taskStatistics"]);
        taskStats.value = tmp["taskStatistics"];

        // Cập nhật danh sách công việc
        allTasks.clear();
        var tmp3 = Task.parse(tmp["tasks"]);

        // Sắp xếp allTasks theo endDateDuKien (gần nhất lên trên)
        tmp3.sort((a, b) {
          // Nếu endDateDuKien của a hoặc b là null, đặt nó xuống cuối danh sách
          if (a.endDateDuKien == null && b.endDateDuKien == null) return 0;
          if (a.endDateDuKien == null) return 1; // Đưa a xuống dưới nếu null
          if (b.endDateDuKien == null) return -1; // Đưa b xuống dưới nếu null

          // So sánh ngày: ngày gần nhất lên trên
          return a.endDateDuKien!.compareTo(b.endDateDuKien!);
        });

        allTasks.addAll(tmp3);
      }
      update();
    } catch (e) {
      print('Error refreshing data: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể làm mới dữ liệu: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }

  getOverviews2() async {
    Map<String, dynamic> tmp = await apiProvider.getTaskStatistics();
    if (tmp.isNotEmpty) {
      taskStats.value = tmp;
      update();
    }
    teamMembers = await apiProvider.getTeamMembers(waiting: true);
    var tmp1 = teamMembers
        .where((w) => w["tenChucVu"].toString().toLowerCase() != "viên chức")
        .toList();
    var tmp2 = teamMembers
        .where((w) => w["tenChucVu"].toString().toLowerCase() == "viên chức")
        .toList();
    //tmp1.sort((ele1, ele2) => ele2["tenChucVu"].toString().compareTo(ele1["tenChucVu"].toString()));
    //tmp2.sort((ele1, ele2) => ele2["ten"].toString().compareTo(ele1["ten"].toString()));
    tmp1 = tmp1.orderBy("tenChucVu", "desc");
    tmp2 = tmp2.orderBy("ten", "asc");
    teamMembers.clear();
    teamMembers.addAll(tmp1);
    teamMembers.addAll(tmp2);
    update();

    var tmp3 = await apiProvider.getAllTasks();
    if (tmp3.isNotEmpty) {
      if (kDebugMode) {
        print(tmp3);
      }
      allTasks.clear();
      allTasks.addAll(tmp3);
      update();
    }
  }
}
