import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:skeletons/skeletons.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxeinfo.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/dieuxetaixe.dart';
import 'package:tvumobile/app/data/models/tms/dieuxe/giaydieuxe.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/components/dieuxe_divider.dart';
import 'package:tvumobile/app/features/giangvien/dieuxe/controllers/dieuxe_detail_controller.dart';
import 'package:tvumobile/app/shared_components/input_decoration_theme.dart';
import 'package:tvumobile/app/shared_components/my_button.dart';
import 'package:tvumobile/app/shared_components/my_spacing.dart';
import 'package:tvumobile/app/shared_components/mytext.dart';

class HdThueXeWidget extends GetView<DieuXeDetailController> {
  const HdThueXeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final width = MediaQuery.of(context).size.width;
    final myDecoration = const InputDecoration().applyDefaults(
      GlobalInputDecoration(context),
    );

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.primaryColor.withOpacity(0.95),
        title: MyText.titleMedium(
          "HỢP ĐỒNG THUÊ XE".tr.toUpperCase(),
          color: theme.colorScheme.onPrimary,
          fontWeight: 700,
        ),
        iconTheme: theme.iconTheme.copyWith(color: theme.colorScheme.onPrimary),
        centerTitle: true,
      ),
      body: Obx(
        () => Column(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(
                  left: 16.0,
                  right: 16.0,
                  bottom: 16.0,
                  top: 4,
                ),
                child: FormBuilder(
                  key: controller.formKeyHDThueXe,
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: MyText.bodyMedium(
                            controller.isEditHDThueXe.value
                                ? "Chỉnh sửa thông tin hợp đồng".toUpperCase()
                                : "Cung cấp thông tin hợp đồng".toUpperCase(),
                            decoration: TextDecoration.underline,
                            color: theme.colorScheme.onSurface,
                            fontSize: 15,
                            fontWeight: 700,
                          ),
                        ),
                      ),

                      // ĐƠN VỊ
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 8.0, top: 0.0),
                          child: MyText.titleMedium(
                            "Đơn vị: ${controller.pdx.donVi!.tenDonVi}",
                            color: theme.colorScheme.onSurface,
                            fontSize: 15,
                            fontWeight: 700,
                          ),
                        ),
                      ),

                      // MySpacing.height(10),
                      // DieuXeDivider(context, "phòng hc-tc đã duyệt"),
                      // MySpacing.height(7),

                      // Obx(
                      //   () =>
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // --------- ĐƠN VỊ CHO THUÊ XE --------
                          MyText.bodyMedium(
                            'Đơn vị cho thuê xe:',
                            fontWeight: 700,
                          ),
                          FormBuilderTextField(
                            name: 'donvichothuexe',
                            validator: FormBuilderValidators.required(
                              errorText: 'Vui lòng nhập tên đơn vị cho thuê xe',
                            ),
                            decoration: InputDecoration(
                              hintText: 'Nhập tên đơn vị cho thuê xe',
                              hintStyle: TextStyle(
                                color: theme.colorScheme.onSurface.withOpacity(
                                  0.3,
                                ),
                              ),
                            ),
                            initialValue:
                                controller.pdx.hopDongThueXe?.donViChoThueXe ??
                                '',
                            maxLines: 2,
                            onChanged: (value) =>
                                controller.formDataHDThueXe['donvichothuexe'] =
                                    value,
                            onSaved: (newValue) =>
                                controller.formDataHDThueXe['donvichothuexe'] =
                                    newValue ?? '',
                            style: theme.textTheme.labelLarge,
                          ),
                          MySpacing.height(14),

                          // -------------- LOẠI XE -------------
                          MyText.bodyMedium('Loại xe:', fontWeight: 700),
                          FormBuilderTextField(
                            name: 'loaixe',
                            validator: FormBuilderValidators.required(
                              errorText: 'Vui lòng nhập thông tin loại xe',
                            ),
                            decoration: InputDecoration(
                              hintText: 'Nhập thông tin loại xe',
                              hintStyle: TextStyle(
                                color: theme.colorScheme.onSurface.withOpacity(
                                  0.3,
                                ),
                              ),
                            ),
                            initialValue:
                                controller.pdx.hopDongThueXe?.loaiXe ?? '',
                            maxLines: 2,
                            onChanged: (value) =>
                                controller.formDataHDThueXe['loaixe'] = value,
                            onSaved: (newValue) =>
                                controller.formDataHDThueXe['loaixe'] =
                                    newValue ?? '',
                            style: theme.textTheme.labelLarge,
                          ),
                          MySpacing.height(14),

                          // -------------- BIỂN SỐ -------------
                          MyText.bodyMedium('Biển số:', fontWeight: 700),
                          FormBuilderTextField(
                            name: 'bienso',
                            // validator: FormBuilderValidators.required(
                            //     errorText: 'Vui lòng nhập thông tin loại xe'),
                            decoration: InputDecoration(
                              hintText: 'Nhập biển số',
                              hintStyle: TextStyle(
                                color: theme.colorScheme.onSurface.withOpacity(
                                  0.3,
                                ),
                              ),
                            ),
                            initialValue:
                                controller.pdx.hopDongThueXe?.bienSo ?? '',
                            maxLines: 2,
                            onChanged: (value) =>
                                controller.formDataHDThueXe['bienso'] = value,
                            onSaved: (newValue) =>
                                controller.formDataHDThueXe['bienso'] =
                                    newValue ?? '',
                            style: theme.textTheme.labelLarge,
                          ),
                          MySpacing.height(14),

                          // ---------- NỘI DUNG HỢP ĐỒNG ----------
                          MyText.bodyMedium(
                            'Nội dung hợp đồng:',
                            fontWeight: 700,
                          ),
                          FormBuilderTextField(
                            name: 'noidunghopdong',
                            validator: FormBuilderValidators.required(
                              errorText: 'Vui lòng nhập nội dung hợp đồng',
                            ),
                            decoration: InputDecoration(
                              hintText: 'Nhập nội dung hợp đồng',
                              hintStyle: TextStyle(
                                color: theme.colorScheme.onSurface.withOpacity(
                                  0.3,
                                ),
                              ),
                            ),
                            initialValue:
                                controller.pdx.hopDongThueXe?.noiDung ?? '',
                            maxLines: 2,
                            onChanged: (value) =>
                                controller.formDataHDThueXe['noidunghopdong'] =
                                    value,
                            onSaved: (newValue) =>
                                controller.formDataHDThueXe['noidunghopdong'] =
                                    newValue ?? '',
                            style: theme.textTheme.labelLarge,
                          ),
                          MySpacing.height(14),

                          // ---------- GIÁ TRỊ HỢP ĐỒNG ----------
                          MyText.bodyMedium(
                            'Giá trị hợp đồng:',
                            fontWeight: 700,
                          ),
                          FormBuilderTextField(
                            name: 'giatrihopdong',
                            validator: FormBuilderValidators.required(
                              errorText: 'Vui lòng nhập giá trị hợp đồng',
                            ),
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              hintText: 'Nhập giá trị hợp đồng',
                              hintStyle: TextStyle(
                                color: theme.colorScheme.onSurface.withOpacity(
                                  0.3,
                                ),
                              ),
                            ),
                            initialValue:
                                controller.pdx.hopDongThueXe?.giaTriHopDong
                                    .toString() ??
                                '',
                            maxLines: 2,
                            onChanged: (value) =>
                                controller.formDataHDThueXe['giatrihopdong'] =
                                    value,
                            onSaved: (newValue) =>
                                controller.formDataHDThueXe['giatrihopdong'] =
                                    newValue ?? '',
                            style: theme.textTheme.labelLarge,
                          ),
                          MySpacing.height(14),

                          // ---------- NGÀY ĐI & NGÀY VỀ ---------
                          MyText.bodyMedium('Ngày thuê xe:', fontWeight: 700),
                          FormBuilderDateRangePicker(
                            name: 'thoigianthuexe',
                            firstDate: DateTime(DateTime.now().year - 1),
                            lastDate: DateTime(DateTime.now().year + 1),
                            format: DateFormat('dd/MM/yyyy'),
                            validator: FormBuilderValidators.required(
                              errorText:
                                  'Vui lòng chọn khoảng thời gian thuê xe',
                            ),
                            initialValue:
                                controller.pdx.hopDongThueXe?.tuNgay != null &&
                                    controller.pdx.hopDongThueXe?.denNgay !=
                                        null
                                ? DateTimeRange(
                                    start:
                                        controller.pdx.hopDongThueXe!.tuNgay!,
                                    end: controller.pdx.hopDongThueXe!.denNgay!,
                                  )
                                : null,
                            onChanged: (value) =>
                                controller.formDataHDThueXe["thoigianthuexe"] =
                                    value,
                            onSaved: (newValue) =>
                                controller.formDataHDThueXe["thoigianthuexe"] =
                                    newValue,
                            decoration: InputDecoration(
                              contentPadding: const EdgeInsets.fromLTRB(
                                4,
                                12,
                                4,
                                1,
                              ),
                              hintText: 'Khoảng thời gian thuê xe',
                              suffixIcon: IconButton(
                                padding: const EdgeInsets.all(0),
                                icon: const Icon(Icons.close),
                                onPressed: () => controller
                                    .formKeyHDThueXe
                                    .currentState!
                                    .fields['thoigianthuexe']
                                    ?.didChange(null),
                              ),
                            ),
                            locale: const Locale('vi'),
                            cancelText: "Hủy bỏ",
                            confirmText: "Xác nhận",
                            saveText: "Lưu",
                            fieldStartLabelText: "Ngày đi",
                            fieldEndLabelText: "Ngày về",
                            fieldStartHintText: "Ngày đi",
                            fieldEndHintText: "Ngày về",
                            // startValuePrefix: "Từ ngày: ",
                            // endValuePrefix: "Đến ngày: ",
                          ),
                          MySpacing.height(14),

                          // ---------- GHI CHÚ THUÊ XE -----------
                          MyText.bodyMedium('Ghi chú:', fontWeight: 700),
                          FormBuilderTextField(
                            name: 'ghichuthuexe',
                            // validator: FormBuilderValidators.required(
                            //     errorText: 'Vui lòng nhập ghi chú'),
                            decoration: InputDecoration(
                              hintText: 'Nhập ghi chú',
                              hintStyle: TextStyle(
                                color: theme.colorScheme.onSurface.withOpacity(
                                  0.3,
                                ),
                              ),
                            ),
                            initialValue:
                                controller.pdx.hopDongThueXe?.ghiChu ?? '',
                            maxLines: 2,
                            onChanged: (value) =>
                                controller.formDataHDThueXe['ghichuthuexe'] =
                                    value,
                            onSaved: (newValue) =>
                                controller.formDataHDThueXe['ghichuthuexe'] =
                                    newValue ?? '',
                            style: theme.textTheme.labelLarge,
                          ),
                        ],
                      ),

                      // ),
                      MySpacing.height(14),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16.0),
              color: Colors.transparent,
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: MyButton.block(
                      onPressed: () async {
                        bool isValid = controller.formKeyHDThueXe.currentState!
                            .validate();
                        if (isValid) {
                          controller.formKeyHDThueXe.currentState!.save();
                          // print("Form data: ${controller.formDataHDThueXe}");
                          await controller.themsuaHopDongThueXe();
                          Navigator.pop(context);
                        }
                      },
                      child: MyText.labelLarge(
                        controller.isEditHDThueXe.value
                            ? 'Cập nhật thông tin'
                            : "Lưu thông tin",
                        fontWeight: 900,
                        color: theme.colorScheme.onPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
